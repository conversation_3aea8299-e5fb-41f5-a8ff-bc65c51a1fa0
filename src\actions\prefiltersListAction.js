import _ from 'lodash';

import { onShowFailureAlert, onShowSuc<PERSON><PERSON>lert } from 'actions/alertActions';
import {
  onToggleLoader,
  onTogglePrefiltersListModal,
  onToggleCreateListModal,
  onToggleConfirmAlertModal,
  onToggleAddToListConfirmAlertModal,
  onToggleUploadLoader
} from 'actions/toggleActions';
import { onFetchTransactionDetails } from 'actions/transactionDetailsActions';
import { onFetchCustomerDetailsWithAllAccounts, onFetchUDSEntityDetails } from 'actions/udsActions';
import {
  ON_FETCH_SPECIALIZED_LIST_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_LOADING,
  ON_FETCH_SPECIALIZED_LIST_SUCCESS,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS,
  ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS,
  ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM,
  ON_FETCH_LIMIT_LIST_LOADING,
  ON_FETCH_LIMIT_LIST_SUCCESS,
  ON_FETCH_LIMIT_LIST_FAILURE,
  ON_FETCH_LIMIT_TYPE_LOADING,
  ON_FETCH_LIMIT_TYPE_SUCCESS,
  ON_FETCH_LIMIT_TYPE_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING,
  ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS,
  ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM,
  ON_FETCH_ALL_LISTS_LOADING,
  ON_FETCH_ALL_LISTS_SUCCESS,
  ON_FETCH_ALL_LISTS_FAILURE,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE,
  ON_RESET_LIMIT_LIST_WITH_PAGINATION,
  ON_ADD_TO_LIST_ITEM_CLICK,
  ON_LIST_BULK_UPLOAD_INITIATED,
  ON_LIST_BULK_UPLOAD_COMPLETED,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function createNewList(formData) {
  return client({
    method: 'POST',
    url: `listsandlimits/list/${formData.listName}/save`,
    badRequestMessage: 'Currently unable to create new list'
  });
}

function onCreateNewList(formData) {
  return function (dispatch) {
    return createNewList(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'List created successfully' }));
        dispatch(onFetchSpecializedListTypes());
        dispatch(onToggleCreateListModal());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function updateList(formData) {
  return client({
    method: 'PUT',
    data: formData,
    url: `listsandlimits/list/save`,
    badRequestMessage: 'Currently unable to update list'
  });
}

function onUpdateList(formData) {
  return function (dispatch) {
    return updateList(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'List updated successfully' }));
        dispatch(onFetchSpecializedListTypes());
        dispatch(onToggleCreateListModal());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function deleteList(listName) {
  return client({
    method: 'DELETE',
    url: `listsandlimits/list/${listName}/delete`,
    badRequestMessage: `Currently unable to delete ${listName} list`
  });
}

function onDeleteList(listName) {
  return function (dispatch) {
    return deleteList(listName).then(
      () => {
        dispatch(onShowSuccessAlert({ message: `${listName} list deleted successfully` }));
        dispatch(onFetchSpecializedListTypes());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchSpecializedListTypes() {
  return client({
    url: `listsandlimits/list/type`,
    badRequestMessage: 'Currently unable to fetch specialized list'
  });
}

function onFetchSpecializedListTypesLoading() {
  return { type: ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING };
}

function onFetchSpecializedListTypesSuccess(response) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS,
    response
  };
}

function onFetchSpecializedListTypesFailure(response) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE,
    response
  };
}

function onFetchSpecializedListTypes() {
  return function (dispatch) {
    dispatch(onFetchSpecializedListTypesLoading());
    return fetchSpecializedListTypes().then(
      (success) => dispatch(onFetchSpecializedListTypesSuccess(success)),
      (error) => dispatch(onFetchSpecializedListTypesFailure(error))
    );
  };
}

function fetchCategories() {
  return client({ url: `listsandlimits/list/category` });
}

function onFetchCategoriesLoading() {
  return { type: ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING };
}

function onFetchCategoriesSuccess(response) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS,
    response
  };
}

function onFetchCategoriesFailure(response) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE,
    response
  };
}

function onFetchCategories() {
  return function (dispatch) {
    dispatch(onFetchCategoriesLoading());
    return fetchCategories().then(
      (success) => dispatch(onFetchCategoriesSuccess(success)),
      (error) => dispatch(onFetchCategoriesFailure(error))
    );
  };
}

function fetchSpecializedList(formData) {
  return client({
    method: 'POST',
    url: `listsandlimits/list/info`,
    data: formData
  });
}

function onFetchSpecializedListLoading() {
  return { type: ON_FETCH_SPECIALIZED_LIST_LOADING };
}

function onFetchSpecializedListSuccess(response, conf, hydrate) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_SUCCESS,
    response,
    conf,
    hydrate
  };
}

function onFetchSpecializedListFailure(response, conf, hydrate) {
  return {
    type: ON_FETCH_SPECIALIZED_LIST_FAILURE,
    response,
    conf,
    hydrate
  };
}

function onFetchSpecializedList(formData, hydrate = true) {
  return function (dispatch) {
    dispatch(onFetchSpecializedListLoading());
    return fetchSpecializedList(formData).then(
      (success) => dispatch(onFetchSpecializedListSuccess(success, formData, hydrate)),
      (error) => dispatch(onFetchSpecializedListFailure(error, formData, hydrate))
    );
  };
}

function updateSpecializedListItem(formData, currentList) {
  return client({
    method: 'PUT',
    url: `listsandlimits/list/${currentList.prefilterValue}` + `/category/${formData.categoryName}`,
    data: formData,
    badRequestMessage: `Currently unable to update ${currentList.prefilterValue} item status`
  });
}

function onUpdateSpecializedListItemSuccess(formData) {
  return {
    type: ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS,
    formData
  };
}

function onUpdateSpecializedListItem(formData, currentList, listType, isEditMode) {
  return function (dispatch, getState) {
    const { userName } = getState().auth.userCreds;
    dispatch(onToggleLoader(true));
    return updateSpecializedListItem(formData, currentList)
      .then(
        () => {
          dispatch(
            onUpdateSpecializedListItemSuccess({
              ...formData,
              userName,
              updatedTimeStamp: new Date()
            })
          );

          !isEditMode &&
            dispatch(
              onShowSuccessAlert({
                message: formData.isActive === 0 ? 'Disabled successfully' : 'Enabled successfully'
              })
            );
          isEditMode && dispatch(onTogglePrefiltersListModal(listType));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteSpecializedListItem(formData, currentList) {
  const requestBody = {
    listType: currentList.prefilterValue,
    category: formData.categoryName,
    identifier: formData.identifier,
    ...(_.has(formData, 'partnerId') && { partnerId: formData.partnerId })
  };

  return client({
    method: 'POST',
    url: `listsandlimits/delete/list/info`,
    data: requestBody,
    badRequestMessage: `Currently unable to delete ${currentList.prefilterValue} item`
  });
}

function onSuccessfulDeleteSpecializedListItem(formData) {
  return {
    type: ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM,
    formData
  };
}

function onDeleteSpecializedListItem(formData, currentList, addToListAction, listType) {
  return function (dispatch, getState) {
    const { details } = getState().transactionDetails;
    dispatch(onToggleLoader(true));
    addToListAction
      ? dispatch(onToggleAddToListConfirmAlertModal())
      : dispatch(onToggleConfirmAlertModal(listType));
    return deleteSpecializedListItem(formData, currentList)
      .then(
        () => {
          dispatch(onSuccessfulDeleteSpecializedListItem(formData));
          dispatch(
            onShowSuccessAlert({
              message: `Entry removed from ${currentList.prefilterValue} list successfully`
            })
          );
          if (addToListAction) {
            const entityId = _.has(details, 'entityId') ? details.entityId.value : '';
            const txnId = _.has(details, 'transactionInfo') ? details.transactionInfo.txnId : '';
            const channel = _.has(details, 'identifiers') ? details.identifiers.channelType : '';

            !_.isEmpty(entityId) &&
              (_.lowerCase(details?.entityCategory) === 'customer'
                ? dispatch(onFetchCustomerDetailsWithAllAccounts(entityId))
                : dispatch(
                    onFetchUDSEntityDetails(
                      _.lowerCase(details?.entityCategory || 'agent'),
                      entityId
                    )
                  ));

            !_.isEmpty(txnId) && dispatch(onFetchTransactionDetails(txnId, channel));
          }
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteSpecializedListItemInBulk(formData) {
  return client({
    method: 'POST',
    url: `listsandlimits/bulk/delete`,
    data: formData
  });
}

function onDeleteSpecializedListItemInBulk(formData, currentList, listType) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    dispatch(onToggleConfirmAlertModal(listType));
    return deleteSpecializedListItemInBulk(formData)
      .then(
        () => {
          dispatch(
            onFetchSpecializedList(
              {
                listName: currentList.prefilterValue,
                categoryName: '',
                identifier: '',
                pageNumber: 1,
                pageRecords: 10
              },
              false
            )
          );
          dispatch(
            onShowSuccessAlert({
              message: `Entries removed from ${currentList.prefilterValue} list successfully`
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function addSingleItemToSpecializedList(formData, currentList) {
  return client({
    method: 'POST',
    url: `listsandlimits/list/${currentList.prefilterValue}/info`,
    data: formData,
    badRequestMessage: `Unable to add item to ${currentList.prefilterValue} list`
  });
}

function onAddSingleItemToSpecializedList(formData, currentList, listType, addToListAction) {
  return function (dispatch, getState) {
    const { details } = getState().transactionDetails;
    addToListAction && dispatch(onToggleAddToListConfirmAlertModal());
    return addSingleItemToSpecializedList(formData, currentList).then(
      () => {
        dispatch(
          onShowSuccessAlert({
            message: `Entity added to ${currentList.prefilterValue} list successfully`
          })
        );
        dispatch(
          onFetchSpecializedList({
            listName: currentList.prefilterValue,
            categoryName: '',
            identifier: '',
            pageNumber: 1,
            pageRecords: 10
          })
        );
        if (addToListAction) {
          const entityId = _.has(details, 'entityId') ? details.entityId.value : '';
          const txnId = _.has(details, 'transactionInfo') ? details.transactionInfo.txnId : '';
          const channel = _.has(details, 'identifiers') ? details.identifiers.channelType : '';

          !_.isEmpty(entityId) &&
            (_.lowerCase(details?.entityCategory) === 'customer'
              ? dispatch(onFetchCustomerDetailsWithAllAccounts(entityId))
              : dispatch(
                  onFetchUDSEntityDetails(_.lowerCase(details?.entityCategory || 'agent'), entityId)
                ));

          !_.isEmpty(txnId) && dispatch(onFetchTransactionDetails(txnId, channel));
        } else dispatch(onTogglePrefiltersListModal(listType));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addItemsInBulkToSpecializedList(formData, currentList) {
  const bulkUpload = new FormData();
  bulkUpload.append('list_info', formData[currentList.prefilterValue]);
  return client({
    method: 'POST',
    url: `listsandlimitsrdbms/list/${currentList.prefilterValue}/listInfo-bulk-upload`,
    multipart: true,
    headers: {
      processData: false,
      contentType: false
    },
    data: bulkUpload,
    badRequestMessage: 'Unable to bulk upload list. Check file data'
  });
}

function onAddItemsInBulkToSpecializedListSuccess(response) {
  return {
    type: ON_LIST_BULK_UPLOAD_INITIATED,
    response
  };
}

function onAddItemsInBulkToSpecializedList(formData, currentList, listType) {
  return function (dispatch) {
    dispatch(onToggleUploadLoader(true));
    return addItemsInBulkToSpecializedList(formData, currentList)
      .then(
        (_success) => {
          dispatch(onTogglePrefiltersListModal(listType));
          dispatch(
            onFetchSpecializedList({
              listName: currentList.prefilterValue,
              categoryName: '',
              identifier: '',
              startDate: '',
              endDate: '',
              pageNumber: 1,
              pageRecords: 10
            })
          );
          dispatch(
            onShowSuccessAlert({
              message: 'List data upload initiated! Please check status in few minutes.'
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleUploadLoader(false)));
  };
}

function fetchLimitList(currentList) {
  return client({ url: `listsandlimits/limit/${currentList.prefilterValue}` });
}

function onFetchLimitListLoading() {
  return { type: ON_FETCH_LIMIT_LIST_LOADING };
}

function onFetchLimitListSuccess(response) {
  return {
    type: ON_FETCH_LIMIT_LIST_SUCCESS,
    response
  };
}

function onFetchLimitListFailure(response) {
  return {
    type: ON_FETCH_LIMIT_LIST_FAILURE,
    response
  };
}

function onFetchLimitList(currentList) {
  return function (dispatch) {
    dispatch(onFetchLimitListLoading());
    return fetchLimitList(currentList).then(
      (success) => dispatch(onFetchLimitListSuccess(success)),
      (error) => dispatch(onFetchLimitListFailure(error))
    );
  };
}

function fetchLimitListWithPagination(currentList, formData) {
  return client({
    method: 'POST',
    url: `listsandlimits/limit/${currentList.prefilterValue}/data`,
    data: formData
  });
}

function onFetchLimitListWithPaginationLoading() {
  return { type: ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING };
}

function onFetchLimitListWithPaginationSuccess(response, fetchFrom, currentId) {
  return {
    type: ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS,
    response,
    fetchFrom,
    currentId
  };
}

function onFetchLimitListWithPaginationFailure(response) {
  return {
    type: ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE,
    response
  };
}

function onFetchLimitListWithPagination(currentList, formData, fetchFrom = '', currentId = '') {
  return function (dispatch) {
    dispatch(onFetchLimitListWithPaginationLoading());
    return fetchLimitListWithPagination(currentList, formData).then(
      (success) => dispatch(onFetchLimitListWithPaginationSuccess(success, fetchFrom, currentId)),
      (error) => dispatch(onFetchLimitListWithPaginationFailure(error))
    );
  };
}

function onResetLimitListWithPagination() {
  return { type: ON_RESET_LIMIT_LIST_WITH_PAGINATION };
}

function updateLimitList(formData, currentList) {
  return client({
    method: 'PUT',
    url: `listsandlimits/limit/${currentList.prefilterValue}`,
    data: formData,
    badRequestMessage: `Currently unable to update item in ${currentList.prefilterValue} list`
  });
}

function onUpdateLimitList(
  formData,
  currentList,
  listType,
  fetchLimitListWithPaginationData,
  currentId = ''
) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateLimitList(formData, currentList)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: `${currentList.prefilterName} Item Updated Successfully`
            })
          );
          fetchLimitListWithPaginationData
            ? dispatch(
                onFetchLimitListWithPagination(
                  currentList,
                  fetchLimitListWithPaginationData,
                  'update',
                  currentId
                )
              )
            : dispatch(onFetchLimitList(currentList));
          dispatch(onTogglePrefiltersListModal(listType));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchLimitType() {
  return client({
    url: `listsandlimits/limit/type`
  });
}

function onFetchLimitTypeLoading() {
  return { type: ON_FETCH_LIMIT_TYPE_LOADING };
}

function onFetchLimitTypeSuccess(response) {
  return {
    type: ON_FETCH_LIMIT_TYPE_SUCCESS,
    response
  };
}

function onFetchLimitTypeFailure(response) {
  return {
    type: ON_FETCH_LIMIT_TYPE_FAILURE,
    response
  };
}

function onFetchLimitType() {
  return function (dispatch) {
    dispatch(onFetchLimitTypeLoading());
    return fetchLimitType().then(
      (success) => dispatch(onFetchLimitTypeSuccess(success)),
      (error) => dispatch(onFetchLimitTypeFailure(error))
    );
  };
}

function addSingleItemToLimitList(formData, currentList) {
  return client({
    method: 'POST',
    url: `listsandlimits/limit/${currentList.prefilterValue}`,
    data: formData,
    badRequestMessage: `Currently unable to add item to ${currentList.prefilterValue} list`
  });
}

function onAddSingleItemToLimitList(
  formData,
  currentList,
  listType,
  fetchLimitListWithPaginationData
) {
  return function (dispatch) {
    return addSingleItemToLimitList(formData, currentList).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Entity added successfully' }));
        fetchLimitListWithPaginationData
          ? dispatch(onFetchLimitListWithPagination(currentList, fetchLimitListWithPaginationData))
          : dispatch(onFetchLimitList(currentList));
        dispatch(onTogglePrefiltersListModal(listType));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addItemsInBulkToLimitList(formData, currentList) {
  const bulkUpload = new FormData();
  bulkUpload.append('limit_info', formData[currentList.prefilterValue]);
  return client({
    method: 'POST',
    url: `listsandlimits/limit/${currentList.prefilterValue}/limitInfo-bulk-upload`,
    multipart: true,
    headers: {
      processData: false,
      contentType: false
    },
    data: bulkUpload,
    badRequestMessage: `Unable to upload items to ${currentList.prefilterValue} list`
  });
}

function onAddItemsInBulkToLimitList(
  formData,
  currentList,
  listType,
  fetchLimitListWithPaginationData
) {
  return function (dispatch) {
    dispatch(onToggleUploadLoader(true));
    return addItemsInBulkToLimitList(formData, currentList)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Entities added successfully' }));
          fetchLimitListWithPaginationData
            ? dispatch(
                onFetchLimitListWithPagination(currentList, fetchLimitListWithPaginationData)
              )
            : dispatch(onFetchLimitList(currentList));
          dispatch(onTogglePrefiltersListModal(listType));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleUploadLoader(false)));
  };
}

function deleteLimitListItem(formData, currentList) {
  return client({
    method: 'DELETE',
    url: _.has(formData, 'merchantlimitType')
      ? `listsandlimits/limit/${formData.merchantlimitType}/${currentList.prefilterValue}/${formData.type}`
      : `listsandlimits/limit/${currentList.prefilterValue}/${formData.type}`,
    badRequestMessage: `Currently unable to delete from ${currentList.prefilterValue} list`
  });
}

function onSuccessfulDeleteLimitListItem(formData) {
  return {
    type: ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM,
    entity: formData
  };
}

function onDeleteLimitListItem(formData, currentList, listType) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    dispatch(onToggleConfirmAlertModal(listType));
    return deleteLimitListItem(formData, currentList)
      .then(
        () => {
          dispatch(onSuccessfulDeleteLimitListItem(formData));
          dispatch(onShowSuccessAlert({ message: 'Entry removed successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchAllLists() {
  return client({ url: `listsandlimits/list/info` });
}

function onFetchAllListsLoading() {
  return { type: ON_FETCH_ALL_LISTS_LOADING };
}

function onFetchAllListsSuccess(response) {
  return {
    type: ON_FETCH_ALL_LISTS_SUCCESS,
    response
  };
}

function onFetchAllListsFailure(response) {
  return {
    type: ON_FETCH_ALL_LISTS_FAILURE,
    response
  };
}

function onFetchAllLists() {
  return function (dispatch) {
    dispatch(onFetchAllListsLoading());
    return fetchAllLists().then(
      (success) => dispatch(onFetchAllListsSuccess(success)),
      (error) => dispatch(onFetchAllListsFailure(error))
    );
  };
}

function onAddToListItemClick(currentItemInfo) {
  return { type: ON_ADD_TO_LIST_ITEM_CLICK, currentItemInfo };
}

function fetchBulkListUploadStatus(formData, listType) {
  return client({
    method: 'POST',
    url: `listsandlimits/file/status`,
    data: formData,
    badRequestMessage: `Failed to upload items in ${listType} list`
  });
}

function onFetchBulkListUploadStatusCompleted() {
  return {
    type: ON_LIST_BULK_UPLOAD_COMPLETED
  };
}

function onFetchBulkListUploadStatus(formData, listType) {
  return function (dispatch) {
    return fetchBulkListUploadStatus(formData, listType).then(
      (success) => {
        if (success?.status !== 'InProgress') {
          dispatch(onFetchBulkListUploadStatusCompleted());
          success?.status === 'Success'
            ? dispatch(onShowSuccessAlert({ message: 'List data uploaded successfully!' }))
            : dispatch(onShowFailureAlert({ message: 'List data upload failed!' }));
          dispatch(onToggleLoader(false));
        }
      },
      (error) => {
        dispatch(onFetchBulkListUploadStatusCompleted());
        dispatch(onShowFailureAlert(error));
        dispatch(onToggleLoader(false));
      }
    );
  };
}

function fetchBlockedListIdentifier(formData) {
  return client({
    method: 'POST',
    url: `listsandlimits/blocked/status`,
    data: formData,
    badRequestMessage: `Unable to fetch block details of ${formData?.categoryName}`
  });
}

function onFetchBlockedListIdentifierLoading(txnId) {
  return { type: ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING, txnId };
}

function onFetchBlockedListIdentifierSuccess(response) {
  return {
    type: ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS,
    response
  };
}

function onFetchBlockedListIdentifierFailure(response) {
  return {
    type: ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE,
    response
  };
}

function onFetchBlockedListIdentifier(txnId, formData) {
  return function (dispatch) {
    dispatch(onFetchBlockedListIdentifierLoading(txnId));
    return fetchBlockedListIdentifier(formData).then(
      (success) => dispatch(onFetchBlockedListIdentifierSuccess(success)),
      (error) => dispatch(onFetchBlockedListIdentifierFailure(error))
    );
  };
}

export {
  onCreateNewList,
  onFetchSpecializedListTypes,
  onFetchCategories,
  onFetchSpecializedList,
  onUpdateSpecializedListItem,
  onDeleteSpecializedListItem,
  onDeleteSpecializedListItemInBulk,
  onAddSingleItemToSpecializedList,
  onAddItemsInBulkToSpecializedList,
  onFetchLimitListWithPagination,
  onResetLimitListWithPagination,
  onFetchLimitList,
  onUpdateLimitList,
  onFetchLimitType,
  onAddItemsInBulkToLimitList,
  onAddSingleItemToLimitList,
  onDeleteLimitListItem,
  onFetchAllLists,
  onAddToListItemClick,
  onFetchBulkListUploadStatus,
  onFetchBlockedListIdentifier,
  onDeleteList,
  onUpdateList
};
