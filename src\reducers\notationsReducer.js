import objectAssign from 'object-assign';

import {
  ON_FETCH_NOTATIONS_LIST_LOADING,
  ON_FETCH_NOTATIONS_LIST_SUCCESS,
  ON_FETCH_NOTATIONS_LIST_FAILURE,
  ON_UPDATE_NOTATION_SUCCESS,
  ON_DELETE_NOTATION_SUCCESS,
  ON_FETCH_CASE_NOTATION_LIST_LOADING,
  ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
  ON_FETCH_CASE_NOTATION_LIST_FAILURE,
  ON_ADD_CASE_NOTATION_SUCCESS
} from 'constants/actionTypes';

import initialState from './initialState';

export default function notationsReducer(state = initialState.notations, action) {
  switch (action.type) {
    case ON_FETCH_NOTATIONS_LIST_LOADING:
      return objectAssign({}, state, {
        master: objectAssign({}, initialState.notations.master, { loader: true })
      });
    case ON_FETCH_NOTATIONS_LIST_SUCCESS:
      return objectAssign({}, state, {
        master: objectAssign({}, initialState.notations.master, { list: action.response })
      });
    case ON_FETCH_NOTATIONS_LIST_FAILURE:
      return objectAssign({}, state, {
        master: objectAssign({}, initialState.notations.master, {
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_UPDATE_NOTATION_SUCCESS:
      return objectAssign({}, state, {
        master: objectAssign({}, state.master, {
          list: state.master.list.map((notation) =>
            notation.id === action.response.id ? action.response : notation
          )
        })
      });
    case ON_DELETE_NOTATION_SUCCESS:
      return objectAssign({}, state, {
        master: objectAssign({}, state.master, {
          list: state.master.list.filter((notation) => notation.id === action.id)
        })
      });
    case ON_FETCH_CASE_NOTATION_LIST_LOADING:
      return objectAssign({}, state, {
        case: objectAssign({}, initialState.notations.case, { loader: true })
      });
    case ON_FETCH_CASE_NOTATION_LIST_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, initialState.notations.case, { list: action.response })
      });
    case ON_FETCH_CASE_NOTATION_LIST_FAILURE:
      return objectAssign({}, state, {
        case: objectAssign({}, initialState.notations.case, {
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_ADD_CASE_NOTATION_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, state.case, {
          list: [
            ...state.case.list,
            {
              caseId: action.notation.caseId,
              notationUserName: action.notation.userName,
              notationTimestamp: new Date(),
              notationComment: action.notation.notationComment
            }
          ],
          error: false,
          errorMessage: ''
        })
      });
    default:
      return state;
  }
}
