import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import { onFetchCaseLogs, onFetchEntityLogs } from 'actions/logsActions';
import responses from 'mocks/responses';

describe('logs actions', () => {
  it('should fetch logs for case', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS,
        response: responses.caseAssignment.caseLog
      }
    ];
    const store = mockStore({ logs: {} });

    return store.dispatch(onFetchCaseLogs('1')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch entity logs', () => {
    const formData = {
      pageNo: 1,
      pageRecords: 10,
      filterCondition: []
    };

    const expectedActions = [
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      {
        type: types.ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS,
        response: responses.caseAssignment.statusLog,
        entityId: '123',
        filterCondition: []
      }
    ];

    const store = mockStore({ logs: {} });

    return store.dispatch(onFetchEntityLogs('123', 'frm', formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
