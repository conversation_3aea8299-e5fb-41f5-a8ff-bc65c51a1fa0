import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { CardTitle, CardSubtitle, Row, Col, Input, Button, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

import RFIReportsTable from './RFIReportsTable';

function TopMerchantsReport({ topMerchant, fetchTopMerchantCount, fetchTopMerchantData }) {
  const [cummValue, setCummValue] = useState(topMerchant.cummValue);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchTopMerchantData({ pageNo: page + 1, pageSize, cummValue });
    setPageNo(page);
  };

  const columnHeaders = [
    {
      Header: 'Customers',
      columns: [
        {
          Header: 'Amount',
          accessor: 'totalTxnAmountByCustomer',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalTxnAmountByCustomer'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalTxnCountByCustomer',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalTxnCountByCustomer'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    },
    {
      Header: 'Merchants Monthly',
      columns: [
        {
          Header: 'Amount',
          accessor: 'totalMonthlyTxnAmountByMerchant',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              step="0.01"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalMonthlyTxnAmountByMerchant'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalMonthlyTxnCountByMerchant',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              step="0.01"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'totalMonthlyTxnCountByMerchant'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  const onSubmit = (e) => {
    e.preventDefault();
    if (cummValue !== topMerchant.cummValue && !isNaN(cummValue))
      fetchTopMerchantCount({ cummValue });
  };

  return (
    <Row>
      <Col md="12" className="mb-3">
        <form onSubmit={onSubmit}>
          <Row>
            <Col xs="6" md="3">
              <Label htmlFor="cummValue">Total credit amount</Label>
              <Input
                type="number"
                id="cummValue"
                name="cummValue"
                value={cummValue}
                placeholder="Enter value to generate report"
                onChange={(e) => setCummValue(+e.target.value)}
                min={0}
                max={99999999999}
                pattern="\d{1,11}"
                title="Please input valid amount"
                required
              />
            </Col>
            <Col className="d-flex align-items-end">
              <Button outline size="sm" color="primary">
                Search
              </Button>
            </Col>
          </Row>
        </form>
      </Col>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{topMerchant.count?.value ?? 0}</CardTitle>
          <CardSubtitle># Top Merchants</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="Top Merchants">
          {topMerchant.count?.value > 0 ? (
            <RFIReportsTable
              count={topMerchant.count?.value}
              additionalHeaders={columnHeaders}
              data={topMerchant.data}
              filter={{ cummValue }}
              fetchData={fetchTopMerchantData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div">No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

TopMerchantsReport.propTypes = {
  topMerchant: PropTypes.object.isRequired,
  fetchTopMerchantCount: PropTypes.func.isRequired,
  fetchTopMerchantData: PropTypes.func.isRequired
};

export default TopMerchantsReport;
