import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onShowFailureAlert } from 'actions/alertActions';
import { onToggleVerdictModal } from 'actions/toggleActions';
import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import { onFetchCasesForClosure, onClearBulkClosureSearch } from 'actions/caseAssignmentActions';
import BulkClosure from 'components/common/BulkClosure';

const mapStateToProps = (state) => {
  return {
    closureCases: state.caseAssignment.closureCases,
    ruleConfigurator: state.ruleConfigurator,
    channels: state.auth.userCreds.channels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRulesList: bindActionCreators(onFetchRulesList, dispatch),
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    toggleVerdictModal: bindActionCreators(onToggleVerdictModal, dispatch),
    fetchCasesForClosure: bindActionCreators(onFetchCasesForClosure, dispatch),
    clearBulkClosureSearch: bindActionCreators(onClearBulkClosureSearch, dispatch)
  };
};

const BulkClosureContainer = connect(mapStateToProps, mapDispatchToProps)(BulkClosure);

export default BulkClosureContainer;
