import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Form, FormGroup, Table, Button, Label, Input } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';

function RoleAssignmentModal({
  theme,
  roleslist,
  userChannels,
  channelslist,
  showUserModal,
  selectedUser,
  updateUserRoles,
  fetchPartnerList,
  toggleUpdateUserRolesModal,
  loginType,
  hasDualRole = 0,
  userslist
}) {
  const [selectedRoles, setSelectedRoles] = useState([]);

  useEffect(() => {
    fetchPartnerList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setSelectedRoles(selectedUser?.channelRoles);
  }, [selectedUser]);

  const channels = userChannels.length === 0 ? channelslist?.map((d) => d.name) : userChannels;

  const filteredRoles = _.filter(
    roleslist,
    (role) =>
      !_.includes(['super-admin', 'admin', 'supervisor'], role.name) &&
      _.intersection(role.channel, channels).length > 0 &&
      (!isCooperative ||
        (loginType.toLowerCase() === 'pulse'
          ? _.includes(role.loginType, selectedUser?.loginType)
          : _.includes(role.loginType, loginType)))
  );

  const shouldRoleBeDisabled = (channel, role, selectedRoles) => {
    if (hasDualRole === 1)
      return (
        _.includes(['maker', 'checker'], role) &&
        _.intersection(selectedRoles, [`${channel}:principal-officer`, `${channel}:auditor`])
          .length > 0
      );

    return selectedRoles.length > 0 && !_.includes(selectedRoles, `${channel}:${role}`);
  };

  const isPOAlreadyExistForSelectedPartner = (channel, role, selectedRoles) => {
    const data = _.find(
      userslist,
      (user) =>
        user.partnerId === selectedUser.partnerId &&
        _.includes(user.channelRoles, `${channel}:${role}`)
    );

    return (
      !_.isEmpty(data) &&
      !_.includes(selectedRoles, `${channel}:${role}`) &&
      !_.includes(selectedUser?.channelRoles, `${channel}:${role}`)
    );
  };

  const roleCheckboxes =
    !_.isEmpty(filteredRoles) &&
    !_.isEmpty(channels) &&
    filteredRoles.map((role) => {
      const disable =
        role.name === 'principal-officer'
          ? isPOAlreadyExistForSelectedPartner(channels[0], role.name, selectedRoles)
          : shouldRoleBeDisabled(channels[0], role.name, selectedRoles);
      return (
        <td key={role.id}>
          <FormGroup check className="ms-4">
            <Input
              type="checkbox"
              id={`checkbox${role.id}`}
              name="channelRole[]"
              value={`${channels[0]}:${role.name}`}
              onChange={() => toggleRoleCheckbox(`${channels[0]}:${role.name}`)}
              checked={_.includes(selectedRoles, `${channels[0]}:${role.name}`)}
              disabled={disable}
            />
            <Label htmlFor={`checkbox${role.id}`} className={disable && 'text-muted'}>
              {role.name}
            </Label>
          </FormGroup>
        </td>
      );
    });

  function toggleRoleCheckbox(channelRole) {
    let newRoles = [];
    const isIncluded = _.includes(selectedRoles, channelRole);
    if (isIncluded) newRoles = _.filter(selectedRoles, (role) => role !== channelRole);
    else if (
      isCooperative ||
      _.split(channelRole, ':')[1] === 'principal-officer' ||
      _.split(channelRole, ':')[1] === 'auditor'
    )
      newRoles = [channelRole];
    else newRoles = [...selectedRoles, channelRole];

    setSelectedRoles(newRoles);
  }

  function addUserRolesSubmit(e) {
    e.preventDefault();
    const userData = {
      userId: selectedUser.id,
      userName: selectedUser.userName,
      channelRoles: selectedRoles
    };
    updateUserRoles({ userData });
  }

  return (
    <ModalContainer
      theme={theme}
      isOpen={showUserModal}
      header={`Update roles for user - ${selectedUser.userName}`}
      toggle={() => toggleUpdateUserRolesModal()}>
      <Form onSubmit={(e) => addUserRolesSubmit(e)}>
        <FormGroup>
          <Table borderless responsive size="sm">
            <tbody>{roleCheckboxes}</tbody>
          </Table>
        </FormGroup>
        <Button
          size="sm"
          color="primary"
          className="d-flex ms-auto"
          disabled={_.isEmpty(selectedRoles)}>
          Submit
        </Button>
      </Form>
    </ModalContainer>
  );
}

RoleAssignmentModal.propTypes = {
  theme: PropTypes.string.isRequired,
  roleslist: PropTypes.array.isRequired,
  partnerList: PropTypes.array.isRequired,
  userChannels: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  showUserModal: PropTypes.bool.isRequired,
  hasDualRole: PropTypes.number.isRequired,
  selectedUser: PropTypes.object.isRequired,
  updateUserRoles: PropTypes.func.isRequired,
  fetchPartnerList: PropTypes.func.isRequired,
  toggleUpdateUserRolesModal: PropTypes.func.isRequired,
  loginType: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired
};

export default RoleAssignmentModal;
