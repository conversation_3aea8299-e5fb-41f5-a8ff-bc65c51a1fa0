import React from 'react';
import { Table } from 'reactstrap';
import { range } from 'lodash';

const TableLoader = () => {
  return (
    <Table responsive className="table-loading">
      <tbody>
        {range(5).map((d) => (
          <tr key={d}>
            {range(5).map((k) => (
              <td key={d + k} className={'td-' + (k + 1)}>
                <span>{''}</span>
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </Table>
  );
};

export default TableLoader;
