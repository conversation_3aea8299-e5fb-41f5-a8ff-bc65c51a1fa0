import { range } from 'lodash';
import React from 'react';
import { Table } from 'reactstrap';

const TableLoader = () => (
  <Table responsive className="table-loading">
    <tbody>
      {range(5).map((d) => (
        <tr key={d}>
          {range(5).map((k) => (
            <td key={d + k} className={`td-${k + 1}`}>
              <span />
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  </Table>
);

export default TableLoader;
