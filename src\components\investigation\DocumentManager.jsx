import { faPaperclip, faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { Card, Input, ListGroup, ListGroupItem, ListGroupItemText } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import Loader from 'components/loader/Loader';
import { BASE_URL, URL_CONTEXT } from 'constants/applicationConstants';

function DocumentManager({ caseRefNo, caseDocument, actions, canUpload = false }) {
  const [fileData, setFileData] = useState(null);

  useEffect(() => {
    if (caseRefNo) actions.onFetchCaseDocuments(caseRefNo);
  }, [actions, caseRefNo]);

  useEffect(() => {
    if (fileData) {
      actions.onSubmitCaseDocument(caseRefNo, { file: fileData });
      setFileData(null);
    }
  }, [actions, caseRefNo, fileData]);

  const uploadAction = canUpload ? (
    <label
      htmlFor="fileUploader"
      className="btn btn-primary"
      title="Supports: Images, PDF, Docs, Spreadsheet, Text & XML">
      <Input
        type="file"
        name="fileUploader"
        id="fileUploader"
        accept="image/*,application/pdf,text/csv,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text,application/vnd.oasis.opendocument.spreadsheet,text/plain,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/xml"
        files={fileData}
        onChange={(event) => setFileData(event.target.files[0])}
        hidden
      />
      <FontAwesomeIcon icon={faPaperclip} /> Add Document
    </label>
  ) : null;

  const renderContent = () => {
    if (caseDocument.loader) return <Loader show={true} />;

    if (caseDocument.error) return <div className="no-data-div">{caseDocument.errorMessage}</div>;

    if (caseDocument.data?.length < 1)
      return <div className="no-data-div">No documents available</div>;

    return (
      <ListGroup className="file-upload-preview">
        {caseDocument.data?.map((file, i) => (
          <ListGroupItem key={i}>
            <ListGroupItemText className="file-title">
              {file.fileName}
              <a
                href={
                  `${BASE_URL}${URL_CONTEXT}casereview/case/${caseRefNo}` +
                  `/evidence-files/${file.fileName}/download`
                }
                download={file.fileName}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary">
                <FontAwesomeIcon icon={faDownload} />
              </a>
            </ListGroupItemText>
          </ListGroupItem>
        ))}
      </ListGroup>
    );
  };

  return (
    <CardContainer title="Documents" action={uploadAction}>
      <Card>{renderContent()}</Card>
    </CardContainer>
  );
}

DocumentManager.propTypes = {
  canUpload: PropTypes.bool,
  caseRefNo: PropTypes.string.isRequired,
  caseDocument: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired
};

export default DocumentManager;
