import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Card, Input, ListGroup, ListGroupItem, ListGroupItemText } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaperclip, faDownload } from '@fortawesome/free-solid-svg-icons';

import Loader from 'components/loader/Loader';
import CardContainer from 'components/common/CardContainer';
import { BASE_URL, URL_CONTEXT } from 'constants/applicationConstants';

function DocumentManager({ caseRefNo, caseDocument, actions, canUpload = false }) {
  const [fileData, setFileData] = useState(null);

  useEffect(() => {
    if (caseRefNo) actions.onFetchCaseDocuments(caseRefNo);
  }, [caseRefNo]);

  useEffect(() => {
    if (fileData) {
      actions.onSubmitCaseDocument(caseRefNo, { file: fileData });
      setFileData(null);
    }
  }, [fileData]);

  const uploadAction = canUpload ? (
    <label
      htmlFor="fileUploader"
      className="btn btn-primary"
      title="Supports: Images, PDF, Docs, Spreadsheet, Text & XML">
      <Input
        type="file"
        name="fileUploader"
        id="fileUploader"
        accept="image/*,application/pdf,text/csv,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.text,application/vnd.oasis.opendocument.spreadsheet,text/plain,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/xml"
        files={fileData}
        onChange={(event) => setFileData(event.target.files[0])}
        hidden
      />
      <FontAwesomeIcon icon={faPaperclip} /> Add Document
    </label>
  ) : null;

  return (
    <CardContainer title="Documents" action={uploadAction}>
      <Card>
        {caseDocument.loader ? (
          <Loader show={true} />
        ) : caseDocument.error ? (
          <div className="no-data-div">{caseDocument.errorMessage}</div>
        ) : caseDocument.data?.length < 1 ? (
          <div className="no-data-div">No documents available</div>
        ) : (
          <ListGroup className="file-upload-preview">
            {caseDocument.data?.map((file, i) => (
              <ListGroupItem key={i}>
                <ListGroupItemText className="file-title">
                  {file.fileName}
                  <a
                    href={
                      `${BASE_URL}${URL_CONTEXT}casereview/case/${caseRefNo}` +
                      `/evidence-files/${file.fileName}/download`
                    }
                    download={file.fileName}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary">
                    <FontAwesomeIcon icon={faDownload} />
                  </a>
                </ListGroupItemText>
              </ListGroupItem>
            ))}
          </ListGroup>
        )}
      </Card>
    </CardContainer>
  );
}

DocumentManager.propTypes = {
  canUpload: PropTypes.bool,
  caseRefNo: PropTypes.string.isRequired,
  caseDocument: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired
};

export default DocumentManager;
