import React from 'react';
import PropTypes from 'prop-types';
import { Alert, Button } from 'reactstrap';

function CaseNotification({
  txnId,
  channel,
  userName,
  notifications,
  acknowledgeNotification,
  resolveNotification
}) {
  const caseNotifications = notifications?.filter((d) => d?.txnId === txnId);
  const onAcknowledge = (caseNotification) => {
    const formData = {
      ...caseNotification,
      accept: true
    };
    acknowledgeNotification(channel, formData);
  };

  const onResolve = (caseNotification) => {
    const formData = {
      ...caseNotification,
      resolve: true
    };
    resolveNotification(channel, formData);
  };

  if (caseNotifications.length === 0) return null;

  const notificationAction = (notification) => {
    switch (true) {
      case !notification.isAcknowledged:
        return (
          <Button outline size="sm" color="primary" onClick={() => onAcknowledge(notification)}>
            Acknowledge
          </Button>
        );
      case notification.isAcknowledged &&
        !notification.isResolved &&
        notification.acknowledgedBy === userName:
        return (
          <Button outline size="sm" color="success" onClick={() => onResolve(notification)}>
            Resolve
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {caseNotifications.map((caseNotification) => (
        <Alert
          color="info"
          key={caseNotification.id}
          className="mb-3 p-2 d-flex justify-content-between align-align-items-center gap-5">
          {caseNotification.message}
          {notificationAction(caseNotification)}
        </Alert>
      ))}
    </>
  );
}

CaseNotification.propTypes = {
  txnId: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  notifications: PropTypes.array.isRequired,
  resolveNotification: PropTypes.func.isRequired,
  acknowledgeNotification: PropTypes.func.isRequired
};

export default CaseNotification;
