import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCustomerTrend } from 'actions/investigationActions';
import TransactionTimeAmountTrend from 'components/common/TransactionTimeAmountTrend';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  trends: state.investigation.trends
});

const mapDispatchToProps = (dispatch) => ({
  fetchCustomerTrend: bindActionCreators(onFetchCustomerTrend, dispatch)
});

const TransactionTimeAmountTrendContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(TransactionTimeAmountTrend);

export default TransactionTimeAmountTrendContainer;
