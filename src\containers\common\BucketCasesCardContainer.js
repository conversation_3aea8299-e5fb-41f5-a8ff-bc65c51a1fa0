import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCases, onFetchBuckets } from 'actions/caseReviewActions';
import BucketCasesCard from 'components/common/BucketCasesCard';

const mapStateToProps = (state, ownProps) => {
  return {
    data: state.caseAssignment.cases[ownProps.channel],
    buckets: state.caseAssignment.buckets[ownProps.channel],
    hasProvisionalFields: state.user.configurations.provisionalFields,
    userList: state.user.userslist
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchCases, dispatch),
    fetchBuckets: bindActionCreators(onFetchBuckets, dispatch)
  };
};

const BucketCasesTableContainer = connect(mapStateToProps, mapDispatchToProps)(BucketCasesCard);

export default BucketCasesTableContainer;
