import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/authActions';
import Layout from 'components/Layout';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    userCreds: state.auth.userCreds,
    session: state.auth.session,
    sandboxStatus: state.sandboxing.testing.status
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch)
  };
};

const LayoutContainer = connect(mapStateToProps, mapDispatchToProps)(Layout);

export default LayoutContainer;
