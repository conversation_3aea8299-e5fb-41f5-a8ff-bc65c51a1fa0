import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/caseDocumentActions';
import DocumentManager from 'components/investigation/DocumentManager';

const mapStateToProps = (state) => {
  return {
    caseDocument: state.caseDocument
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch)
  };
};

const DocumentManagerContainer = connect(mapStateToProps, mapDispatchToProps)(DocumentManager);

export default DocumentManagerContainer;
