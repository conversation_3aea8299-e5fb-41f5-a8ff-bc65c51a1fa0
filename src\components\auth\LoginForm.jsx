import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { isEmpty, chain, capitalize } from 'lodash';
import { Row, Col, Form, FormGroup, Input, InputGroup, Label, Button } from 'reactstrap';

import { isCooperative } from 'constants/publicKey';

function LoginForm({
  roles,
  channels,
  loginTypes,
  fetchRoles,
  fetchChannels,
  fetchLoginTypes,
  handleLoginSubmit
}) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loginType, setLoginType] = useState('');
  const [channel, setChannel] = useState('');
  const [role, setRole] = useState('');

  useEffect(() => {
    isEmpty(channels) && fetchChannels();
    if (isEmpty(roles)) fetchRoles();
    if (isEmpty(loginTypes) && isCooperative) fetchLoginTypes();
  }, []);

  useEffect(() => {
    !isEmpty(loginTypes) && setLoginType(loginTypes[0].loginType);
  }, [loginTypes]);

  const updateLoginType = (loginType) => {
    const type = loginType.toLowerCase();
    const roleMap = { qrt: 'admin', bank: 'investigator' };

    setLoginType(loginType);
    setChannel(type === 'bank' ? 'frm' : '');
    setRole(roleMap[type] || '');
    setUsername('');
    setPassword('');
  };

  const channelOption = channels
    ?.filter((channel) => !isCooperative || channel.loginType?.includes(loginType))
    .map((channel) => <option key={channel.name}>{channel.name}</option>);

  const userRolesOption = chain(roles)
    .filter((role) => {
      if (!channel && !isCooperative) return role.channel?.length < 1;
      if (!channel && isCooperative)
        return role.channel?.length < 1 && role?.loginType?.includes(loginType);
      if (channel && isCooperative)
        return role?.loginType?.includes(loginType) && role?.channel?.includes(channel);
      return role.channel?.includes(channel);
    })
    .thru((filteredData) => {
      if (filteredData.length === 1 && role !== filteredData[0].name) {
        setRole(filteredData[0].name);
      }

      return filteredData.map(({ id, name }) => (
        <option key={id} value={name}>
          {capitalize(name)}
        </option>
      ));
    })
    .value();

  return (
    <Form onSubmit={handleLoginSubmit}>
      {isCooperative && !isEmpty(loginTypes) && (
        <Row className="mb-3">
          <Col md="3" sm="3" xs="12" className="p-0">
            <Label>Login Type</Label>
          </Col>
          {loginTypes.map((item) => (
            <Col md="3" sm="3" xs="12" className="p-0" key={item.id}>
              <FormGroup check>
                <Label check>
                  <Input
                    type="radio"
                    name="loginType"
                    value={item.loginType}
                    checked={loginType === item.loginType}
                    onChange={(e) => updateLoginType(e.target.value)}
                  />
                  {capitalize(item.loginType)}
                </Label>
              </FormGroup>
            </Col>
          ))}
        </Row>
      )}
      <FormGroup>
        <Label>User Name</Label>
        <InputGroup>
          <Input
            type="text"
            name="username"
            id="username"
            placeholder="Enter username"
            onChange={(e) => setUsername(e.target.value)}
            value={username}
            required
            autoComplete="off"
          />
        </InputGroup>
      </FormGroup>
      <FormGroup>
        <Label>Domain</Label>
        <InputGroup>
          <Input
            type="select"
            name="channel"
            id="channel"
            onChange={(e) => setChannel(e.target.value)}
            value={channel}>
            {loginType.toLowerCase() !== 'bank' && <option value=""> -- SELECT --</option>}
            {channelOption}
          </Input>
        </InputGroup>
      </FormGroup>
      <FormGroup>
        <Label>Role</Label>
        <InputGroup>
          <Input
            type="select"
            name="role"
            id="role"
            onChange={(e) => setRole(e.target.value)}
            value={role}
            required>
            {userRolesOption.length > 1 && <option value=""> -- SELECT --</option>}
            {userRolesOption}
          </Input>
        </InputGroup>
      </FormGroup>
      <FormGroup>
        <Label>Password</Label>
        <InputGroup>
          <Input
            type="password"
            name="password"
            id="password"
            placeholder="Enter password"
            onChange={(e) => setPassword(e.target.value)}
            value={password}
            required
            autoComplete="off"
            onPaste={(event) => {
              event.preventDefault();
            }}
          />
        </InputGroup>
      </FormGroup>
      <FormGroup className="d-flex justify-content-center">
        <Button color="primary">Login</Button>
      </FormGroup>
    </Form>
  );
}

LoginForm.propTypes = {
  roles: PropTypes.array.isRequired,
  channels: PropTypes.array.isRequired,
  loginTypes: PropTypes.array.isRequired,
  fetchRoles: PropTypes.func.isRequired,
  fetchChannels: PropTypes.func.isRequired,
  fetchLoginTypes: PropTypes.func.isRequired,
  handleLoginSubmit: PropTypes.func.isRequired
};

export default LoginForm;
