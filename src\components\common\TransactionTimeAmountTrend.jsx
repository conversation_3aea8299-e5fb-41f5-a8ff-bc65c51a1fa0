import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import { FormGroup, Label, Button } from 'reactstrap';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import GraphContainer from 'components/common/GraphContainer';
import XChannelDropDownContainer from 'containers/common/XChannelDropDownContainer';

function TransactionTimeAmountTrend({
  theme,
  txnId,
  entityId,
  txnAmount,
  txnTimestamp,
  trends,
  fetchCustomerTrend
}) {
  const [xchannelId, setXchannelId] = useState(null);
  const [startDate, setStartDate] = useState(Moment(txnTimestamp).subtract(3, 'days'));
  const [endDate, setEndDate] = useState(txnTimestamp);

  const fetchTrendData = (txnId, entityId, startDate, endDate) => {
    if (!_.isEmpty(txnId) && !_.isEmpty(entityId) && startDate && endDate) {
      const formData = {
        txnId,
        channel: 'frm',
        customerId: entityId,
        filter: {
          startTimestamp: Moment(startDate).format('YYYY-MM-DDTHH:mm:ss'),
          endTimestamp: Moment(endDate).format('YYYY-MM-DDTHH:mm:ss'),
          ...(xchannelId && { xChannelId: xchannelId })
        }
      };
      fetchCustomerTrend(formData);
    }
  };

  useEffect(() => {
    fetchTrendData(txnId, entityId, startDate, endDate);
  }, [txnId, entityId]);

  const timeRangeSubtitle = !_.isEmpty(trends.timeRange.startTime) && (
    <span>
      <Label>Avg. Transaction Time range: </Label> {trends.timeRange.startTime} -
      {trends.timeRange.endTime}
    </span>
  );

  const timeTrendData = !_.isEmpty(trends.timeTrend)
    ? trends.timeTrend.filter((d) => d.dateTime !== txnTimestamp)
    : [];

  const timeTrendFraudData = timeTrendData
    .filter((d) => d.bucket === 'FRAUD')
    .map((data) => [
      Moment(txnTimestamp).format('YYYY-MM-DD') + ' ' + Moment(data.dateTime).format('HH:mm:ss'),
      data.transactionAmount
    ]);

  const timeTrendNonFraudData = timeTrendData
    .filter((d) => d.bucket !== 'FRAUD')
    .map((data) => [
      Moment(txnTimestamp).format('YYYY-MM-DD') + ' ' + Moment(data.dateTime).format('HH:mm:ss'),
      data.transactionAmount
    ]);

  const timeTrendConfig = {
    grid: {
      left: 10,
      containLabel: true,
      bottom: 10,
      top: 10,
      right: 30
    },
    calculable: true,
    xAxis: [
      {
        type: 'time',
        boundaryGap: false,
        axisLabel: {
          formatter: function (value) {
            return Moment(value).format('HH:mm');
          }
        }
      }
    ],
    yAxis: { scale: true },
    legend: {
      data: ['Fraud Txns', 'Normal Txns', 'Current Txn'],
      right: 10
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        return `<b>${params?.marker} ${params?.seriesName}</b></br>
          <b>Time:</b> ${Moment(params?.data?.[0]).format('HH:mm')}<br />
          <b>Amount:</b> ${params?.data?.[1]}`;
      }
    },
    series: [
      {
        name: 'Fraud Txns',
        type: 'scatter',
        data: timeTrendFraudData
      },
      {
        name: 'Normal Txns',
        type: 'scatter',
        data: timeTrendNonFraudData
      },
      {
        name: 'Current Txn',
        type: 'effectScatter',
        symbolSize: 20,
        data: txnTimestamp && txnAmount ? [[txnTimestamp, txnAmount]] : []
      }
    ]
  };

  const searchTrendForDate = (e) => {
    e.preventDefault();
    fetchTrendData(txnId, entityId, startDate, endDate);
  };

  const filterForm = (
    <form onSubmit={searchTrendForDate} className="mt-1 ms-3 me-3">
      <div className="d-flex align-items-end">
        <span className="flex-fill me-3">
          <XChannelDropDownContainer value={xchannelId} onChange={setXchannelId} />
        </span>
        <FormGroup className="flex-fill me-3">
          <Label>From date</Label>
          <Datetime
            name="startDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            value={startDate}
            onChange={(dateObj) => setStartDate(dateObj._d)}
            inputProps={{ required: true }}
          />
        </FormGroup>
        <FormGroup className="flex-fill me-3">
          <Label>To date</Label>
          <Datetime
            name="endDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            value={endDate}
            onChange={(dateObj) => setEndDate(dateObj._d)}
            inputProps={{ required: true }}
          />
        </FormGroup>
        <FormGroup>
          <Button size="sm" type="submit" color="primary">
            <FontAwesomeIcon icon={faSearch} /> Search
          </Button>
        </FormGroup>
      </div>
    </form>
  );

  return (
    <GraphContainer
      type="scatter"
      theme={theme}
      config={timeTrendConfig}
      subtitle={timeRangeSubtitle}
      title="Transaction Time Vs Amount Trend"
      noData={_.isEmpty(trends.timeTrend)}
      loader={trends.loader}
      error={{
        flag: trends.error,
        errorMessage: trends.errorMessage
      }}
      graphForm={filterForm}
    />
  );
}

TransactionTimeAmountTrend.propTypes = {
  theme: PropTypes.string.isRequired,
  txnId: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  txnAmount: PropTypes.number.isRequired,
  txnTimestamp: PropTypes.string.isRequired,
  trends: PropTypes.object.isRequired,
  fetchCustomerTrend: PropTypes.func.isRequired
};

export default TransactionTimeAmountTrend;
