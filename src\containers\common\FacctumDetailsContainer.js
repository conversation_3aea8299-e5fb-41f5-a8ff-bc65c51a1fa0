import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchFacctumDetails } from 'actions/udsActions';
import FacctumDetails from 'components/common/FacctumDetails';

const mapStateToProps = (state) => ({
  facctumData: state.uds.facctumData
});

const mapDispatchToProps = (dispatch) => ({
  fetchFacctumDetails: bindActionCreators(onFetchFacctumDetails, dispatch)
});

const FacctumDetailsContainer = connect(mapStateToProps, mapDispatchToProps)(FacctumDetails);

export default FacctumDetailsContainer;
