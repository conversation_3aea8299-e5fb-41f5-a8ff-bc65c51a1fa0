import React from 'react';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getPayerInfo = (details) => {
  let payerInfoList = [];

  addItemToList(
    details?.payerAccount?.payerId?.value,
    'ID',
    details?.payerAccount?.payerId?.value,
    payerInfoList,
    details?.payerAccount?.payerId
  );

  addItemToList(
    details?.payerAccount?.payerType,
    'Type',
    details?.payerAccount?.payerType,
    payerInfoList
  );

  addItemToList(
    details?.masterFields?.payerMccCodeName && details?.payerAccount?.payerMcc,
    'MCC',
    details?.masterFields?.payerMccCodeName,
    payerInfoList,
    details?.payerAccount?.payerMcc,
    true
  );

  addItemToList(
    details?.masterFields?.payerAccountTypeName,
    'Account Type',
    details?.masterFields?.payerAccountTypeName,
    payerInfoList,
    null,
    true
  );

  addItemToList(
    details?.payerAccount?.payerAccountNumber?.value,
    'Account No',
    details?.payerAccount?.payerAccountNumber?.value,
    payerInfoList,
    details?.payerAccount?.payerAccountNumber
  );

  addItemToList(
    details?.payerAccount?.payerBankIfsc?.value,
    'Bank IFSC',
    details?.payerAccount?.payerBankIfsc?.value,
    payerInfoList,
    details?.payerAccount?.payerBankIfsc
  );

  addItemToList(
    details?.payerAccount?.payerMmidMobileNumber,
    'MMID Mobile',
    details?.payerAccount?.payerMmidMobileNumber,
    payerInfoList
  );

  addItemToList(
    details?.payerAccount?.payerVpa?.value,
    'VPA',
    details?.payerAccount?.payerVpa?.value,
    payerInfoList,
    details?.payerAccount?.payerVpa
  );

  addItemToList(
    details?.payerAccount?.payerCardNumberMask,
    'Masked Card Number',
    details?.payerAccount?.payerCardNumberMask,
    payerInfoList
  );

  addItemToList(
    details?.payerAccount?.payerCardNumberHash?.value,
    'Hashed Card Number',
    details?.payerAccount?.payerCardNumberHash?.value,
    payerInfoList,
    details?.payerAccount?.payerCardNumberHash
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.payerAccountIsDormant,
    'Is Dormant',
    details?.masterFields?.txnAdditionalFields?.payerAccountIsDormant,
    payerInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.payerAccountIsSuspense,
    'Is Suspense',
    details?.masterFields?.txnAdditionalFields?.payerAccountIsSuspense,
    payerInfoList
  );

  return payerInfoList;
};
function TransactionPayerInfo({ details }) {
  const payerInfoList = getPayerInfo(details);

  if (payerInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Payer Details</b>
      {renderDataColumnList(payerInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionPayerInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionPayerInfo;
