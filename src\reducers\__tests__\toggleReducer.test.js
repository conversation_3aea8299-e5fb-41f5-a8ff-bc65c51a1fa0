import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import toggleReducer from 'reducers/toggleReducer';

describe('Toggle Reducer', () => {
  it('should return the intial state', () => {
    expect(toggleReducer(undefined, {})).toEqual(initialState.toggle);
  });

  it('should handle ON_TOGGLE_BLACKLIST_MODAL', () => {
    expect(
      toggleReducer(
        {
          blacklistModal: false
        },
        {
          type: types.ON_TOGGLE_BLACKLIST_MODAL
        }
      )
    ).toEqual({
      blacklistModal: true
    });
  });

  it('should handle ON_TOGGLE_THEME', () => {
    expect(
      toggleReducer(
        {
          theme: 'dark'
        },
        {
          type: types.ON_TOGGLE_THEME
        }
      )
    ).toEqual({
      theme: 'light'
    });
  });

  it('should handle ON_TOGGLE_LOADER', () => {
    expect(
      toggleReducer(
        {
          loader: false
        },
        {
          type: types.ON_TOGGLE_LOADER,
          state: true
        }
      )
    ).toEqual({
      loader: true
    });
  });

  it('should handle ON_TOGGLE_SHIFT_MODAL', () => {
    expect(
      toggleReducer(
        {
          shiftModal: false
        },
        {
          type: types.ON_TOGGLE_SHIFT_MODAL
        }
      )
    ).toEqual({
      shiftModal: true
    });
  });

  it('should handle ON_TOGGLE_VERDICT_MODAL', () => {
    expect(
      toggleReducer(
        {
          verdictModal: false
        },
        {
          type: types.ON_TOGGLE_VERDICT_MODAL,
          channel: 'frm'
        }
      )
    ).toEqual({
      verdictModal: { frm: true }
    });
  });

  it('should handle ON_TOGGLE_APPROVAL_MODAL', () => {
    expect(
      toggleReducer(
        {
          approvalModal: false
        },
        {
          type: types.ON_TOGGLE_APPROVAL_MODAL
        }
      )
    ).toEqual({
      approvalModal: true
    });
  });

  it('should handle ON_TOGGLE_ADD_USER_MODAL', () => {
    expect(
      toggleReducer(
        {
          addUserModal: false
        },
        {
          type: types.ON_TOGGLE_ADD_USER_MODAL
        }
      )
    ).toEqual({
      addUserModal: true
    });
  });

  it('should handle ON_TOGGLE_WATCHLIST_MODAL', () => {
    expect(
      toggleReducer(
        {
          watchlistModal: false
        },
        {
          type: types.ON_TOGGLE_WATCHLIST_MODAL
        }
      )
    ).toEqual({
      watchlistModal: true
    });
  });

  it('should handle ON_TOGGLE_RULE_EDIT_MODAL', () => {
    expect(
      toggleReducer(
        {
          ruleEditModal: {
            rpsl: false
          }
        },
        {
          type: types.ON_TOGGLE_RULE_EDIT_MODAL,
          channel: 'rpsl'
        }
      )
    ).toEqual({
      ruleEditModal: {
        rpsl: true
      }
    });
  });

  it('should handle ON_TOGGLE_PREFILTER_MODAL', () => {
    expect(
      toggleReducer(
        {
          prefilterModal: {
            rpsl: false
          }
        },
        {
          type: types.ON_TOGGLE_PREFILTER_MODAL,
          channel: 'rpsl'
        }
      )
    ).toEqual({
      prefilterModal: {
        rpsl: true
      }
    });
  });

  it('should handle ON_TOGGLE_ESCALATION_MODAL', () => {
    expect(
      toggleReducer(
        {
          escalationModal: false
        },
        {
          type: types.ON_TOGGLE_ESCALATION_MODAL
        }
      )
    ).toEqual({
      escalationModal: true
    });
  });

  it('should handle ON_TOGGLE_RULE_CREATE_MODAL', () => {
    expect(
      toggleReducer(
        {
          ruleCreateModal: {
            rpsl: false
          }
        },
        {
          type: types.ON_TOGGLE_RULE_CREATE_MODAL,
          channel: 'rpsl'
        }
      )
    ).toEqual({
      ruleCreateModal: {
        rpsl: true
      }
    });
  });

  it('should handle ON_TOGGLE_ASSIGN_SHIFT_MODAL', () => {
    expect(
      toggleReducer(
        {
          assignShiftModal: false
        },
        {
          type: types.ON_TOGGLE_ASSIGN_SHIFT_MODAL
        }
      )
    ).toEqual({
      assignShiftModal: true
    });
  });

  it('should handle ON_TOGGLE_RULE_DUPLICATE_MODAL', () => {
    expect(
      toggleReducer(
        {
          ruleDuplicateModal: {
            rpsl: false
          }
        },
        {
          type: types.ON_TOGGLE_RULE_DUPLICATE_MODAL,
          channel: 'rpsl'
        }
      )
    ).toEqual({
      ruleDuplicateModal: {
        rpsl: true
      }
    });
  });

  it('should handle ON_TOGGLE_UPDATE_USER_ROLES_MODAL', () => {
    expect(
      toggleReducer(
        {
          updateUserRolesModal: false
        },
        {
          type: types.ON_TOGGLE_UPDATE_USER_ROLES_MODAL
        }
      )
    ).toEqual({
      updateUserRolesModal: true
    });
  });

  it('should handle ON_TOGGLE_PREFILTERS_LIST_MODAL', () => {
    expect(
      toggleReducer(
        {
          prefiltersListModal: {
            specializedList: false
          }
        },
        {
          type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL,
          listType: 'specializedList'
        }
      )
    ).toEqual({
      prefiltersListModal: {
        specializedList: true
      }
    });
  });

  it('should handle ON_TOGGLE_CREATE_LIST_MODAL', () => {
    expect(
      toggleReducer(
        {
          prefiltersListModal: {
            createListModal: false
          }
        },
        {
          type: types.ON_TOGGLE_CREATE_LIST_MODAL
        }
      )
    ).toEqual({
      prefiltersListModal: {
        createListModal: true
      }
    });
  });

  it('should handle ON_TOGGLE_RELEASE_FUNDS_MODAL', () => {
    expect(
      toggleReducer(
        {
          releaseFundsModal: false
        },
        {
          type: types.ON_TOGGLE_RELEASE_FUNDS_MODAL
        }
      )
    ).toEqual({
      releaseFundsModal: true
    });
  });

  it('should handle ON_TOGGLE_HOLD_CASE_MODAL', () => {
    expect(
      toggleReducer(
        {
          holdCaseModal: false
        },
        {
          type: types.ON_TOGGLE_HOLD_CASE_MODAL
        }
      )
    ).toEqual({
      holdCaseModal: true
    });
  });

  it('should handle ON_TOGGLE_REQUEST_DOCUMENT_MODAL', () => {
    expect(
      toggleReducer(
        {
          requestDocumentModal: false
        },
        {
          type: types.ON_TOGGLE_REQUEST_DOCUMENT_MODAL
        }
      )
    ).toEqual({
      requestDocumentModal: true
    });
  });

  it('should handle ON_TOGGLE_CONFIRM_ALERT_MODAL', () => {
    expect(
      toggleReducer(
        {
          confirmAlertModal: {
            specializedList: false
          }
        },
        {
          type: types.ON_TOGGLE_CONFIRM_ALERT_MODAL,
          listType: 'specializedList'
        }
      )
    ).toEqual({
      confirmAlertModal: {
        specializedList: true
      }
    });
  });

  it('should handle ON_TOGGLE_STATUS_LOG_MODAL', () => {
    expect(
      toggleReducer(
        {
          statusLogModal: false
        },
        {
          type: types.ON_TOGGLE_STATUS_LOG_MODAL
        }
      )
    ).toEqual({
      statusLogModal: true
    });
  });

  it('should handle ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL', () => {
    expect(
      toggleReducer(
        {
          addToListConfirmAlertModal: false
        },
        {
          type: types.ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL
        }
      )
    ).toEqual({
      addToListConfirmAlertModal: true
    });
  });

  it('should handle ON_TOGGLE_DOWNLOAD_STR_MODAL', () => {
    expect(
      toggleReducer(
        {
          downloadSTRModal: false
        },
        {
          type: types.ON_TOGGLE_DOWNLOAD_STR_MODAL
        }
      )
    ).toEqual({
      downloadSTRModal: true
    });
  });

  it('should handle ON_TOGGLE_CREATE_CASE_MODAL', () => {
    expect(
      toggleReducer(
        {
          createCaseModal: false
        },
        {
          type: types.ON_TOGGLE_CREATE_CASE_MODAL
        }
      )
    ).toEqual({
      createCaseModal: true
    });
  });

  it('should handle ON_TOGGLE_ADD_BANK_MODAL', () => {
    expect(
      toggleReducer(
        {
          addBankModal: false
        },
        {
          type: types.ON_TOGGLE_ADD_BANK_MODAL
        }
      )
    ).toEqual({
      addBankModal: true
    });
  });

  it('should handle ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL', () => {
    expect(
      toggleReducer(
        {
          dynamicCountersCreateModal: false
        },
        {
          type: types.ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL
        }
      )
    ).toEqual({
      dynamicCountersCreateModal: true
    });
  });

  it('should handle ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL', () => {
    expect(
      toggleReducer(
        {
          requestForInformationModal: false
        },
        {
          type: types.ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL
        }
      )
    ).toEqual({
      requestForInformationModal: true
    });
  });

  it('should handle ON_TOGGLE_CPIFR_FORM_MODAL', () => {
    expect(
      toggleReducer(
        {
          cpifrFormModal: false
        },
        {
          type: types.ON_TOGGLE_CPIFR_FORM_MODAL
        }
      )
    ).toEqual({
      cpifrFormModal: true
    });
  });

  it('should handle ON_TOGGLE_USER_CASE_CRITERIA_MODAL', () => {
    expect(
      toggleReducer(
        {
          userCaseCriteriaModal: false
        },
        {
          type: types.ON_TOGGLE_USER_CASE_CRITERIA_MODAL
        }
      )
    ).toEqual({
      userCaseCriteriaModal: true
    });
  });
});
