/* eslint-disable react/prop-types */
/* eslint-disable react/display-name */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CallEndButton from '../caseReview/CallEndButton';
import moment from 'moment';

jest.mock('components/common/ModalContainer', () => ({ children }) => <div>{children}</div>);

const defaultProps = {
  theme: 'dark',
  channel: 'mockChannel',
  callDetails: {
    caseRefNo: 'mockCaseRefNo',
    status: 'PLACED',
    timeStamp: moment().toISOString()
  },
  dispositions: {
    loader: false,
    error: false,
    list: [{ id: '1', name: 'Disposition 1' }]
  },
  endCall: jest.fn(),
  fetchDispositionsList: jest.fn()
};

describe('CallEndButton Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when call is placed', () => {
    render(<CallEndButton {...defaultProps} />);
    expect(screen.getByText(/Call placed at/)).toBeInTheDocument();
    expect(screen.getByTestId('display-btn')).toBeInTheDocument();
  });

  it('fetches dispositions list if not already fetched', () => {
    const dispositionsProps = {
      loader: false,
      error: false,
      list: []
    };
    render(<CallEndButton {...defaultProps} dispositions={dispositionsProps} />);
    expect(defaultProps.fetchDispositionsList).toHaveBeenCalled();
  });

  it('displays loading option when dispositions are loading', () => {
    const dispositionsProps = {
      loader: true,
      error: false,
      list: []
    };
    render(<CallEndButton {...defaultProps} dispositions={dispositionsProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    expect(screen.getByText(/Loading.../)).toBeInTheDocument();
  });

  it('displays error message when there is an error fetching dispositions', () => {
    const dispositionsProps = {
      loader: false,
      error: true,
      errorMessage: 'Error fetching dispositions',
      list: []
    };
    render(<CallEndButton {...defaultProps} dispositions={dispositionsProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    expect(screen.getByText(/Error fetching dispositions/)).toBeInTheDocument();
  });

  it('displays disposition options when fetched', () => {
    render(<CallEndButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    expect(screen.getByText(/Disposition 1/)).toBeInTheDocument();
  });

  it('handles disposition selection change', () => {
    render(<CallEndButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    fireEvent.change(screen.getByRole('combobox'), { target: { value: '1' } });
    expect(screen.getByRole('combobox').value).toBe('1');
  });

  it('calls `endCall` and closes modal on form submit', async () => {
    render(<CallEndButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    fireEvent.change(screen.getByRole('combobox'), { target: { value: '1' } });
    fireEvent.submit(screen.getByTestId('form'));

    await waitFor(() => {
      expect(defaultProps.endCall).toHaveBeenCalledWith('mockChannel', 'mockCaseRefNo', '1');
      expect(screen.queryByText(/Set disposition for call/)).not.toBeInTheDocument();
    });
  });

  it('closes modal on button click', () => {
    render(<CallEndButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    fireEvent.click(screen.getByTestId('display-btn'));
    expect(screen.queryByText(/Set disposition for call/)).not.toBeInTheDocument();
  });

  it('disables button if call status is not "PLACED"', () => {
    const callDetailsProps = {
      caseRefNo: 'mockCaseRefNo',
      status: 'ENDED',
      timeStamp: moment().toISOString()
    };
    render(<CallEndButton {...defaultProps} callDetails={callDetailsProps} />);
    expect(screen.getByTestId('display-btn')).toBeDisabled();
  });

  it('opens the modal when button is clicked', () => {
    const callDetailsProps = {
      caseRefNo: 'mockCaseRefNo',
      status: 'ENDED',
      timeStamp: moment().toISOString()
    };
    render(<CallEndButton {...defaultProps} callDetails={callDetailsProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    expect(screen.getByTestId('form')).toBeInTheDocument();
  });

  it('sets `selectedDisposition` on change', () => {
    render(<CallEndButton {...defaultProps} />);
    fireEvent.click(screen.getByTestId('display-btn'));
    fireEvent.change(screen.getByRole('combobox'), { target: { value: '1' } });
    expect(screen.getByRole('combobox').value).toBe('1');
  });
});
