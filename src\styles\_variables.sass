// width definition (optional)
$s: "600px"
$sm: "767px"
$m: "768px"
$ml: "991px"
$l: "992px"
$lxl: "1199px"
$xl: "1200px"

@function min-width($width)
  @return '(min-width: #{$width})'

@function max-width($width)
  @return '(max-width: #{$width})'


@function min-height($height)
  @return '(min-height: #{$height})'


@function max-height($height)
  @return '(max-height: #{$height})'


// preset configuration (optional)
$mobile: (#{max-width($s)})
$phablet: (#{min-width($s)}, #{max-width($sm)})
$tablet: (#{min-width($m)}, #{max-width($ml)})
$mobileLand: (#{max-width($sm)})
$tabletLand: (#{max-width($lxl)})
$laptop: (#{min-width($l)}, #{max-width($lxl)})
$tv: (#{min-width($xl)})
