'use strict';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useMemo, useState } from 'react';
import { MultiSelect } from 'react-multi-select-component';
import { Row, Col, FormGroup, Label, Input, FormFeedback } from 'reactstrap';

import RuleLabel from 'components/ruleEngine/RuleLabel';
import { isCooperative } from 'constants/publicKey';

function RuleDetailsForm({
  channel,
  moduleType,
  formName,
  hasMaker<PERSON>hecker,
  hasAcquirerPortals,
  ruleData,
  actionList,
  ruleChannels,
  alertCategories,
  fraudCategories,
  combinedRuleList,
  updateRuleData,
  updateNextDisable,
  ruleLabels,
  toggle,
  onToggleCreateLabelModal,
  ruleCreationActions
}) {
  const {
    code,
    name,
    description,
    order,
    assignmentPriority,
    explicit,
    actionCode,
    actionName,
    methodType,
    isMerchantSpecific,
    lowLevelOutcome,
    medLevelOutcome,
    highLevelOutcome,
    alertCategoryId,
    fraudCategory,
    channels
  } = ruleData;

  // Memoize expensive computations to prevent unnecessary re-calculations
  const ruleChannelsOptions = useMemo(
    () =>
      _.map(ruleChannels, (ruleChannel) => ({
        value: ruleChannel.name,
        label: ruleChannel.name
      })),
    [ruleChannels]
  );

  const selectedChannelsArray = useMemo(() => channels.split(' / '), [channels]);

  const formDataChannels = useMemo(
    () => _.filter(ruleChannelsOptions, (d) => _.includes(selectedChannelsArray, d.value)),
    [ruleChannelsOptions, selectedChannelsArray]
  );

  const [invalidName, setInvalidName] = useState(false);
  const [invalidOrder, setInvalidOrder] = useState(false);
  const [selectedRuleChannels, setSelectedRuleChannels] = useState(formDataChannels || []);

  useEffect(() => {
    setInvalidName(
      combinedRuleList.filter((d) => d.name === name && (formName !== 'edit' || d.code !== code))
        .length > 0
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name]);

  useEffect(() => {
    setInvalidOrder(
      combinedRuleList.filter(
        (d) => order !== 0 && d.order === order && (formName !== 'edit' || d.code !== code)
      ).length > 0
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [order]);

  useEffect(() => {
    updateRuleData(
      'channels',
      _.chain(selectedRuleChannels)
        .map((d) => d.value)
        .join(' / ')
        .value()
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRuleChannels]);

  useEffect(() => {
    updateNextDisable(
      _.isEmpty(name) ||
        _.isEmpty(description) ||
        _.isElement(actionCode) ||
        _.isEmpty(methodType) ||
        alertCategoryId === -1 ||
        _.isEmpty(fraudCategory) ||
        _.isEmpty(selectedRuleChannels) ||
        invalidName ||
        invalidOrder ||
        (isMerchantSpecific
          ? _.isEmpty(lowLevelOutcome) || _.isEmpty(medLevelOutcome) || _.isEmpty(highLevelOutcome)
          : false)
    );
  }, [
    name,
    description,
    actionCode,
    methodType,
    alertCategoryId,
    fraudCategory,
    selectedRuleChannels,
    invalidName,
    invalidOrder,
    isMerchantSpecific,
    lowLevelOutcome,
    medLevelOutcome,
    highLevelOutcome,
    updateNextDisable
  ]);

  const actionsOptions = useMemo(
    () =>
      actionList &&
      actionList.map((action) => (
        <option key={action.actionCode} value={`${action.actionCode} - ${action.actionName}`}>
          {`${action.actionCode} - ${action.actionName}`}
        </option>
      )),
    [actionList]
  );

  const actionNameOptions = useMemo(
    () =>
      actionList &&
      actionList.map((action) => <option key={action.actionCode}>{action.actionName}</option>),
    [actionList]
  );

  const alertCategoryOptions = _.chain(alertCategories)
    .filter((category) => category.categoryName !== 'NA')
    .map((category) => (
      <option key={category.id} value={category.id}>
        {category.categoryName}
      </option>
    ))
    .value();

  const fraudCategoriesOptions =
    fraudCategories &&
    fraudCategories.map((category) => (
      <option key={category.id} value={category.name}>
        {category.name}
      </option>
    ));

  return (
    <Row>
      <Col md={channel === 'frm' ? 6 : 8} sm="6" xs="12">
        <FormGroup>
          <Label>Name</Label>
          <Input
            type="text"
            name="name"
            value={name}
            onChange={(event) => updateRuleData('name', event.target.value)}
            required
            spellCheck={false}
            invalid={invalidName || undefined}
          />
          <FormFeedback invalid={invalidName ? 'invalid' : undefined}>
            Rule with same name already exists.
          </FormFeedback>
        </FormGroup>
        <FormGroup>
          <Label>Description</Label>
          <Input
            type="textarea"
            name="description"
            rows="3"
            value={description}
            onChange={(event) => updateRuleData('description', event.target.value)}
            required
          />
        </FormGroup>
      </Col>
      <Col md={channel === 'frm' ? 6 : 4} sm="6" xs="12">
        <Row>
          <Col xl="4" md={6} sm="6" xs="12">
            <FormGroup>
              <Label>Order</Label>
              <Input
                type="number"
                name="order"
                value={order}
                onChange={(event) => updateRuleData('order', +event.target.value)}
                min={0}
                max={100}
                pattern="[0-9]{0,3}"
                title="Max. value 100"
                invalid={invalidOrder || undefined}
              />
              <FormFeedback invalid={invalidOrder ? 'invalid' : undefined}>
                Rule with same order already exists.
              </FormFeedback>
            </FormGroup>
          </Col>
          {channel === 'frm' && (
            <Col xl="4" md="6" sm="6" xs="12">
              <FormGroup>
                <Label>Transaction Type</Label>
                <Input
                  type="select"
                  name="methodType"
                  value={methodType}
                  onChange={(event) => {
                    updateRuleData('methodType', event.target.value);
                    event.target.value === 'Post' && updateRuleData('actionCode', '01');
                    event.target.value === 'Post' && updateRuleData('actionName', 'ACCEPTED');
                  }}
                  required>
                  {channel !== 'str' && <option value="Pre">Pre-Auth</option>}
                  <option value="Post">Post-Auth</option>
                </Input>
              </FormGroup>
            </Col>
          )}
          {channel !== 'str' && methodType !== 'Post' && (
            <Col xl="4" md="6" sm="6" xs="12">
              <FormGroup>
                <Label>Action</Label>
                <Input
                  type="select"
                  name="actionCode"
                  value={`${actionCode} - ${actionName}`}
                  onChange={(event) => {
                    const action = _.split(event.target.value, ' - ');
                    updateRuleData('actionCode', action[0]);
                    updateRuleData('actionName', action[1]);
                  }}
                  required>
                  <option value="">-- Select --</option>
                  {actionsOptions}
                </Input>
              </FormGroup>
            </Col>
          )}
          {channel !== 'str' && (
            <Col xl="4" md={explicit ? 4 : 6} sm="6" xs="12" className="pt-4">
              <FormGroup>
                <Label check>
                  <Input
                    type="checkbox"
                    name="explicit"
                    className="me-1"
                    onChange={() => updateRuleData('explicit', !explicit)}
                    checked={explicit}
                  />
                  Should create a Case
                </Label>
              </FormGroup>
            </Col>
          )}
          {!isCooperative && explicit && hasMakerChecker && channel !== 'str' && (
            <Col xl="4" md="6" sm="6" xs="12">
              <FormGroup>
                <Label>Assignment</Label>
                <Input
                  type="select"
                  name="assignmentPriority"
                  value={assignmentPriority}
                  onChange={(event) => updateRuleData('assignmentPriority', event.target.value)}
                  required>
                  <option value={0}>Maker</option>
                  <option value={1}>Checker</option>
                </Input>
              </FormGroup>
            </Col>
          )}
        </Row>
      </Col>

      <Col md={12} className="p-0">
        <Row>
          <Col>
            <FormGroup>
              <Label>Alert Category</Label>
              <Input
                type="select"
                name="alertCategoryId"
                value={alertCategoryId}
                onChange={(event) => updateRuleData('alertCategoryId', event.target.value)}
                required>
                <option value={-1}>-- Select --</option>
                {alertCategoryOptions}
              </Input>
            </FormGroup>
          </Col>
          <Col>
            <FormGroup>
              <Label>Fraud Category</Label>
              <Input
                type="select"
                name="fraudCategory"
                value={fraudCategory}
                onChange={(event) => updateRuleData('fraudCategory', event.target.value)}
                required>
                <option value="">-- Select --</option>
                {fraudCategoriesOptions}
              </Input>
            </FormGroup>
          </Col>
          <Col>
            <FormGroup>
              <Label>Channels</Label>
              <MultiSelect
                options={ruleChannelsOptions}
                labelledBy="select category"
                name="txnCategory"
                value={selectedRuleChannels}
                onChange={setSelectedRuleChannels}
              />
            </FormGroup>
          </Col>
          <Col>
            <RuleLabel
              channel={channel}
              ruleData={ruleData}
              updateRuleData={updateRuleData}
              ruleLabels={ruleLabels}
              toggle={toggle}
              onToggleCreateLabelModal={onToggleCreateLabelModal}
              ruleCreationActions={ruleCreationActions}
            />
          </Col>
        </Row>
      </Col>

      {channel === 'frm' && moduleType !== 'issuer' && hasAcquirerPortals === 1 && (
        <Col md={12} className="p-0">
          <Row>
            <Col>
              <FormGroup>
                <Label check>
                  <Input
                    type="checkbox"
                    name="isMerchantSpecific"
                    className="me-1"
                    onChange={() => updateRuleData('isMerchantSpecific', !isMerchantSpecific)}
                    checked={isMerchantSpecific}
                  />
                  Merchant Category Specific Rule
                </Label>
              </FormGroup>
            </Col>
            {isMerchantSpecific && (
              <>
                <Col>
                  <FormGroup>
                    <Label>Low Risk Action</Label>
                    <Input
                      type="select"
                      name="lowLevelOutcome"
                      value={lowLevelOutcome}
                      onChange={(event) => updateRuleData('lowLevelOutcome', event.target.value)}
                      required>
                      <option value="">-- Select --</option>
                      {actionNameOptions}
                    </Input>
                  </FormGroup>
                </Col>
                <Col>
                  <FormGroup>
                    <Label>Medium Risk Action</Label>
                    <Input
                      type="select"
                      name="medLevelOutcome"
                      value={medLevelOutcome}
                      onChange={(event) => updateRuleData('medLevelOutcome', event.target.value)}
                      required>
                      <option value="">-- Select --</option>
                      {actionNameOptions}
                    </Input>
                  </FormGroup>
                </Col>
                <Col>
                  <FormGroup>
                    <Label>High Risk Action</Label>
                    <Input
                      type="select"
                      name="highLevelOutcome"
                      value={highLevelOutcome}
                      onChange={(event) => updateRuleData('highLevelOutcome', event.target.value)}
                      required>
                      <option value="">-- Select --</option>
                      {actionNameOptions}
                    </Input>
                  </FormGroup>
                </Col>
              </>
            )}
          </Row>
        </Col>
      )}
    </Row>
  );
}

RuleDetailsForm.propTypes = {
  channel: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  hasMakerChecker: PropTypes.bool.isRequired,
  hasAcquirerPortals: PropTypes.number.isRequired,
  ruleData: PropTypes.object.isRequired,
  actionList: PropTypes.array.isRequired,
  ruleChannels: PropTypes.array.isRequired,
  alertCategories: PropTypes.array.isRequired,
  fraudCategories: PropTypes.array.isRequired,
  combinedRuleList: PropTypes.array.isRequired,
  updateRuleData: PropTypes.func.isRequired,
  updateNextDisable: PropTypes.func.isRequired,
  ruleLabels: PropTypes.array.isRequired,
  toggle: PropTypes.object.isRequired,
  onToggleCreateLabelModal: PropTypes.func.isRequired,
  ruleCreationActions: PropTypes.object.isRequired
};

export default React.memo(RuleDetailsForm);
