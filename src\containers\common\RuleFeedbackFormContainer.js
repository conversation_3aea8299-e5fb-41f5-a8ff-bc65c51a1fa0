import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRulesWithConditions, onSaveFeedback } from 'actions/ruleConfiguratorActions';
import { onToggleRuleFeedbackModal } from 'actions/toggleActions';
import RuleFeedbackForm from 'components/common/RuleFeedbackForm';

const mapStateToProps = (state) => ({
  rulesWithConditions: state.ruleConfigurator.rulesWithConditions.list,
  channels: state.auth.userCreds.channels,
  ruleFeedbackModal: state.toggle.ruleFeedbackModal,
  theme: state.toggle.theme,
  txnDetails: state.transactionDetails.details
});

const mapDispatchToProps = (dispatch) => ({
  fetchRulesWithConditions: bindActionCreators(onFetchRulesWithConditions, dispatch),
  saveFeedback: bindActionCreators(onSaveFeedback, dispatch),
  toggleRuleFeedbackModal: bindActionCreators(onToggleRuleFeedbackModal, dispatch)
});

const RuleFeedbackFormContainer = connect(mapStateToProps, mapDispatchToProps)(RuleFeedbackForm);

export default RuleFeedbackFormContainer;
