import { faBell as noNotification } from '@fortawesome/free-regular-svg-icons';
import { faBell as notification } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Badge } from 'reactstrap';

import { useInterval } from 'utility/customHooks';

function NotificationCounter({ notifications, fetchNotifications }) {
  useEffect(() => {
    fetchNotifications('frm');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useInterval(() => {
    fetchNotifications('frm');
  }, 60_000);

  const unreadNotifications =
    notifications.list?.filter((notification) => !notification.isAcknowledged)?.length || 0;

  const notificationBadge =
    unreadNotifications > 0 ? (
      <Badge color="danger" pill>
        {unreadNotifications}
      </Badge>
    ) : null;

  return (
    <span className="notification-counter">
      <FontAwesomeIcon icon={unreadNotifications > 0 ? notification : noNotification} />
      {notificationBadge}
    </span>
  );
}

NotificationCounter.propTypes = {
  notifications: PropTypes.object.isRequired,
  fetchNotifications: PropTypes.func.isRequired
};

export default NotificationCounter;
