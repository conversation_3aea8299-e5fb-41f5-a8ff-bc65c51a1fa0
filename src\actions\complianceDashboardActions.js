import {
  ON_FETCH_FMR_REPORTED_CASE_LOADING,
  ON_FETCH_FMR_REPORTED_CASE_SUCCESS,
  ON_FETCH_FMR_REPORTED_CASE_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchFMRReportedCases(channel) {
  return client({ url: `casereview/case/investigator/${channel}/frm/report/cases` });
}

function onFetchFMRReportedCasesLoading() {
  return { type: ON_FETCH_FMR_REPORTED_CASE_LOADING };
}

function onFetchFMRReportedCasesSuccess(response) {
  return { type: ON_FETCH_FMR_REPORTED_CASE_SUCCESS, response: response.cases };
}

function onFetchFMRReportedCasesFailure(response) {
  return { type: ON_FETCH_FMR_REPORTED_CASE_FAILURE, response };
}

function onFetchFMRReportedCases() {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchFMRReportedCasesLoading());
    return fetchFMRReportedCases(userCreds.channels[0]).then(
      (success) => dispatch(onFetchFMRReportedCasesSuccess(success)),
      (error) => dispatch(onFetchFMRReportedCasesFailure(error))
    );
  };
}

export { onFetchFMRReportedCases };
