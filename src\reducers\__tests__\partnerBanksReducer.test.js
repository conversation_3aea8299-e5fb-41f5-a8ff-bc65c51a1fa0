import partnerBanksReducer from 'reducers/partnerBanksReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import responses from 'mocks/responses';

describe('partnerBanks Reducer', () => {
  it('should return the intial state', () => {
    expect(partnerBanksReducer(undefined, {})).toEqual(initialState.partnerBanks);
  });

  it('should handle ON_FETCH_PARTNER_BANK_LIST_LOADING', () => {
    expect(
      partnerBanksReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_PARTNER_BANK_LIST_LOADING
        }
      )
    ).toEqual({
      list: [],
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_PARTNER_BANK_LIST_SUCCESS', () => {
    expect(
      partnerBanksReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_PARTNER_BANK_LIST_SUCCESS,
          response: responses.partnerBanks
        }
      )
    ).toEqual({
      list: responses.partnerBanks,
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_PARTNER_BANK_LIST_FAILURE', () => {
    expect(
      partnerBanksReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_PARTNER_BANK_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      list: [],
      loader: false,
      error: true,
      errorMessage: 'error message'
    });
  });
});
