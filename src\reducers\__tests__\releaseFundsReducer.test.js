import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import releaseFundsReducer from 'reducers/releaseFundsReducer';

describe('Release Funds Reducer', () => {
  it('should return the intial state', () => {
    expect(releaseFundsReducer(undefined, {})).toEqual(initialState.releaseFunds);
  });

  it('should handle ON_FETCH_RELEASE_FUNDS_LIST_LOADING', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_FETCH_RELEASE_FUNDS_LIST_LOADING
        }
      )
    ).toEqual({
      fundsToBeRelease: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST,
          response: responses.release.list
        }
      )
    ).toEqual({
      fundsToBeRelease: {
        list: responses.release.list,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RELEASE_FUNDS_LIST_FAILURE', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_FETCH_RELEASE_FUNDS_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      fundsToBeRelease: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_DOCUMENT_STATUS_LOADING', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_FETCH_DOCUMENT_STATUS_LOADING
        }
      )
    ).toEqual({
      documentStatus: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_DOCUMENT_STATUS_SUCCESS', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_FETCH_DOCUMENT_STATUS_SUCCESS,
          response: responses.release.docStatus
        }
      )
    ).toEqual({
      documentStatus: {
        details: responses.release.docStatus,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_DOCUMENT_STATUS_FAILURE', () => {
    expect(
      releaseFundsReducer(
        {},
        {
          type: types.ON_FETCH_DOCUMENT_STATUS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      documentStatus: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
