import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchFeedBackTimeRangeAnalysis } from 'actions/ruleDashboardActions';
import RuleFeedbackAnalysisStats from 'components/dashboards/RuleFeedbackAnalysisStats';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleFeedbackAnalysisStats: state.ruleDashboard.ruleFeedbackAnalysisStats
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFeedBackTimeRangeAnalysis: bindActionCreators(onFetchFeedBackTimeRangeAnalysis, dispatch)
  };
};

const RuleFeedbackAnalysisStatsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleFeedbackAnalysisStats);

export default RuleFeedbackAnalysisStatsContainer;
