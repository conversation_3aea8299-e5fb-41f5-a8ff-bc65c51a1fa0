import {
  ON_<PERSON>ET<PERSON>_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS,
  ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE,
  ON_FETCH_AGENT_DEMOGRAPH<PERSON>_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS,
  ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE,
  ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS,
  ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';
import { isString } from 'lodash';

export default function demographicDetailsReducer(state = initialState.demographicDetails, action) {
  switch (action.type) {
    case ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING:
      return objectAssign({}, state, {
        merchant: {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS:
      return objectAssign({}, state, {
        merchant: {
          details: isString(action.response) ? JSON.parse(action.response) : action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE:
      return objectAssign({}, state, {
        merchant: {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING:
      return objectAssign({}, state, {
        agent: {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS:
      return objectAssign({}, state, {
        agent: {
          details: isString(action.response) ? JSON.parse(action.response) : action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE:
      return objectAssign({}, state, {
        agent: {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING:
      return objectAssign({}, state, {
        customer: {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS:
      return objectAssign({}, state, {
        customer: {
          details: isString(action.response) ? JSON.parse(action.response) : action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE:
      return objectAssign({}, state, {
        customer: {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    default:
      return state;
  }
}
