import objectAssign from 'object-assign';

import {
  ON_FETCH_PARTNER_BANK_LIST_LOADING,
  ON_FETCH_PARTNER_BANK_LIST_SUCCESS,
  ON_FETCH_PARTNER_BANK_LIST_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function partnerBanksReducer(state = initialState.partnerBanks, action) {
  switch (action.type) {
    case ON_FETCH_PARTNER_BANK_LIST_LOADING:
      return objectAssign({}, state, {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      });
    case ON_FETCH_PARTNER_BANK_LIST_SUCCESS:
      return objectAssign({}, state, {
        list: action.response,
        loader: false,
        error: false,
        errorMessage: ''
      });
    case ON_FETCH_PARTNER_BANK_LIST_FAILURE:
      return objectAssign({}, state, {
        list: [],
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      });
    default:
      return state;
  }
}
