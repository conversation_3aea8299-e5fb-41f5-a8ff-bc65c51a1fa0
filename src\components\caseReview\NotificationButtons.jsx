import PropTypes from 'prop-types';
import React from 'react';
import { Button } from 'reactstrap';

function NotificationButtons({
  channel,
  entityId,
  caseRefNo,
  caseDetails,
  communicationLogs,
  communicationActions,
  disabled = false
}) {
  let smsCount = 0;
  let emailCount = 0;

  communicationLogs.data?.forEach((d) => {
    if (d.media === 'EMAIL') emailCount++;
    if (d.media === 'SMS') smsCount++;
  });

  return (
    <>
      <Button
        size="sm"
        outline
        color="primary"
        className="ms-3"
        onClick={() =>
          !disabled &&
          communicationActions.onSMSCustomer({
            channel,
            caseRefNo,
            customerId: entityId,
            data: caseDetails
          })
        }
        disabled={disabled}>
        Notify via SMS
        {smsCount > 0 && <span className="ms-2">({smsCount})</span>}
      </Button>
      <Button
        size="sm"
        outline
        color="primary"
        className="ms-3"
        onClick={() =>
          !disabled &&
          communicationActions.onEmailCustomer({
            channel,
            caseRefNo,
            customerId: entityId,
            data: caseDetails
          })
        }
        disabled={disabled}>
        Notify via Email
        {emailCount > 0 && <span className="ms-2">({emailCount})</span>}
      </Button>
    </>
  );
}

NotificationButtons.propTypes = {
  disabled: PropTypes.bool,
  channel: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  caseDetails: PropTypes.object.isRequired,
  communicationLogs: PropTypes.object.isRequired,
  communicationActions: PropTypes.object.isRequired
};

export default NotificationButtons;
