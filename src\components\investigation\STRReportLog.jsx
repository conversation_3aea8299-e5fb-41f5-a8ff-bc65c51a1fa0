import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import ReactTable from 'react-table';
import { Card } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';

function STRReportLog({ caseRefNo, strReportLogs, fetchSTRReportLogs }) {
  useEffect(() => {
    caseRefNo && fetchSTRReportLogs(caseRefNo);
  }, [caseRefNo, fetchSTRReportLogs]);

  const columns = [
    {
      Header: 'Report Version',
      accessor: 'fileVersion',
      Cell: ({ value }) => `Version ${value}`
    },
    { Header: 'Downloaded By', accessor: 'userName' },
    { Header: 'Downloaded On', accessor: 'creationDate' }
  ];

  const renderContent = () => {
    if (strReportLogs.loader) return <TableLoader />;

    if (strReportLogs.error)
      return <div className="no-data-div no-data-card-padding">{strReportLogs.errorMessage}</div>;

    return (
      <Card>
        <ReactTable
          filterable
          columns={columns}
          data={strReportLogs.data}
          noDataText="No download Logs found"
          showPaginationTop={true}
          showPaginationBottom={false}
          defaultPageSize={5}
          minRows={3}
          showPageJump={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          className="-highlight -striped"
        />
      </Card>
    );
  };

  return <CardContainer title="STR Report Log">{renderContent()}</CardContainer>;
}

STRReportLog.propTypes = {
  userRole: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  strReportLogs: PropTypes.object.isRequired,
  fetchSTRReportLogs: PropTypes.func.isRequired,
  submitBatchId: PropTypes.func.isRequired
};

export default STRReportLog;
