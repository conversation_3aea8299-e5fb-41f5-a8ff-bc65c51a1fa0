import { isEmpty, map } from 'lodash';
import PropTypes from 'prop-types';
import React, { useCallback, useMemo, useState } from 'react';
import { Badge, Dropdown, DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';

import { BUCKET_NAMES } from 'constants/applicationConstants';
import { useBucketLogic } from 'hooks/useBucketLogic';

const BucketListDropdown = React.memo(
  ({ buckets, conf, fetchBuckets, fetchCases, channel, userRole }) => {
    const [toggle, setToggle] = useState(false);

    // Use the custom hook to manage bucket logic
    const { bucketKey, handleBucketChange, bucketKeys } = useBucketLogic({
      buckets,
      conf,
      fetchBuckets,
      fetchCases,
      channel,
      userRole
    });

    // Memoize the toggle handler to prevent unnecessary rerenders
    const handleToggle = useCallback(() => {
      setToggle((prev) => !prev);
    }, []);

    // Memoize the bucket title and count for the dropdown toggle
    const bucketTitle = useMemo(() => BUCKET_NAMES[bucketKey]?.title || '', [bucketKey]);
    const bucketCount = useMemo(
      () => (!isEmpty(buckets.stats) ? buckets.stats[bucketKey] : 0),
      [buckets.stats, bucketKey]
    );

    // Memoize the dropdown items to prevent unnecessary rerenders
    const dropdownItems = useMemo(
      () =>
        map(bucketKeys, (key) => {
          // Memoize the click handler for each dropdown item
          const handleItemClick = () => handleBucketChange(BUCKET_NAMES[key].id);

          return (
            <DropdownItem
              key={key}
              active={bucketKey === key}
              onClick={handleItemClick}
              className="d-flex justify-content-between align-items-center">
              <span>{BUCKET_NAMES[key]?.title || ''}</span>
              <Badge pill className="ms-4">
                {buckets.stats[key]}
              </Badge>
            </DropdownItem>
          );
        }),
      [bucketKeys, bucketKey, buckets.stats, handleBucketChange]
    );

    return (
      <Dropdown toggle={handleToggle} isOpen={toggle} className="title-dropdown">
        <DropdownToggle caret className="dropdown-card-title">
          {bucketTitle}
          <Badge pill className="ms-4 me-1" color="primary">
            {bucketCount}
          </Badge>
        </DropdownToggle>
        <DropdownMenu>{dropdownItems}</DropdownMenu>
      </Dropdown>
    );
  }
);

BucketListDropdown.displayName = 'BucketListDropdown';

BucketListDropdown.propTypes = {
  conf: PropTypes.object.isRequired,
  buckets: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  userRole: PropTypes.string.isRequired,
  fetchCases: PropTypes.func.isRequired,
  fetchBuckets: PropTypes.func.isRequired
};

export default BucketListDropdown;
