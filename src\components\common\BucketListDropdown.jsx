import React, { useEffect, useState } from 'react';
import { isEmpty, map, findKey } from 'lodash';
import PropTypes from 'prop-types';
import { Badge, Dropdown, DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';
import objectAssign from 'object-assign';

import { BUCKET_NAMES } from 'constants/applicationConstants';

const BucketListDropdown = ({ buckets, conf, fetchBuckets, fetchCases, channel, userRole }) => {
  const bucketKeys = !isEmpty(buckets.stats) ? Object.keys(buckets.stats) : [];
  const [toggle, setToggle] = useState(false);
  const { bucket } = conf;
  const bucketKey = !isEmpty(bucket) ? findKey(BUCKET_NAMES, (key) => key['id'] === bucket) : '';

  const handleBucketChange = (bucket) => {
    !isEmpty(bucket) &&
      fetchCases(
        objectAssign({}, conf, {
          bucket,
          pageNo: 1,
          role: userRole,
          sortBy: 'updatedTimestamp',
          sortOrder: 'desc',
          filterCondition: []
        }),
        channel
      );
  };

  useEffect(() => {
    fetchBuckets(userRole, channel);
  }, [userRole, channel]);

  useEffect(() => {
    isEmpty(bucket) &&
      !buckets.loader &&
      !buckets.error &&
      !isEmpty(bucketKeys) &&
      handleBucketChange(BUCKET_NAMES[bucketKeys[0]]?.id);
  }, [buckets.stats, bucket]);

  return (
    <Dropdown toggle={() => setToggle(!toggle)} isOpen={toggle} className="title-dropdown">
      <DropdownToggle caret className="dropdown-card-title">
        {BUCKET_NAMES[bucketKey]?.title || ''}
        <Badge pill className="ms-4 me-1" color="primary">
          {!isEmpty(buckets.stats) ? buckets.stats[bucketKey] : 0}
        </Badge>
      </DropdownToggle>
      <DropdownMenu>
        {map(bucketKeys, (key) => {
          return (
            <DropdownItem
              key={key}
              active={bucketKey === key}
              onClick={() => handleBucketChange(BUCKET_NAMES[key].id)}
              className="d-flex justify-content-between align-items-center">
              <span>{BUCKET_NAMES[key]?.title || ''}</span>
              <Badge pill className="ms-4">
                {buckets.stats[key]}
              </Badge>
            </DropdownItem>
          );
        })}
      </DropdownMenu>
    </Dropdown>
  );
};

BucketListDropdown.propTypes = {
  conf: PropTypes.object.isRequired,
  buckets: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  userRole: PropTypes.string.isRequired,
  fetchCases: PropTypes.func.isRequired,
  fetchBuckets: PropTypes.func.isRequired
};

export default BucketListDropdown;
