import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSTRReportsList } from 'actions/strReportActions';
import STRReportList from 'components/dashboards/STRReportList';

const mapStateToProps = (state) => ({
  userRole: state.auth.userCreds.roles,
  data: state.strReport.allReports,
  hasProvisionalFields: state.user.configurations.provisionalFields
});

const mapDispatchToProps = (dispatch) => ({
  fetchCases: bindActionCreators(onFetchSTRReportsList, dispatch)
});

const STRReportListContainer = connect(mapStateToProps, mapDispatchToProps)(STRReportList);

export default STRReportListContainer;
