import { faArrowUpRightFromSquare } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import React from 'react';
import { Label, UncontrolledTooltip } from 'reactstrap';

import AddToListButtonContainer from 'containers/common/AddToListButtonContainer';
import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';

export const dataColumn = (label, data, addToListData, showTooltip = false) => {
  const tooltipData = showTooltip && data.split(',');

  return (
    <div key={_.kebabCase(label)} className="data-columns no-break p-1">
      <span>
        {label !== '' && (
          <>
            <Label>
              {label === 'attribute1' || label === 'attribute2' ? (
                <ProvisionalFieldsValueContainer attrName={label} />
              ) : (
                label
              )}
            </Label>{' '}
            :{' '}
          </>
        )}
        <span className={showTooltip ? 'tooltip-link' : ''} id={_.camelCase(label)}>
          {showTooltip ? tooltipData[0] : data}
        </span>
        {addToListData && (
          <AddToListButtonContainer
            categoryName={addToListData.entityDetails.categoryName}
            identifier={addToListData.entityDetails.value}
            listTypeData={addToListData.entityDetails.listTypeData}
            partnerId={addToListData.partnerId}
          />
        )}
        {label === 'custRefCif' && (
          <FontAwesomeIcon
            title="Open"
            className="ms-2 open-link-btn color-primary"
            onClick={() => window.open(`/profiling/id/${data}`)}
            icon={faArrowUpRightFromSquare}
          />
        )}
      </span>
      {showTooltip && (
        <UncontrolledTooltip target={_.camelCase(label)}>
          {tooltipData[1] || ''}
        </UncontrolledTooltip>
      )}
    </div>
  );
};

export const displayTooltip = (id, data) => {
  const tooltipData = data.split(',');

  return (
    <>
      <span className="tooltip-link" id={_.camelCase(id)}>
        {tooltipData[0]}
      </span>
      <UncontrolledTooltip target={_.camelCase(Label)}>{tooltipData[1] || ''}</UncontrolledTooltip>
    </>
  );
};

export const renderDataColumnList = (list, partnerId = '') =>
  list.map(({ label, value, entityDetails, showToolTip = false }) => (
    <div key={_.snakeCase(label)}>
      {dataColumn(label, value, entityDetails ? { entityDetails, partnerId } : null, showToolTip)}
    </div>
  ));
