import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col } from 'reactstrap';

import FraudTypeStats from 'containers/dashboards/FraudTypeStatsContainer';
import XChannelDropDownContainer from 'containers/common/XChannelDropDownContainer';
import RuleCategoryGraphContainer from 'containers/dashboards/RuleCategoryGraphContainer';
import TransactionActionTrend from 'containers/dashboards/TransactionActionTrendContainer';

import DownloadReportsButtons from 'components/common/DownloadReportsButtons';
function BusinessDashboard({ period, dashboardRef }) {
  const [xchannelId, setXchannelId] = useState('');

  return (
    <div>
      <Row>
        <Col>
          <XChannelDropDownContainer
            value={xchannelId}
            onChange={setXchannelId}
            className="flex-grow-1"
          />
        </Col>
        <Col className="d-flex justify-content-end align-items-end">
          <DownloadReportsButtons
            elementId="dashboard-content"
            fileNamePrefix={`Business Dashboard - ${period.startDate} - ${period.endDate}`}
            isDisabled={xchannelId === ''}
            dashboardRef={dashboardRef}
          />
        </Col>
      </Row>
      {xchannelId && (
        <div>
          <Col lg="12">
            <FraudTypeStats period={period} xchannelId={xchannelId} />
          </Col>

          <Row>
            <Col lg="4" md="12">
              <TransactionActionTrend period={period} xchannelId={xchannelId} />
            </Col>
            <Col lg="8" md="12">
              <RuleCategoryGraphContainer period={period} xchannelId={xchannelId} />
            </Col>
          </Row>
        </div>
      )}
    </div>
  );
}

BusinessDashboard.propTypes = {
  period: PropTypes.object.isRequired,
  dashboardRef: PropTypes.shape({ current: PropTypes.instanceOf(Element) })
};

export default BusinessDashboard;
