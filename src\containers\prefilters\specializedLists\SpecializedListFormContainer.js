import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import SpecializedListForm from 'components/prefilters/specializedLists/SpecializedListForm';

const mapStateToProps = (state) => ({
  toggle: state.toggle
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const SpecializedListFormContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(SpecializedListForm);

export default SpecializedListFormContainer;
