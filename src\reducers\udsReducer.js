import objectAssign from 'object-assign';

import {
  ON_FETCH_UDS_ENTITY_DETAILS_LOADING,
  ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
  ON_FETCH_UDS_ENTITY_DETAILS_FAILURE,
  ON_FETCH_UDS_ENTITY_SCORES_LOADING,
  ON_FETCH_UDS_ENTITY_SCORES_SUCCESS,
  ON_FETCH_UDS_ENTITY_SCORES_FAILURE,
  ON_FETCH_FACCTUM_DETAILS_LOADING,
  ON_FETCH_FACCTUM_DETAILS_SUCCESS,
  ON_FETCH_FACCTUM_DETAILS_FAILURE,
  ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS,
  ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function udsReducer(state = initialState.uds, action) {
  switch (action.type) {
    case ON_FETCH_UDS_ENTITY_DETAILS_LOADING:
      return objectAssign({}, state, {
        [action.entity]: objectAssign({}, state[action.entity], {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        [action.entity]: objectAssign({}, state[action.entity], {
          details: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_UDS_ENTITY_DETAILS_FAILURE:
      return objectAssign({}, state, {
        [action.response.entity]: objectAssign({}, state[action.response.entity], {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_UDS_ENTITY_SCORES_LOADING:
      return objectAssign({}, state, {
        [action.entity]: objectAssign({}, state[action.entity], {
          riskScore: {
            details: [],
            loader: true,
            error: false,
            errorMessage: ''
          }
        })
      });
    case ON_FETCH_UDS_ENTITY_SCORES_SUCCESS:
      return objectAssign({}, state, {
        [action.entity]: objectAssign({}, state[action.entity], {
          riskScore: {
            details: action.response,
            loader: false,
            error: false,
            errorMessage: ''
          }
        })
      });
    case ON_FETCH_UDS_ENTITY_SCORES_FAILURE:
      return objectAssign({}, state, {
        [action.response.entity]: objectAssign({}, state[action.response.entity], {
          riskScore: {
            details: [],
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          }
        })
      });
    case ON_FETCH_FACCTUM_DETAILS_LOADING:
      return objectAssign({}, state, {
        facctumData: {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_FACCTUM_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        facctumData: {
          details: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_FACCTUM_DETAILS_FAILURE:
      return objectAssign({}, state, {
        facctumData: {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS:
      return objectAssign({}, state, {
        customer: objectAssign({}, state.customer, {
          vulnerability: {
            riskScore: action.response,
            details: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        })
      });
    case ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE:
      return objectAssign({}, state, {
        customer: objectAssign({}, state.customer, {
          vulnerability: {
            details: [],
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          }
        })
      });
    default:
      return state;
  }
}
