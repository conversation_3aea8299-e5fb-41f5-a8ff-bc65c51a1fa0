import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleStats, onFetchOverallFeedbackAnalysis } from 'actions/ruleDashboardActions';
import RuleViolationStats from 'components/dashboards/RuleViolationStats';

const mapStateToProps = (state) => {
  return {
    ruleStats: state.ruleDashboard.stats,
    ruleFeedbackAnalysis: state.ruleDashboard.ruleFeedbackAnalysis
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleStats: bindActionCreators(onFetchRuleStats, dispatch),
    fetchOverallFeedbackAnalysis: bindActionCreators(onFetchOverallFeedbackAnalysis, dispatch)
  };
};

const RuleViolationStatsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleViolationStats);

export default RuleViolationStatsContainer;
