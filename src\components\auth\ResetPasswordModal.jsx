import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { JSEncrypt } from 'jsencrypt';
import { isEmpty, toLower } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect, useCallback } from 'react';
import { Button, FormGroup, Label, Input, Alert, InputGroup, InputGroupText } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { PUB_KEY, isCooperative } from 'constants/publicKey';

const initialStateInputData = {
  oldPassword: '',
  newPassword: '',
  confirmNewPassword: ''
};

const initialStateDisplayInputData = {
  oldPassword: false,
  newPassword: false,
  confirmNewPassword: false
};

const initialStateAlert = {
  visible: false,
  message: '',
  type: ''
};

function ResetPasswordModal({
  theme,
  showChangePassword,
  userCreds,
  toggleShowChangePassword,
  updatePassword
}) {
  const [inputData, setInputData] = useState(initialStateInputData);
  const [displayInputData, setDisplayInputData] = useState(initialStateDisplayInputData);
  const [isValidPassword, setIsValidPassword] = useState(false);
  const [alert, setAlert] = useState(initialStateAlert);

  useEffect(() => {
    if (!showChangePassword) {
      setInputData(initialStateInputData);
      setDisplayInputData(initialStateDisplayInputData);
      setIsValidPassword(false);
      setAlert(initialStateAlert);
    }
  }, [showChangePassword]);

  useEffect(() => {
    validatePassword(inputData);
  }, [inputData, validatePassword]);

  const createPasswordInput = (id, label, patternCheck = true) => {
    let inputProps = {};
    if (patternCheck)
      inputProps = {
        minLength: 7,
        pattern: '(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{7,}',
        title:
          'Password should contain - Minimum seven characters, at least one uppercase letter, one lowercase letter, one number and one special character. Do not use username, firstname or lastname'
      };

    return (
      <FormGroup>
        <Label>{label}</Label>
        <InputGroup>
          <Input
            type={displayInputData[id] ? 'text' : 'password'}
            id={id}
            name={id}
            value={inputData[id]}
            onChange={(e) => setInputData((prev) => ({ ...prev, [id]: e.target.value }))}
            required
            autoComplete="off"
            onPaste={(event) => {
              event.preventDefault();
            }}
            {...inputProps}
          />
          <InputGroupText
            onClick={() =>
              setDisplayInputData((prev) => ({ ...prev, [id]: !displayInputData[id] }))
            }>
            <FontAwesomeIcon icon={displayInputData[id] ? faEyeSlash : faEye} />
          </InputGroupText>
        </InputGroup>
      </FormGroup>
    );
  };

  const toggleAlert = useCallback(
    (message = '', type = '') =>
      setAlert({
        type: !isEmpty(message) ? type : '',
        visible: !isEmpty(message),
        message
      }),
    []
  );

  const validatePassword = useCallback(
    (inputData) => {
      const { oldPassword, newPassword, confirmNewPassword } = inputData;
      if (newPassword.length < 1 || confirmNewPassword.length < 1 || oldPassword.length < 1)
        setIsValidPassword(false);
      else if (newPassword !== confirmNewPassword) {
        toggleAlert('New password and Confirm new password does not match', 'danger');
        setIsValidPassword(false);
      } else if (isCooperative && toLower(newPassword) === toLower(oldPassword)) {
        toggleAlert('New password cannot be same as old password', 'danger');
        setIsValidPassword(false);
      } else {
        setIsValidPassword(true);
        toggleAlert();
      }
    },
    [toggleAlert]
  );

  const encryptPassword = (password) => {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(PUB_KEY);

    return encrypt.encrypt(password);
  };

  const submit = (e) => {
    e.preventDefault();

    if (isValidPassword) {
      const formData = {
        oldPassword: encryptPassword(inputData.oldPassword),
        newPassword: encryptPassword(inputData.newPassword)
      };
      updatePassword(formData);
    }
  };

  return (
    <ModalContainer
      size="md"
      theme={theme}
      isOpen={showChangePassword}
      allowClose={userCreds.roles === 'super-admin' || !userCreds.isFirstLogin}
      toggle={toggleShowChangePassword}
      header="Change Password">
      <form onSubmit={submit}>
        <Alert color={alert.type} isOpen={alert.visible} toggle={() => toggleAlert()}>
          {alert.message}
        </Alert>
        {createPasswordInput('oldPassword', 'Old Password', false)}
        {createPasswordInput('newPassword', 'New Password')}
        {createPasswordInput('confirmNewPassword', 'Confirm New Password')}
        <Button size="sm" color="primary" className="d-flex ms-auto" disabled={!isValidPassword}>
          Submit
        </Button>
      </form>
    </ModalContainer>
  );
}

ResetPasswordModal.propTypes = {
  theme: PropTypes.string.isRequired,
  userCreds: PropTypes.object.isRequired,
  showChangePassword: PropTypes.bool.isRequired,
  updatePassword: PropTypes.func.isRequired,
  toggleShowChangePassword: PropTypes.func.isRequired
};

export default ResetPasswordModal;
