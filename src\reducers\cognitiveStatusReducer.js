import {
  ON_<PERSON>ET<PERSON>_COGNITIVE_STATUS_LOADING,
  ON_FETCH_COGNITIVE_STATUS_FAILURE,
  ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS
} from 'constants/actionTypes';
import initialState from './initialState';
import objectAssign from 'object-assign';

export default function cognitiveStatusReducer(state = initialState.cognitiveStatus, action) {
  switch (action.type) {
    case ON_FETCH_COGNITIVE_STATUS_LOADING:
      return objectAssign({}, state, { loader: true });
    case ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS:
      return objectAssign({}, state, {
        list: objectAssign({}, state.list, {
          [action.channel]: action.response
        }),
        loader: false
      });
    case ON_FETCH_COGNITIVE_STATUS_FAILURE:
      return objectAssign({}, state, {
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      });
    default:
      return state;
  }
}
