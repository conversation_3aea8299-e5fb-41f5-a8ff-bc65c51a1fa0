/* eslint-disable react/no-multi-comp */
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { CardTitle, CardSubtitle, Row, Col, Input, Button, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import RFIReportsTable from './RFIReportsTable';

function NBFCReport({ nbfcTrxns, fetchNBFCTrxnsCount, fetchNBFCTrxnsData }) {
  const [cummValue, setCummValue] = useState(nbfcTrxns.cummValue);
  const [period, setPeriod] = useState(nbfcTrxns.period ?? 'Day');
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchNBFCTrxnsData({ pageNo: page + 1, pageSize, cummValue, period });
    setPageNo(page);
  };

  const columnHeaders = [
    {
      Header: 'Total Credit',
      columns: [
        {
          Header: 'Transaction Amount',
          accessor: 'totalCreditTxnAmount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnAmount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalCreditTxnCount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnCount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    },
    {
      Header: 'Total Debit',
      columns: [
        {
          Header: 'Transaction Amount',
          accessor: 'totalDebitTxnAmount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalDebitTxnAmount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalDebitTxnCount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'totalDebitTxnCount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  const onSubmit = (e) => {
    e.preventDefault();
    if ((cummValue !== nbfcTrxns.cummValue || period !== nbfcTrxns.period) && !isNaN(cummValue))
      fetchNBFCTrxnsCount({ cummValue, period });
  };

  return (
    <Row>
      <Col md="12" className="mb-3">
        <form onSubmit={onSubmit}>
          <Row>
            <Col xs="4" sm="3">
              <Label htmlFor="cummValue">Total credit amount</Label>
              <Input
                type="number"
                id="cummValue"
                name="cummValue"
                value={cummValue}
                placeholder="Enter value to generate report"
                onChange={(e) => setCummValue(+e.target.value)}
                min={0}
                max={99999999999}
                pattern="\d{1,11}"
                title="Please input valid amount"
                required
              />
            </Col>
            <Col xs="4" sm="3">
              <Label htmlFor="period">Period</Label>
              <Input
                type="select"
                id="period"
                name="period"
                value={period}
                onChange={(e) => setPeriod(e.target.value)}
                required>
                <option value=""> Select Period</option>
                <option value="Day">Day</option>
                <option value="Month">Month</option>
                <option value="Year">Year</option>
              </Input>
            </Col>
            <Col className="d-flex align-items-end">
              <Button outline size="sm" color="primary">
                Search
              </Button>
            </Col>
          </Row>
        </form>
      </Col>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{nbfcTrxns.count?.value ?? 0}</CardTitle>
          <CardSubtitle># High Value NBFC</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="High Value NBFC">
          {nbfcTrxns.count?.value > 0 ? (
            <RFIReportsTable
              count={nbfcTrxns.count?.value}
              additionalHeaders={columnHeaders}
              data={nbfcTrxns.data}
              filter={{ cummValue, period }}
              fetchData={fetchNBFCTrxnsData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div">No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

NBFCReport.propTypes = {
  nbfcTrxns: PropTypes.object.isRequired,
  fetchNBFCTrxnsCount: PropTypes.func.isRequired,
  fetchNBFCTrxnsData: PropTypes.func.isRequired
};

export default NBFCReport;
