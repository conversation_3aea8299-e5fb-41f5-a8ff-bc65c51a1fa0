import React, { useEffect, useState, createRef } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Row, Col, Label, Input, FormGroup } from 'reactstrap';

import RuleEfficiencyGraph from 'containers/dashboards/RuleEfficiencyGraphContainer';
import RuleViolationStats from 'containers/dashboards/RuleViolationStatsContainer';
import RuleEfficacyGraph from 'containers/dashboards/RuleEfficacyGraphContainer';
import RuleBehaviourGraph from 'containers/dashboards/RuleBehaviourGraphContainer';
import RuleEffectivenessGraph from 'containers/dashboards/RuleEffectivenessGraphContainer';
import RuleFeedbackStats from 'containers/dashboards/RuleFeedbackStatsContainer';
import RuleFeedbackAnalysisStats from 'containers/dashboards/RuleFeedbackAnalysisStatsContainer';

import DownloadReportsButtons from 'components/common/DownloadReportsButtons';
import { isCooperative } from 'constants/publicKey';

function RuleEfficiencyDashboard({
  ruleNames,
  hasSandbox,
  fetchRuleNamesList,
  period,
  channels,
  dashboardRef
}) {
  const ruleList = ruleNames?.list[channels[0]];
  const [selectedRuleName, setSelectedRuleName] = useState('');
  const [selectedRule, setSelectedRule] = useState('');

  useEffect(() => {
    if (ruleList?.length === 0 && !ruleNames.loader) fetchRuleNamesList(channels[0]);
  }, []);

  function onChangeSelectedRule(selectedRule) {
    const rule = selectedRule.split(', ');
    setSelectedRule(rule[0]);
    setSelectedRuleName(rule[1]);
  }

  return (
    <div>
      <div className="d-flex align-items-center gap-3 p-2">
        <FormGroup className="flex-grow-1 mb-4">
          <Label>Select Rule: </Label>
          <Input
            type="select"
            name="rule"
            value={`${selectedRule}, ${selectedRuleName}`}
            onChange={(e) => onChangeSelectedRule(e.target.value)}>
            <option value="">-- SELECT --</option>
            {ruleList?.map((d) => (
              <option key={d.code} value={`${d.code}, ${d.name}`}>
                {d.name}
              </option>
            ))}
          </Input>
        </FormGroup>
        {selectedRule !== '' && (
          <Link
            className="btn btn-sm btn-outline-primary mt-2"
            disabled
            to={{
              pathname: '/dsl',
              state: { name: selectedRuleName }
            }}>
            Go to Rule
          </Link>
        )}

        <DownloadReportsButtons
          elementId="dashboard-content"
          fileNamePrefix={`RuleEfficiency Dashboard - ${selectedRuleName} - ${period.startDate} - ${period.endDate}`}
          isDisabled={!selectedRule}
          dashboardRef={dashboardRef}
        />
      </div>
      {selectedRule !== '' && (
        <>
          <RuleViolationStats period={period} rule={selectedRule} />
          <Row>
            <Col lg={hasSandbox === 1 ? 9 : 12} md={hasSandbox === 1 ? 8 : 12}>
              <RuleEffectivenessGraph period={period} rule={selectedRule} />
            </Col>
            {hasSandbox === 1 && (
              <Col lg="3" md="4">
                <RuleEfficacyGraph period={period} rule={selectedRule} />
              </Col>
            )}
            <Col lg="12" md="12">
              <RuleEfficiencyGraph period={period} rule={selectedRule} />
              <RuleBehaviourGraph period={period} rule={selectedRule} />
            </Col>
            {!isCooperative && (
              <>
                <Col lg="12" md="12">
                  <RuleFeedbackStats period={period} rule={selectedRule} />
                </Col>
                <Col lg="12" md="12">
                  <RuleFeedbackAnalysisStats period={period} rule={selectedRule} />
                </Col>
              </>
            )}
          </Row>
        </>
      )}
    </div>
  );
}

RuleEfficiencyDashboard.propTypes = {
  period: PropTypes.object.isRequired,
  ruleNames: PropTypes.object.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired,
  dashboardRef: PropTypes.shape({ current: PropTypes.instanceOf(Element) })
};

export default RuleEfficiencyDashboard;
