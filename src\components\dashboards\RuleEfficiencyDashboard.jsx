import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { MultiSelect } from 'react-multi-select-component';
import { Link } from 'react-router-dom';
import { Row, Col, Label, FormGroup } from 'reactstrap';

import DownloadReportsButtons from 'components/common/DownloadReportsButtons';
import { isCooperative } from 'constants/publicKey';
import RuleBehaviourGraph from 'containers/dashboards/RuleBehaviourGraphContainer';
import RuleEffectivenessGraph from 'containers/dashboards/RuleEffectivenessGraphContainer';
import RuleEfficacyGraph from 'containers/dashboards/RuleEfficacyGraphContainer';
import RuleEfficiencyGraph from 'containers/dashboards/RuleEfficiencyGraphContainer';
import RuleFeedbackAnalysisStats from 'containers/dashboards/RuleFeedbackAnalysisStatsContainer';
import RuleFeedbackStats from 'containers/dashboards/RuleFeedbackStatsContainer';
import RuleViolationStats from 'containers/dashboards/RuleViolationStatsContainer';

function RuleEfficiencyDashboard({
  ruleNames,
  hasSandbox,
  fetchRuleNamesList,
  period,
  channels,
  dashboardRef
}) {
  const ruleList = ruleNames?.list[channels[0]];
  const [selectedRuleName, setSelectedRuleName] = useState('');
  const [selectedRule, setSelectedRule] = useState([]);
  const [selectedRuleCode, setSelectedRuleCode] = useState('');

  useEffect(() => {
    if (ruleList?.length === 0 && !ruleNames.loader) fetchRuleNamesList(channels[0]);
  }, []);

  function onChangeSelectedRule(selectedOptions) {
    const lastSelected = selectedOptions[selectedOptions.length - 1];
    if (lastSelected) {
      setSelectedRule([lastSelected]);
      const [code, name] = lastSelected.value.split(', ');
      setSelectedRuleName(name);
      setSelectedRuleCode(code);
    } else {
      setSelectedRule([]);
      setSelectedRuleName('');
      setSelectedRuleCode('');
    }
  }

  const ruleOptions = ruleList?.map((d) => ({
    label: d.name,
    value: `${d.code}, ${d.name}`
  }));

  return (
    <div>
      <div className="d-flex align-items-center gap-3 p-2">
        <FormGroup className="flex-grow-1 mb-4">
          <Label>Select Rule: </Label>
          <MultiSelect
            options={ruleOptions}
            value={selectedRule}
            onChange={(selected) => onChangeSelectedRule(selected)}
            labelledBy="-- SELECT --"
            hasSelectAll={false}
            disableSearch={false}
            overrideStrings={{
              selectSomeItems: '-- SELECT --',
              search: 'Search...',
              allItemsAreSelected: '',
              noOptions: 'No rules found',
              selectAll: '',
              clearSearch: '×'
            }}
          />
        </FormGroup>
        {selectedRuleCode !== '' && (
          <Link
            className="btn btn-sm btn-outline-primary mt-2"
            disabled
            to={{
              pathname: '/dsl',
              state: { name: selectedRuleName }
            }}>
            Go to Rule
          </Link>
        )}

        <DownloadReportsButtons
          elementId="dashboard-content"
          fileNamePrefix={`RuleEfficiency Dashboard - ${selectedRuleName} - ${period.startDate} - ${period.endDate}`}
          isDisabled={!selectedRuleCode}
          dashboardRef={dashboardRef}
        />
      </div>
      {selectedRuleCode !== '' && (
        <>
          <RuleViolationStats period={period} rule={selectedRuleCode} />
          <Row>
            <Col lg={hasSandbox === 1 ? 9 : 12} md={hasSandbox === 1 ? 8 : 12}>
              <RuleEffectivenessGraph period={period} rule={selectedRuleCode} />
            </Col>
            {hasSandbox === 1 && (
              <Col lg="3" md="4">
                <RuleEfficacyGraph period={period} rule={selectedRuleCode} />
              </Col>
            )}
            <Col lg="12" md="12">
              <RuleEfficiencyGraph period={period} rule={selectedRuleCode} />
              <RuleBehaviourGraph period={period} rule={selectedRuleCode} />
            </Col>
            {!isCooperative && (
              <>
                <Col lg="12" md="12">
                  <RuleFeedbackStats period={period} rule={selectedRuleCode} />
                </Col>
                <Col lg="12" md="12">
                  <RuleFeedbackAnalysisStats period={period} rule={selectedRuleCode} />
                </Col>
              </>
            )}
          </Row>
        </>
      )}
    </div>
  );
}

RuleEfficiencyDashboard.propTypes = {
  period: PropTypes.object.isRequired,
  ruleNames: PropTypes.object.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired,
  dashboardRef: PropTypes.shape({ current: PropTypes.instanceOf(Element) })
};

export default RuleEfficiencyDashboard;
