import responses from 'mocks/responses';

import { onFetchTransactionDetails } from 'actions/transactionDetailsActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('transaction details actions', () => {
  it('should fetch transaction details for channel-txn', () => {
    const expectedActions = [
      { type: types.ON_FETCH_TRANSACTION_DETAIL_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL,
        response: responses.caseAssignment.transaction
      }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(onFetchTransactionDetails(1, 'rpsl')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
