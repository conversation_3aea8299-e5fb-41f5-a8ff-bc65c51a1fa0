import * as types from 'constants/actionTypes';
import cognitiveStatusReducer from 'reducers/cognitiveStatusReducer';
import initialState from 'reducers/initialState';

describe('Cognitive Status Reducer', () => {
  it('should return the intial state', () => {
    expect(cognitiveStatusReducer(undefined, {})).toEqual(initialState.cognitiveStatus);
  });

  it('should handle ON_FETCH_COGNITIVE_STATUS_LOADING', () => {
    expect(
      cognitiveStatusReducer(
        {
          list: {
            rpsl: false
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_COGNITIVE_STATUS_LOADING
        }
      )
    ).toEqual({
      list: {
        rpsl: false
      },
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS', () => {
    expect(
      cognitiveStatusReducer(
        {
          list: {
            rpsl: false
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS,
          channel: 'rpsl',
          response: true
        }
      )
    ).toEqual({
      list: {
        rpsl: true
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_COGNITIVE_STATUS_FAILURE', () => {
    expect(
      cognitiveStatusReducer(
        {
          list: {
            rpsl: false
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_COGNITIVE_STATUS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      list: {
        rpsl: false
      },
      loader: false,
      error: true,
      errorMessage: 'error message'
    });
  });
});
