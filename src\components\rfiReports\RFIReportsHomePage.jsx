import React from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import HighValueClosedAccountsReportContainer from 'containers/rfiReports/HighValueClosedAccountsReportContainer';
import HighValueNewAccountsReportContainer from 'containers/rfiReports/HighValueNewAccountsReportContainer';
import FraudToSalesRatioReportContainer from 'containers/rfiReports/FraudToSalesRatioReportContainer';
import NBFCReportContainer from 'containers/rfiReports/NBFCReportContainer';
import UnusualDeclineTurnoverReportContainer from 'containers/rfiReports/UnusalDeclineTurnoverReportContainer';

function RFIReportsHomePage() {
  return (
    <div className="content-wrapper">
      <Tabs
        pills
        tabNames={[
          'High Volume Closed Accounts',
          'High Value New Accounts',
          'High Fraud to Sales Ratio',
          'High Value NBFC',
          'Unusual Decline in Turnover'
        ]}>
        <TabPane tabId={0}>
          <HighValueClosedAccountsReportContainer />
        </TabPane>
        <TabPane tabId={1}>
          <HighValueNewAccountsReportContainer />
        </TabPane>
        <TabPane tabId={2}>
          <FraudToSalesRatioReportContainer />
        </TabPane>
        <TabPane tabId={3}>
          <NBFCReportContainer />
        </TabPane>
        <TabPane tabId={4}>
          <UnusualDeclineTurnoverReportContainer />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default RFIReportsHomePage;
