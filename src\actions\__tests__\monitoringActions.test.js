import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/monitoringActions';
import responses from 'mocks/responses';

const { count, transaction } = responses.monitoring;

describe('monitoring actions', () => {
  it('should fetch transaction count', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT,
        channel: 'frm',
        response: count
      }
    ];
    const store = mockStore({ monitoringActions: {} });

    return store.dispatch(actions.onFetchTransactionCount('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch monitoring data', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_MONITORING_DATA,
        response: transaction
      }
    ];
    const store = mockStore({ monitoringActions: {} });

    return store.dispatch(actions.onFetchMonitoringData()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
