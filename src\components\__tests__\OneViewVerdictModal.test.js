/* eslint-disable react/prop-types */
/* eslint-disable react/display-name */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import OneViewVerdictModal from '../../components/caseReview/OneViewVerdictModal';

jest.mock('components/common/ModalContainer', () => ({ children, header }) => (
  <div>
    <div>{header}</div>
    {children}
  </div>
));

describe('OneViewVerdictModal Component', () => {
  const mockFetchNotationsList = jest.fn();
  const mockFetchFraudTypesList = jest.fn();
  const mockCloseCase = jest.fn();
  const mockToggleModal = jest.fn();

  const mockNotations = {
    master: {
      list: [
        { id: '1', notation: 'Test Notation 1', category: 'Fraud' },
        { id: '2', notation: 'Test Notation 2', category: 'Genuine' },
        { id: '3', notation: 'Test Notation 3', category: 'Fraud' },
        { id: '4', notation: 'Test Notation 4', category: 'Fraud' }
      ],
      loader: false,
      error: false
    }
  };

  const mockFraudTypes = {
    list: [
      { id: '1', fraudName: 'Phishing', verdict: 'Fraud' },
      { id: '2', fraudName: 'Carding', verdict: 'Fraud' }
    ],
    loader: false,
    error: false
  };

  const renderComponent = (props) => {
    return render(
      <OneViewVerdictModal
        isOpen={true}
        theme="dark"
        verdict="Fraud"
        notations={mockNotations}
        fraudTypes={mockFraudTypes}
        closeCase={mockCloseCase}
        toggleModal={mockToggleModal}
        fetchNotationsList={mockFetchNotationsList}
        fetchFraudTypesList={mockFetchFraudTypesList}
        {...props}
      />
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the modal with fraud type options and notation options', () => {
    renderComponent();

    expect(screen.getByText('Mark as Fraud')).toBeInTheDocument();

    const fraudTypeSelect = screen.getByLabelText('Fraud type');
    expect(fraudTypeSelect).toBeInTheDocument();

    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(5);
    expect(options[0]).toHaveTextContent('-- SELECT --');
    expect(options[1]).toHaveTextContent('Test Notation 1');
    expect(options[2]).toHaveTextContent('Test Notation 3');
  });

  it('should handle form submission correctly', () => {
    renderComponent();

    fireEvent.change(screen.getByLabelText('Fraud type'), { target: { value: '1' } });
    fireEvent.change(screen.getByLabelText('Notation'), { target: { value: '1' } });

    fireEvent.submit(screen.getByRole('button', { name: /submit/i }));

    expect(mockCloseCase).toHaveBeenCalled();
  });

  it('should display comment input when "Others" is selected for notation', () => {
    renderComponent();

    fireEvent.change(screen.getByLabelText('Notation'), { target: { value: 'Others' } });

    const commentInput = screen.getByLabelText('Comment');
    expect(commentInput).toBeInTheDocument();

    fireEvent.change(commentInput, { target: { value: 'Test comment' } });
    expect(commentInput).toHaveValue('Test comment');
  });

  it('should call fetchNotationsList and fetchFraudTypesList on mount if lists are empty', () => {
    const emptyNotations = { ...mockNotations, master: { list: [], loader: false, error: false } };
    const emptyFraudTypes = { ...mockFraudTypes, list: [], loader: false, error: false };

    renderComponent({ notations: emptyNotations, fraudTypes: emptyFraudTypes });

    expect(mockFetchNotationsList).toHaveBeenCalled();
    expect(mockFetchFraudTypesList).toHaveBeenCalled();
  });

  it('should display loading options when loaders are true', () => {
    const loadingNotations = {
      ...mockNotations,
      master: { ...mockNotations.master, loader: true }
    };
    const loadingFraudTypes = { ...mockFraudTypes, loader: true };

    renderComponent({ notations: loadingNotations, fraudTypes: loadingFraudTypes });

    const loadingElements = screen.getAllByText('Loading...');
    expect(loadingElements).toHaveLength(2);
  });

  it('should display error messages if errors exist', () => {
    const errorNotations = {
      ...mockNotations,
      master: { ...mockNotations.master, error: true, errorMessage: 'Notation error' }
    };
    const errorFraudTypes = { ...mockFraudTypes, error: true, errorMessage: 'Fraud type error' };

    renderComponent({ notations: errorNotations, fraudTypes: errorFraudTypes });

    expect(screen.getByText('Notation error')).toBeInTheDocument();
    expect(screen.getByText('Fraud type error')).toBeInTheDocument();
  });
});
