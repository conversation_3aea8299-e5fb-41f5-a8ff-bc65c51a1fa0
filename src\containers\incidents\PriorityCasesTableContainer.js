import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchPriorityCases } from 'actions/incidentActions';
import PriorityCasesTable from 'components/incidents/PriorityCasesTable';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  data: state.incidents.priorityList,
  hasProvisionalFields: state.user.configurations.provisionalFields
});
const mapDispatchToProps = (dispatch) => ({
  fetchCases: bindActionCreators(onFetchPriorityCases, dispatch)
});

const PriorityCasesTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(PriorityCasesTable);

export default PriorityCasesTableContainer;
