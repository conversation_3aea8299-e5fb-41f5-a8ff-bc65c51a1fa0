import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Escalation from 'components/common/Escalation';
import { onEscalateByMail } from 'actions/investigationActions';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    authDetails: state.auth.userCreds,
    transactionDetails: state.transactionDetails,
    display: state.toggle.escalationModal,
    notations: state.caseAssignment.notations.list
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    escalateByMail: bindActionCreators(onEscalateByMail, dispatch)
  };
};

const EscalationContainer = connect(mapStateToProps, mapDispatchToProps)(Escalation);

export default EscalationContainer;
