import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onEscalateByMail } from 'actions/investigationActions';
import Escalation from 'components/common/Escalation';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  authDetails: state.auth.userCreds,
  transactionDetails: state.transactionDetails,
  display: state.toggle.escalationModal,
  notations: state.caseAssignment.notations.list
});

const mapDispatchToProps = (dispatch) => ({
  escalateByMail: bindActionCreators(onEscalateByMail, dispatch)
});

const EscalationContainer = connect(mapStateToProps, mapDispatchToProps)(Escalation);

export default EscalationContainer;
