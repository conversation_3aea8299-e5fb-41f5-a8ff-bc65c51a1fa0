import {
  ON_CUSTOMER_CALL_PLACED_SUCCESS,
  ON_CUSTOMER_CALL_END_SUCCESS,
  ON_FETCH_CALL_DISPOSITION_LIST_LOADING,
  ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS,
  ON_FETCH_CALL_DISPOSITION_LIST_FAILURE,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE
} from 'constants/actionTypes';
import initialState from './initialState';

export default function customerCommunicationReducer(
  state = initialState.customerCommunication,
  action
) {
  switch (action.type) {
    case ON_CUSTOMER_CALL_PLACED_SUCCESS:
      return {
        ...state,
        call: {
          caseRefNo: action.caseRefNo,
          status: 'PLACED',
          timeStamp: new Date()
        }
      };
    case ON_CUSTOMER_CALL_END_SUCCESS:
      return {
        ...state,
        call: initialState.customerCommunication.call
      };
    case ON_FETCH_CALL_DISPOSITION_LIST_LOADING:
      return {
        ...state,
        dispositions: {
          ...state.dispositions,
          loader: true,
          error: false,
          errorMessage: ''
        }
      };
    case ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS:
      return {
        ...state,
        dispositions: {
          ...state.dispositions,
          list: action.response,
          loader: false
        }
      };
    case ON_FETCH_CALL_DISPOSITION_LIST_FAILURE:
      return {
        ...state,
        dispositions: {
          ...state.dispositions,
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      };
    case ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING:
      return {
        ...state,
        logs: {
          ...state.logs,
          loader: true,
          error: false,
          errorMessage: ''
        }
      };
    case ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS:
      return {
        ...state,
        logs: {
          ...state.logs,
          loader: false,
          data: action.response
        }
      };
    case ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE:
      return {
        ...state,
        logs: {
          ...state.logs,
          loader: false,
          error: true,
          errorMessage: action.response?.message
        }
      };
    default:
      return state;
  }
}
