import PropTypes from 'prop-types';
import React from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import ClosedCasesTableContainer from 'containers/dashboards/ClosedCasesTableContainer';
import FMRReportedTxnsContainer from 'containers/dashboards/FMRReportedTxnsContainer';

function ComplianceDashboard({ period }) {
  return (
    <div>
      <Tabs tabNames={['Closed Cases', 'FMR Reports']}>
        <TabPane key={0} tabId={0}>
          <ClosedCasesTableContainer period={period} />
        </TabPane>
        <TabPane key={1} tabId={1}>
          <FMRReportedTxnsContainer period={period} />
        </TabPane>
      </Tabs>
    </div>
  );
}

ComplianceDashboard.propTypes = {
  period: PropTypes.object.isRequired
};

export default ComplianceDashboard;
