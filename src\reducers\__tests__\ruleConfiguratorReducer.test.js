import { sortBy } from 'lodash';
import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import ruleConfiguratorReducer from 'reducers/ruleConfiguratorReducer';

const updateRuleState = (list, ruleCode, accessor, newState) =>
  list.map((item) =>
    item.code === ruleCode ? Object.assign({}, item, { [accessor]: newState }) : item
  );

describe('Rule Configurator reducer', () => {
  it('should return the intial state', () => {
    expect(ruleConfiguratorReducer(undefined, {})).toEqual(initialState.ruleConfigurator);
  });

  it('should handle ON_FETCH_RULE_LIST_LOADING', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: []
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_RULE_LIST_LOADING
        }
      )
    ).toEqual({
      list: {
        frm: []
      },
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_RULE_LIST', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: []
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_RULE_LIST,
          channel: 'frm',
          response: responses.rules.list
        }
      )
    ).toEqual({
      list: {
        frm: sortBy(responses.rules.list, ['name'])
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_RULE_LIST_FAILURE', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: []
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_RULE_LIST_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      list: {
        frm: []
      },
      loader: false,
      error: true,
      errorMessage: 'error msg'
    });
  });

  it('should handle ON_SUCCESSFUL_TOGGLE_RULE_STATUS', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: sortBy(responses.rules.list, ['name'])
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_SUCCESSFUL_TOGGLE_RULE_STATUS,
          ruleCode: '15dd9b65-549d-44e1-b922-aa065d98766e',
          channel: 'frm',
          status: 'Activated'
        }
      )
    ).toEqual({
      list: {
        frm: updateRuleState(
          sortBy(responses.rules.list, ['name']),
          '15dd9b65-549d-44e1-b922-aa065d98766e',
          'status',
          'Activated'
        )
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: sortBy(responses.rules.list, ['name'])
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT,
          ruleCode: '15dd9b65-549d-44e1-b922-aa065d98766e',
          channel: 'frm',
          explicit: false
        }
      )
    ).toEqual({
      list: {
        frm: updateRuleState(
          sortBy(responses.rules.list, ['name']),
          '15dd9b65-549d-44e1-b922-aa065d98766e',
          'explicit',
          false
        )
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION', () => {
    expect(
      ruleConfiguratorReducer(
        {
          list: {
            frm: sortBy(responses.rules.list, ['name'])
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION,
          ruleCode: '15dd9b65-549d-44e1-b922-aa065d98766e',
          channel: 'frm',
          active: 'Enable'
        }
      )
    ).toEqual({
      list: {
        frm: updateRuleState(
          sortBy(responses.rules.list, ['name']),
          '15dd9b65-549d-44e1-b922-aa065d98766e',
          'active',
          'Enable'
        )
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_RULE_NAMES_LIST_LOADING', () => {
    expect(
      ruleConfiguratorReducer(
        {
          ruleNames: {
            list: {
              frm: [],
              str: []
            },
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_NAMES_LIST_LOADING
        }
      )
    ).toEqual({
      ruleNames: {
        list: {
          frm: [],
          str: []
        },
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_NAMES_LIST_SUCCESS', () => {
    expect(
      ruleConfiguratorReducer(
        {
          ruleNames: {
            list: {
              frm: [],
              str: []
            },
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_NAMES_LIST_SUCCESS,
          response: responses.rules.ruleNames,
          channel: 'frm'
        }
      )
    ).toEqual({
      ruleNames: {
        list: {
          frm: sortBy(responses.rules.ruleNames, ['name']),
          str: []
        },
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_NAMES_LIST_FAILURE', () => {
    expect(
      ruleConfiguratorReducer(
        {
          ruleNames: {
            list: {
              frm: [],
              str: []
            },
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_NAMES_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      ruleNames: {
        list: {
          frm: [],
          str: []
        },
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
