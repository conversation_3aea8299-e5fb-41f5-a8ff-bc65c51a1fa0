import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchOccupancyRate } from 'actions/slaDashboardActions';
import OccupancyGraph from 'components/dashboards/OccupancyGraph';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  occupancyRate: state.slaDashboard.occupancyRate
});

const mapDispatchToProps = (dispatch) => ({
  fetchOccupancyRate: bindActionCreators(onFetchOccupancyRate, dispatch)
});

const OccupancyGraphContainer = connect(mapStateToProps, mapDispatchToProps)(OccupancyGraph);

export default OccupancyGraphContainer;
