import {
  ON_FETCH_NOTATIONS_LIST_LOADING,
  ON_FETCH_NOTATIONS_LIST_SUCCESS,
  ON_FETCH_NOTATIONS_LIST_FAILURE,
  ON_UPDATE_NOTATION_SUCCESS,
  ON_DELETE_NOTATION_SUCCESS,
  ON_FETCH_CASE_NOTATION_LIST_LOADING,
  ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
  ON_FETCH_CASE_NOTATION_LIST_FAILURE,
  ON_ADD_CASE_NOTATION_SUCCESS
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader } from 'actions/toggleActions';
import { onFetchCaseLogs, onFetchEntityLogs } from 'actions/logsActions';
import client from 'utility/apiClient';

function fetchNotationsList() {
  return client({
    url: `casereview/notation/fetch/masters`
  });
}

function onFetchNotationsListLoading() {
  return { type: ON_FETCH_NOTATIONS_LIST_LOADING };
}

function onFetchNotationsListSuccess(response) {
  return {
    type: ON_FETCH_NOTATIONS_LIST_SUCCESS,
    response
  };
}

function onFetchNotationsListFailure(response) {
  return {
    type: ON_FETCH_NOTATIONS_LIST_FAILURE,
    response
  };
}

function onFetchNotationsList() {
  return function (dispatch) {
    dispatch(onFetchNotationsListLoading());
    return fetchNotationsList().then(
      (success) => dispatch(onFetchNotationsListSuccess(success)),
      (error) => dispatch(onFetchNotationsListFailure(error))
    );
  };
}

function addNotationToMaster(formData) {
  return client({
    method: 'POST',
    url: `casereview/notation/add/notation`,
    data: formData,
    badRequestMessage: 'Unable to add notation'
  });
}

function onAddNotationToMaster(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addNotationToMaster(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Notation added successfully!' }));
          dispatch(onFetchNotationsList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateNotationFromMaster(formData) {
  return client({
    method: 'PUT',
    url: `casereview/notation/update/notation`,
    data: formData,
    badRequestMessage: 'Unable to update notation'
  });
}

function onUpdateNotationFromMasterSuccess(response) {
  return {
    type: ON_UPDATE_NOTATION_SUCCESS,
    response
  };
}

function onUpdateNotationFromMaster(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateNotationFromMaster(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Notation updated successfully!' }));
          dispatch(onUpdateNotationFromMasterSuccess(formData));
          dispatch(onFetchNotationsList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteNotationFromMaster(id) {
  return client({
    method: 'PUT',
    url: `casereview/notation/delete/id/${id}`,
    badRequestMessage: 'Unable to delete notation'
  });
}

function onDeleteNotationFromMasterSuccess(id) {
  return {
    type: ON_DELETE_NOTATION_SUCCESS,
    id
  };
}

function onDeleteNotationFromMaster(id) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteNotationFromMaster(id)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Notation deleted successfuully!' }));
          dispatch(onDeleteNotationFromMasterSuccess(id));
          dispatch(onFetchNotationsList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCaseNotationList(caseId, channel) {
  return client({
    url: `casereview/case/${caseId}/${channel}/notation`
  });
}

function onFetchCaseNotationListLoading() {
  return { type: ON_FETCH_CASE_NOTATION_LIST_LOADING };
}

function onSuccessfulFetchCaseNotationList(response) {
  return {
    type: ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
    response
  };
}

function onFetchCaseNotationListFailure(response) {
  return {
    type: ON_FETCH_CASE_NOTATION_LIST_FAILURE,
    response
  };
}

function onFetchCaseNotationList(caseId, channel) {
  return function (dispatch) {
    dispatch(onFetchCaseNotationListLoading());
    return fetchCaseNotationList(caseId, channel).then(
      (success) => dispatch(onSuccessfulFetchCaseNotationList(success)),
      (error) => dispatch(onFetchCaseNotationListFailure(error))
    );
  };
}

function addCaseNotation(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/notation`,
    data: formData,
    badRequestMessage: 'Unable to add notation'
  });
}

function onSuccessfulAddCaseNotation(notation) {
  return {
    type: ON_ADD_CASE_NOTATION_SUCCESS,
    notation
  };
}

function onAddCaseNotation(formData) {
  return function (dispatch, getState) {
    const { selectedCase } = getState().caseAssignment;
    dispatch(onToggleLoader(true));
    return addCaseNotation(formData)
      .then(
        () => {
          dispatch(onSuccessfulAddCaseNotation(formData));
          dispatch(onFetchCaseLogs('Case', formData.caseRefNo));
          selectedCase.entityId &&
            dispatch(
              onFetchEntityLogs(selectedCase.entityId, selectedCase.channel, {
                pageNo: 1,
                pageRecords: 10,
                filterCondition: []
              })
            );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchNotationsList,
  onAddNotationToMaster,
  onUpdateNotationFromMaster,
  onDeleteNotationFromMaster,
  onFetchCaseNotationList,
  onAddCaseNotation
};
