import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import ruleCreationReducer from 'reducers/ruleCreationReducer';

describe('Rule Creation reducer', () => {
  it('should return the intial state', () => {
    expect(ruleCreationReducer(undefined, {})).toEqual(initialState.ruleCreation);
  });

  it('should handle ON_DSL_VERIFY_FAILURE', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_DSL_VERIFY_FAILURE,
          response: { message: 'Unable to verify DSL' }
        }
      )
    ).toEqual({
      validation: {
        message: 'Unable to verify DSL',
        status: false
      }
    });
  });

  it('should handle ON_CLEAR_DSL_VALIDATION', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_CLEAR_DSL_VALIDATION
        }
      )
    ).toEqual({
      validation: {
        message: '',
        status: false
      }
    });
  });

  it('should handle ON_SUCCESSFUL_DSL_VERIFY', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_DSL_VERIFY,
          response: { message: 'DSL string validated successfully' }
        }
      )
    ).toEqual({
      validation: {
        message: 'DSL string validated successfully',
        status: true
      }
    });
  });

  it('should handle ON_FETCH_DSL_HELPERS_LOADING', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_DSL_HELPERS_LOADING
        }
      )
    ).toEqual({
      channel: '',
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_DSL_HELPERS_FAILURE', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_DSL_HELPERS_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      channel: '',
      loader: false,
      error: true,
      errorMessage: 'Insufficient rights to access data'
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_DSL_HELPERS', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_DSL_HELPERS,
          response: responses.rules.components,
          channel: 'frm'
        }
      )
    ).toEqual({
      channel: 'frm',
      helperList: { frm: responses.rules.components },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_ACTION_LIST', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_ACTION_LIST,
          response: responses.rules.actionCode
        }
      )
    ).toEqual({
      actionList: responses.rules.actionCode
    });
  });

  it('should handle ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING
        }
      )
    ).toEqual({
      nonProductionRules: {
        loader: true
      }
    });
  });

  it('should handle ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS', () => {
    expect(
      ruleCreationReducer(
        {
          nonProductionRules: {
            list: {
              frm: [],
              str: []
            },
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS,
          response: responses.rules.approval,
          channel: 'frm'
        }
      )
    ).toEqual({
      nonProductionRules: {
        list: {
          frm: responses.rules.approval,
          str: []
        },
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE,
          response: { message: 'Curently unable to fetch non production rules' }
        }
      )
    ).toEqual({
      nonProductionRules: {
        loader: false,
        error: true,
        errorMessage: 'Curently unable to fetch non production rules'
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES', () => {
    const response = [
      {
        id: 1,
        categoryName: 'COMPLIANCE',
        desc: 'Description for category 1'
      },
      {
        id: 2,
        categoryName: 'FRAUD',
        desc: 'Description for category 2'
      },
      {
        id: 3,
        categoryName: 'ON HOLD',
        desc: 'Description for category 3'
      }
    ];
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES,
          response
        }
      )
    ).toEqual({
      alertCategories: response
    });
  });

  it('should handle ON_SUCCESSFUL_UPDATE_ACTION_LIST', () => {
    const response = [];
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_UPDATE_ACTION_LIST,
          response
        }
      )
    ).toEqual({
      actionList: response
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS', () => {
    const response = [];
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS,
          response
        }
      )
    ).toEqual({
      checkListOptions: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CHECKLIST', () => {
    const response = [];
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CHECKLIST,
          response
        }
      )
    ).toEqual({
      checkList: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CHECKLIST_OPTIONS_FAILURE', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_CHECKLIST_OPTIONS_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      checkListOptions: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_CHECKLIST_FAILURE', () => {
    expect(
      ruleCreationReducer(
        {},
        {
          type: types.ON_FETCH_CHECKLIST_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      checkList: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });
});
