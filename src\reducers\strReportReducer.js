import initialState from './initialState';
import {
  ON_FETCH_STR_REPORT_MASTERS_LOADING,
  ON_FETCH_STR_REPORT_MASTERS_SUCCESS,
  ON_FETCH_STR_REPORT_MASTERS_FAILURE,
  ON_FETCH_STR_REPORT_DETAILS_LOADING,
  ON_FETCH_STR_REPORT_DETAILS_SUCCESS,
  ON_FETCH_STR_REPORT_DETAILS_FAILURE,
  ON_FETCH_STR_REPORT_LOGS_LOADING,
  ON_FETCH_STR_REPORT_LOGS_SUCCESS,
  ON_FETCH_STR_REPORT_LOGS_FAILURE,
  ON_FETCH_STR_REPORTS_LIST_LOADING,
  ON_FETCH_STR_REPORTS_LIST_SUCCESS,
  ON_FETCH_STR_REPORTS_LIST_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';

export default function strReportReducer(state = initialState.strReport, action) {
  switch (action.type) {
    case ON_FETCH_STR_REPORT_MASTERS_LOADING:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, { loader: true, error: false, errorMessage: '' })
      });
    case ON_FETCH_STR_REPORT_MASTERS_SUCCESS:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, { data: action.response, loader: false })
      });
    case ON_FETCH_STR_REPORT_MASTERS_FAILURE:
      return objectAssign({}, state, {
        masters: objectAssign({}, state.masters, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_STR_REPORT_DETAILS_LOADING:
      return objectAssign({}, state, {
        details: { data: {}, loader: true, error: false, errorMessage: '' }
      });
    case ON_FETCH_STR_REPORT_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        details: objectAssign({}, state.details, { data: action.response, loader: false })
      });
    case ON_FETCH_STR_REPORT_DETAILS_FAILURE:
      return objectAssign({}, state, {
        details: objectAssign({}, state.details, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_STR_REPORT_LOGS_LOADING:
      return objectAssign({}, state, {
        history: { data: [], loader: true, error: false, errorMessage: '' }
      });
    case ON_FETCH_STR_REPORT_LOGS_SUCCESS:
      return objectAssign({}, state, {
        history: objectAssign({}, state.history, { data: action.response, loader: false })
      });
    case ON_FETCH_STR_REPORT_LOGS_FAILURE:
      return objectAssign({}, state, {
        history: objectAssign({}, state.history, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_STR_REPORTS_LIST_LOADING:
      return objectAssign({}, state, {
        allReports: { conf: {}, list: [], loader: true, error: false, errorMessage: '' }
      });
    case ON_FETCH_STR_REPORTS_LIST_SUCCESS:
      return objectAssign({}, state, {
        allReports: objectAssign({}, state.allReports, {
          conf: action.conf,
          list: action.response?.strHistoryListResp || [],
          loader: false
        })
      });
    case ON_FETCH_STR_REPORTS_LIST_FAILURE:
      return objectAssign({}, state, {
        allReports: objectAssign({}, state.allReports, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
