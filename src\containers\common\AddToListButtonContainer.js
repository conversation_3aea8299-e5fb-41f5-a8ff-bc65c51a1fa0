import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import AddToListButton from 'components/common/AddToListButton';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    prefiltersList: state.prefiltersList,
    role: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const AddToListButtonContainer = connect(mapStateToProps, mapDispatchToProps)(AddToListButton);

export default AddToListButtonContainer;
