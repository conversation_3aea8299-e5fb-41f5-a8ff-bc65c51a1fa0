import {
  ON_<PERSON>ET<PERSON>_<PERSON>A_KPI_LOADING,
  ON_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_KPI_SUCCESS,
  ON_<PERSON>ET<PERSON>_SLA_KPI_FAILURE,
  ON_FETCH_EMPLOYEE_SLA_LOADING,
  ON_FETCH_EMPLOYEE_SLA_SUCCESS,
  ON_FETCH_EMPLOYEE_SLA_FAILURE,
  ON_FETCH_OCCUPANCY_RATE_LOADING,
  ON_FETCH_OCCUPANCY_RATE_SUCCESS,
  ON_FETCH_OCCUPANCY_RATE_FAILURE,
  ON_<PERSON>ET<PERSON>_SLA_BREACH_CASES_LOADING,
  ON_<PERSON>ET<PERSON>_SLA_BREACH_CASES_SUCCESS,
  ON_FETCH_SLA_BREACH_CASES_FAILURE,
  ON_FETCH_FIRST_CONTACT_RATE_LOADING,
  ON_FETCH_FIRST_CONTACT_RATE_SUCCESS,
  ON_FETCH_FIRST_CONTACT_RATE_FAILURE,
  ON_FETCH_SHIFT_DETAILS_LOADING,
  ON_FETCH_SHIFT_DETAILS_SUCCESS,
  ON_FETCH_SHIFT_DETAILS_FAILURE,
  ON_FETCH_ANALYST_TAT_LOADING,
  ON_FETCH_ANALYST_TAT_SUCCESS,
  ON_FETCH_ANALYST_TAT_FAILURE,
  ON_FETCH_PARTNERS_CASE_STATS_LOADING,
  ON_FETCH_PARTNERS_CASE_STATS_SUCCESS,
  ON_FETCH_PARTNERS_CASE_STATS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchShiftDetails() {
  return client({ url: `useraccessmanagement/user/shift/active` });
}

function onFetchShiftDetailsLoading() {
  return { type: ON_FETCH_SHIFT_DETAILS_LOADING };
}

function onFetchShiftDetailsSuccess(response) {
  return { type: ON_FETCH_SHIFT_DETAILS_SUCCESS, response };
}

function onFetchShiftDetailsFailure(response) {
  return { type: ON_FETCH_SHIFT_DETAILS_FAILURE, response };
}

function onFetchShiftDetails() {
  return function (dispatch) {
    dispatch(onFetchShiftDetailsLoading());
    return fetchShiftDetails().then(
      (success) => dispatch(onFetchShiftDetailsSuccess(success)),
      (error) => dispatch(onFetchShiftDetailsFailure(error))
    );
  };
}

function fetchSlaKpis(channel, shiftDetails) {
  return client({
    method: 'POST',
    url: `casereview/case/supervisor/${channel}/count`,
    data: shiftDetails
  });
}

function onFetchSlaKpisLoading() {
  return { type: ON_FETCH_SLA_KPI_LOADING };
}

function onFetchSlaKpisSuccess(response) {
  return { type: ON_FETCH_SLA_KPI_SUCCESS, response };
}

function onFetchSlaKpisFailure(response) {
  return { type: ON_FETCH_SLA_KPI_FAILURE, response };
}

function onFetchSlaKpis(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchSlaKpisLoading());
    return fetchSlaKpis(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchSlaKpisSuccess(success)),
      (error) => dispatch(onFetchSlaKpisFailure(error))
    );
  };
}

function fetchEmployeesSla(channel, shiftDetails) {
  return client({
    method: 'POST',
    url: `casereview/case/supervisor/${channel}/analysts/sla`,
    data: shiftDetails
  });
}

function onFetchEmployeesSlaLoading() {
  return { type: ON_FETCH_EMPLOYEE_SLA_LOADING };
}

function onFetchEmployeesSlaSuccess(response) {
  return { type: ON_FETCH_EMPLOYEE_SLA_SUCCESS, response };
}

function onFetchEmployeesSlaFailure(response) {
  return { type: ON_FETCH_EMPLOYEE_SLA_FAILURE, response };
}

function onFetchEmployeesSla(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchEmployeesSlaLoading());
    return fetchEmployeesSla(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchEmployeesSlaSuccess(success)),
      (error) => dispatch(onFetchEmployeesSlaFailure(error))
    );
  };
}

function fetchOccupancyRate() {
  return client({ url: `useraccessmanagement/reviewer/occupancyrate` });
}

function onFetchOccupancyRateLoading() {
  return { type: ON_FETCH_OCCUPANCY_RATE_LOADING };
}

function onFetchOccupancyRateSuccess(response) {
  return { type: ON_FETCH_OCCUPANCY_RATE_SUCCESS, response };
}

function onFetchOccupancyRateFailure(response) {
  return { type: ON_FETCH_OCCUPANCY_RATE_FAILURE, response };
}

function onFetchOccupancyRate() {
  return function (dispatch) {
    dispatch(onFetchOccupancyRateLoading());
    return fetchOccupancyRate().then(
      (success) => dispatch(onFetchOccupancyRateSuccess(success)),
      (error) => dispatch(onFetchOccupancyRateFailure(error))
    );
  };
}

function fetchSlaBreachCases(channel, shiftDetails) {
  return client({
    method: 'POST',
    url: `casereview/case/supervisor/${channel}/sla/breaches`,
    data: shiftDetails
  });
}

function onFetchSlaBreachCasesLoading() {
  return { type: ON_FETCH_SLA_BREACH_CASES_LOADING };
}

function onFetchSlaBreachCasesSuccess(response) {
  return { type: ON_FETCH_SLA_BREACH_CASES_SUCCESS, response };
}

function onFetchSlaBreachCasesFailure(response) {
  return { type: ON_FETCH_SLA_BREACH_CASES_FAILURE, response };
}

function onFetchSlaBreachCases(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchSlaBreachCasesLoading());
    return fetchSlaBreachCases(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchSlaBreachCasesSuccess(success)),
      (error) => dispatch(onFetchSlaBreachCasesFailure(error))
    );
  };
}

function fetchFirstContactRate(channel, shiftDetails) {
  return client({
    method: 'POST',
    url: `casereview/case/supervisor/${channel}/resolution/rate`,
    data: shiftDetails
  });
}

function onFetchFirstContactRateLoading() {
  return { type: ON_FETCH_FIRST_CONTACT_RATE_LOADING };
}

function onFetchFirstContactRateSuccess(response) {
  return { type: ON_FETCH_FIRST_CONTACT_RATE_SUCCESS, response };
}

function onFetchFirstContactRateFailure(response) {
  return { type: ON_FETCH_FIRST_CONTACT_RATE_FAILURE, response };
}

function onFetchFirstContactRate(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchFirstContactRateLoading());
    return fetchFirstContactRate(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchFirstContactRateSuccess(success)),
      (error) => dispatch(onFetchFirstContactRateFailure(error))
    );
  };
}

function fetchAnalystTAT(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/supervisor/${channel}/analysts/sla/trends`,
    data: formData
  });
}

function onFetchAnalystTATLoading() {
  return { type: ON_FETCH_ANALYST_TAT_LOADING };
}

function onFetchAnalystTATSuccess(response) {
  return { type: ON_FETCH_ANALYST_TAT_SUCCESS, response };
}

function onFetchAnalystTATFailure(response) {
  return { type: ON_FETCH_ANALYST_TAT_FAILURE, response };
}

function onFetchAnalystTAT(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchAnalystTATLoading());
    return fetchAnalystTAT(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchAnalystTATSuccess(success)),
      (error) => dispatch(onFetchAnalystTATFailure(error))
    );
  };
}

function fetchPartnersCaseStats(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/${channel}/case/supervisor/partner/case/count`,
    data: formData
  });
}

function onFetchPartnersCaseStatsLoading() {
  return { type: ON_FETCH_PARTNERS_CASE_STATS_LOADING };
}

function onFetchPartnersCaseStatsSuccess(response) {
  return { type: ON_FETCH_PARTNERS_CASE_STATS_SUCCESS, response };
}

function onFetchPartnersCaseStatsFailure(response) {
  return { type: ON_FETCH_PARTNERS_CASE_STATS_FAILURE, response };
}

function onFetchPartnersCaseStats(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchPartnersCaseStatsLoading());
    return fetchPartnersCaseStats(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchPartnersCaseStatsSuccess(success)),
      (error) => dispatch(onFetchPartnersCaseStatsFailure(error))
    );
  };
}

export {
  onFetchSlaKpis,
  onFetchAnalystTAT,
  onFetchShiftDetails,
  onFetchEmployeesSla,
  onFetchOccupancyRate,
  onFetchSlaBreachCases,
  onFetchFirstContactRate,
  onFetchPartnersCaseStats
};
