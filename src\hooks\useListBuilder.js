import { faPlusCircle } from '@fortawesome/free-solid-svg-icons';
import React, { useMemo } from 'react';

import ListItem from 'components/common/ListItem';

// Memoized search function to avoid repeated toLowerCase calls
const createSearchFilter = (search) => {
  const lowerSearch = search.toLowerCase();
  return (item) => {
    const lowerName = item?.name?.toLowerCase() || '';
    const lowerDesc = item?.description?.toLowerCase() || '';
    return lowerName.includes(lowerSearch) || lowerDesc.includes(lowerSearch);
  };
};

const useListBuilder = (list, search, appendDSL) => {
  // Memoize the search filter to avoid recreating it on every render
  const searchFilter = useMemo(() => createSearchFilter(search), [search]);

  // Memoize the filtered list
  const filteredList = useMemo(() => {
    if (!Array.isArray(list)) return [];
    return search ? list.filter(searchFilter) : list;
  }, [list, search, searchFilter]);

  // Memoize the rendered components
  return useMemo(
    () =>
      filteredList.map((item, i) => (
        <ListItem
          key={item.id || item.name || i}
          icon={faPlusCircle}
          item={item}
          onClick={appendDSL}
        />
      )),
    [filteredList, appendDSL]
  );
};

export default useListBuilder;
