import { onShowF<PERSON>ure<PERSON><PERSON>t, onShowSuccess<PERSON>lert } from 'actions/alertActions';
import { onFetchDSLHelpers } from 'actions/ruleCreationActions';
import { onToggleLoader, onToggleDynamicCountersCreateModal } from 'actions/toggleActions';
import {
  ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING,
  ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS,
  ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE,
  ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING,
  ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS,
  ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE,
  ON_FETCH_ALL_ATTRIBUTES_LOADING,
  ON_FETCH_ALL_ATTRIBUTES_SUCCESS,
  ON_FETCH_ALL_ATTRIBUTES_FAILURE,
  ON_FETCH_SUB_ATTRIBUTES_LOADING,
  ON_<PERSON>ETCH_SUB_ATTRIBUTES_SUCCESS,
  ON_<PERSON><PERSON><PERSON>_SUB_ATTRIBUTES_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function createDynamicCounter(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/ruleengine/create/counter`,
    data: formData.counter,
    badRequestMessage: 'Unable to create dynamic counter. Please check input data.'
  });
}

function onCreateDynamicCounter(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return createDynamicCounter(formData)
      .then(
        (success) => {
          dispatch(onShowSuccessAlert({ message: success }));
          dispatch(onFetchDynamicCountersList(formData.channel));
          dispatch(onToggleDynamicCountersCreateModal());
          dispatch(onFetchDSLHelpers(formData.channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchDynamicCountersList(channel) {
  return client({ url: `${channel}/ruleengine/fetch/dynamic/counters` });
}

function onFetchDynamicCountersListLoading() {
  return { type: ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING };
}

function onSuccessfulFetchDynamicCountersList(response) {
  return {
    type: ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS,
    response
  };
}

function onFetchDynamicCountrsListFailure(response) {
  return {
    type: ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE,
    response
  };
}

function onFetchDynamicCountersList(channel) {
  return function (dispatch) {
    dispatch(onFetchDynamicCountersListLoading());
    return fetchDynamicCountersList(channel).then(
      (success) => dispatch(onSuccessfulFetchDynamicCountersList(success)),
      (error) => dispatch(onFetchDynamicCountrsListFailure(error))
    );
  };
}

function fetchConditionalAttributes(channel) {
  return client({
    url: `${channel}/ruleengine/fetch/conditions`
  });
}

function onFetchConditionalAttributesLoading() {
  return { type: ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING };
}

function onFetchConditionalAttributesSuccess(response) {
  return {
    type: ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS,
    response
  };
}

function onFetchConditionalAttributesFailure(response) {
  return {
    type: ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE,
    response
  };
}

function onFetchConditionalAttributes(channel) {
  return function (dispatch) {
    dispatch(onFetchConditionalAttributesLoading());
    return fetchConditionalAttributes(channel).then(
      (success) => dispatch(onFetchConditionalAttributesSuccess(success)),
      (error) => dispatch(onFetchConditionalAttributesFailure(error))
    );
  };
}

function fetchAllAttributes(channel) {
  return client({
    url: `${channel}/ruleengine/fetch/primary/attributes`
  });
}

function onFetchAllAttributesLoading() {
  return { type: ON_FETCH_ALL_ATTRIBUTES_LOADING };
}

function onFetchAllAttributesSuccess(response) {
  return {
    type: ON_FETCH_ALL_ATTRIBUTES_SUCCESS,
    response
  };
}

function onFetchAllAttributesFailure(response) {
  return {
    type: ON_FETCH_ALL_ATTRIBUTES_FAILURE,
    response
  };
}

function onFetchAllAttributes(channel) {
  return function (dispatch) {
    dispatch(onFetchAllAttributesLoading());
    return fetchAllAttributes(channel).then(
      (success) => dispatch(onFetchAllAttributesSuccess(success)),
      (error) => {
        dispatch(onFetchAllAttributesFailure(error));
        dispatch(onShowFailureAlert(error));
      }
    );
  };
}

function fetchSubAttributes(channel) {
  return client({ url: `${channel}/ruleengine/fetch/secondary/attributes` });
}

function onFetchSubAttributesLoading() {
  return { type: ON_FETCH_SUB_ATTRIBUTES_LOADING };
}

function onFetchSubAttributesSuccess(response) {
  return {
    type: ON_FETCH_SUB_ATTRIBUTES_SUCCESS,
    response
  };
}

function onFetchSubAttributesFailure(response) {
  return {
    type: ON_FETCH_SUB_ATTRIBUTES_FAILURE,
    response
  };
}

function onFetchSubAttributes(channel) {
  return function (dispatch) {
    dispatch(onFetchSubAttributesLoading());
    return fetchSubAttributes(channel).then(
      (success) => dispatch(onFetchSubAttributesSuccess(success)),
      (error) => dispatch(onFetchSubAttributesFailure(error))
    );
  };
}

function uploadDynamicCounters(formData) {
  const fileUpload = new FormData();
  fileUpload.append('file', formData?.file);
  return client({
    method: 'POST',
    url: `${formData.channel}/ruleengine/counter/upload`,
    multipart: true,
    headers: {
      processData: false,
      contentType: false
    },
    data: fileUpload
  });
}

function onUploadDynamicCounters(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return uploadDynamicCounters(formData)
      .then(
        () => {
          dispatch(onFetchDynamicCountersList(formData.channel));
          dispatch(onShowSuccessAlert({ message: 'Dynamic counters uploaded successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteCounter(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/delete/counter`,
    data: formData,
    badRequestMessage: 'Currently unable to delete counter.'
  });
}

function onDeleteCounter(formData, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteCounter(formData, channel)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Counter deleted successfully' }));
          dispatch(onFetchDynamicCountersList(channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchDynamicCountersList,
  onCreateDynamicCounter,
  onFetchConditionalAttributes,
  onFetchAllAttributes,
  onFetchSubAttributes,
  onUploadDynamicCounters,
  onDeleteCounter
};
