import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleFeedback } from 'actions/ruleDashboardActions';
import RuleFeedbackStats from 'components/dashboards/RuleFeedbackStats';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleFeedbacks: state.ruleDashboard.ruleFeedbacks
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleFeedback: bindActionCreators(onFetchRuleFeedback, dispatch)
  };
};

const RuleFeedbackStatsContainer = connect(mapStateToProps, mapDispatchToProps)(RuleFeedbackStats);

export default RuleFeedbackStatsContainer;
