'use strict';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, TabPane } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencilRuler } from '@fortawesome/free-solid-svg-icons';

import Tabs from 'components/common/Tabs';
import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import RuleForm from 'containers/ruleEngine/RuleFormContainer';
import RuleEngineConfigurator from 'components/ruleEngine/RuleEngineConfigurator';
import ProductionRuleTable from 'containers/ruleEngine/ProductionRuleTableContainer';
import SnoozeRuleTableContainer from 'containers/ruleEngine/SnoozeRuleTableContainer';
import RuleUploadButtonContainer from 'containers/ruleEngine/RuleUploadButtonContainer';
import NonProductionRuleTable from 'containers/ruleEngine/NonProductionRuleTableContainer';
import ArchievedRuleTableContainer from 'containers/ruleEngine/ArchievedRuleTableContainer';
import RuleDownloadExcel from './RuleDownloadExcel';
import { isCooperative } from 'constants/publicKey';

const RuleListTable = ({
  role,
  toggle,
  channel,
  ruleCreation,
  toggleActions,
  ruleCreationActions,
  fetchSnoozeRulesList
}) => {
  const [selectedRule, setSelectedRule] = useState({});
  const [editType, setEditType] = useState('');
  const [isSandbox, setIsSandbox] = useState(false);
  const [selectedProductionRules, setSelectedProductionRules] = useState([]);

  useEffect(() => {
    ruleCreationActions.onFetchActionList(channel);
    ruleCreationActions.onFetchAlertCategories(channel);

    ruleCreationActions.onFetchRuleChannelsList(channel);
    ruleCreationActions.onFetchRuleFraudCategoriesList(channel);
    ruleCreationActions.onFetchRuleLabels(channel);

    fetchSnoozeRulesList(channel);
  }, []);

  const toggleEditModal = (rule, type, openSandbox = false) => {
    const updatedRule = type == 'archieved' ? { ...rule, isDelete: 0 } : rule;
    if (!toggle.ruleEditModal[channel]) {
      setSelectedRule(updatedRule);
      setEditType(type);
      setIsSandbox(openSandbox);
    }
    ruleCreationActions.onClearValidation();
    toggleActions.onToggleRuleEditModal(channel);
  };

  const handleSubmit = (type) => (formData) => {
    ruleCreationActions.onCreateRule(formData, type);
  };

  const handleEditSubmit = (data) => {
    let formData = data;
    formData['editUrl'] =
      editType == 'nonProduction'
        ? `checker/${selectedRule.approvalId}/review`
        : `supervisor/${selectedRule.code}/update`;
    ruleCreationActions.onUpdateRule(formData);
  };

  let createAction = (
    <span className={`${_.isEmpty(selectedProductionRules) ? 'disable-btn' : ''}`}>
      {role === 'supervisor' && <RuleUploadButtonContainer channel={channel} />}
      <RuleDownloadExcel
        rulesList={selectedProductionRules}
        alertCategories={ruleCreation.alertCategories}
      />
      {_.includes(['checker', 'investigator'], role) ? (
        <Button
          color="primary"
          size="sm"
          title="Create rule"
          className="ms-1"
          onClick={() => toggleActions.onToggleRuleCreateModal(channel)}>
          <FontAwesomeIcon icon={faPencilRuler} className="me-1" /> Create new rule
        </Button>
      ) : (
        <RuleEngineConfigurator theme={toggle.theme} channel={channel} />
      )}
    </span>
  );

  let tabNames = ['Production Rules', 'Non-Production Rules', 'Archived Rules'];
  !isCooperative && channel !== 'str' && tabNames.push('Snooze Requests');

  return (
    <div>
      <CardContainer title={_.upperCase(channel) + ' Rule List'} action={createAction}>
        <Tabs tabNames={tabNames}>
          <TabPane tabId={0}>
            <ProductionRuleTable
              channel={channel}
              handleCreateSubmit={handleSubmit('duplicate')}
              toggleEditModal={toggleEditModal}
              ruleCreation={ruleCreation}
              updateSelectedProductionRules={setSelectedProductionRules}
            />
          </TabPane>
          <TabPane tabId={1}>
            <NonProductionRuleTable
              channel={channel}
              toggleEditModal={toggleEditModal}
              nonProductionRules={ruleCreation.nonProductionRules}
              ruleCreation={ruleCreation}
            />
          </TabPane>
          <TabPane tabId={2}>
            <ArchievedRuleTableContainer channel={channel} toggleEditModal={toggleEditModal} />
          </TabPane>
          <TabPane tabId={3}>
            <SnoozeRuleTableContainer channel={channel} />
          </TabPane>
        </Tabs>
      </CardContainer>

      <ModalContainer
        size="xl"
        theme={`${toggle.theme} rule-modal`}
        header={'Create Rule'}
        isOpen={toggle.ruleCreateModal[channel]}
        toggle={() => toggleActions.onToggleRuleCreateModal(channel)}>
        <RuleForm formName="create" channel={channel} submit={handleSubmit('create')} />
      </ModalContainer>

      <ModalContainer
        size="xl"
        header={isSandbox ? 'Sandbox Testing' : 'Edit Rule'}
        theme={`${toggle.theme} rule-modal`}
        isOpen={toggle.ruleEditModal[channel]}
        toggle={() => toggleEditModal()}>
        <RuleForm
          formName="edit"
          channel={channel}
          isSandbox={isSandbox}
          formData={selectedRule}
          submit={handleEditSubmit}
        />
      </ModalContainer>
    </div>
  );
};

RuleListTable.propTypes = {
  role: PropTypes.string.isRequired,
  toggle: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  ruleCreation: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired,
  fetchSnoozeRulesList: PropTypes.func.isRequired
};

export default RuleListTable;
