import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onToggleCreateCaseModal } from 'actions/toggleActions';
import CreateCaseModal from 'components/common/CreateCaseModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    display: state.toggle.createCaseModal
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggle: bindActionCreators(onToggleCreateCaseModal, dispatch)
  };
};

const CreateCaseModalContainer = connect(mapStateToProps, mapDispatchToProps)(CreateCaseModal);

export default CreateCaseModalContainer;
