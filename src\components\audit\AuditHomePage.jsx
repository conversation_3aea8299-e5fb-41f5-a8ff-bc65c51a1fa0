import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { TabPane } from 'reactstrap';
import { map, upperCase } from 'lodash';
import { useHistory } from 'react-router-dom';

import Tabs from 'components/common/Tabs';
import BucketCasesCardContainer from 'containers/common/BucketCasesCardContainer';

const AuditHomePage = ({ userRoles, channels }) => {
  const history = useHistory();
  useEffect(() => {
    if (userRoles != 'auditor') history.goBack();
    document.title = 'BANKiQ FRC | Supervise Cases';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const tabNames = map(channels, (tab) => upperCase(tab));

  const memoizedCasesTable = (channel) =>
    useMemo(() => <BucketCasesCardContainer userRole="auditor" channel={channel} />, [1]);

  return (
    <div className={'content-wrapper'}>
      {channels.length > 1 ? (
        <Tabs tabNames={tabNames} pills>
          {channels.map((channel, idx) => (
            <TabPane tabId={idx} key={channel}>
              {memoizedCasesTable(channel)}
            </TabPane>
          ))}
        </Tabs>
      ) : (
        memoizedCasesTable(channels[0])
      )}
    </div>
  );
};

AuditHomePage.propTypes = {
  userRoles: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired
};

export default AuditHomePage;
