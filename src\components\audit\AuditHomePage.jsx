import { map, upperCase } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import BucketCasesCardContainer from 'containers/common/BucketCasesCardContainer';

const AuditHomePage = ({ userRoles, channels }) => {
  const history = useHistory();
  useEffect(() => {
    if (userRoles !== 'auditor') history.goBack();
    document.title = 'BANKiQ FRC | Supervise Cases';

    return () => {
      document.title = 'BANKiQ FRC';
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const tabNames = map(channels, (tab) => upperCase(tab));

  return (
    <div className="content-wrapper">
      {channels.length > 1 ? (
        <Tabs tabNames={tabNames} pills>
          {channels.map((channel, idx) => (
            <TabPane tabId={idx} key={channel}>
              <BucketCasesCardContainer userRole="auditor" channel={channel} />
            </TabPane>
          ))}
        </Tabs>
      ) : (
        <BucketCasesCardContainer userRole="auditor" channel={channels[0]} />
      )}
    </div>
  );
};

AuditHomePage.propTypes = {
  userRoles: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired
};

export default AuditHomePage;
