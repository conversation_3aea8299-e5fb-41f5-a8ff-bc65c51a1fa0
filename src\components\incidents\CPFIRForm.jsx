/* eslint-disable no-useless-escape */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAsterisk } from '@fortawesome/free-solid-svg-icons';
import objectAssign from 'object-assign';

import ModalContainer from 'components/common/ModalContainer';
import { FormGroup, Label, Input, Row, Col, Button, FormFeedback, TabPane } from 'reactstrap';
import FormStepper from 'components/common/FormStepper';

const initialFormValue = {
  internalId: '',
  isReportedByCustomer: '',
  isAttemptedFraud: '',
  pmtInst: '',
  pmtSys: '',
  fraudSys: '',
  pmtChannel: '',
  fraudNature: '',
  dateOfOccRepByBank: '',
  dateOfDetByBank: '',
  dateOfEntry: '',
  custDateOfOccRep: '',
  custTimeOfOccRep: '',
  custRepDateToBank: '',
  custdateOfEntry: '',
  uniqueTxnRef: '',
  isDomestic: '',
  custRepName: '',
  custMobileNo: '',
  custEmail: '',
  custOtherDetails: '',
  isPGPAinvolved: 'N',
  pgName: '',
  isTPartyPspInvolved: 'N',
  tPartyPSPName: '',
  amtInvolvedInFraud: '',
  amtRecInFraud: '',
  isInsuranceAvailable: '',
  insurerNameTxnCoverage: '',
  amtRecDueToInsCover: '',
  benName: '',
  benMobile: '',
  benEmail: '',
  benAccNo: '',
  benBank: '',
  benBranchCode: '',
  benBranchIfsc: '',
  benPanNo: '',
  benCardNo: '',
  benPPICardNo: '',
  benUPIId: '',
  destPPIIssuerName: '',
  destMerchantId: '',
  destMerchantName: '',
  destPGPA: '',
  destATMId: '',
  suspWebsiteUsed: '',
  suspMobAppUsed: '',
  suspDeviceId: '',
  suspIPAddress: '',
  suspIMEI: '',
  suspGeoTag: '',
  suspOtherDetails: '',
  initialModOperandi: '',
  modOperandiUpd1: '',
  modOperandiUpd2: '',
  modOperandiUpd3: '',
  modOperandiUpd4: '',
  modOperandiUpd5: '',
  isFalseAlert: '',
  isRegWithLEA: '',
  detLeaCase: '',
  isFraudClosed: 'N',
  dateOfClosure: '',
  reasonFraudClosure: '',
  otherFraudInfo: '',
  preventiveSteps: ''
};

function CPFIRForm({
  theme,
  displayModal,
  incident,
  cpifrMaster,
  actions,
  toggleModal,
  allowClose = true
}) {
  const validationArray = Object.keys(initialFormValue).map((d) => [d, false]);
  const validationList = Object.fromEntries(validationArray);
  const [activeFormStep, setActiveFormStep] = useState(0);
  const [formData, setFormData] = useState(initialFormValue);
  const [formValidation, setFormValidation] = useState(validationList);
  const isFiled = incident?.data?.incidents?.length > 0 && incident?.data?.incidents[0]?.isFiled;
  const isClosed =
    incident?.data?.incidents?.length > 0 && incident?.data?.incidents[0]?.isFraudClosed === 'Y';

  useEffect(() => {
    if (!cpifrMaster?.list) actions.onFetchCPFIRMasters();
  }, []);

  useEffect(() => {
    if (!displayModal) {
      setFormData(initialFormValue);
      setActiveFormStep(0);
    } else {
      incident.data &&
        setFormData(() =>
          objectAssign({}, initialFormValue, {
            incidentId: incident.data.incidentId,
            ...(Object.hasOwn(incident?.data, 'incidents') && incident?.data?.incidents[0])
          })
        );
    }
  }, [displayModal, incident]);

  useEffect(() => {
    if (formData.isPGPAinvolved !== 'Y' && formData.pgName) {
      setFormData((prev) =>
        objectAssign({}, prev, {
          pgName: ''
        })
      );
      setFormValidation((prev) =>
        objectAssign({}, prev, {
          pgName: false
        })
      );
    }
    if (formData.isTPartyPspInvolved !== 'Y' && formData.tPartyPSPName) {
      setFormData((prev) =>
        objectAssign({}, prev, {
          tPartyPSPName: ''
        })
      );
      setFormValidation((prev) =>
        objectAssign({}, prev, {
          tPartyPSPName: false
        })
      );
    }
    if (
      formData.isInsuranceAvailable !== 'Y' &&
      (formData.insurerNameTxnCoverage || formData.amtRecDueToInsCover)
    ) {
      setFormData((prev) =>
        objectAssign({}, prev, {
          insurerNameTxnCoverage: '',
          amtRecDueToInsCover: ''
        })
      );
      setFormValidation((prev) =>
        objectAssign({}, prev, {
          insurerNameTxnCoverage: false,
          amtRecDueToInsCover: false
        })
      );
    }
    if (formData.isRegWithLEA !== 'Y' && formData.detLeaCase) {
      setFormData((prev) =>
        objectAssign({}, prev, {
          detLeaCase: ''
        })
      );
      setFormValidation((prev) =>
        objectAssign({}, prev, {
          detLeaCase: false
        })
      );
    }
    if (formData.isFraudClosed !== 'Y' && (formData.dateOfClosure || formData.reasonFraudClosure)) {
      setFormData((prev) =>
        objectAssign({}, prev, {
          dateOfClosure: '',
          reasonFraudClosure: ''
        })
      );
      setFormValidation((prev) =>
        objectAssign({}, prev, {
          dateOfClosure: false,
          reasonFraudClosure: false
        })
      );
    }
  }, [formData]);

  const disableFraudNext =
    !formData.pmtInst ||
    !formData.pmtChannel ||
    !formData.pmtSys ||
    !formData.fraudSys ||
    !formData.fraudNature;

  const disableReportNext =
    formData.isReportedByCustomer === 'N'
      ? !formData.dateOfOccRepByBank || !formData.dateOfDetByBank || !formData.dateOfEntry
      : !formData.custDateOfOccRep ||
        !formData.custTimeOfOccRep ||
        !formData.custRepDateToBank ||
        !formData.custdateOfEntry ||
        !formData.custRepName ||
        !formData.custMobileNo ||
        !formData.custEmail ||
        (formData.isPGPAinvolved === 'Y' && !formData.pgName) ||
        (formData.isTPartyPspInvolved === 'Y' && !formData.tPartyPSPName) ||
        (formData.isAttemptedFraud === 'Y' && !formData.amtInvolvedInFraud) ||
        (formData.isInsuranceAvailable === 'Y' &&
          (!formData.insurerNameTxnCoverage || formData.amtRecDueToInsCover));

  const disableInvestigationNext =
    formData.isFraudClosed === 'Y' && (!formData.dateOfClosure || !formData.reasonFraudClosure);

  const inputValidators = {
    alphaNumChar: {
      pattern: /^[A-Za-z0-9\-.,':;/\s]{0,100}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semicolon, forward slash and multi-spaces between them are allowed. Max-length: 100'
    },
    alphaNumExtraChar50: {
      pattern: /^[A-Za-z0-9\-.,':;/()&@#+\s]{0,50}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semi colon, forward slash, parentheses, ampersand, back slash, @ sign, hash, + and multiple spaces are allowed. Max-length: 50'
    },
    alphaNumExtraChar: {
      pattern: /^[A-Za-z0-9\-.,':;/()&@#+\s]{0,100}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semi colon, forward slash, parentheses, ampersand, back slash, @ sign, hash, + and multiple spaces are allowed. Max-length: 100'
    },
    amount: {
      pattern: /^[\d]{0,20}$/,
      title: 'Only numbers are allowed. Max-length: 20.'
    },
    alphaNumCharNospace: {
      pattern: /^[A-Za-z0-9\-.,':;\/#]{0,100}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semi colon, forward slash, hash and no spaces are allowed. Max-length: 100'
    },
    alphaNumChar100: {
      pattern: /^[A-Za-z0-9\-.,':;\/#\s]{0,100}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semi colon, forward slash, hash and multiple spaces between them are allowed. Max-length: 100'
    },
    alphaNumChar50: {
      pattern: /^[A-Za-z0-9\-.,':;\/#\s]{0,50}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, colon, semi colon, forward slash, hash and multiple spaces between them are allowed. Max-length: 50'
    },
    ipAddr: {
      pattern: /^[0-9.:]{0,50}$/,
      title: 'Only numbers, dot and colon are allowed. Max-length: 50'
    },
    alphaNum20: {
      pattern: /^[A-Za-z0-9]{0,20}$/,
      title: 'Only alphabets and numbers are allowed. Max-length: 20'
    },
    textarea2000: {
      pattern: /^[A-Za-z0-9\-.,'":&:;()\/$€£₹kr\n\s]{0,2000}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, double quotes, ampersand, colon, semicolon, parentheses, forward slash, dollar, euro, pound, rupee, krona, back slash, multiple spaces and line break are allowed. Max-length: 2000'
    },
    textarea500: {
      pattern: /^[A-Za-z0-9\-.,'":&:;()\/$€£₹kr\n\s]{0,500}$/,
      title:
        'Only alphabets, numbers, hyphen, dot, comma, single quote, double quotes, ampersand, colon, semicolon, parentheses, forward slash, dollar, euro, pound, rupee, krona, back slash, multiple spaces and line break are allowed. Max-length: 500'
    },
    alphaNumExtraChar100: {
      pattern: /^[A-Za-z0-9.()'&,\-\/\\_ ]{0,100}$/,
      title:
        'Only alphabets, numbers, dot, parentheses, single quote / apostrophe, ampersand, comma, hyphen, forward slash, back slash, underscore and multiple spaces are allowed. Max-length: 100'
    },
    mobile: {
      pattern: /^\+?\d{1,10}([- ]\d{1,5})*$/,
      title: 'Only numeric, single space, plus (1st char) and hyphen are allowed. Max-length: 15'
    },
    email: {
      pattern: /^([a-zA-Z0-9._%+-]{1,30}@)+([a-zA-Z0-9.-]{1,10}\.)+[a-zA-Z]{2,8}$/,
      title: 'Only valid Email Id are allowed. Max-length: 50'
    },
    alphaNum50: {
      pattern: /^[A-Za-z0-9]{0,50}$/,
      title: 'Only alphabets and numbers are allowed. Max-length: 50'
    },
    alphaNum11: {
      pattern: /^[A-Za-z0-9]{0,11}$/,
      title: 'Only alphabets and numbers are allowed. Max-length: 11'
    },
    alphaNum10: {
      pattern: /^[A-Za-z0-9]{0,10}$/,
      title: 'Only alphabets and numbers are allowed. Max-length: 10'
    },
    numeric16: {
      pattern: /^[0-9]{0,16}$/,
      title: 'Only numbers are allowed. Max-length: 16'
    },
    alphaNumPlus50: {
      pattern: /^[A-Za-z0-9+\s]{0,50}$/,
      title: 'Only numbers are allowed. Max-length: 16'
    }
  };

  const optionsYN = (
    <>
      <option value="">select</option>
      <option value="Y">Yes</option>
      <option value="N">No</option>
    </>
  );

  function generateOptions(list, item) {
    const options = list?.[item]?.map((d) => (
      <option key={d.code} value={d.code}>
        {d.name}
      </option>
    ));

    return (
      <>
        <option value="">-- SELECT --</option>
        {options}
      </>
    );
  }

  const formStructuredInput = ({
    id,
    label,
    type = 'text',
    required = false,
    disabled = false,
    additionalProps = {},
    children = null,
    validatorId = null
  }) => {
    return (
      <Col md="6" className="d-flex align-items-end">
        <FormGroup className="flex-grow-1">
          <Label for={id}>
            {label}
            {required && <FontAwesomeIcon icon={faAsterisk} className="required-star" />}
          </Label>
          {type === 'select' ? (
            <Input
              type="select"
              id={id}
              name={id}
              value={formData[id]}
              onChange={(e) =>
                setFormData((data) =>
                  objectAssign({}, data, {
                    [id]: e.target.value
                  })
                )
              }
              disabled={disabled}
              required={required}
              {...additionalProps}>
              {children}
            </Input>
          ) : (
            <Input
              type={type}
              id={id}
              name={id}
              value={
                type == 'date'
                  ? moment(formData[id], 'DDMMYYYY').format('YYYY-MM-DD')
                  : formData[id]
              }
              onChange={(e) => {
                setFormData((data) =>
                  objectAssign({}, data, {
                    [id]:
                      type == 'date' ? moment(e.target.value).format('DDMMYYYY') : e.target.value
                  })
                );
                !['date', 'time'].includes(type) && validateInput(validatorId, id, e.target.value);
              }}
              required={required}
              disabled={disabled}
              {...additionalProps}
              {...(formValidation[id] && { invalid: true })}
            />
          )}
          <FormFeedback>{inputValidators[validatorId]?.title || 'Invalid Input'}</FormFeedback>
        </FormGroup>
      </Col>
    );
  };

  const fraudNatureOptions = generateOptions(cpifrMaster?.data, 'fraudNature');
  const pmtChannelsOptions = generateOptions(cpifrMaster?.data, 'pmtChannels');
  const pmtInstrumentsOptions = generateOptions(cpifrMaster?.data, 'pmtInstruments');
  const pmtCategoriesOptions = generateOptions(cpifrMaster?.data, 'pmtCategories');
  const pmtSystemsOptions = generateOptions(
    cpifrMaster?.data?.pmtSystemResponses?.find((d) => d.categoryCode === formData?.pmtSys),
    'pmtSystems'
  );

  function validateInput(validatorId, field, value) {
    setFormValidation((data) =>
      objectAssign({}, data, {
        [field]:
          value == '' ? false : validatorId && !inputValidators[validatorId]?.pattern.test(value)
      })
    );
  }

  function saveCPFIRReport() {
    if (!Object.values(formValidation).includes(true)) {
      const data = incident.data.incidents?.map((d) =>
        objectAssign({}, d, { ...formData, uniqueTxnRef: d.uniqueTxnRef })
      );
      actions.onSaveCPFIRReport({
        incidentId: incident.data.incidentId,
        incidents: data,
        saveType: 'draft'
      });
    }
  }

  function submitCPFIRReport(e) {
    e.preventDefault();
    if (!Object.values(formValidation).includes(true)) {
      const data = incident.data.incidents.map((d) =>
        objectAssign({}, d, { ...formData, uniqueTxnRef: d.uniqueTxnRef })
      );
      actions.onSubmitCPFIRReport({
        incidentId: incident.data.incidentId,
        incidents: data,
        saveType: 'submit'
      });
    }
  }

  return (
    <ModalContainer
      header="File CPFIR Report"
      isOpen={displayModal}
      theme={theme}
      size="lg"
      toggle={toggleModal}
      allowClose={allowClose}>
      <form onSubmit={submitCPFIRReport}>
        <FormStepper
          steps={['Fraud', 'Reporting', 'Beneficiary', 'Suspicion', 'Investigation']}
          active={activeFormStep}>
          <TabPane tabId={0}>
            <Row>
              {formStructuredInput({ id: 'internalId', label: 'Internal Id', disabled: true })}
              {formStructuredInput({
                id: 'isReportedByCustomer',
                label: 'Was the fraud reported by customer ?',
                type: 'select',
                disabled: true,
                children: optionsYN
              })}
              {formStructuredInput({
                id: 'isAttemptedFraud',
                label: 'Was it an attempted fraud ?',
                type: 'select',
                disabled: true,
                children: optionsYN
              })}
              {formStructuredInput({
                type: 'select',
                id: 'pmtInst',
                label: 'Payment transaction instrument used',
                required: true,
                children: pmtInstrumentsOptions,
                disabled: isFiled
              })}
              {formStructuredInput({
                type: 'select',
                id: 'pmtSys',
                label: 'Payment system category',
                required: true,
                children: pmtCategoriesOptions,
                disabled: isFiled
              })}
              {formStructuredInput({
                type: 'select',
                id: 'fraudSys',
                label: 'System involved in the fraudulent transaction',
                required: true,
                children: pmtSystemsOptions,
                disabled: isFiled
              })}
              {formStructuredInput({
                type: 'select',
                id: 'pmtChannel',
                label: 'Payment channel used for fraudulent transaction',
                required: true,
                children: pmtChannelsOptions,
                disabled: isFiled
              })}
              {formStructuredInput({
                type: 'select',
                id: 'fraudNature',
                label: 'Nature of fraudulent transaction',
                required: true,
                children: fraudNatureOptions,
                disabled: isFiled
              })}
              <Col md="12">
                <FormGroup className="d-flex justify-content-end">
                  {!isClosed && (
                    <Button
                      color="success"
                      type="button"
                      size="sm"
                      className="me-2"
                      onClick={saveCPFIRReport}>
                      Save as draft
                    </Button>
                  )}
                  <Button
                    color="info"
                    type="button"
                    size="sm"
                    disabled={disableFraudNext}
                    onClick={() => setActiveFormStep(1)}>
                    Next
                  </Button>
                </FormGroup>
              </Col>
            </Row>
          </TabPane>
          <TabPane tabId={1}>
            <Row>
              {formData?.isReportedByCustomer === 'N' ? (
                <>
                  {formStructuredInput({
                    id: 'dateOfOccRepByBank',
                    label:
                      'Date of occurrence of the fraud as identified by the bank / non-bank entity',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'dateOfDetByBank',
                    label: 'Date of detection of the fraud by bank / non- bank entity',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'dateOfEntry',
                    label: 'Date of entering in the system',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                </>
              ) : (
                <>
                  {formStructuredInput({
                    id: 'custDateOfOccRep',
                    label: 'Date of occurrence of the fraud transaction reported by customer',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'custTimeOfOccRep',
                    label: 'Time of occurrence of the fraud transaction reported by customer',
                    type: 'time',
                    required: true,
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'custRepDateToBank',
                    label: 'Reporting date of fraud by the customer to bank / PPI issuer / PSO',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'custdateOfEntry',
                    label:
                      'Date of entering the fraud by the bank / PPI issuer / PSO in the system',
                    type: 'date',
                    required: true,
                    disabled: isFiled
                  })}
                </>
              )}
              {formStructuredInput({
                id: 'isDomestic',
                label: 'Is the fraud a domestic transaction?',
                type: 'select',
                required: true,
                disabled: true,
                children: optionsYN
              })}
              {formStructuredInput({
                id: 'custRepName',
                label: 'Reporting customer name',
                required: formData.isReportedByCustomer === 'Y',
                validatorId: 'alphaNumExtraChar100',
                disabled: isFiled
              })}
              {formStructuredInput({
                id: 'custMobileNo',
                label: 'Reporting customer mobile no.',
                required: formData.isReportedByCustomer === 'Y',
                validatorId: 'mobile',
                disabled: isFiled
              })}
              {formStructuredInput({
                id: 'custEmail',
                label: 'Reporting customer e-mail',
                validatorId: 'email',
                disabled: isClosed,
                additionalProps: {
                  maxLength: 50
                }
              })}
              {formStructuredInput({
                id: 'custOtherDetails',
                label: 'Any other detail of the reporting customer',
                disabled: isClosed,
                validatorId: 'alphaNumChar'
              })}
              {formStructuredInput({
                id: 'isPGPAinvolved',
                label: 'Was any PA / PG Involved ?',
                type: 'select',
                required: true,
                children: optionsYN,
                disabled: isFiled
              })}
              {formData.isPGPAinvolved === 'Y' &&
                formStructuredInput({
                  id: 'pgName',
                  label: 'If PA / PG involved, the name may be provided',
                  required: true,
                  validatorId: 'alphaNumExtraChar',
                  disabled: isFiled
                })}
              {formStructuredInput({
                id: 'isTPartyPspInvolved',
                label: 'Was any third party PSP involved ?',
                type: 'select',
                required: true,
                children: optionsYN,
                disabled: isFiled
              })}
              {formData.isTPartyPspInvolved === 'Y' &&
                formStructuredInput({
                  id: 'tPartyPSPName',
                  label: 'If third party PSP involved, the name may be provided',
                  required: true,
                  validatorId: 'alphaNumExtraChar',
                  disabled: isFiled
                })}
              {formStructuredInput({
                id: 'amtInvolvedInFraud',
                label: 'Amount involved (INR actuals) in the fraudulent transaction',
                required: formData.isAttemptedFraud == 'N',
                validatorId: 'amount',
                disabled: isFiled
              })}
              {formStructuredInput({
                id: 'amtRecInFraud',
                label: 'Amount recovered (INR actuals) in the fraudulent transaction',
                disabled: isClosed,
                validatorId: 'amount'
              })}
              {formStructuredInput({
                id: 'isInsuranceAvailable',
                label: 'Was insurance coverage available?',
                type: 'select',
                disabled: isClosed,
                children: optionsYN
              })}
              {formData.isInsuranceAvailable === 'Y' && (
                <>
                  {formStructuredInput({
                    id: 'insurerNameTxnCoverage',
                    label: 'Name of insurer and per transaction coverage amount',
                    type: 'textarea',
                    required: true,
                    validatorId: 'textarea2000',
                    disabled: isFiled
                  })}
                  {formStructuredInput({
                    id: 'amtRecDueToInsCover',
                    label: 'Amount recovered due to Insurance cover',
                    required: true,
                    validatorId: 'amount',
                    disabled: isFiled
                  })}
                </>
              )}
              <Col md="12">
                <FormGroup className="d-flex justify-content-between">
                  <Button
                    color="warning"
                    type="button"
                    size="sm"
                    onClick={() => setActiveFormStep(0)}>
                    Previous
                  </Button>
                  <div>
                    {!isClosed && (
                      <Button
                        color="success"
                        type="button"
                        size="sm"
                        className="me-2"
                        onClick={saveCPFIRReport}>
                        Save as draft
                      </Button>
                    )}
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      disabled={disableReportNext}
                      onClick={() => setActiveFormStep(2)}>
                      Next
                    </Button>
                  </div>
                </FormGroup>
              </Col>
            </Row>
          </TabPane>
          <TabPane tabId={2}>
            <Row>
              {formStructuredInput({
                id: 'benName',
                label: 'Beneficiary Name',
                disabled: isClosed,
                validatorId: 'alphaNumExtraChar100'
              })}
              {formStructuredInput({
                id: 'benMobile',
                label: 'Beneficiary Mobile',
                disabled: isClosed,
                validatorId: 'mobile'
              })}
              {formStructuredInput({
                id: 'benEmail',
                label: 'Beneficiary Email',
                disabled: isClosed,
                validatorId: 'email',
                additionalProps: {
                  maxLength: 50
                }
              })}
              {formStructuredInput({
                id: 'benAccNo',
                label: 'Beneficiary Account Number',
                disabled: isClosed,
                validatorId: 'alphaNum50'
              })}
              {formStructuredInput({
                id: 'benBank',
                label: 'Beneficiary Bank',
                disabled: isClosed,
                additionalProps: {
                  maxLength: 7
                }
              })}
              {formStructuredInput({
                id: 'benBranchCode',
                label: 'Beneficiary Branch',
                disabled: isClosed,
                additionalProps: {
                  maxLength: 7
                }
              })}
              {formStructuredInput({
                id: 'benBranchIfsc',
                label: 'Beneficiary Branch IFSC',
                disabled: isClosed,
                validatorId: 'alphaNum11'
              })}
              {formStructuredInput({
                id: 'benPanNo',
                label: 'Beneficiary PAN Card',
                disabled: isClosed,
                validatorId: 'alphaNum10'
              })}
              {formStructuredInput({
                id: 'benCardNo',
                label: 'Beneficiary debit/credit card no',
                disabled: isClosed,
                validatorId: 'numeric16'
              })}
              {formStructuredInput({
                id: 'benPPICardNo',
                label: 'Beneficiary PPI card/wallet no',
                disabled: isClosed,
                validatorId: 'alphaNumPlus50'
              })}
              {formStructuredInput({
                id: 'destPPIIssuerName',
                label: 'Name of Destination PPI Issuer',
                disabled: isClosed,
                validatorId: 'alphaNumExtraChar'
              })}
              {formStructuredInput({
                id: 'destMerchantId',
                label: 'Destination merchant ID',
                disabled: isClosed,
                validatorId: 'alphaNumExtraChar50'
              })}
              {formStructuredInput({
                id: 'destMerchantName',
                label: 'Destination merchant Name',
                disabled: isClosed,
                validatorId: 'alphaNumExtraChar'
              })}
              {formStructuredInput({
                id: 'destPGPA',
                label: 'Destination payment gateway/aggregator',
                disabled: isClosed,
                validatorId: 'alphaNumExtraChar50'
              })}
              {formStructuredInput({
                id: 'destATMId',
                label: 'Destination ATM ID',
                disabled: isClosed,
                validatorId: 'alphaNum50'
              })}
              <Col md="12">
                <FormGroup className="d-flex justify-content-between">
                  <Button
                    color="warning"
                    type="button"
                    size="sm"
                    onClick={() => setActiveFormStep(1)}>
                    Previous
                  </Button>
                  <div>
                    {!isClosed && (
                      <Button
                        color="success"
                        type="button"
                        size="sm"
                        className="me-2"
                        onClick={saveCPFIRReport}>
                        Save as draft
                      </Button>
                    )}
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      onClick={() => setActiveFormStep(3)}>
                      Next
                    </Button>
                  </div>
                </FormGroup>
              </Col>
            </Row>
          </TabPane>
          <TabPane tabId={3}>
            <Row>
              {formStructuredInput({
                id: 'suspWebsiteUsed',
                label: 'Suspect website used',
                disabled: isClosed,
                validatorId: 'alphaNumCharNospace'
              })}
              {formStructuredInput({
                id: 'suspMobAppUsed',
                label: 'Suspect mobile app used',
                disabled: isClosed,
                validatorId: 'alphaNumChar100'
              })}
              {formStructuredInput({
                id: 'suspDeviceId',
                label: 'Suspect device ID',
                disabled: isClosed,
                validatorId: 'alphaNumChar50'
              })}
              {formStructuredInput({
                id: 'suspIPAddress',
                label: 'Suspect IP Address',
                disabled: isClosed,
                validatorId: 'ipAddr'
              })}
              {formStructuredInput({
                id: 'suspIMEI',
                label: 'Suspect IMEI number',
                disabled: isClosed,
                validatorId: 'alphaNum20'
              })}
              {formStructuredInput({
                id: 'suspGeoTag',
                label: 'Suspect geotag ID',
                disabled: isClosed,
                validatorId: 'alphaNumChar50'
              })}
              {formStructuredInput({
                id: 'suspOtherDetails',
                label: 'Any other details of suspect',
                disabled: isClosed,
                validatorId: 'alphaNumChar50'
              })}
              {formStructuredInput({
                id: 'initialModOperandi',
                label: 'Initial inputs on modus operandi of fraud',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'modOperandiUpd1',
                label: 'Modus Operandi - update 1',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'modOperandiUpd2',
                label: 'Modus Operandi - update 2',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'modOperandiUpd3',
                label: 'Modus Operandi - update 3',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'modOperandiUpd4',
                label: 'Modus Operandi - update 4',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'modOperandiUpd5',
                label: 'Modus Operandi - update 5',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'isFalseAlert',
                label: 'False alert - transaction was not a fraud',
                type: 'select',
                disabled: isClosed,
                children: optionsYN
              })}
              <Col md="12">
                <FormGroup className="d-flex justify-content-between">
                  <Button
                    color="warning"
                    type="button"
                    size="sm"
                    onClick={() => setActiveFormStep(2)}>
                    Previous
                  </Button>
                  <div>
                    {!isClosed && (
                      <Button
                        color="success"
                        type="button"
                        size="sm"
                        className="me-2"
                        onClick={saveCPFIRReport}>
                        Save as draft
                      </Button>
                    )}
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      onClick={() => setActiveFormStep(4)}>
                      Next
                    </Button>
                  </div>
                </FormGroup>
              </Col>
            </Row>
          </TabPane>
          <TabPane tabId={4}>
            <Row>
              {formStructuredInput({
                id: 'isRegWithLEA',
                label: 'Fraud was registered with Law Enforcement Agencies (LEA) / sub-judice',
                type: 'select',
                disabled: isClosed,
                children: optionsYN
              })}
              {formStructuredInput({
                id: 'detLeaCase',
                label: 'If fraud was registered with LEA, details of case reported',
                validatorId: 'textarea500',
                disabled: isClosed
              })}
              {isFiled && (
                <>
                  {formStructuredInput({
                    id: 'isFraudClosed',
                    label: 'Has the fraud incident been closed?',
                    type: 'select',
                    required: true,
                    children: optionsYN,
                    disabled: isClosed
                  })}
                  {formData.isFraudClosed === 'Y' && (
                    <>
                      {formStructuredInput({
                        id: 'dateOfClosure',
                        label: 'Date of closure of fraud',
                        type: 'date',
                        required: formData.isFraudClosed === 'Y',
                        disabled: isClosed
                      })}
                      {formStructuredInput({
                        id: 'reasonFraudClosure',
                        label: 'Justification for closure of fraud',
                        type: 'textarea',
                        required: formData.isFraudClosed === 'Y',
                        validatorId: 'textarea2000',
                        disabled: isClosed
                      })}
                    </>
                  )}
                </>
              )}
              {formStructuredInput({
                id: 'otherFraudInfo',
                label: 'Any other information pertaining to the fraud',
                type: 'textarea',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              {formStructuredInput({
                id: 'preventiveSteps',
                type: 'textarea',
                label: 'Steps taken to address /prevent such frauds in future',
                validatorId: 'textarea2000',
                disabled: isClosed
              })}
              <FormGroup className="d-flex justify-content-between">
                <Button
                  color="warning"
                  type="button"
                  size="sm"
                  onClick={() => setActiveFormStep(3)}>
                  Previous
                </Button>
                {!isClosed && (
                  <div>
                    <Button
                      color="success"
                      type="button"
                      size="sm"
                      className="me-2"
                      onClick={saveCPFIRReport}>
                      Save as draft
                    </Button>
                    <Button
                      size="sm"
                      color="primary"
                      disabled={
                        Object.values(formValidation).includes(true) ||
                        disableFraudNext ||
                        disableReportNext ||
                        disableInvestigationNext
                      }>
                      Submit
                    </Button>
                  </div>
                )}
              </FormGroup>
            </Row>
          </TabPane>
        </FormStepper>
      </form>
    </ModalContainer>
  );
}

CPFIRForm.propTypes = {
  allowClose: PropTypes.bool,
  theme: PropTypes.string.isRequired,
  displayModal: PropTypes.bool.isRequired,
  actions: PropTypes.object.isRequired,
  incident: PropTypes.object.isRequired,
  cpifrMaster: PropTypes.object.isRequired,
  toggleModal: PropTypes.func.isRequired
};

export default CPFIRForm;
