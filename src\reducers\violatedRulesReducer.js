import {
  ON_VIOLATED_RULES_FETCH_LOADING,
  ON_SUCCESSFUL_VIOLATED_RULES_FETCH,
  ON_FAILURE_VIOLATED_RULES_FETCH,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE
} from 'constants/actionTypes';
import initialState from './initialState';

export default function violatedRulesReducer(state = initialState.violatedRules, action) {
  switch (action.type) {
    case ON_VIOLATED_RULES_FETCH_LOADING:
      return {
        list: [],
        loader: true,
        error: false,
        errorMessage: '',
        transactionLoader: false
      };
    case ON_SUCCESSFUL_VIOLATED_RULES_FETCH:
      return {
        list: action.response.list,
        loader: false,
        error: false,
        errorMessage: '',
        transactionLoader: false
      };
    case ON_FAILURE_VIOLATED_RULES_FETCH:
      return {
        list: [],
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error',
        transactionLoader: false
      };
    case ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING:
      return {
        ...state,
        transactionLoader: true
      };
    case ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS:
      return {
        ...state,
        transactionLoader: false,
        list: {
          rules: state.list.rules.map((d) =>
            d.code === action.ruleCode ? { ...d, transactions: action.response.transactions } : d
          )
        }
      };
    case ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE:
      return {
        ...state,
        transactionLoader: false,
        list: {
          rules: state.list.rules.map((d) =>
            d.code === action.ruleCode ? { ...d, transactions: [] } : d
          )
        }
      };
    default:
      return state;
  }
}
