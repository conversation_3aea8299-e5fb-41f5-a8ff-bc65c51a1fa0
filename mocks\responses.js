export default {
  auth: {
    userRoles: ['super-admin', 'admin', 'supervisor', 'maker', 'checker'],
    login: {
      id: 1,
      userName: 'test',
      email: 'test',
      channelRoles: ['frm:checker'],
      moduleType: 'issuer'
    },
    allRoles: [
      { id: '0', name: 'super-admin', channel: [] },
      { id: '1', name: 'checker', channel: ['str', 'frm'] },
      { id: '2', name: 'supervisor', channel: ['str', 'frm'] },
      { id: '3', name: 'admin', channel: [] },
      { id: '5', name: 'reviewer', channel: ['frm'] },
      { id: '6', name: 'investigator', channel: ['frm'] },
      { id: '8', name: 'principal-officer', channel: ['str'] }
    ]
  },
  uam: {
    users: [
      {
        id: 13,
        partnerId: 1,
        partnerName: 'Acquirer Bank',
        userName: 'akashs',
        email: '<EMAIL>',
        firstName: 'akash',
        lastName: 'patil',
        channelRoles: ['str:supervisor'],
        shiftNames: []
      },
      {
        id: 18,
        partnerId: 1,
        partnerName: 'Acquirer Bank',
        userName: 'democ',
        email: '<EMAIL>',
        firstName: 'Rahul',
        lastName: 'Dravid',
        channelRoles: ['str:checker'],
        shiftNames: ['Full Shift']
      }
    ],
    roles: [
      { id: '0', name: 'super-admin', channel: [] },
      { id: '1', name: 'checker', channel: ['str', 'frm'] },
      { id: '2', name: 'supervisor', channel: ['str', 'frm'] },
      { id: '3', name: 'admin', channel: [] },
      { id: '5', name: 'reviewer', channel: ['frm'] },
      { id: '6', name: 'investigator', channel: ['frm'] },
      { id: '8', name: 'principal-officer', channel: ['str'] }
    ],
    channels: [
      { id: '1', name: 'frm' },
      { id: '2', name: 'str' }
    ],
    shifts: [
      { id: 1, shiftName: 'Morning', fromTime: '07:00:00', toTime: '16:00:00' },
      { id: 2, shiftName: 'Afternoon', fromTime: '13:00:00', toTime: '21:00:00' }
    ],
    externalChecker: [
      {
        id: 75,
        partnerId: 2,
        userName: 'user1',
        email: '<EMAIL>',
        firstName: 'user',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      }
    ],
    caseCriteriaAttributes: [
      {
        key: 'txnAmount',
        value: 'txnAmount',
        dataType: 'numeric'
      },
      {
        key: 'payerId',
        value: 'payerId',
        dataType: 'text'
      },
      {
        key: 'payeeId',
        value: 'payeeId',
        dataType: 'text'
      },
      {
        key: 'txnType',
        value: 'txnType',
        dataType: 'text'
      },
      {
        key: 'xchannelId',
        value: 'xchannelId',
        dataType: 'text'
      }
    ]
  },
  demographics: {
    customerInfo: {
      customer: {
        customerId: 'merchant1',
        accountNumber: '123456',
        accountOpeningDate: '2006-03-15 05:30:00',
        dob: '2006-03-15 05:30:00',
        gender: 'male',
        name: 'ramesh',
        branchName: 'Fort, South Mumbai',
        mobileNo: '**********',
        constDesc: 'Individual',
        district: 'Mumbai',
        state: 'Maharashtra'
      },
      activity: 'ACTIVE',
      totalTxns: 4,
      totalAmount: 2500
    },
    customerAllAccounts: {
      cif: {
        value: 'cif123',
        categoryName: 'Customer ID',
        listTypeData: [
          'Black',
          'Watch',
          'White',
          'Blocked',
          'Proxy',
          'Special',
          'TorNode',
          'Suspicious'
        ]
      },
      createdOn: '2020-04-01T14:33:12.55207',
      name: 'leo',
      dob: '1998-06-02T14:33:33.667709',
      gender: 'F',
      mobileNo: {
        value: '**********',
        categoryName: 'Mobile Number',
        listTypeData: []
      },
      email: '<EMAIL>',
      isMarried: 1,
      occupation: 'IT Engg',
      incomeRange: '6L',
      isStaff: 1,
      category: 'category',
      pan: 'DHTF354678',
      aadhar: '************',
      updatedOn: '2021-03-09T14:37:49.085761',
      loopType: 'loop',
      address: {
        address1: 'Hinjewadi',
        address2: 'Baner',
        address3: 'Hadapsar',
        city: 'Pune',
        state: 'MH',
        country: 'IND',
        pin: '123456',
        lat: '23.5',
        lon: '56.8',
        workAddress: 'Hadapsar',
        workCity: 'Pune',
        workState: 'MH',
        workCountry: 'IND',
        workPin: '123456',
        workLat: '56.7',
        workLon: '65.6',
        isUrbanRural: 1
      },
      account: [
        {
          accountNo: {
            value: '***********',
            categoryName: 'Account Number',
            listTypeData: []
          },
          accountOpeningDate: '2021-06-24T14:32:17.006137',
          accountType: 'savings',
          branch: 'B11',
          ifscCode: {
            value: '12348',
            categoryName: 'Bank Ifsc',
            listTypeData: []
          }
        },
        {
          accountNo: {
            value: '***********',
            categoryName: 'Account Number',
            listTypeData: []
          },
          accountOpeningDate: '2021-06-24T14:32:17.006137',
          accountType: 'savings',
          branch: 'B12',
          ifscCode: {
            value: '12348',
            categoryName: 'Bank Ifsc',
            listTypeData: []
          }
        },
        {
          accountNo: {
            value: '***********',
            categoryName: 'Account Number',
            listTypeData: []
          },
          accountOpeningDate: '2021-06-24T14:32:17.006137',
          accountType: 'savings',
          isDormant: 1,
          branch: 'B7',
          ifscCode: {
            value: '12348',
            categoryName: 'Bank Ifsc',
            listTypeData: []
          },
          nominee: 'abc',
          nomineeRelation: 'mother',
          kycType: 'kyc'
        },
        {
          accountNo: {
            value: '***********',
            categoryName: 'Account Number',
            listTypeData: []
          },
          accountOpeningDate: '2021-06-24T14:32:17.006137',
          accountType: 'savings',
          isDormant: 1,
          branch: 'B7',
          ifscCode: {
            value: '12348',
            categoryName: 'Bank Ifsc',
            listTypeData: []
          },
          nominee: 'abc',
          nomineeRelation: 'mother',
          kycType: 'kyc'
        }
      ],
      customerTxnCounterResponse: {
        txnCount: 29,
        txnAmount: 287450,
        fraudCount: 27,
        fraudAmount: 197000,
        averageTransaction: 9912,
        averageFraud: 7296
      }
    },
    customerRiskScore: 'high',
    merchant: {
      merchantKey: 1,
      id: {
        value: 'merchant00101',
        categoryName: 'Merchant ID',
        listTypeData: []
      },
      mccData: {
        mcc: {
          value: '4011',
          categoryName: 'Mcc',
          listTypeData: []
        },
        mccName: '4011, others'
      },
      businessType: 'B',
      location: 'Kharadi',
      lat: {
        value: '19.0760',
        categoryName: 'Latitude',
        listTypeData: []
      },
      lng: {
        value: '72.8777',
        categoryName: 'Longitude',
        listTypeData: []
      },
      channelCode: '0',
      channelName: 'iuca',
      settlementType: 'isolated',
      account: {
        accountNo: {
          categoryName: 'Account Number',
          listTypeData: []
        },
        ifscCode: {
          categoryName: 'Bank Ifsc',
          listTypeData: []
        },
        branch:
          'NARIMAN POINT (M.M.O) - MUMBAI,239, UNION BANK BHAVAN, VIDHAN BHAVAN MARG, NARIMAN POINT,MUMBAI - 400021',
        accountOpeningDate: '2020-06-09T12:06:12'
      },
      address: {
        address1: 'Nagar-Pune Highway',
        address2: 'Radison Blu',
        address3: 'Kharadi',
        address4: 'Pune',
        city: 'Pune',
        provStateAbbrev: '27',
        postalZip: '411013'
      },
      personalDetails: {
        score: '4100',
        kycType: 'EKYC',
        pan: '**********',
        aadhar: '************',
        merchantName: 'Osama Bin Laden',
        risk: 'low',
        expressMAF: 'E'
      },
      isOnHold: 0,
      isActive: 1,
      createdAt: '2020-06-09T12:06:12',
      activationDate: '2020-06-09T12:06:12',
      lastUpdatedOn: '2020-06-09T12:06:12',
      loopType: 'tloop',
      onboardingType: 'Online',
      merchantTxnCounterResponse: {
        txnCount: 1268,
        txnAmount: '*********',
        fraudCount: 6,
        fraudAmount: '30000',
        averageTransaction: '431966.19',
        averageFraud: '5000'
      }
    }
  },
  caseAssignment: {
    stages: [
      { id: 1, roleName: 'Maker' },
      { id: 2, roleName: 'Checker' }
    ],
    liability: [{ id: 1, liabilityType: 'NotApplicable' }],
    fraudTypes: [
      {
        id: 2,
        verdict: 'Confirmed Fraud',
        types: [
          { id: 8, fraudName: 'NRI NRC' },
          { id: 7, fraudName: 'Account Takeover' },
          { id: 6, fraudName: 'Internet MOTO' },
          { id: 5, fraudName: 'Application Fraud' },
          { id: 4, fraudName: 'Identity Theft' },
          { id: 3, fraudName: 'Lost Stolen' },
          { id: 2, fraudName: 'Skimming Counterfeit' },
          { id: 1, fraudName: 'Unknown' }
        ]
      },
      {
        id: 1,
        verdict: 'Undetermined',
        types: [{ id: 9, fraudName: 'Not Applicable' }]
      },
      {
        id: 3,
        verdict: 'Confirmed Genuine',
        types: [{ id: 9, fraudName: 'Not Applicable' }]
      },
      {
        id: 4,
        verdict: 'Assumed Genuine',
        types: [{ id: 9, fraudName: 'Not Applicable' }]
      }
    ],
    closeBuckets: [
      { id: 1, name: 'Fraud' },
      { id: 2, name: 'NonFraud' },
      { id: 3, name: 'Suspicious' }
    ],
    verdictBucket: [
      {
        id: 3,
        verdict: 'Confirmed Genuine',
        buckets: [{ id: 2, name: 'NonFraud' }]
      },
      {
        id: 0,
        verdict: 'Others',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 1,
        verdict: 'Undetermined',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 4,
        verdict: 'Assumed Genuine',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 2,
        verdict: 'Confirmed Fraud',
        buckets: [{ id: 1, name: 'Fraud' }]
      }
    ],
    buckets: { openCases: 1, rejectedCases: 2, closedCases: 3, pendingCases: 4, releaseFunds: 4 },
    cases: {
      records: [
        {
          isOnUs: '',
          payeeMmid: '',
          payeeAccountType: 'NA',
          payerMcc: 'NA',
          txnCurrency: '356',
          payeeType: '',
          payeeVpa: '',
          payerAccountTypeName: 'NA, NA',
          txnType: '00',
          receivedAt: '2023-10-16T11:04:06.842',
          deviceId: '',
          payeeMccCodeName: '4722, Travel Industry',
          merchantName: 'cust13',
          responseCode: '00',
          createdAt: '2023-10-16T11:04:06.266351',
          txnTypeName: '00, Purchase',
          isLien: '',
          entityCategory: 'Customer',
          payeeId: 'merchant15',
          acquirerId: '8000',
          payeeDeviceOf: '',
          longitude: 0,
          payerMmidMobileNumber: '',
          isMerchantApiBased: '',
          mti: '200',
          payeeCardNumberMask: '',
          reViolatedRules: ['d6eae84d-8f3e-458e-bc23-0701797f66a7'],
          payeeMobileNumber: '',
          responseCodeName: '00',
          initiatorId: '',
          sourceInstitutionId: '00006',
          payerVpa: '',
          txnTimestamp: '2023-10-16T20:00:07',
          payerCardNumberHash: '****************',
          ifrmVerdict: 'REJECTED',
          initiatorMobile: '',
          paymentMethod: 'DEBIT',
          referenceTxnId: '',
          txnId: 'issuerTxn20231016207',
          initiatorType: '',
          txnCategoryId: '',
          agentId: '',
          cognitiveResponse: '{"cognitiveIsSuspicious":0}',
          deviceOs: '',
          latitude: 0,
          isCognitiveActive: 0,
          payeeCardNumberHash: '',
          payerId: 'cust4, cust13',
          terminalId: '1189621',
          payeeBankIfsc: '',
          payerBankIfsc: '',
          caseType: 'frm',
          payerAccountNumber: '',
          subAgentId: '',
          payerAccountType: 'NA',
          payerCardNumberMask: '',
          payeeMcc: '4722',
          ifrmPostauthVerdictName: 'N/A',
          channelId: '42',
          payeeAccountNumber: '',
          customerIp: '************',
          currentStatus: '',
          txnMessageId: 'issuerTxn',
          payeeAccountTypeName: 'NA, NA',
          payerMccCodeName: 'NA, No MCC',
          payerMmid: '',
          entityId: 'cust13',
          payeeMmidMobileNumber: '',
          txnCategoryName: '',
          payerType: '',
          channelName: '42, UPI',
          txnAmount: 110001,
          remarks: '',
          payerMobileNumber: ''
        }
      ],
      isLastPage: true,
      count: 1
    },
    bucketCases: {
      cases: [
        {
          caseId: 111,
          caseRefNo: '929064a6-2617-4a4e-969c-4813321ec024',
          txnId: 'acqTxn2023125A276',
          txnAmount: 1000001,
          txnTimestamp: '2023-12-07 18:57:28',
          entityId: 'merchant1',
          entityIdOpt: 'merchant1, merchant1',
          entityCategory: 'Merchant',
          weight: 0,
          createdTimestamp: '2023-12-07 13:06:35',
          investigationVerdict: 'NA',
          ifrmVerdict: 'ACCEPTED',
          investigationStatus: 'Rejected',
          terminalId: '122321',
          deviceId: 'IMEI112278',
          payerId: 'cust15',
          payeeId: 'merchant1',
          payeeMcc: '5533, Auto Mobile',
          channelId: 'NB, NB',
          txnType: '11, P2U – credit',
          responseCode: '00, Approved',
          senderMaskedCard: '****************',
          senderHashedCard: '****************',
          reViolatedRules: [
            '21d6ab8c-a72a-4ce0-b0ba-b48396a378d5',
            '153c0480-74fc-47cb-8f1e-41e8a2193072'
          ],
          bucketId: 4,
          ifrmPostauthVerdictName: 'ACCEPTED',
          caseStage: 'Checker',
          assignedTo: 'akashsm',
          partnerId: 1,
          channel: 'str',
          makerAction: 'File STR',
          checkerAction: 'Approved',
          attribute1: '',
          attribute2: ''
        }
      ],
      isLastPage: true,
      count: 1
    },
    caseDetail: {
      caseId: '123',
      caseRefNo: '123',
      channel: 'rpsl',
      customerId: '123',
      txnId: '123',
      txnAmount: 75500,
      txnTimestamp: '2020-06-26 03:58:12',
      ifrmVerdict: 'Stop',
      investigationVerdict: 'Others',
      lastUpdatedTimestamp: '2020-06-26 18:59:52',
      lastActionName: 'Reviewed&Closed',
      remarks: '23erf',
      fraudType: 'Auto closure as per banks requirement',
      weight: 65,
      currentStage: 'Investigation',
      currentStatus: 'Closed',
      liability: 'NotApplicable',
      rrn: '*****************',
      isAcknowledged: 1,
      currentlyAssignedTo: 'user',
      internalId: 'IC000000000000000022'
    },
    notations: {
      master: [
        {
          id: 1,
          notation: 'Called the customer - Customer confirmed having done the transaction',
          category: 'NotFraud'
        },
        {
          id: 2,
          notation: 'Called the customer - Customer confirmed not having done the transaction',
          category: 'Fraud'
        },
        {
          id: 3,
          notation: 'Called the customer - Customer not reachable',
          category: 'NotFraud'
        },
        {
          id: 4,
          notation: 'Called the customer - Customer is travelling - Will call back later',
          category: 'NotFraud'
        },
        {
          id: 5,
          notation: 'Called the customer - Left a note to call back',
          category: 'NotFraud'
        }
      ],
      case: [
        {
          caseId: '1',
          notationUserId: 4,
          notationUserName: 'ojjol',
          notationTimestamp: '2019-03-27T10:15:30',
          notationComment: 'created notation'
        }
      ]
    },
    closeCaseBuckets: [
      { id: 1, name: 'Fraud' },
      { id: 2, name: 'NonFraud' },
      { id: 3, name: 'Suspicious' }
    ],
    fraudTypesWithBuckets: [
      {
        id: 3,
        verdict: 'Confirmed Genuine',
        buckets: [{ id: 2, name: 'NonFraud' }]
      },
      {
        id: 0,
        verdict: 'Others',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 1,
        verdict: 'Undetermined',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 4,
        verdict: 'Assumed Genuine',
        buckets: [
          { id: 3, name: 'Suspicious' },
          { id: 2, name: 'NonFraud' },
          { id: 1, name: 'Fraud' }
        ]
      },
      {
        id: 2,
        verdict: 'Confirmed Fraud',
        buckets: [{ id: 1, name: 'Fraud' }]
      }
    ],
    violatedRules: {
      rules: [
        {
          id: 8,
          code: 'R4',
          name: 'Benford Law',
          description:
            'Checks whether the leading digit of all transacting amounts for a particular user follows Benfords Law within certain confidence level.',
          weight: 10,
          priority: 1,
          logic: 'logic',
          isTypeDefault: 1,
          isExplicit: 1,
          startTimestamp: '2020-12-20T18:29:23.725826'
        },
        {
          id: 6,
          code: '23cf3540-323d-48d7-ae92-542fe4a47643',
          name: 'Transaction Limit Breach',
          description:
            'Checks whether the leading digit of all transacting amounts for a particular user follows Benfords Law within certain confidence level.',
          weight: 10,
          priority: 1,
          logic: 'logic',
          isTypeDefault: 1,
          isExplicit: 1,
          startTimestamp: '2020-12-21T16:29:23.725826',
          endTimestamp: '2020-12-26T18:29:23.725826'
        }
      ]
    },
    transaction: {
      entityId: {
        value: 'cust13',
        categoryName: 'Customer ID',
        listTypeData: []
      },
      entityCategory: 'Customer',
      transactionInfo: {
        txnTimestamp: '2023-10-16T20:00:07',
        txnId: 'issuerTxn20231016207',
        txnAmount: 110001,
        txnCurrency: '356',
        txnType: '00, Purchase',
        txnCategoryName: '',
        txnMessageId: 'issuerTxn',
        txnCategoryId: '',
        referenceTxnId: '',
        receivedAt: '2023-10-16T11:04:06.842'
      },
      masterFields: {
        txnTypeName: '00, Purchase',
        payerAccountTypeName: 'NA, NA',
        payeeAccountTypeName: 'NA, NA',
        channelName: '42, UPI',
        sourceInstitutionName: '00006,  Worldline Card',
        paymentMethodName: 'DEBIT, DEBIT',
        acquirerName: '8000, PaaP-Payment Service',
        responseCodeName: '00',
        payerMccCodeName: 'NA, No MCC',
        payeeMccCodeName: '4722, Travel Industry',
        attribute1: 'abcd',
        attribute2: 'kbc'
      },
      isMerchantApiBased: '',
      deviceInfo: {
        isOnUs: '',
        customerIp: {
          value: '************',
          categoryName: 'Customer IP',
          listTypeData: []
        },
        deviceId: {
          value: '',
          categoryName: 'Device ID',
          listTypeData: []
        },
        deviceOs: {
          value: '',
          categoryName: 'Device OS',
          listTypeData: []
        },
        terminalId: {
          value: '1189621',
          categoryName: 'Terminal ID',
          listTypeData: []
        }
      },
      payeeDeviceOf: '',
      identifiers: {
        agentId: {
          value: '',
          categoryName: 'Agent ID',
          listTypeData: []
        },
        acquirerId: {
          value: '8000',
          categoryName: 'Acquirer ID',
          listTypeData: []
        },
        initiatorId: {
          value: '',
          categoryName: 'Initiator ID',
          listTypeData: []
        },
        sourceInstitutionId: '00006',
        subAgentId: {
          value: '',
          categoryName: 'Sub Agent ID',
          listTypeData: []
        },
        initiatorMobile: {
          value: '',
          categoryName: 'Initiator Mobile',
          listTypeData: []
        },
        initiatorType: '',
        merchantName: 'cust13',
        channelType: 'frm',
        partnerId: 0
      },
      isLien: '',
      locationCoordinates: {
        latitude: {
          value: '0.0',
          categoryName: 'Latitude',
          listTypeData: []
        },
        longitude: {
          value: '0.0',
          categoryName: 'Longitude',
          listTypeData: []
        }
      },
      mti: '200',
      payeeAccount: {
        payeeAccountNumber: {
          value: '',
          categoryName: 'Payee Account Number',
          listTypeData: []
        },
        payeeAccountType: 'NA',
        payeeBankIfsc: {
          value: '',
          categoryName: 'Payee Bank Ifsc',
          listTypeData: []
        },
        payeeId: {
          value: 'merchant15',
          categoryName: 'Customer ID',
          listTypeData: []
        },
        payeeMcc: {
          value: '4722',
          categoryName: 'Payee Mcc',
          listTypeData: []
        },
        payeeMmid: '',
        payeeType: '',
        payeeCardNumberMask: '',
        payeeCardNumberHash: {
          value: '',
          categoryName: 'Payee Card Number',
          listTypeData: []
        },
        payeeVpa: {
          value: '',
          categoryName: 'Payee VPA'
        }
      },
      payerAccount: {
        payerAccountNumber: {
          value: '',
          categoryName: 'Payer Account Number',
          listTypeData: []
        },
        payerAccountType: 'NA',
        payerBankIfsc: {
          value: '',
          categoryName: 'Payer Bank Ifsc',
          listTypeData: []
        },
        payerId: {
          value: 'cust4',
          categoryName: 'Customer ID',
          listTypeData: []
        },
        payerMcc: {
          value: 'NA',
          categoryName: 'Payer Mcc',
          listTypeData: []
        },
        payerMmid: '',
        payerType: '',
        payerCardNumberMask: '',
        payerCardNumberHash: {
          value: '****************',
          categoryName: 'Payer Card Number',
          listTypeData: []
        },
        payerVpa: {
          value: '',
          categoryName: 'Payer VPA'
        }
      },
      paymentMethod: 'DEBIT',
      responseCode: '00',
      ifrmVerdict: 'REJECTED',
      ifrmPostauthVerdictName: 'N/A',
      createdAt: '2023-10-16T11:04:06.266351',
      cognitiveResponse: '{"cognitiveIsSuspicious":0}',
      isCognitiveActive: 0,
      reViolatedRules: ['d6eae84d-8f3e-458e-bc23-0701797f66a7'],
      remarks: ''
    },
    createCaseAndAssign: [{ isSuccess: true }],
    selectedCase: {
      caseId: 553062,
      caseRefNo: 'a32059c5-59ac-434d-bb75-5a951d8fe1af',
      txnId: 'Test20230111506',
      txnAmount: 45000,
      txnTimestamp: '2023-01-11 22:11:06',
      entityId: 'merchant1',
      entityCategory: 'Merchant',
      weight: 60,
      createdTimestamp: '2023-01-12 18:51:28',
      assignmentTimeStamp: '2023-01-12 18:51:28',
      investigationVerdict: 'NA',
      ifrmVerdict: 'REJECTED',
      investigationStatus: 'Open',
      terminalId: '1234',
      deviceId: '04745YVBxcvn069C',
      customerId: 'cust108',
      beneficiaryId: 'merchant1',
      payeeMcc: '5199, Fruits And Vegetables',
      channel: 'frm',
      txnType: '11, P2U - credit',
      responseCode: 'NA, No Response',
      senderMaskedCard: 'NA',
      senderHashedCard: '************',
      reViolatedRules: '23cf3540-323d-48d7-ae92-542fe4a47643',
      bucketId: 4,
      ifrmPostauthVerdictName: 'ACCEPTED',
      caseStage: 'Maker',
      assignedTo: 'akashm',
      partnerId: 1
    },
    caseLog: [
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated',
        entityId: 'undefined',
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated1',
        entityId: 'undefined',
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated2',
        entityId: 'undefined',
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated3',
        entityId: 'undefined',
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      }
    ],
    statusLog: {
      statusLogs: [
        {
          dateAndTime: '2021-01-26T17:32:07',
          txnType: 'txn123',
          userName: 'user1',
          action: 'closed',
          description: 'rule violated with id 123',
          txnId: 'hnr93A',
          txnAmount: 17000,
          responseCode: '00,Accepted',
          violatedRules: '92f2fd11-44cd-4cfb-bb36-1af301fb54a5',
          caseId: '614',
          txnTypeName: 'Bill Payment'
        },
        {
          dateAndTime: '2021-01-27T17:32:07',
          txnType: 'txn987',
          userName: 'user2',
          action: 'closed',
          description: 'rule violated closed case',
          txnId: 'hnr94A',
          txnAmount: 17000,
          responseCode: '00',
          violatedRules: '92f2fd11-44cd-4cfb-bb36-1af301fb54a5',
          caseId: '614',
          txnTypeName: 'Bill Payment'
        }
      ],
      isLastPage: false,
      count: 100
    },
    investigatedTxns: {
      data: [
        {
          caseId: 615,
          caseRefNo: 'hnr94A',
          txnId: 'hnr94A',
          txnAmount: 17000,
          txnTimestamp: '2021-01-26T17:32:07',
          ifrmVerdict: 'OTP',
          currentStatus: 'Closed',
          liability: 'NotApplicable',
          caseVerdict: 'Undetermined',
          isAcknowledged: 1,
          assignedTo: 22,
          bucket: 3,
          terminalId: '12345',
          deviceId: '78',
          customerId: '992233',
          beneficiaryId: '1113',
          payeeMcc: '5411, hotel',
          channel: 'rrr',
          txnType: '00, Purchase',
          responseCode: '00, Accepted',
          senderMaskedCard: '6666',
          senderHashedCard: '7777',
          reViolatedRules: ['15dd9b65-549d-44e1-b922-aa065d98766e'],
          attribute1: '13-07-2023',
          attribute2: '1372673'
        }
      ],
      isLastPage: true,
      count: 1
    },
    similar: {
      data: [
        {
          isOnUs: '0',
          payeeMmid: '126581',
          payeeAccountType: '00',
          payerMcc: '5199',
          txnCurrency: '356',
          payeeType: 'ENTITY',
          payerAccountTypeName: '12, JPB/CBS DSB Account',
          caseVerdict: 'NA',
          txnType: '11',
          deviceId: '04745YVBxcvn069C',
          payeeMccCodeName: '5411, others',
          responseCode: ' ',
          createdAt: '2023-01-18T08:00:24.936897',
          txnTypeName: '11, P2U – credit',
          isLien: '',
          entityCategory: 'Merchant',
          payeeId: 'merchant00101',
          acquirerId: '9092',
          payeeDeviceOf: 'M',
          longitude: 79.460944,
          payerMmidMobileNumber: '',
          isMerchantApiBased: '1',
          mti: '200',
          payeeCardNumberMask: '',
          reViolatedRules: ['92f2fd11-44cd-4cfb-bb36-1af301fb54a5'],
          payeeMobileNumber: '',
          currentlyAssignedTo: 'NA',
          caseRefNo: '683bc5bf-6958-4d66-8f98-a36ed9c74c8f',
          responseCodeName: ' , PreAuth',
          initiatorId: '1234567',
          sourceInstitutionId: '8001',
          txnTimestamp: '2023-01-18T15:08:10',
          payerCardNumberHash: '************',
          ifrmVerdict: 'REJECTED',
          initiatorMobile: '',
          paymentMethod: '124',
          referenceTxnId: '',
          txnId: 'Test20240118003',
          initiatorType: '',
          txnCategoryId: '',
          agentId: '',
          cognitiveResponse: '{"cognitiveIsSuspicious":0}',
          deviceOs: 'linux',
          latitude: 22.670131,
          isCognitiveActive: 0,
          payeeCardNumberHash: '************',
          payerId: 'cust101',
          bucketId: 4,
          terminalId: '1234',
          payeeBankIfsc: 'eHcv0142287',
          payerBankIfsc: 'ifsc123',
          caseType: 'frm',
          payerAccountNumber: 'acc123',
          subAgentId: '',
          payerAccountType: '12',
          payerCardNumberMask: '',
          payeeMcc: '5411',
          ifrmPostauthVerdictName: 'ACCEPTED',
          channelId: '',
          currentStage: 'Maker',
          payeeAccountNumber: '***********',
          customerIp: '*************',
          currentStatus: 'New',
          txnMessageId: 'iuca001',
          payeeAccountTypeName: '00, JIO Money Pre Paid/Default',
          payerMccCodeName: '5199, Fruits And Vegetables',
          payerMmid: '********',
          entityId: 'merchant00101',
          payeeMmidMobileNumber: '**********',
          txnCategoryName: '',
          payerType: 'PERSON',
          channelName: ', frm',
          txnAmount: 2000000,
          payerMobileNumber: '**********'
        }
      ],
      isLastPage: true,
      count: 45
    },
    similarCategory: {
      similarTxnCategory: [{ label: 'terminalId', value: 'terminalId' }]
    },
    closureCases: [
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      }
    ],
    xChannel: [
      { id: 'UPI', xchannelIdType: 'UPI' },
      { id: 'POS', xchannelIdType: 'POS' },
      { id: 'NB', xchannelIdType: 'NB' },
      { id: 'DC', xchannelIdType: 'DC' },
      { id: 'CC', xchannelIdType: 'CC' }
    ],
    snoozeConditions: [
      {
        ruleName: 'Rule1',
        ruleCodes: '125467',
        investigationVerdictId: 1,
        fraudTypeId: 2,
        investigationLiabilityId: 3,
        remark: 'Auto-Closed initiated',
        bucketId: 3,
        userName: 'maker',
        conditionList: [
          {
            attribute: 'locationName',
            operator: '=',
            value: 'Delhi'
          },
          {
            attribute: 'txnType',
            operator: '=',
            value: '1'
          }
        ],
        snoozedUntil: '2019-10-11 15:08:31'
      }
    ]
  },
  str: {
    citation: {
      caseRefNo: '38f486fe-44f1-4592-a636-792a2f0c5669',
      ruleCitationResponses: [
        {
          citationResponses: [
            {
              ruleCitationResponse: {
                userId: 89,
                userName: 'struser',
                userRole: 'str:maker',
                response: 'yes',
                creationDate: '2023-01-10T18:21:56.368'
              }
            },
            {
              ruleCitationResponse: {
                userId: 89,
                userName: 'struser',
                userRole: 'str:maker',
                response: 'yes',
                creationDate: '2023-01-10T18:21:56.368'
              }
            }
          ],
          citationId: 45,
          citationName: 'Is kyc done'
        }
      ]
    },
    downloadHistory: [
      {
        userId: 123,
        userName: 'Analyst-123',
        userRole: 'Analyst',
        xmlDownloadTimestamp: '2020-05-14 14:18:01',
        fileVersion: 1.0
      },
      {
        userId: 456,
        userName: 'PO-456',
        userRole: 'PO',
        xmlDownloadTimestamp: '2020-05-14 14:18:01',
        fileVersion: 2.0
      }
    ],
    suspicionsMaster: {
      reportingEntity: [
        {
          key: 'ReportingEntityName',
          value:
            'Complete name of the reporting entity (Bank, financial institution, intermediary).'
        },
        {
          key: 'ReportingEntityCategory',
          value:
            'The category to which the Reporting entity belongs. FIU has provided a five digit category code to specify the category of the reporting entity. Example: BAPUB for Public Sector banks, Refer section 11.1.3.1 for details on enumerations.'
        },
        {
          key: 'RERegistrationNum',
          value:
            'Any unique number for the Reporting Entity. This number can be the registration number or any number used in correspondence with the regulator. This number will be used during verification of the registration of the reporting entity and in correspondence with the regulators. If the regulator has not issued any number, the reporting entity may use any other self generated number.'
        },
        {
          key: 'FIUREID',
          value:
            'Unique ID issued by FIU. FIU will communicate the FIUREID during the registration of the reporting entity on the FINnet Gateway Portal. The FIUREID will be in the fomat XXXXXNNNNN where XXXXX is generated in accordance with section 11.1.3.1 and NNNNN is a sequentially generated numder. Use XXXXXNNNNN till the ID is communicated.'
        }
      ],
      reasonRevision: [
        {
          key: 'A',
          value:
            'Acknowledgement of original batch had many fatal, non fatal or probable errors which are being resolved'
        },
        {
          key: 'B',
          value:
            'Operational errors in original batch have been identified and reports are being revised or deleted suo moto'
        },
        {
          key: 'C',
          value: 'The replacement report is on account of additional information being submitted'
        },
        { key: 'N', value: 'Not applicable as this is a new batch' },
        { key: 'Z', value: 'Other reason' }
      ],
      sourceOfAlert: [
        { key: 'CV', value: 'Customer Verification' },
        { key: 'WL', value: 'Watch List' },
        { key: 'TY', value: 'Typology' },
        { key: 'TM', value: 'Transaction Monitoring' },
        { key: 'RM', value: 'Risk Management System' },
        { key: 'MR', value: 'Media Reports' },
        { key: 'LQ', value: 'Law Enforcement Agency Query' },
        { key: 'El', value: 'Employee Initiated' },
        { key: 'PC', value: 'Public Complaint' },
        { key: 'BA', value: 'Business Associates' },
        { key: 'ZZ', value: 'Others' },
        { key: 'XX', value: 'Not Categorised' }
      ],
      leaInformed: [
        { key: 'R', value: 'Information received' },
        { key: 'S', value: 'Information sent' },
        { key: 'N', value: 'No correspondence sent or received' },
        { key: 'X', value: 'Not categorised' }
      ],
      priorityRating: [
        { key: 'P1', value: 'Very High Priority' },
        { key: 'P2', value: 'High Priority' },
        { key: 'P3', value: 'Normal Priority' },
        { key: 'XX', value: 'Not categorised' }
      ],
      reportCoverage: [
        { key: 'C', value: 'Complete' },
        { key: 'P', value: 'Partial' },
        { key: 'X', value: 'Not categorised' }
      ],
      batchType: [
        { key: 'N', value: 'New Report' },
        { key: 'D', value: 'Deletion Report' },
        { key: 'R', value: 'Replacement Report' }
      ]
    },
    filing: [
      { key: 'ReportingEntityName', value: 'ICICI' },
      { key: 'ReportingEntityCategory', value: 'BAPUB' },
      { key: 'RERegistrationNumber', value: '************' },
      { key: 'FIUREID', value: 'XXXXXNNNNN' },
      { key: 'POName', value: 'Akash' },
      { key: 'PODesignation', value: 'QA' },
      { key: 'Address', value: 'kharadi nagar, pune district' },
      { key: 'City', value: 'Pune' },
      { key: 'StateCode', value: 'MH' },
      { key: 'PinCode', value: '410206' },
      { key: 'CountryCode', value: 'IN' },
      { key: 'Telephone', value: '0212-343453455345435345' },
      { key: 'Mobile', value: '+************' },
      { key: 'Fax', value: '022-********' },
      { key: 'POEmail', value: '<EMAIL>' }
    ],
    suspicionDetail: {
      caseRefNo: '99afd673-5417-4711-acce-09a3048b6202',
      batchType: 'N',
      reasonOfRevision: 'N',
      mainPersonName: 'Akash',
      sourceOfAlert: 'RM',
      alertIndicator: ['Activity in Dormant Account'],
      suspicionDueToProceedsOfCrime: 'Y',
      suspicionDueToComplexTrans: 'Y',
      suspicionDueToNoEcoRationale: 'N',
      suspicionOfFinancingOfTerrorism: 'N',
      attemptedTransaction: 'Y',
      leaInformed: 'S',
      leaDetails: 'Suraj Kharadi',
      priorityRating: 'P1',
      reportCoverage: 'C',
      additionalDocuments: 'Y'
    },
    autoClose: [
      {
        ruleName: 'Rule1',
        ruleCodes: '125467',
        investigationVerdictId: 1,
        fraudTypeId: 2,
        investigationLiabilityId: 3,
        remark: 'Auto-Closed initiated',
        bucketId: 3,
        userName: 'maker',
        conditionList: [
          {
            attribute: 'locationName',
            operator: '=',
            value: 'Delhi'
          },
          {
            attribute: 'txnType',
            operator: '=',
            value: '1'
          }
        ],
        snoozedUntil: '2019-10-11 15:08:31'
      }
    ]
  },
  trend: {
    trendTimeGraph: {
      data: [
        {
          dateTime: '05/02/2017 19:40:35',
          transactionAmount: 100,
          transactionType: 'COLLECT',
          bucket: 'FRAUD'
        },
        {
          dateTime: '09/02/2017 20:40:35',
          transactionAmount: 150,
          transactionType: 'COLLECT',
          bucket: 'FRAUD'
        },
        {
          dateTime: '03/02/2017 21:40:35',
          transactionAmount: 130,
          transactionType: 'COLLECT',
          bucket: 'NOT FRAUD'
        }
      ],
      usualTxnTimeRange: {
        startTime: 'x',
        endTime: 'y'
      },
      isEmpty: false
    },
    perPayeeTrend: {
      data: [
        {
          payeeVirtualAdd: 't@okhdfcbank',
          totalCount: 3,
          totalAmount: 300.369
        }
      ],
      averageTransactionAmount: 100.123,
      isEmpty: false
    }
  },
  rules: {
    components: {
      prefix: [
        {
          name: 'Transactions',
          description: 'Rules related to transactions should start with this keyword'
        }
      ],
      parameters: [
        {
          name: 'merchantId',
          description: 'Filtering transactions based on a particular merchantId'
        },
        {
          name: 'terminalId',
          description: 'Filtering transactions based on a particular terminalId'
        },
        {
          name: 'txnDateTime',
          description: 'Filtering transactions based on a particular txnDateTime'
        },
        {
          name: 'txnAmount',
          description: 'Filtering transactions based on a particular txnAmount'
        },
        {
          name: 'accountId',
          description: 'Filtering transactions based on a particular accountId'
        },
        {
          name: 'cardNumber',
          description: 'Filtering transactions based on a particular cardNumber'
        },
        {
          name: 'txnId',
          description: 'Filtering transactions based on a particular txnId'
        },
        {
          name: 'customerId',
          description: 'Filtering transactions based on a particular customerId'
        }
      ],
      functions: [
        {
          name: 'should have',
          description: 'Specify the parameter to be computed for fraudulency'
        },
        {
          name: 'within',
          description: 'Filtering transactions until certain time in past'
        },
        {
          name: 'from same',
          description: 'Filter transactions based on some transacional parmeter'
        },
        {
          name: 'minutes',
          description: 'This function is used to specify time period with respect to minutes'
        },
        {
          name: 'to same',
          description: 'Filter transactions based on account number'
        },
        {
          name: 'hours',
          description: 'This function is used to specify time period with respect to hours'
        },
        {
          name: 'count',
          description: 'For calculating the transaction count across PSPs'
        },
        {
          name: 'sum',
          description: 'For calculating the transaction amount across PSPs'
        },
        {
          name: 'days',
          description: 'This function is used to specify time period with respect to days'
        }
      ],
      operators: [
        {
          name: 'greater than',
          description: '> than operator'
        },
        {
          name: 'equal to',
          description: '= to operator'
        },
        {
          name: 'less than',
          description: '< than operator'
        }
      ]
    },
    actionCode: [
      {
        order: 1,
        actionCode: '01',
        actionName: 'ACCEPTED'
      },
      {
        order: 2,
        actionCode: '02',
        actionName: 'REJECTED'
      },
      {
        order: 3,
        actionCode: '03',
        actionName: 'OTP'
      },
      {
        order: 4,
        actionCode: '04',
        actionName: 'MPIN'
      },
      {
        order: 5,
        actionCode: '05',
        actionName: 'PASSWORD'
      },
      {
        order: 6,
        actionCode: '06',
        actionName: 'CC BLOCK'
      }
    ],
    list: [
      {
        code: '15dd9b65-549d-44e1-b922-aa065d98766e',
        name: 'BenfordLawCheck',
        logic: '',
        description:
          'Checks whether the leading digit of all transacting amounts for a particular user follows Benfords Law within certain confidence level.',
        order: 0,
        assignmentPriority: 0,
        ruleType: 'Dsl',
        status: 'Activated',
        active: 'Enable',
        explicit: false,
        actionCode: '01',
        actionName: 'ACCEPTED',
        createdBy: 'admin',
        createdAt: '2019-10-11 15:08:31',
        updatedBy: '',
        isMerchantSpecific: true,
        lowLevelOutcome: 'ACCEPTED',
        medLevelOutcome: 'ACCEPTED',
        highLevelOutcome: 'REJECTED',
        alertCategoryId: 1,
        groupId: 1
      },
      {
        code: 'bb70ee59-eca4-4c89-9885-cfdf9417317a',
        name: 'TransactionFrequenciesLargeAmountCheck',
        logic: '',
        description:
          'Checks whether a current transaction is a fraud or not by calculating time frequencies for last three successful transactions for the large amount.',
        order: 10,
        assignmentPriority: 1,
        ruleType: 'Default',
        status: 'Activated',
        active: 'Enable',
        explicit: false,
        actionCode: '01',
        actionName: 'ACCEPTED',
        createdBy: 'admin',
        createdAt: '2019-10-11 15:08:31',
        updatedBy: '',
        isMerchantSpecific: false,
        alertCategoryId: 1,
        groupId: 2
      }
    ],
    verify: 'DSL string validated successfully',
    createRule: {},
    updateRule: 'DSL updated successfully',
    approval: [
      {
        approvalId: '15dd9b65-549d-44e1-b922-aa065d98766e',
        code: '12345-eca4-4c89-9885-cfdf9417317a',
        name: 'BenfordLawCheck',
        logic: '',
        description:
          'Checks whether the leading digit of all transacting amounts for a particular user follows Benfords Law within certain confidence level.',
        weight: 10,
        priority: 1,
        ruleType: 'Default',
        explicit: false,
        actionCode: '01',
        actionName: 'ACCEPTED',
        createdBy: 'admin',
        createdAt: '2019-10-11 15:08:31',
        updatedBy: '',
        approvalStatus: 'Pending',
        comments: 'Some comments'
      },
      {
        approvalId: 'bb70ee59-eca4-4c89-9885-cfdf9417317a',
        code: '6789-eca4-4c89-9885-cfdf9417317a',
        name: 'TransactionFrequenciesLargeAmountCheck',
        logic: '',
        description:
          'Checks whether a current transaction is a fraud or not by calculating time frequencies for last three successful transactions for the large amount.',
        weight: 10,
        priority: 1,
        ruleType: 'Default',
        explicit: false,
        actionCode: '01',
        actionName: 'ACCEPTED',
        createdBy: 'admin',
        createdAt: '2019-10-11 15:08:31',
        updatedBy: '',
        approvalStatus: 'Pending',
        comments: 'Some comments'
      }
    ],
    ruleNames: [
      {
        code: '92f2fd11-44cd-4cfb-bb36-1af301fb54a5',
        name: 'sdfds'
      },
      {
        code: '61f8f86a-788f-49f8-8779-afe849321c54',
        name: 'Rule #039l'
      },
      {
        code: '865da198-a94f-4e51-a7b9-432d5d24088e',
        name: 'Rule #203a2'
      },
      {
        code: 'fe763413-d8d7-4ad1-941a-ebc3dc35c2f3',
        name: 'Rule #HigherAmt'
      },
      {
        code: '26d1aefd-e5c3-4bc3-84cd-02d610bc9bfa',
        name: 'Rule #038a'
      },
      {
        code: '905b8835-51ba-4b13-8770-d3693807d1e2',
        name: 'Rule #065a1'
      },
      {
        code: '6ff7d851-35c6-4fe3-b556-d78e366d5f32',
        name: 'Rule #064'
      },
      {
        code: 'f6d6606f-0c0b-4e65-927c-156495096769',
        name: 'Rule #mp016'
      }
    ],
    categories: [{ id: 1, categoryName: 'FRAUD' }],
    snoozeAttributes: [
      { key: 'entityId', value: 'entityId', dataType: 'text' },
      { key: 'txnAmount', value: 'txnAmount', dataType: 'numeric' }
    ],
    snoozedRules: [
      {
        code: '15dd9b65-549d-44e1-b922-aa065d98766e',
        name: 'BenfordLawCheck',
        userId: 23,
        userName: 'checker',
        comments: 'Rule to be snoozed!',
        isApproved: 0,
        snoozeUntil: '2019-10-11 15:08:31',
        createdAt: '2019-10-11 15:08:31',
        attributeList: 'mer~=~1234,id~<=~1234'
      },
      {
        code: '15dd9b65-549d-44e1-b922-aa065d98766e',
        name: 'BenfordLawCheck2',
        userId: 23,
        userName: 'checker',
        comments: 'Rule to be snoozed!',
        isApproved: 1,
        snoozeUntil: '2019-10-11 15:08:31',
        createdAt: '2019-10-11 15:08:31',
        attributeList: 'mer~=~1234,id~<=~1234'
      }
    ],
    alertCategories: [
      {
        id: 1,
        categoryName: 'COMPLIANCE',
        desc: 'Description for category 1'
      },
      {
        id: 2,
        categoryName: 'FRAUD',
        desc: 'Description for category 2'
      },
      {
        id: 3,
        categoryName: 'ON HOLD',
        desc: 'Description for category 3'
      }
    ]
  },
  uds: {
    profile: [
      {
        id: 123,
        name: 'neha last-name',
        entityCategory: 'customer',
        mobileNumber: '**********',
        accountNumber: '*************'
      },
      {
        id: 1234,
        name: 'neha last-name',
        entityCategory: 'customer',
        mobileNumber: '**********',
        accountNumber: '*************'
      }
    ],
    merchantScore: [
      { scoreName: 'Merchant OnBoarding Score', scoreValue: 'MEDIUM' },
      { scoreName: 'Merchant Monitoring Score', scoreValue: 'High' }
    ]
  },
  sandbox: {
    dateRange: {
      startTimestamp: '2022-09-25T10:19:44',
      endTimestamp: '2022-09-30T18:12:36'
    },
    testResult: [
      {
        date: '2021-04-27T10:12:01',
        alerts: [
          { rule: 'BenfordLawCheck', noOfTxns: 20 },
          { rule: 'TransactionFrequenciesLargeAmountCheck', noOfTxns: 15 }
        ]
      },
      {
        date: '2021-04-28T10:12:01',
        alerts: [{ rule: 'BenfordLawCheck', noOfTxns: 5 }]
      },
      {
        date: '2021-04-29T10:12:01',
        alerts: [
          { rule: 'BenfordLawCheck', noOfTxns: 22 },
          { rule: 'TransactionFrequenciesLargeAmountCheck', noOfTxns: 19 }
        ]
      }
    ]
  },
  listsandlimits: {
    types: [
      { id: 1, listName: 'Negative', groupName: 'special' },
      { id: 2, listName: 'VIP', groupName: 'special' },
      { id: 3, listName: 'OTP', groupName: 'special' },
      { id: 4, listName: 'Blocked', groupName: 'special' },
      { id: 5, listName: 'grey2', groupName: 'custom' },
      { id: 6, listName: 'Grey', groupName: 'custom' }
    ],
    categories: [
      { id: 1, categoryName: 'Agent ID' },
      { id: 2, categoryName: 'Terminal ID' },
      { id: 3, categoryName: 'Account Number' },
      { id: 4, categoryName: 'IFSC' },
      { id: 5, categoryName: 'Device ID' },
      { id: 5, categoryName: 'Customer ID' },
      { id: 5, categoryName: 'MCC' }
    ],
    list: [
      {
        id: 58,
        listName: 'Watch',
        categoryName: 'Category ID',
        identifier: 'cate001',
        partnerId: 1,
        updatedTimeStamp: '2023-10-19T08:28:21.915',
        amount: 5000,
        count: 5,
        isActive: 0,
        userName: 'checker',
        remark: ''
      },
      {
        id: 59,
        listName: 'Watch',
        categoryName: 'Category ID',
        identifier: 'cate001',
        partnerId: 0,
        updatedTimeStamp: '2023-10-19T08:28:47.298',
        amount: 5000,
        count: 5,
        isActive: 0,
        userName: 'checker',
        remark: ''
      }
    ],
    merchantPaginated: {
      data: [
        {
          id: 22,
          merchantId: 'merchant2',
          limitType: 'CustomerRides',
          remark: 'TestRemar',
          addedBy: 'testUser',
          addedOn: '2020-11-30T23:00:34.746',
          dailyCountLimit: 50,
          dailyAmountLimit: 700,
          monthlyCountLimit: 100,
          monthlyAmountLimit: 200,
          txnAmountLimit: 4000
        }
      ],
      isLastPage: true,
      count: 100
    },
    merchantlimit: {
      merchant: [
        {
          id: 22,
          merchantId: 'merchant2',
          limitType: 'CustomerRides',
          remark: 'TestRemar',
          addedBy: 'testUser',
          addedOn: '2020-11-30T23:00:34.746',
          dailyCountLimit: 50,
          dailyAmountLimit: 700,
          monthlyCountLimit: 100,
          monthlyAmountLimit: 200,
          txnAmountLimit: 4000
        }
      ],
      isLastPage: true,
      count: 100
    },
    limitType: [
      { id: 1, limitName: 'OpenLoop' },
      { id: 2, limitName: 'ClosedLoop' },
      { id: 3, limitName: 'OpenLoop-Refund' },
      { id: 4, limitName: 'ClosedLoop-Refund' },
      { id: 5, limitName: 'CustomerRides' },
      { id: 6, limitName: 'NA' }
    ],
    onboardingLimit: [
      {
        onboardingType: 'sdsgjgd',
        dailyCountLimit: 132,
        dailyAmountLimit: 21,
        monthlyCountLimit: 21,
        monthlyAmountLimit: 21,
        txnAmountLimit: 21
      },
      {
        onboardingType: 'sdsd',
        dailyCountLimit: 132,
        dailyAmountLimit: 21,
        monthlyCountLimit: 21,
        monthlyAmountLimit: 21,
        txnAmountLimit: 21
      }
    ],
    settlementlimit: {
      settlement: [
        {
          settlementType: 'sdsgjgd',
          dailyCountLimit: 132,
          dailyAmountLimit: 21,
          monthlyCountLimit: 21,
          monthlyAmountLimit: 21,
          txnAmountLimit: 21
        },
        {
          settlementType: 'sdsd',
          dailyCountLimit: 132,
          dailyAmountLimit: 21,
          monthlyCountLimit: 21,
          monthlyAmountLimit: 21,
          txnAmountLimit: 21
        }
      ],
      isLastPage: true,
      count: 9
    },
    merchantDaily: {
      merchantdaily: [
        {
          merchantId: 'merchant2',
          openLoopCountLimit: 651,
          openLoopAmountLimit: 5312,
          closedLoopCountLimit: 12,
          closedLoopAmountLimit: 421,
          openLoopRefundCountLimit: 120,
          openLoopRefundAmountLimit: 12,
          closedLoopRefundCountLimit: 12,
          closedLoopRefundAmountLimit: 210,
          openLoopExitCountLimit: 321,
          openLoopExitAmountLimit: 312,
          closedLoopExitCountLimit: 54,
          closedLoopExitAmountLimit: 321,
          openLoopExitRefundCountLimit: 651,
          openLoopExitRefundAmountLimit: 1231,
          closedLoopExitRefundCountLimit: 122,
          closedLoopExitRefundAmountLimit: 10
        }
      ],
      isLastPage: true,
      count: 100
    }
  },
  prefilter: {
    tps: [{ channelId: 1, channelName: 'rpsl', tpsLimit: 10 }],
    category: [
      { channelId: 1, categoryId: 1, channelName: 'rpsl', categoryName: 'Transaction Amount' },
      { channelId: 1, categoryId: 2, channelName: 'rpsl', categoryName: 'Terminal ID' },
      { channelId: 1, categoryId: 3, channelName: 'rpsl', categoryName: 'MCC' }
    ],
    filter: [
      {
        id: 7,
        channelId: 1,
        categoryId: 2,
        channelName: 'rpsl',
        categoryName: 'Terminal ID',
        identifier: 'terminalid@1',
        isActive: false
      },
      {
        id: 8,
        channelId: 1,
        categoryId: 2,
        channelName: 'rpsl',
        categoryName: 'Terminal ID',
        identifier: 'terminalid@2',
        isActive: false
      },
      {
        id: 9,
        channelId: 1,
        categoryId: 2,
        channelName: 'rpsl',
        categoryName: 'Terminal ID',
        identifier: 'terminalid@3',
        isActive: false
      }
    ]
  },
  release: {
    list: [
      {
        caseRefNo: 'atma42020-01-08T04:48:12.000+05:30',
        txnId: 'abc01:tasdahritgvn:129723',
        txnTimestamp: '2019-03-27 10:15:30',
        txnAmount: 4500,
        merchantId: '12345',
        institutionId: '7172 ',
        response: 'ACCEPTED',
        verdict: 'Confirmed Fraud',
        liability: 'Not Applicable',
        fraudType: 'Identity Theft',
        explanation: 'Held due to some reason',
        docVerified: 1
      },
      {
        caseRefNo: 'atma42020-01-08T04:48:12.000+05:30',
        txnId: 'abc02:tasdahritgvn:129723',
        txnTimestamp: '2019-03-27 10:15:30',
        txnAmount: 4500,
        merchantId: '12345',
        institutionId: '7172 ',
        response: 'ACCEPTED',
        verdict: 'Confirmed Fraud',
        liability: 'Not Applicable',
        fraudType: 'Identity Theft',
        explanation: 'Held due to some reason',
        docVerified: 1
      }
    ],
    docStatus: {
      txnId: '1234',
      status: 0,
      holdStatus: 'OnHold'
    }
  },
  monitoring: {
    count: 12,
    transaction: {
      transactions: { 3: [], 2: [], 1: [] },
      flaggedTransactions: 0,
      identifiedFrauds: 0
    }
  },
  scp: {
    config: [
      {
        configType: 'autoClosure',
        configPoints: {
          timeOfDay: '',
          activation: 'Disabled',
          jobInterval: '',
          fraudTypeId: '',
          autoClosureDays: '',
          explanation: '',
          liabilityId: '',
          verdictTypeId: ''
        }
      }
    ]
  },
  businessDashboard: {
    businessKpis: {
      totalAlerts: 15,
      notFraudCases: 3,
      fraudCasesWithViolations: 2,
      fraudCasesWithoutViolations: 10,
      totalFraudAmount: 5
    },
    actionShare: {
      accepted: 15,
      rejected: 3,
      otp: 2,
      mpin: 10,
      password: 5,
      ccBlock: 2,
      hold: 10,
      noAction: 5
    },
    highAlertCustomers: {
      customers: [
        {
          customerId: '1234565',
          customerName: 'Chirag',
          count: 5,
          amount: 2000.0
        },
        {
          customerId: '574533434',
          customerName: 'Paras',
          count: 5,
          amount: 2000.0
        }
      ]
    },
    noViolationFraud: [
      {
        txnId: 'txn12345',
        customerName: 'Abhijeet',
        amount: 50000
      },
      {
        txnId: 'txn123456',
        customerName: 'Keshave kumar',
        amount: 20000
      },
      {
        txnId: 'txn987',
        customerName: 'Kailesh kumar',
        amount: 10000
      }
    ],
    ruleCategoryTrend: [
      {
        category: 'Inconsistent Activity',
        rules: 5,
        alerts: 50,
        frauds: 45
      },
      {
        category: 'Threshold Monitoring',
        rules: 5,
        alerts: 60,
        frauds: 45
      },
      {
        category: 'Velocity Monitoring',
        rules: 5,
        alerts: 60,
        frauds: 45
      },
      {
        category: 'Activity in Dormant Account',
        rules: 8,
        alerts: 60,
        frauds: 45
      },
      {
        category: 'Split Payment',
        rules: 4,
        alerts: 60,
        frauds: 45
      },
      {
        category: 'Muling Activity',
        rules: 5,
        alerts: 60,
        frauds: 45
      }
    ]
  },
  citations: {
    caseRefNo: '38f486fe-44f1-4592-a636-792a2f0c5669',
    ruleCitationResponses: [
      {
        citationResponses: [],
        citationId: 45,
        citationName: 'Is kyc done'
      }
    ]
  },
  caseDocument: {
    files: [
      {
        fileName: 'Holiday List 2022 (2).pdf',
        fileUrl:
          'http://localhost:8001/api/v1/ifrm/casereview/case/4d83cd3b-fc7a-45f9-a260-c90122212310/evidence-files/Holiday%20List%202022%20(2).pdf/download'
      },
      {
        fileName: 'labrador-retriever-1210559_340(1)(1)(1)(1).jpg',
        fileUrl:
          'http://localhost:8001/api/v1/ifrm/casereview/case/4d83cd3b-fc7a-45f9-a260-c90122212310/evidence-files/labrador-retriever-1210559340(1)(1)(1)(1).jpg/download'
      },
      {
        fileName: 'labrador-retriever-1210559340(1)(1)(1).jpg',
        fileUrl:
          'http://localhost:8001/api/v1/ifrm/casereview/case/4d83cd3b-fc7a-45f9-a260-c90122212310/evidence-files/labrador-retriever-1210559_340(1)(1)(1).jpg/download'
      }
    ]
  },
  customerCommunication: [],
  complianceDashboard: [
    {
      caseId: 8,
      txnId: 'TXN8',
      caseRefNo: '67887',
      entityId: 'Entity3',
      entityCategory: 'Agent',
      txnTimestamp: '2020-05-14T14:18:01.522344',
      createdTimeStamp: '2020-05-14T14:18:01.522344',
      assignmentTimeStamp: '2020-05-14T14:18:01.522344',
      weight: 10,
      txnAmount: 300,
      investigationVerdict: 'Confirmed Fraud',
      ifrmVerdict: 'OTP',
      investigationStatus: 'Open',
      terminalId: 'Terminal123',
      deviceId: 'd123',
      customerId: 'cust123',
      beneficiaryId: 'b123',
      payeeMcc: '1121, shop',
      channel: 'ch123',
      txnType: 'cASH',
      responseCode: '00,Accepted',
      senderMaskedCard: 'JGHER3545',
      senderHashedCard: 'SJHGS2653',
      reViolatedRules: 'SD-DD4-34,4545-4354FDG-DV',
      bucketId: 2
    }
  ],
  dynamicCounters: {
    counters: [
      {
        identifier: 'merchantCummulativeMonthlyAmt',
        description: 'This calculates the cumulative amount for a month.',
        funcName: 'fnc_merchantCummulativeMonthlyAmt',
        period: 'Monthly',
        ttl: null,
        isSchedulerActive: 1,
        schedulerName: 'sch_merchantCummulativeMonthlyAmt',
        primaryAttribute: 'payerId',
        secondaryAttribute: null,
        isSingleAttribute: 1,
        fields: 'locationName~txnType',
        operators: '=~IN',
        values: "'Delhi'~('abc','cde')",
        conditionList: ["locationName = 'Delhi'", "txnType IN ('abc,'cde')"],
        createdBy: 45,
        creationDate: '2019-10-16 00:48:55',
        isPostAuth: 1
      },
      {
        identifier: 'merchantCummulativeMonthlyAmt',
        description: 'This calculates the cumulative amount for a month for a merchant',
        funcName: 'fnc_merchantCummulativeMonthlyAmt',
        period: 'Custom Days',
        ttl: 33,
        isSchedulerActive: 1,
        schedulerName: 'sch_merchantCummulativeMonthlyAmt',
        primaryAttribute: 'payerId',
        secondaryAttribute: 'payeeId',
        isSingleAttribute: 0,
        fields: 'locationName~txnType',
        operators: '=~IN',
        values: "'Delhi'~('abc','cde')",
        conditionList: ["locationName = 'Delhi'", "txnType IN ('abc,'cde')"],
        createdBy: 45,
        creationDate: '2019-10-16 00:48:55',
        isPostAuth: 0
      }
    ],
    conditionalAttributes: [
      {
        key: 'IS_LIEN',
        value: 'isLien',
        datatype: 'text'
      },
      {
        key: 'INITIATOR_TYPE',
        value: 'initiatorType',
        datatype: 'numeric'
      }
    ],
    allAttributes: [
      {
        key: 'ENTITY_ID',
        value: 'entityId'
      },
      {
        key: 'AGENT_ID',
        value: 'agentId'
      }
    ],
    subAttributes: []
  },
  incidents: {
    incident: {
      incidentId: 4,
      incidents: [
        {
          isReportedByCustomer: 'N',
          isAttemptedFraud: 'N',
          dateOfOccRepByBank: '********',
          dateOfDetByBank: '********',
          dateOfEntry: '********',
          custDateOfOccRep: '',
          custTimeOfOccRep: '',
          custRepDateToBank: '',
          custdateOfEntry: '',
          uniqueTxnRef: 'issuTxn20230830056',
          isDomestic: 'Y',
          custRepName: 'NA',
          custMobileNo: '**********',
          custEmail: 'email',
          amtInvolvedInFraud: '15000.0'
        }
      ]
    },
    allIncidents: [
      {
        incidentId: 5,
        status: 'Open',
        incidents: [
          {
            id: 5,
            incidentId: 5,
            frnNo: 'frn101',
            internalId: 'IC000000000000000005',
            isReportedByCustomer: 'N',
            isAttemptedFraud: 'N',
            pmtInst: '',
            pmtSys: '',
            fraudSys: '',
            pmtChannel: '',
            fraudNature: '',
            dateOfOccRepByBank: '********',
            dateOfDetByBank: '********',
            dateOfEntry: '********',
            custDateOfOccRep: '',
            custTimeOfOccRep: '',
            custRepDateToBank: '',
            custdateOfEntry: '',
            uniqueTxnRef: 'issuTxn20230830052',
            isDomestic: 'Y',
            custRepName: 'NA',
            custMobileNo: '**********',
            custEmail: 'email',
            custOtherDetails: '',
            isPGPAinvolved: 'N',
            pgName: '',
            isTPartyPspInvolved: 'N',
            tPartyPSPName: '',
            amtInvolvedInFraud: '15000.0',
            amtRecInFraud: '',
            isInsuranceAvailable: 'N',
            insurerNameTxnCoverage: '',
            amtRecDueToInsCover: '',
            benName: '',
            benMobile: '',
            benEmail: '',
            benAccNo: '',
            benBank: '',
            benBranchCode: '',
            benBranchIfsc: '',
            benPanNo: '',
            benCardNo: '',
            benPPICardNo: '',
            benUPIId: '',
            destPPIIssuerName: '',
            destMerchantId: '',
            destPGPA: '',
            destATMId: '',
            suspWebsiteUSed: '',
            suspMobAppUsed: '',
            suspDeviceId: '',
            suspIPAddress: '',
            suspIMEI: '',
            suspGeoTag: '',
            suspOtherDetails: '',
            initialModOperandi: '',
            modOperandiUpd1: '',
            modOperandiUpd2: '',
            modOperandiUpd3: '',
            modOperandiUpd4: '',
            modOperandiUpd5: '',
            isFalseAlert: '',
            isRegWithLEA: '',
            detLeaCase: '',
            isFraudClosed: '',
            dateOfClosure: '',
            reasonFraudClosure: '',
            otherFraudInfo: '',
            preventiveSteps: '',
            isFiled: false,
            reportedBy: ''
          }
        ]
      }
    ],
    masters: {
      fraudNature: [
        {
          code: 'ACH',
          name: 'Account Hacking / Compromise / Identity theft'
        },
        {
          code: 'PHH',
          name: 'Phishing'
        },
        {
          code: 'RMD',
          name: 'Remote Capture of Device'
        },
        {
          code: 'LSI',
          name: 'Lost / Stolen Device / Instrument'
        },
        {
          code: 'CRS',
          name: 'Card Skimming'
        },
        {
          code: 'VIS',
          name: 'Vishing'
        },
        {
          code: 'SMI',
          name: 'Smishing'
        },
        {
          code: 'SIS',
          name: 'SIM Swap'
        },
        {
          code: 'WBC',
          name: 'Website Cloning / Fraudulent Link'
        },
        {
          code: 'FRA',
          name: 'Fraudulent App'
        },
        {
          code: 'EHC',
          name: 'Email Hacking / Compromise'
        },
        {
          code: 'FMP',
          name: 'Forgery / Modification of Payment'
        },
        {
          code: 'MRC',
          name: 'Merchant Collusion'
        },
        {
          code: 'CLR',
          name: 'Collect Payment Request'
        },
        {
          code: 'OTH',
          name: 'Other'
        }
      ],
      pmtChannels: [
        {
          code: 'BRN',
          name: 'Branch'
        },
        {
          code: 'INT',
          name: 'Internet (Online)'
        },
        {
          code: 'MBL',
          name: 'Mobile'
        },
        {
          code: 'ITB',
          name: 'Internet Banking'
        },
        {
          code: 'MOB',
          name: 'Mobile Banking'
        },
        {
          code: 'ATM',
          name: 'ATM'
        },
        {
          code: 'POS',
          name: 'POS'
        },
        {
          code: 'BCA',
          name: 'BC Agent'
        },
        {
          code: 'IVR',
          name: 'IVR'
        },
        {
          code: 'MOT',
          name: 'MOTO'
        },
        {
          code: 'OTH',
          name: 'Others'
        }
      ],
      pmtInstruments: [
        {
          code: 'BNK',
          name: 'Bank Account'
        },
        {
          code: 'PAI',
          name: 'Paper Instruments'
        },
        {
          code: 'DEC',
          name: 'Debit Cards (including tokenised debit card or virtual debit card)'
        },
        {
          code: 'CRC',
          name: 'Credit Cards (including tokenised credit card or virtual credit card)'
        },
        {
          code: 'PPI',
          name: 'Pre-paid Payment Instruments (wallet or physical card)'
        },
        {
          code: 'OTH',
          name: 'Other'
        }
      ],
      pmtCategories: [
        {
          code: 'ROP',
          name: 'RBI Operated Payment Systems (RTGS / NEFT)'
        },
        {
          code: 'NOP',
          name:
            'NPCI Operated Payment Systems (IMPS, NACH, UPI, BBPS, NETC, CTS, AEPS, BHIM Aadhaar Pay)'
        },
        {
          code: 'CAN',
          name: 'Card Networks (VISA, Mastercard, Rupay, Diners, Amex)'
        },
        {
          code: 'ATM',
          name: 'ATM Networks'
        },
        {
          code: 'PII',
          name: 'Prepaid Payment Instrument Issuers'
        },
        {
          code: 'CMO',
          name: 'Cross-Border Money Transfer Operators'
        },
        {
          code: 'TRD',
          name: 'Trade Receivables Discounting System (TReDS)'
        },
        {
          code: 'IMO',
          name: 'Instant Money Transfer Operators'
        },
        {
          code: 'INB',
          name: 'Intra-Bank (Banks’ Core Banking System)'
        },
        {
          code: 'OTH',
          name: 'Other'
        }
      ],
      pmtSystemResponses: [
        {
          categoryCode: 'CAN',
          pmtSystems: [
            {
              code: 'American Express Banking Corp., USA',
              name: 'Card Networks'
            },
            {
              code: 'Diners Club International Ltd.,USA',
              name: 'Card Networks'
            },
            {
              code: 'MasterCard Asia / Pacific Pte. Ltd., Singapore',
              name: 'Card Networks'
            },
            {
              code: 'National Payments Corporation of India (RuPay)',
              name: 'Card Networks'
            },
            {
              code: 'Visa Worldwide Pte. Limited, Singapore',
              name: 'Card Networks'
            }
          ]
        },
        {
          categoryCode: 'ROP',
          pmtSystems: [
            {
              code: 'Real Time Gross Settlement',
              name: 'RBI operated payment systems'
            },
            {
              code: 'National Electronic Funds Transfer',
              name: 'RBI operated payment systems'
            }
          ]
        },
        {
          categoryCode: 'PII',
          pmtSystems: [
            {
              code: 'Prepaid Payment Instrument Issuers– Not Applicable',
              name: 'Prepaid Payment Instrument Issuers'
            }
          ]
        },
        {
          categoryCode: 'NOP',
          pmtSystems: [
            {
              code: 'Immediate Payment Service',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'National Automated Clearing House',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'Unified Payments Interface',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'Bharat Bill Payment System',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'National Electronic Toll Collection',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'Cheque Truncation System',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'Aadhaar enabled Payment System',
              name: 'NPCI Operated Payment Systems'
            },
            {
              code: 'BHIM Aadhaar Pay',
              name: 'NPCI Operated Payment Systems'
            }
          ]
        },
        {
          categoryCode: 'INB',
          pmtSystems: [
            {
              code: 'Intra-Bank – Not Applicable',
              name: 'Intra-Bank'
            }
          ]
        },
        {
          categoryCode: 'IMO',
          pmtSystems: [
            {
              code: 'Instant Money Transfer Operators – Not Applicable',
              name: 'Instant Money Transfer Operators'
            }
          ]
        },
        {
          categoryCode: 'TRD',
          pmtSystems: [
            {
              code: 'A.TREDS Limited',
              name: 'TReDS'
            },
            {
              code: 'Mynd Solutions Private Limited',
              name: 'TReDS'
            },
            {
              code: 'Receivables Exchange of India Limited (RXIL)',
              name: 'TReDS'
            }
          ]
        },
        {
          categoryCode: 'ATM',
          pmtSystems: [
            {
              code: 'Bank of India',
              name: 'ATM Networks'
            },
            {
              code: 'Euronet Services India Private Limited',
              name: 'ATM Networks'
            },
            {
              code: 'National Payments Corporation of India (NFS)',
              name: 'ATM Networks'
            },
            {
              code: 'Punjab National Bank',
              name: 'ATM Networks'
            },
            {
              code: 'State Bank of India',
              name: 'ATM Networks'
            },
            {
              code: 'Other – On Us Transaction',
              name: 'ATM Networks'
            }
          ]
        },
        {
          categoryCode: 'CMO',
          pmtSystems: [
            {
              code: 'Bahrain Financing Company, BSC (C)',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Continental Exchange Solutions Inc, USA',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Fast Encash Money Transfer Services Ltd.',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Mastercard Transaction Services (Canada) Inc.',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'MoneyGram Payment Systems Inc, USA',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Muthoot Finserve USA Inc.',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'UAE Exchange Centre LLC, UAE',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Wall Street Exchange Centre LLC, UAE',
              name: 'Cross-Border Money Transfer Operators'
            },
            {
              code: 'Western Union Financial Services Incorporated, USA',
              name: 'Cross-Border Money Transfer Operators'
            }
          ]
        },
        {
          categoryCode: 'OTH',
          pmtSystems: [
            {
              code: 'Others - Not Applicable',
              name: 'Others'
            }
          ]
        }
      ],
      download:
        'PFR:U:010:08092023:1;IC000000000000000018|N|N|BNK|ROP|RTGS|INT|PHH|04092023|04092023|04092023|||||issuTxn20230830077|Y|NA|**********|email||N||N||15000.0||||||||||||||||||||||||||||||||||||N||||'
    },
    closedList: [
      {
        caseId: 555993,
        caseRefNo: 'c93e61fb-43da-4eae-b9de-5465956fbea7',
        txnId: 'issuTxn20230830140',
        txnAmount: 15000,
        txnTimestamp: '2023-09-11T17:55:09',
        entityId: 'CUST2',
        entityCategory: 'Customer',
        weight: 30,
        createdTimestamp: '2023-09-11T10:31:46.245',
        assignmentTimeStamp: '2023-09-11T12:18:42.705',
        investigationVerdict: 'Confirmed Fraud',
        ifrmVerdict: 'REJECTED',
        investigationStatus: 'Closed',
        terminalId: '1189621',
        deviceId: '',
        payerId: 'CUST2',
        payeeId: 'merchant00102, Reliance Enterprises',
        payeeMcc: '5199, Meat Shop',
        channelId: 'NB, NB',
        txnType: 'issuTxn20230830140, P2U – credit',
        responseCode: '00, Approved',
        senderMaskedCard: 'NA',
        senderHashedCard: '****************',
        reViolatedRules:
          '3eabd43f-ddc4-49db-ba71-2ba06ba780ab,277d59a6-7fbf-4f7a-815c-0b2570d1bcb0',
        bucketId: 1,
        attribute1: '13-07-2023',
        attribute2: '1372673',
        ifrmPostauthVerdictName: 'REJECTED',
        caseStage: 'Checker',
        assignedTo: 'akash',
        partnerId: 1,
        channel: 'frm',
        makerAction: '',
        checkerAction: ''
      }
    ],
    history: []
  },
  oneView: {
    case: {
      caseId: 308,
      caseRefNo: 'af8e3388-30e4-43a8-8783-143123511dc4',
      txnId: 'issuerTxn20231016207',
      txnAmount: 110001,
      txnTimestamp: '2023-10-16T20:00:07',
      entityId: 'cust13',
      entityCategory: 'Customer',
      weight: 1,
      createdTimestamp: '2023-10-16T11:04:07.011',
      assignmentTimeStamp: '2023-10-16T16:38:41.265',
      investigationVerdict: 'NA',
      ifrmVerdict: 'REJECTED',
      investigationStatus: 'Open',
      terminalId: '1189621',
      deviceId: '',
      payerId: 'cust4, cust13',
      payeeId: 'merchant15',
      payeeMcc: '4722, Travel Industry',
      channelId: '42, UPI',
      txnType: '00, Purchase',
      responseCode: ' , PreAuth',
      senderMaskedCard: 'NA',
      senderHashedCard: '****************',
      reViolatedRules: ['d6eae84d-8f3e-458e-bc23-0701797f66a7'],
      bucketId: 4,
      ifrmPostauthVerdictName: 'N/A',
      caseStage: 'Reviewer',
      assignedTo: 'akashm',
      partnerId: 1,
      channel: 'frm',
      makerAction: '',
      checkerAction: ''
    }
  },
  partnerBanks: [
    {
      id: 1,
      partnerName: 'SHIVALIK BANK',
      isActive: 1,
      registrationDate: '2023-09-18 04:55:31.347395'
    },
    {
      id: 2,
      partnerName: 'YES BANK',
      isActive: 1,
      registrationDate: '2023-09-18 06:24:06.0'
    },
    {
      id: 3,
      partnerName: 'CO-OP BANK',
      isActive: 1,
      registrationDate: '2023-09-18 06:24:52.0'
    }
  ],
  rfiReports: {
    highValueClosedAccount: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-04-25 14:17:58.0',
            accountClosingDate: '2023-06-25 14:17:58.0',
            merchantID: 'merchant2',
            merchantName: 'merchant',
            merchantEmail: null,
            merchantContactNumber: '**********',
            totalCreditTxnCount: 62,
            totalCreditTxnAmount: 933708,
            totalDebitTxnCount: 3,
            totalDebitTxnAmount: 140000
          },
          {
            accountOpeningDate: '2023-04-25 14:17:58.0',
            accountClosingDate: '2023-06-25 14:17:58.0',
            merchantID: 'merchant1',
            merchantName: 'merchant',
            merchantEmail: null,
            merchantContactNumber: '**********',
            totalCreditTxnCount: 24,
            totalCreditTxnAmount: 185833,
            totalDebitTxnCount: 0,
            totalDebitTxnAmount: 0
          }
        ]
      }
    },
    highValueNewAccount: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-06-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'John Doe',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            txnCountInPrevMonth: 103,
            txnAmountInPrevMonth: 1455668.0
          },
          {
            accountOpeningDate: '2023-07-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'Jane Smith',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            txnCountInPrevMonth: 104,
            txnAmountInPrevMonth: 1355668.0
          }
        ]
      }
    },
    fraudToSaleRatio: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-06-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'John Doe',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            totalTxnAmount: 1500,
            totalFraudAmount: 1000,
            f2sRatio: 66.67
          },
          {
            accountOpeningDate: '2023-07-01 14:29:34',
            merchantID: 'merchant12',
            merchantName: 'JaneSmith',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            totalTxnAmount: 1600,
            totalFraudAmount: 1100,
            f2sRatio: 66.67
          }
        ]
      }
    },
    topMerchant: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-06-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'John Doe',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            customerIdentifier: 'abc',
            totalTxnCountByCustomer: 145,
            totalTxnAmountByCustomer: 145333,
            totalMonthlyTxnCountByMerchant: 150,
            totalMonthlyTxnAmountByMerchant: 150666
          },
          {
            accountOpeningDate: '2023-07-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'Jane Smith',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            customerIdentifier: 'def',
            totalTxnCountByCustomer: 147,
            totalTxnAmountByCustomer: 155333,
            totalMonthlyTxnCountByMerchant: 150,
            totalMonthlyTxnAmountByMerchant: 160666
          }
        ]
      }
    },
    nbfcTrxns: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-08-18 16:48:04.0',
            merchantID: 'merchant78',
            merchantName: 'merchant78',
            merchantEmail: null,
            merchantContactNumber: '**********',
            totalCreditTxnCount: 13,
            totalCreditTxnAmount: 81746,
            totalDebitTxnCount: 0,
            totalDebitTxnAmount: 0
          },
          {
            accountOpeningDate: '2023-08-18 16:48:04.0',
            merchantID: 'merchant77',
            merchantName: 'merchant77',
            merchantEmail: null,
            merchantContactNumber: '**********',
            totalCreditTxnCount: 13,
            totalCreditTxnAmount: 55255,
            totalDebitTxnCount: 0,
            totalDebitTxnAmount: 0
          },
          {
            accountOpeningDate: '2023-08-18 16:48:04.0',
            merchantID: 'merchant76',
            merchantName: 'merchant76',
            merchantEmail: null,
            merchantContactNumber: '**********',
            totalCreditTxnCount: 13,
            totalCreditTxnAmount: 57414,
            totalDebitTxnCount: 0,
            totalDebitTxnAmount: 0
          }
        ]
      }
    },
    hrcTrxns: {
      count: {
        count: 14
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2022-08-25 14:17:58.0',
            merchantID: 'merchant10',
            merchantName: 'merchant',
            merchantEmail: null,
            merchantContactNumber: '**********',
            customerIdentifier: 'CUST2',
            totalCreditTxnCountByCustomer: 1,
            totalCreditTxnAmountByCustomer: 5199
          },
          {
            accountOpeningDate: '2008-04-25 14:17:58.0',
            merchantID: 'merchant21112',
            merchantName: 'Bank',
            merchantEmail: 'test.com',
            merchantContactNumber: '**********',
            customerIdentifier: 'CUST1',
            totalCreditTxnCountByCustomer: 58,
            totalCreditTxnAmountByCustomer: 5800000
          },
          {
            accountOpeningDate: '2022-08-25 14:17:58.0',
            merchantID: 'merchant234',
            merchantName: 'merchant',
            merchantEmail: null,
            merchantContactNumber: '**********',
            customerIdentifier: 'CUST2',
            totalCreditTxnCountByCustomer: 1,
            totalCreditTxnAmountByCustomer: 5199
          }
        ]
      }
    },
    unusualDeclineTurnover: {
      count: {
        count: 2
      },
      data: {
        merchantDetails: [
          {
            accountOpeningDate: '2023-06-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'John Doe',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            txnCountInPrevMonth: 103,
            txnAmountInPrevMonth: 1455668,
            txnCountInCurrentMonth: 90,
            txnAmountInCurrentMonth: 1055668
          },
          {
            accountOpeningDate: '2023-07-01 14:29:34',
            merchantID: 'merchant11',
            merchantName: 'Jane Smith',
            merchantEmail: '<EMAIL>',
            merchantContactNumber: '**********',
            txnCountInPrevMonth: 104,
            txnAmountInPrevMonth: 1355668,
            txnCountInCurrentMonth: 80,
            txnAmountInCurrentMonth: 955668
          }
        ]
      }
    }
  },
  ruleDashboard: {
    stats: {
      totalViolations: 15,
      notFraudCases: 3,
      fraudCases: 2,
      totalFraudAmount: 5000.0
    },
    efficiency: {
      ruleEfficiency: [
        {
          time: '2023-07-09 00:00:00',
          alert: 15,
          manualCases: 2,
          confirmedFraud: 3,
          efficiency: 80.0
        },
        {
          time: '2023-07-10 00:00:00',
          alert: 21,
          manualCases: 2,
          confirmedFraud: 6,
          efficiency: 71.42
        }
      ]
    },
    efficacy: {
      ruleEfficacy: 56.8
    },
    effectiveness: {
      alertRates: [
        {
          time: '2023-06-09',
          totalTxn: 100,
          alert: 55,
          manualAlert: 5
        },
        {
          time: '2023-06-10',
          totalTxn: 100,
          alert: 85,
          manualAlert: 8
        }
      ]
    },
    behaviour: [
      {
        payeeMccCodeName: '6540',
        txnTypeName: 'Purchase',
        entityCategory: 'Customer',
        responseCodeName: 'No Response',
        txnTimestamp: '2023-07-06 17:52:04.0',
        ifrmVerdict: 'REJECTED',
        txnId: 'iuccTxn20230706804',
        ifrmPostauthVerdictName: 'N/A',
        payerMccCodeName: 'NA',
        txnAmount: 500,
        caseVerdict: 'NA'
      },
      {
        payeeMccCodeName: '6540',
        txnTypeName: 'Purchase',
        entityCategory: 'Customer',
        responseCodeName: 'No Response',
        txnTimestamp: '2023-07-06 17:52:03.0',
        ifrmVerdict: 'REJECTED',
        txnId: 'iuccTxn20230706803',
        ifrmPostauthVerdictName: 'N/A',
        payerMccCodeName: 'NA',
        txnAmount: 500,
        caseVerdict: 'NA'
      },
      {
        payeeMccCodeName: '6540',
        txnTypeName: 'Purchase',
        entityCategory: 'Customer',
        responseCodeName: 'No Response',
        txnTimestamp: '2023-07-06 17:52:02.0',
        ifrmVerdict: 'REJECTED',
        txnId: 'iuccTxn20230706802',
        ifrmPostauthVerdictName: 'N/A',
        payerMccCodeName: 'NA',
        txnAmount: 500,
        caseVerdict: 'NA'
      }
    ]
  },
  snoozeRules: {
    attributes: [
      {
        key: 'entityId',
        value: 'entityId',
        dataType: 'text'
      },
      {
        key: 'txnAmount',
        value: 'txnAmount',
        dataType: 'numeric'
      }
    ],
    list: [
      {
        code: '15dd9b65-549d-44e1-b922-aa065d98766e',
        name: 'BenfordLawCheck',
        userId: 23,
        userName: 'checker',
        comments: 'Rule to be snoozed!',
        isApproved: 0,
        snoozeUntil: '2019-10-11 15:08:31',
        createdAt: '2019-10-11 15:08:31',
        attributeList: 'mer~=~1234,id~<=~1234'
      },
      {
        code: '15dd9b65-549d-44e1-b922-aa065d98766e',
        name: 'BenfordLawCheck2',
        userId: 23,
        userName: 'checker',
        comments: 'Rule to be snoozed!',
        isApproved: 1,
        snoozeUntil: '2019-10-11 15:08:31',
        createdAt: '2019-10-11 15:08:31',
        attributeList: 'mer~=~1234,id~<=~1234'
      }
    ]
  },
  sandboxing: {
    dateRange: {
      startTimestamp: '2022-09-25T10:19:44',
      endTimestamp: '2022-09-30T18:12:36'
    },
    testingMsg: 'Test Started',
    testing: [
      {
        date: '2021-04-27T10:12:01',
        alerts: [
          {
            rule: 'BenfordLawCheck',
            noOfTxns: 20
          },
          {
            rule: 'TransactionFrequenciesLargeAmountCheck',
            noOfTxns: 15
          },
          {
            rule: 'HenfordLawCheck',
            noOfTxns: 2
          }
        ]
      },
      {
        date: '2021-04-28T10:12:01',
        alerts: [
          {
            rule: 'BenfordLawCheck',
            noOfTxns: 5
          },
          {
            rule: 'HenfordLawCheck',
            noOfTxns: 24
          }
        ]
      },
      {
        date: '2021-04-29T10:12:01',
        alerts: [
          {
            rule: 'BenfordLawCheck',
            noOfTxns: 22
          },
          {
            rule: 'TransactionFrequenciesLargeAmountCheck',
            noOfTxns: 19
          },
          {
            rule: 'HenfordLawCheck',
            noOfTxns: 1
          }
        ]
      }
    ],
    violationDetails: {
      records: [
        {
          isOnUs: '0',
          payeeMmid: '451738',
          payeeAccountType: 'accno203a1',
          payerMcc: '2200',
          txnCurrency: '356',
          payeeType: 'ENTITY',
          payeeVpa: 'vp123',
          payerAccountTypeName: '16, NetBanking',
          txnType: '09',
          deviceId: '07803egLZFFg080U',
          payeeMccCodeName: '5411, others',
          merchantName: 'cust2',
          responseCode: ' ',
          createdAt: '2023-09-06T11:48:21.113979',
          txnTypeName: '09, M2P',
          isLien: '',
          entityCategory: 'Customer',
          payeeId: 'merchant448, cust2',
          acquirerId: 'acquirerId',
          payeeDeviceOf: 'M',
          longitude: 16.14264,
          payerMmidMobileNumber: '**********',
          isMerchantApiBased: '0',
          mti: '200',
          payeeCardNumberMask: '***************',
          reViolatedRules: [],
          payeeMobileNumber: '**********',
          responseCodeName: ' ',
          initiatorId: '466266',
          sourceInstitutionId: '2107',
          payerVpa: 'pvc123',
          txnTimestamp: '2022-09-17T17:20:36',
          payerCardNumberHash: '***************',
          ifrmVerdict: 'ACCEPTED',
          initiatorMobile: '**********',
          paymentMethod: '05',
          referenceTxnId: '',
          txnId: 'R74P10fe617a1-f32c-4ca8-b054-c2cc66b3eb03000032',
          initiatorType: '',
          txnCategoryId: '',
          agentId: '',
          cognitiveResponse: '{"cognitiveIsSuspicious":0}',
          deviceOs: 'Windows phone OS',
          latitude: 65.784025,
          isCognitiveActive: 0,
          payeeCardNumberHash: '***************',
          payerId: 'CUST2',
          terminalId: '3865306',
          payeeBankIfsc: 'ZXza0866367',
          payerBankIfsc: 'xITf0343878',
          caseType: 'frm',
          payerAccountNumber: '**********',
          subAgentId: '',
          payerAccountType: '16',
          payerCardNumberMask: '**************',
          payeeMcc: '5411',
          ifrmPostauthVerdictName: 'N/A',
          channelId: '0',
          payeeAccountNumber: '**********',
          customerIp: '*************',
          currentStatus: '',
          txnMessageId: 'iuca1248',
          payeeAccountTypeName: 'accno203a1, acctype',
          payerMccCodeName: '2200, Other',
          payerMmid: '990680',
          entityId: 'CUST2',
          payeeMmidMobileNumber: '**********',
          txnCategoryName: '',
          payerType: 'PERSON',
          channelName: '0, iuca',
          txnAmount: 1000,
          remarks: '',
          payerMobileNumber: '**********'
        }
      ],
      isLastPage: true,
      count: 1
    },
    testHistory: []
  },
  settings: [
    { key: 'ReportingEntityName', value: 'ICICI' },
    { key: 'ReportingEntityCategory', value: 'BAPUB' },
    { key: 'RERegistrationNumber', value: '************' },
    { key: 'FIUREID', value: 'XXXXXNNNNN' },
    { key: 'POName', value: 'Akash' },
    { key: 'PODesignation', value: 'QA' },
    { key: 'Address', value: 'kharadi nagar, pune district' },
    { key: 'City', value: 'Pune' },
    { key: 'StateCode', value: 'MH' },
    { key: 'PinCode', value: '410206' },
    { key: 'CountryCode', value: 'IN' },
    { key: 'Telephone', value: '0212-343453455345435345' },
    { key: 'Mobile', value: '+************' },
    { key: 'Fax', value: '022-********' },
    { key: 'POEmail', value: '<EMAIL>' }
  ],
  configurations: {
    cognitive: 0,
    holdAndRelease: 0,
    provisionalFields: 0,
    knowageReport: 0,
    caseCriteria: 1
  },
  slaDashboard: {
    shiftDetails: {
      shiftId: 1,
      shiftName: 'afternoon',
      shiftStartTime: '2022-06-24 12:00:00.000',
      shiftEndTime: '2022-06-24 18:00:00.000',
      users: [
        {
          userId: 123,
          userName: 't_test',
          firstName: 'Test',
          lastName: 'Test'
        }
      ]
    },
    slaKpis: {
      monthlyResolvedCount: 15,
      monthlySLAViolationsCount: 3,
      monthlySuccessRateCount: 2,
      unassignedCount: 10,
      openCount: 5,
      resolvedCount: 15,
      holdCount: 3,
      fraudCount: 2,
      notFoundCount: 0,
      slaViolations: 0,
      successRate: 2
    },
    employeeSla: {
      analysts: [
        {
          userId: 1,
          name: 'Chirag',
          todayCasesCount: 5,
          totalCasesCount: 20,
          timeTakenInMinutes: 120.5
        },
        {
          userId: 2,
          name: 'Amar',
          todayCasesCount: 10,
          totalCasesCount: 50,
          timeTakenInMinutes: 180.3
        }
      ],
      average: 5
    },
    occupancyRate: {
      totalUser: 17,
      logedInUser: 6
    },
    slaBreachCases: {
      transactions: [
        {
          sla: '2',
          analyst: 'John Doe',
          transactionTimestamp: '2023-06-23T15:30:00.000Z',
          transactionId: 'ABC123',
          amount: 100.0,
          customerId: '123456789',
          closeVerdictBucket: 'notFraud'
        },
        {
          sla: '1',
          analyst: 'Jane Smith',
          transactionTimestamp: '2023-06-23T16:45:00.000Z',
          transactionId: 'DEF456',
          amount: 200.0,
          customerId: '987654321',
          closeVerdictBucket: 'fraud'
        }
      ]
    },
    firstContactRate: {
      resolutionRate: 56.8
    },
    analystTAT: {
      userId: 12,
      slaTimeTaken: [
        {
          timeInterval: '2023-10-11T00:00:00',
          timetaken: -619.23,
          assignedCasesCount: 5
        },
        {
          timeInterval: '2023-10-03T00:00:00',
          timetaken: -1175.3,
          assignedCasesCount: 10
        },
        {
          timeInterval: '2023-10-13T00:00:00',
          timetaken: -301.71,
          assignedCasesCount: 43
        },
        {
          timeInterval: '2023-10-09T00:00:00',
          timetaken: 6177.69,
          assignedCasesCount: 3
        },
        {
          timeInterval: '2023-10-06T00:00:00',
          timetaken: 1742.08,
          assignedCasesCount: 15
        },
        {
          timeInterval: '2023-10-05T00:00:00',
          timetaken: 994.59,
          assignedCasesCount: 8
        },
        {
          timeInterval: '2023-10-04T00:00:00',
          timetaken: 1885.27,
          assignedCasesCount: 20
        },
        {
          timeInterval: '2023-10-16T00:00:00',
          timetaken: -970.93,
          assignedCasesCount: 7
        },
        {
          timeInterval: '2023-10-12T00:00:00',
          timetaken: 5458.16,
          assignedCasesCount: 7
        }
      ]
    }
  },
  strReport: {
    masters: {
      reportingEntity: [
        {
          key: 'ReportingEntityName',
          value:
            'Complete name of the reporting entity (Bank, financial institution, intermediary).'
        },
        {
          key: 'ReportingEntityCategory',
          value:
            'The category to which the Reporting entity belongs. FIU has provided a five digit category code to specify the category of the reporting entity. Example: BAPUB for Public Sector banks, Refer section 11.1.3.1 for details on enumerations.'
        },
        {
          key: 'RERegistrationNum',
          value:
            'Any unique number for the Reporting Entity. This number can be the registration number or any number used in correspondence with the regulator. This number will be used during verification of the registration of the reporting entity and in correspondence with the regulators. If the regulator has not issued any number, the reporting entity may use any other self generated number.'
        },
        {
          key: 'FIUREID',
          value:
            'Unique ID issued by FIU. FIU will communicate the FIUREID during the registration of the reporting entity on the FINnet Gateway Portal. The FIUREID will be in the fomat XXXXXNNNNN where XXXXX is generated in accordance with section 11.1.3.1 and NNNNN is a sequentially generated numder. Use XXXXXNNNNN till the ID is communicated.'
        }
      ],
      reasonRevision: [
        {
          key: 'A',
          value:
            'Acknowledgement of original batch had many fatal, non fatal or probable errors which are being resolved'
        },
        {
          key: 'B',
          value:
            'Operational errors in original batch have been identified and reports are being revised or deleted suo moto'
        },
        {
          key: 'C',
          value: 'The replacement report is on account of additional information being submitted'
        },
        {
          key: 'N',
          value: 'Not applicable as this is a new batch'
        },
        {
          key: 'Z',
          value: 'Other reason'
        }
      ],
      sourceOfAlert: [
        {
          key: 'CV',
          value: 'Customer Verification'
        },
        {
          key: 'WL',
          value: 'Watch List'
        },
        {
          key: 'TY',
          value: 'Typology'
        },
        {
          key: 'TM',
          value: 'Transaction Monitoring'
        },
        {
          key: 'RM',
          value: 'Risk Management System'
        },
        {
          key: 'MR',
          value: 'Media Reports'
        },
        {
          key: 'LQ',
          value: 'Law Enforcement Agency Query'
        },
        {
          key: 'El',
          value: 'Employee Initiated'
        },
        {
          key: 'PC',
          value: 'Public Complaint'
        },
        {
          key: 'BA',
          value: 'Business Associates'
        },
        {
          key: 'ZZ',
          value: 'Others'
        },
        {
          key: 'XX',
          value: 'Not Categorised'
        }
      ],
      leaInformed: [
        {
          key: 'R',
          value: 'Information received'
        },
        {
          key: 'S',
          value: 'Information sent'
        },
        {
          key: 'N',
          value: 'No correspondence sent or received'
        },
        {
          key: 'X',
          value: 'Not categorised'
        }
      ],
      priorityRating: [
        {
          key: 'P1',
          value: 'Very High Priority'
        },
        {
          key: 'P2',
          value: 'High Priority'
        },
        {
          key: 'P3',
          value: 'Normal Priority'
        },
        {
          key: 'XX',
          value: 'Not categorised'
        }
      ],
      reportCoverage: [
        {
          key: 'C',
          value: 'Complete'
        },
        {
          key: 'P',
          value: 'Partial'
        },
        {
          key: 'X',
          value: 'Not categorised'
        }
      ],
      batchType: [
        {
          key: 'N',
          value: 'New Report'
        },
        {
          key: 'D',
          value: 'Deletion Report'
        },
        {
          key: 'R',
          value: 'Replacement Report'
        }
      ]
    },
    details: {
      caseRefNo: '99afd673-5417-4711-acce-09a3048b6202',
      batchType: 'N',
      reasonOfRevision: 'N',
      mainPersonName: 'Akash',
      sourceOfAlert: 'RM',
      alertIndicator: ['Activity in Dormant Account'],
      suspicionDueToProceedsOfCrime: 'Y',
      suspicionDueToComplexTrans: 'Y',
      suspicionDueToNoEcoRationale: 'N',
      suspicionOfFinancingOfTerrorism: 'N',
      attemptedTransaction: 'Y',
      leaInformed: 'S',
      leaDetails: 'Suraj Kharadi',
      priorityRating: 'P1',
      reportCoverage: 'C',
      additionalDocuments: 'Y'
    },
    history: [
      {
        userId: 123,
        userName: 'Analyst-123',
        userRole: 'Analyst',
        xmlFileContent: '<xmlContent></xmlContent>',
        xmlDownloadTimestamp: '2020-05-14 14:18:01',
        fileVersion: 1.0
      },
      {
        userId: 456,
        userName: 'PO-456',
        userRole: 'PO',
        xmlFileContent: '<xmlContent></xmlContent>',
        xmlDownloadTimestamp: '2020-05-14 14:18:01',
        fileVersion: 1.0
      }
    ]
  },
  statisticsDetails: {
    customerStatistics: {
      errMsg: '',
      data: [
        {
          key: 'bin',
          values: [
            {
              code: '133245',
              name: '133245',
              count: 1,
              amount: 2000
            }
          ]
        },
        {
          key: 'brand',
          values: [
            {
              code: '',
              name: '',
              count: 27,
              amount: 445400
            },
            {
              code: 'AIRPLUS',
              name: 'AIRPLUS',
              count: 1,
              amount: 2000
            },
            {
              code: 'Br1',
              name: 'Br1',
              count: 1,
              amount: 2000
            },
            {
              code: 'PRIVATE LABEL CARD',
              name: 'PRIVATE LABEL CARD',
              count: 162,
              amount: 2816200
            }
          ]
        },
        {
          key: 'type',
          values: [
            {
              code: '',
              name: '',
              count: 27,
              amount: 445400
            },
            {
              code: 'DEBIT',
              name: 'DEBIT',
              count: 1,
              amount: 2000
            },
            {
              code: 'Debit',
              name: 'Debit',
              count: 162,
              amount: 2816200
            },
            {
              code: 'Type',
              name: 'Type',
              count: 1,
              amount: 2000
            }
          ]
        },
        {
          key: 'category',
          values: [
            {
              code: '',
              name: '',
              count: 27,
              amount: 445400
            },
            {
              code: 'Card',
              name: 'Card',
              count: 162,
              amount: 2816200
            },
            {
              code: 'Category',
              name: 'Category',
              count: 1,
              amount: 2000
            },
            {
              code: 'PREPAID',
              name: 'PREPAID',
              count: 1,
              amount: 2000
            }
          ]
        },
        {
          key: 'country',
          values: [
            {
              code: '',
              name: '',
              count: 27,
              amount: 445400
            },
            {
              code: 'Country',
              name: 'Country',
              count: 1,
              amount: 2000
            },
            {
              code: 'India',
              name: 'India',
              count: 162,
              amount: 2816200
            },
            {
              code: 'JAPAN',
              name: 'JAPAN',
              count: 1,
              amount: 2000
            }
          ]
        }
      ]
    },
    transactionStatistics: {
      errMsg: '',
      data: [
        {
          key: 'txnType',
          values: [
            {
              code: '28',
              name: 'Load Money',
              count: 9,
              amount: 504
            }
          ]
        },
        {
          key: 'responseName',
          values: [
            {
              code: '00',
              name: 'Approved or completed successfully or Normal',
              count: 9,
              amount: 504
            }
          ]
        },
        {
          key: 'hour',
          values: [
            {
              code: '9',
              name: '9',
              count: 3,
              amount: 168
            },
            {
              code: '11',
              name: '11',
              count: 3,
              amount: 168
            },
            {
              code: '15',
              name: '15',
              count: 3,
              amount: 168
            }
          ]
        },
        {
          key: 'dow',
          values: [
            {
              code: 'Tuesday',
              name: 'Tuesday',
              count: 1,
              amount: 56
            },
            {
              code: 'Wednesday',
              name: 'Wednesday',
              count: 1,
              amount: 56
            },
            {
              code: 'Thursday',
              name: 'Thursday',
              count: 6,
              amount: 336
            },
            {
              code: 'Sunday',
              name: 'Sunday',
              count: 1,
              amount: 56
            }
          ]
        }
      ]
    }
  }
};
