import {
  ON_LOGOUT_FAILURE,
  ON_SUCCESSFUL_LOGIN,
  ON_SUCCESSFUL_CHECK_ROLE,
  ON_RESET_LOGIN_FORM,
  ON_TOGGLE_LOGOUT_MODAL,
  ON_TOGGLE_SESSION_IDLE,
  ON_RESET_SESSION_TIMEOUT,
  ON_SUCCESSFUL_FETCH_ALL_ROLES,
  ON_SUCCESSFUL_FETCH_LOGIN_TYPES,
  ON_UPDATE_PASSWORD_SUCCESS,
  ON_2FA_AUTHENTICATE_USER_SUCCESS,
  ON_2FA_AUTHENTICATE_OTP_SUCCESS
} from 'constants/actionTypes';
import initialState from './initialState';
import objectAssign from 'object-assign';
import { IDEL_TIMER_VALUES } from 'constants/applicationConstants';

const setSessionTimeout = () =>
  new Date().setMinutes(new Date().getMinutes() + IDEL_TIMER_VALUES.timeRemaining / 60000);

export default function authReducer(state = initialState.auth, action) {
  switch (action.type) {
    case ON_SUCCESSFUL_CHECK_ROLE:
      return objectAssign({}, state, {
        userCreds: objectAssign({}, state.userCreds, { userName: action.userName }),
        availableRoles: action.response
      });
    case ON_RESET_LOGIN_FORM:
      return initialState.auth;
    case ON_SUCCESSFUL_LOGIN:
      return objectAssign({}, state, {
        userCreds: {
          userId: action.response.id,
          email: action.response.email,
          userName: action.userDetails.userName,
          channelRoles:
            action.userDetails.effectiveRole == 'super-admin' ||
            action.userDetails.effectiveRole == 'admin'
              ? [`${action.userDetails.effectiveRole}`]
              : [`${action.userDetails.channel}:${action.userDetails.effectiveRole}`],
          roles: action.userDetails.effectiveRole,
          channels: [action.userDetails.channel],
          isFirstLogin: action.response.isFirstLogin
        },
        session: {
          isLoggedIn: true,
          sessionTimeout: setSessionTimeout(),
          isIdle: false,
          logoutModal: false
        },
        moduleType: action.response.moduleType,
        loginType: action.response?.loginType || '',
        errorMessage: 'Successfully logged in'
      });
    case ON_2FA_AUTHENTICATE_USER_SUCCESS:
      return objectAssign({}, state, {
        userCreds: objectAssign({}, state.userCreds, {
          userName: action.userDetails.userName,
          channelRoles:
            action.userDetails.effectiveRole == 'admin'
              ? [`${action.userDetails.effectiveRole}`]
              : [`${action.userDetails.channel}:${action.userDetails.effectiveRole}`],
          roles: action.userDetails.effectiveRole,
          channels: [action.userDetails.channel]
        })
      });
    case ON_2FA_AUTHENTICATE_OTP_SUCCESS:
      return objectAssign({}, state, {
        userCreds: objectAssign({}, state.userCreds, {
          userId: action.response.id,
          email: action.response.email,
          isFirstLogin: action.response.isFirstLogin
        }),
        session: {
          isLoggedIn: true,
          sessionTimeout: setSessionTimeout(),
          isIdle: false,
          logoutModal: false
        },
        moduleType: action.response.moduleType,
        loginType: action.response?.loginType || '',
        errorMessage: 'Successfully logged in'
      });
    case ON_TOGGLE_LOGOUT_MODAL:
      return objectAssign({}, state, {
        session: objectAssign({}, state.session, { logoutModal: action.newState })
      });
    case ON_TOGGLE_SESSION_IDLE:
      return objectAssign({}, state, {
        session: objectAssign({}, state.session, { isIdle: action.newState })
      });
    case ON_RESET_SESSION_TIMEOUT:
      return objectAssign({}, state, {
        session: objectAssign({}, state.session, {
          sessionTimeout: setSessionTimeout()
        })
      });
    case ON_LOGOUT_FAILURE:
      return objectAssign({}, state, {
        errorMessage: action.response?.message || 'Unknown error'
      });
    case ON_SUCCESSFUL_FETCH_ALL_ROLES:
      return objectAssign({}, state, {
        allRoles: action.response
      });
    case ON_SUCCESSFUL_FETCH_LOGIN_TYPES:
      return objectAssign({}, state, {
        loginTypes: action.response
      });
    case ON_UPDATE_PASSWORD_SUCCESS: {
      return objectAssign({}, state, {
        userCreds: objectAssign({}, state.userCreds, { isFirstLogin: false })
      });
    }
    default:
      return state;
  }
}
