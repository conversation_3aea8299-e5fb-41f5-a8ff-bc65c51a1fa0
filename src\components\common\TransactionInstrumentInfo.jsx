import React from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getInstrumentInfoList = (details, hasProvisionalFields) => {
  let instrumentInfoList = [];

  addItemToList(
    details?.transactionInfo?.referenceTxnId,
    'Reference Transaction ID',
    details?.transactionInfo?.referenceTxnId,
    instrumentInfoList
  );

  addItemToList(
    details?.entityId?.value,
    'Entity ID',
    details?.entityId?.value,
    instrumentInfoList,
    details?.entityId
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.entityCode,
    'Entity Code',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.entityCode,
    instrumentInfoList
  );

  addItemToList(
    details?.transactionInfo?.txnCategoryName,
    'Category',
    details?.transactionInfo?.txnCategoryName,
    instrumentInfoList
  );

  addItemToList(
    details?.masterFields?.txnTypeName,
    'Type',
    details?.masterFields?.txnTypeName,
    instrumentInfoList,
    null,
    true
  );

  addItemToList(
    details?.masterFields?.channelName,
    'Channel',
    details?.masterFields?.channelName,
    instrumentInfoList,
    null,
    true
  );

  addItemToList(
    details?.masterFields?.paymentMethodName,
    'Method',
    details?.masterFields?.paymentMethodName,
    instrumentInfoList,
    null,
    true
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.posEntryMode,
    'POS Entry Mode',
    details?.masterFields?.txnAdditionalFields?.posEntryMode,
    instrumentInfoList
  );

  addItemToList(
    details?.transactionInfo?.txnAmount,
    'Amount',
    `${details?.transactionInfo?.txnAmount} (${
      details?.transactionInfo?.txnCurrency === '356'
        ? 'INR'
        : details?.transactionInfo?.txnCurrency
    })`,
    instrumentInfoList
  );

  addItemToList(
    details.masterFields?.txnAdditionalFields?.amountForeign > 0,
    'Foreign Amount',
    details.masterFields?.txnAdditionalFields?.amountForeign,
    instrumentInfoList
  );

  addItemToList(
    details?.transactionInfo?.txnTimestamp,
    'Timestamp',
    Moment(details?.transactionInfo?.txnTimestamp).format('YYYY-MM-DD hh:mm A'),
    instrumentInfoList
  );

  addItemToList(
    details?.transactionInfo?.receivedAt,
    'PostAuth Timestamp',
    Moment(details?.transactionInfo?.receivedAt).format('YYYY-MM-DD hh:mm A'),
    instrumentInfoList
  );

  if (hasProvisionalFields === 1) {
    addItemToList(
      details?.masterFields?.attribute1,
      'Attribute 1',
      details?.masterFields?.attribute1,
      instrumentInfoList
    );

    addItemToList(
      details?.masterFields?.attribute2,
      'Attribute 2',
      details?.masterFields?.attribute2,
      instrumentInfoList
    );
  }

  return instrumentInfoList;
};

function TransactionInstrumentInfo({ hasProvisionalFields, details }) {
  const instrumentInfoList = getInstrumentInfoList(details, hasProvisionalFields);

  if (instrumentInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Instrument Details</b>
      {renderDataColumnList(instrumentInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionInstrumentInfo.propTypes = {
  hasProvisionalFields: PropTypes.number.isRequired,
  details: PropTypes.object.isRequired
};

export default TransactionInstrumentInfo;
