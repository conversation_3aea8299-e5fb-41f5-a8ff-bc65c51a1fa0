import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchNotationsList } from 'actions/notationActions';
import { onFetchFraudTypesList } from 'actions/caseReviewActions';
import OneViewVerdictModal from 'components/caseReview/OneViewVerdictModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    notations: state.notations,
    fraudTypes: state.caseAssignment.fraudTypes
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchNotationsList: bindActionCreators(onFetchNotationsList, dispatch),
    fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch)
  };
};

const OneViewVerdictModalContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(OneViewVerdictModal);

export default OneViewVerdictModalContainer;
