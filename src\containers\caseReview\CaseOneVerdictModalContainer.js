import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchFraudTypesList } from 'actions/caseReviewActions';
import { onFetchNotationsList } from 'actions/notationActions';
import OneViewVerdictModal from 'components/caseReview/OneViewVerdictModal';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  notations: state.notations,
  fraudTypes: state.caseAssignment.fraudTypes
});

const mapDispatchToProps = (dispatch) => ({
  fetchNotationsList: bindActionCreators(onFetchNotationsList, dispatch),
  fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch)
});

const OneViewVerdictModalContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(OneViewVerdictModal);

export default OneViewVerdictModalContainer;
