import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onParkCase, onAssignNewCase } from 'actions/caseAssignmentActions';
import * as communicationActions from 'actions/communicationActions';
import { onFetchChannelwiseCounterpartyId } from 'actions/investigationActions';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import { onFetchStages } from 'actions/userManagementActions';
import OneViewActions from 'components/caseReview/OneViewActions';

const mapStateToProps = (state) => ({
  stages: state.user.stages,
  theme: state.toggle.theme,
  prefiltersList: state.prefiltersList,
  userId: state.auth.userCreds.userId,
  userName: state.auth.userCreds.userName,
  txnDetails: state.transactionDetails.details,
  communicationLogs: state.customerCommunication.logs,
  channelCounterpartyId: state.investigation.channelCounterpartyId
});

const mapDispatchToProps = (dispatch) => ({
  parkCase: bindActionCreators(onParkCase, dispatch),
  fetchStages: bindActionCreators(onFetchStages, dispatch),
  assignNewCase: bindActionCreators(onAssignNewCase, dispatch),
  prefilterActions: bindActionCreators(prefiltersListAction, dispatch),
  communicationActions: bindActionCreators(communicationActions, dispatch),
  fetchChannelCounterpartyId: bindActionCreators(onFetchChannelwiseCounterpartyId, dispatch)
});

const OneViewActionsContainer = connect(mapStateToProps, mapDispatchToProps)(OneViewActions);

export default OneViewActionsContainer;
