import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { FormGroup, Label, Input, Button } from 'reactstrap';

import ExcelDownload from 'components/common/ExcelDownload';
import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';

const ListExcelDownload = ({ theme, categoryOptions, listName, data, partnerIdList }) => {
  const [display, setDisplay] = useState(false);
  const [categoryName, setCategoryName] = useState('');

  const headers = [
    { label: 'Category', value: 'categoryName' },
    { label: 'Identifier', value: 'identifier' },
    { label: 'Amount', value: 'amount' },
    { label: 'Count', value: 'count' },
    { label: 'Start Date', value: 'startDate' },
    { label: 'End Date', value: 'endDate' },
    ...(isCooperative
      ? [
          {
            label: 'Partner Name',
            value: 'partnerId'
          }
        ]
      : []),
    {
      label: 'Status',
      value: (col) => (col.isActive === 1 ? 'Active' : 'Inactive')
    },
    { label: 'Added by', value: 'userName' },
    { label: 'Added on', value: 'updatedTimeStamp' }
  ];

  const filteredData =
    categoryName === '' ? data : _.filter(data, (d) => d.categoryName === categoryName);

  const mapPartnerId = (partnerId) => {
    const partner = partnerIdList.filter((d) => _.includes(partnerId, d.id));
    return !_.isEmpty(partner) ? partner.map((p) => p.partnerName).join(', ') : null;
  };

  const mappedData = isCooperative
    ? _.map(filteredData, (obj) => ({
        ...obj,
        partnerId: mapPartnerId(obj.partnerId)
      }))
    : filteredData;

  return (
    <>
      <Button outline size="sm" color="primary" onClick={() => setDisplay(true)}>
        Download
      </Button>
      <ModalContainer
        size="sm"
        theme={theme}
        header="Download Excel"
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form>
          <FormGroup>
            <Label for="categoryName">Category</Label>
            <Input
              type="select"
              id="categoryName"
              name="categoryName"
              value={categoryName}
              onChange={(event) => setCategoryName(event.target.value)}
              required>
              <option value="">All</option>
              {categoryOptions}
            </Input>
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <ExcelDownload
              headers={headers}
              sheetName={`${listName} ${categoryName}`}
              data={mappedData}
              action={() => setDisplay(false)}
            />
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

ListExcelDownload.propTypes = {
  data: PropTypes.array.isRequired,
  theme: PropTypes.string.isRequired,
  categoryOptions: PropTypes.array.isRequired,
  listName: PropTypes.string.isRequired,
  partnerIdList: PropTypes.array.isRequired
};

export default ListExcelDownload;
