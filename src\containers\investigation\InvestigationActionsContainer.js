import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as caseAssignmentActions from 'actions/caseAssignmentActions';
import { onClearSelectedCase, onSelectCase, onOpenSTRCase } from 'actions/caseReviewActions';
import * as toggleActions from 'actions/toggleActions';
import { onCaseHold, onRequestDocument } from 'actions/releaseFundsActions';
import { onCreateIncidents, onFetchIncidentById } from 'actions/incidentActions';
import InvestigationActions from 'components/investigation/InvestigationActions';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    role: state.auth.userCreds.roles,
    userslist: state.user.userslist,
    txnDetails: state.transactionDetails,
    userId: state.auth.userCreds.userId,
    userName: state.auth.userCreds.userName,
    selectedCase: state.caseAssignment.selectedCase,
    documentStatus: state.releaseFunds.documentStatus,
    moduleType: state.auth.moduleType,
    hasHoldAndRelease: state.user.configurations.holdAndRelease
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    caseAssignmentActions: bindActionCreators(caseAssignmentActions, dispatch),
    clearSelectedCase: bindActionCreators(onClearSelectedCase, dispatch),
    toggleActions: bindActionCreators(toggleActions, dispatch),
    holdCase: bindActionCreators(onCaseHold, dispatch),
    requestDocument: bindActionCreators(onRequestDocument, dispatch),
    selectCase: bindActionCreators(onSelectCase, dispatch),
    createIncidents: bindActionCreators(onCreateIncidents, dispatch),
    fetchIncident: bindActionCreators(onFetchIncidentById, dispatch),
    openSTRCase: bindActionCreators(onOpenSTRCase, dispatch)
  };
};

const InvestigationActionsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(InvestigationActions);

export default InvestigationActionsContainer;
