import {
  ON_DSL_VERIFY_FAILURE,
  ON_<PERSON><PERSON>AR_DSL_VALIDATION,
  ON_SUCCESSFUL_DSL_VERIFY,
  ON_FETCH_DSL_HELPERS_LOADING,
  ON_FETCH_DSL_HELPERS_FAILURE,
  ON_SUCCESSFUL_FETCH_DSL_HELPERS,
  ON_SUCCESSFUL_FETCH_ACTION_LIST,
  ON_SUCCESSFUL_UPDATE_ACTION_LIST,
  ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE,
  ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS,
  ON_SUCCESSFUL_FETCH_CHECKLIST,
  ON_FETCH_CHECKLIST_OPTIONS_FAILURE,
  ON_FETCH_CHECKLIST_FAILURE,
  ON_FETCH_RULE_CHANNELS_LIST_SUCCESS,
  ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS,
  ON_FETCH_RULE_LABELS_SUCCESS,
  ON_UPDATE_LABELS_ORDER_SUCCESS,
  ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS,
  ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE,
  ON_FETCH_SIMILAR_RULE_SUCCESS,
  ON_FETCH_SIMILAR_RULE_FAILURE
} from 'constants/actionTypes';
import initialState from './initialState';
import objectAssign from 'object-assign';
import { sortBy, map } from 'lodash';

const updateOrder = (list, mutation, idKey = 'id') =>
  map(list, (item) => mutation.find((updated) => updated[idKey] === item[idKey]) ?? item);

export default function ruleCreationReducer(state = initialState.ruleCreation, action) {
  switch (action.type) {
    case ON_FETCH_DSL_HELPERS_LOADING:
      return objectAssign({}, state, {
        channel: '',
        loader: true,
        error: false,
        errorMessage: ''
      });
    case ON_SUCCESSFUL_FETCH_DSL_HELPERS:
      return objectAssign({}, state, {
        channel: action.channel == 'nrt' ? 'frm' : action.channel,
        helperList: objectAssign({}, state.helperList, {
          [action.channel]: action.response
        }),
        loader: false,
        error: false,
        errorMessage: ''
      });
    case ON_FETCH_DSL_HELPERS_FAILURE:
      return objectAssign({}, state, {
        channel: '',
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      });
    case ON_SUCCESSFUL_FETCH_ACTION_LIST:
      return objectAssign({}, state, { actionList: action.response });
    case ON_SUCCESSFUL_UPDATE_ACTION_LIST:
      return objectAssign({}, state, {
        actionList: updateOrder(state.actionList, action.response, 'actionCode')
      });
    case ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES:
      return objectAssign({}, state, { alertCategories: action.response });
    case ON_SUCCESSFUL_DSL_VERIFY:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          message: action.response.message,
          status: true
        })
      });
    case ON_DSL_VERIFY_FAILURE:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          message: action.response.message,
          status: false
        })
      });
    case ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          ruleFalsePositiveData: action.response
        })
      });
    case ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          ruleFalsePositiveData: {}
        })
      });
    case ON_FETCH_SIMILAR_RULE_SUCCESS:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          ruleSimilarityData: action.response
        })
      });
    case ON_FETCH_SIMILAR_RULE_FAILURE:
      return objectAssign({}, state, {
        validation: objectAssign({}, state.validation, {
          ruleSimilarityData: {}
        })
      });
    case ON_CLEAR_DSL_VALIDATION:
      return objectAssign({}, state, {
        validation: {
          message: '',
          status: false,
          ruleFalsePositiveData: {},
          ruleSimilarityData: {}
        }
      });
    case ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING:
      return objectAssign({}, state, {
        nonProductionRules: objectAssign({}, state.nonProductionRules, { loader: true })
      });
    case ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS:
      return objectAssign({}, state, {
        nonProductionRules: objectAssign({}, state.nonProductionRules, {
          list: objectAssign({}, state.nonProductionRules.list, {
            [action.channel]: sortBy(action.response, ['name'])
          }),
          loader: false
        })
      });
    case ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE:
      return objectAssign({}, state, {
        nonProductionRules: objectAssign({}, state.nonProductionRules, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS:
      return objectAssign({}, state, {
        checkListOptions: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CHECKLIST_OPTIONS_FAILURE:
      return objectAssign({}, state, {
        checkListOptions: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_SUCCESSFUL_FETCH_CHECKLIST:
      return objectAssign({}, state, {
        checkList: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CHECKLIST_FAILURE:
      return objectAssign({}, state, {
        checkList: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_RULE_CHANNELS_LIST_SUCCESS:
      return objectAssign({}, state, {
        ruleChannels: action.response
      });
    case ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS:
      return objectAssign({}, state, {
        fraudCategories: action.response
      });
    case ON_FETCH_RULE_LABELS_SUCCESS:
      return objectAssign({}, state, {
        ruleLabels: action.response
      });
    case ON_UPDATE_LABELS_ORDER_SUCCESS:
      return objectAssign({}, state, {
        ruleLabels: updateOrder(state.ruleLabels, action.response)
      });
    default:
      return state;
  }
}
