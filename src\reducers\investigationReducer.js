import _ from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_SIMILAR_TXNS_LOADING,
  ON_FETCH_SIMILAR_TXNS_FAILURE,
  ON_CUSTOMER_TRENDS_FETCH_LOADING,
  ON_CUSTOMER_TRENDS_FETCH_FAILURE,
  ON_SUCCESSFUL_FETCH_SIMILAR_TXNS,
  ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH,
  ON_C<PERSON>AR_TRANSACTION_HISTORY_SEARCH,
  ON_FETCH_TRANSACTION_HISTORY_LOADING,
  ON_FETCH_TRANSACTION_HISTORY_FAILURE,
  ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE,
  ON_REMOVE_SEAR<PERSON>_TRANSACTION,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE,
  ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS
} from 'constants/actionTypes';

import initialState from './initialState';

export default function investigationReducer(state = initialState.investigation, action) {
  switch (action.type) {
    case ON_CUSTOMER_TRENDS_FETCH_LOADING:
      return objectAssign({}, state, {
        trends: {
          timeTrend: [],
          payeeTrend: [],
          timeRange: {},
          averageAmount: 0,
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH:
      return objectAssign({}, state, {
        trends: {
          timeTrend: action.response.trendTimeGraph.data,
          payeeTrend: action.response.perPayeeTrend.data,
          timeRange: action.response.trendTimeGraph.usualTxnTimeRange,
          averageAmount: action.response.perPayeeTrend.averageTransactionAmount,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_CUSTOMER_TRENDS_FETCH_FAILURE:
      return objectAssign({}, state, {
        trends: {
          timeTrend: [],
          payeeTrend: [],
          timeRange: {},
          averageAmount: 0,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_TRANSACTION_HISTORY_LOADING:
      return objectAssign({}, state, {
        transactionHistorySearch: objectAssign({}, state.transactionHistorySearch, {
          [action.channel]: objectAssign({}, state.transactionHistorySearch[action.channel], {
            loader: true,
            error: false,
            errorMessage: ''
          })
        })
      });
    case ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY:
      return objectAssign({}, state, {
        transactionHistorySearch: objectAssign({}, state.transactionHistorySearch, {
          [action.channel]: objectAssign({}, state.transactionHistorySearch[action.channel], {
            selectedDates: action.selectedDates,
            selectedCriteria: action.selectedCriteria,
            list:
              _.isEqual(
                action.selectedDates,
                state.transactionHistorySearch[action.channel].selectedDates
              ) &&
              _.isEqual(
                action.selectedCriteria,
                state.transactionHistorySearch[action.channel].selectedCriteria
              )
                ? _.unionBy(
                    state.transactionHistorySearch[action.channel].list,
                    action.response.records,
                    'txnId'
                  )
                : action.response.records,
            count: action.response.count,
            isLastPage: action.response.isLastPage,
            loader: false
          })
        })
      });
    case ON_FETCH_TRANSACTION_HISTORY_FAILURE:
      return objectAssign({}, state, {
        transactionHistorySearch: objectAssign({}, state.transactionHistorySearch, {
          [action.channel]: objectAssign({}, state.transactionHistorySearch[action.channel], {
            selectedDates: action.selectedDates,
            selectedCriteria: action.selectedCriteria,
            list:
              _.isEqual(
                action.selectedDates,
                state.transactionHistorySearch[action.channel].selectedDates
              ) &&
              _.isEqual(
                action.selectedCriteria,
                state.transactionHistorySearch[action.channel].selectedCriteria
              )
                ? state.transactionHistorySearch[action.channel].list
                : [],
            count:
              _.isEqual(
                action.selectedDates,
                state.transactionHistorySearch[action.channel].selectedDates
              ) &&
              _.isEqual(
                action.selectedCriteria,
                state.transactionHistorySearch[action.channel].selectedCriteria
              )
                ? state.transactionHistorySearch[action.channel].count
                : 0,
            isLastPage:
              _.isEqual(
                action.selectedDates,
                state.transactionHistorySearch[action.channel].selectedDates
              ) &&
              _.isEqual(
                action.selectedCriteria,
                state.transactionHistorySearch[action.channel].selectedCriteria
              )
                ? state.transactionHistorySearch[action.channel].isLastPage
                : true,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_REMOVE_SEARCH_TRANSACTION:
      return objectAssign({}, state, {
        transactionHistorySearch: objectAssign({}, state.transactionHistorySearch, {
          [action.channel]: objectAssign({}, state.transactionHistorySearch[action.channel], {
            list: _.filter(
              state.transactionHistorySearch[action.channel].list,
              (listItem) =>
                !_.includes(action.items, listItem.caseRefNo) &&
                !_.includes(action.items, listItem.txnId)
            )
          })
        })
      });
    case ON_CLEAR_TRANSACTION_HISTORY_SEARCH:
      return objectAssign({}, state, {
        transactionHistorySearch: objectAssign({}, state.transactionHistorySearch, {
          [action.channel]: initialState.investigation.transactionHistorySearch[action.channel]
        })
      });
    case ON_FETCH_SIMILAR_TXNS_LOADING:
      return objectAssign({}, state, {
        similar: objectAssign({}, state.similar, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_SUCCESSFUL_FETCH_SIMILAR_TXNS:
      return objectAssign({}, state, {
        similar: objectAssign({}, state.similar, {
          similarTxnCategory: action.similarTxnCategory,
          list: _.isEqual(action.similarTxnCategory, state.similar.similarTxnCategory)
            ? _.unionBy(state.similar.list, action.response.similarTxns, 'txnId')
            : action.response.similarTxns,
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_SIMILAR_TXNS_FAILURE:
      return objectAssign({}, state, {
        similar: objectAssign({}, state.similar, {
          similarTxnCategory: action.similarTxnCategory,
          list: _.isEqual(action.similarTxnCategory, state.similar.similarTxnCategory)
            ? state.similar.list
            : [],
          count: _.isEqual(action.similarTxnCategory, state.similar.similarTxnCategory)
            ? state.similar.count
            : 0,
          isLastPage: _.isEqual(action.similarTxnCategory, state.similar.similarTxnCategory)
            ? state.similar.isLastPage
            : true,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING:
      return objectAssign({}, state, {
        similarTxnCategoryList: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS:
      return objectAssign({}, state, {
        similarTxnCategoryList: {
          list: action.response.similarTxnCategory,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE:
      return objectAssign({}, state, {
        similarTxnCategoryList: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING:
      return objectAssign({}, state, {
        channelCounterpartyId: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS:
      return objectAssign({}, state, {
        channelCounterpartyId: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE:
      return objectAssign({}, state, {
        channelCounterpartyId: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING:
      return objectAssign({}, state, {
        transactionHistoryByStatus: objectAssign({}, state.transactionHistoryByStatus, {
          [action.channel]: objectAssign({}, state.transactionHistoryByStatus[action.channel], {
            loader: true,
            error: false,
            errorMessage: ''
          })
        })
      });
    case ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS:
      return objectAssign({}, state, {
        transactionHistoryByStatus: objectAssign({}, state.transactionHistoryByStatus, {
          [action.channel]: objectAssign({}, state.transactionHistoryByStatus[action.channel], {
            selectedDates: action.selectedDates,
            entityId: action.entityId,
            list:
              _.isEqual(
                action.selectedDates,
                state.transactionHistoryByStatus[action.channel].selectedDates
              ) && action.entityId === state.transactionHistoryByStatus[action.channel].entityId
                ? _.unionBy(
                    state.transactionHistoryByStatus[action.channel].list,
                    action.response.records,
                    'txnId'
                  )
                : action.response.records,
            count: action.response.count,
            isLastPage: action.response.isLastPage,
            loader: false
          })
        })
      });
    case ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE:
      return objectAssign({}, state, {
        transactionHistoryByStatus: objectAssign({}, state.transactionHistoryByStatus, {
          [action.channel]: objectAssign({}, state.transactionHistoryByStatus[action.channel], {
            selectedDates: action.selectedDates,
            entityId: action.entityId,
            list:
              _.isEqual(action.selectedDates, state.transactionHistoryByStatus.selectedDates) &&
              action.entityId === state.transactionHistoryByStatus[action.channel].entityId
                ? state.transactionHistoryByStatus.list
                : [],
            count: _.isEqual(action.selectedDates, state.transactionHistoryByStatus.selectedDates)
              ? state.transactionHistoryByStatus.count
              : 0,
            isLastPage:
              _.isEqual(action.selectedDates, state.transactionHistoryByStatus.selectedDates) &&
              action.entityId === state.transactionHistoryByStatus[action.channel].entityId
                ? state.transactionHistoryByStatus.isLastPage
                : true,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS:
      return objectAssign({}, state, {
        transactionHistoryByStatus: objectAssign({}, state.transactionHistoryByStatus, {
          [action.channel]: initialState.investigation.transactionHistoryByStatus[action.channel]
        })
      });
    default:
      return state;
  }
}
