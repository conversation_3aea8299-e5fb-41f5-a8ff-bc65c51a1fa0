import {
  ON_SELECT_ENTITY,
  ON_<PERSON>ETCH_ENTITY_LIST_FAILURE,
  ON_FETCH_ENTITY_LIST_LOADING,
  ON_FETCH_ENTITY_LIST_SUCCESS,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchEntityList(formData) {
  return client({
    method: 'POST',
    url: `uds/entity-profile`,
    data: formData
  });
}

function onFetchEntityListLoading() {
  return { type: ON_FETCH_ENTITY_LIST_LOADING };
}

function onFetchEntityListSuccess(response) {
  return {
    type: ON_FETCH_ENTITY_LIST_SUCCESS,
    response
  };
}

function onFetchEntityListFailure(response) {
  return {
    type: ON_FETCH_ENTITY_LIST_FAILURE,
    response
  };
}

function onFetchEntityList(formData) {
  return function (dispatch) {
    dispatch(onFetchEntityListLoading());
    return fetchEntityList(formData).then(
      (success) => dispatch(onFetchEntityListSuccess(success)),
      (error) => dispatch(onFetchEntityListFailure(error))
    );
  };
}

function fetchProfilingSearchConditions() {
  return client({
    url: `uds/profile-condition`
  });
}

function onFetchProfilingSearchConditionsLoading() {
  return { type: ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING };
}

function onFetchProfilingSearchConditionsSuccess(response) {
  return {
    type: ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS,
    response
  };
}

function onFetchProfilingSearchConditionsFailure(response) {
  return {
    type: ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE,
    response
  };
}

function onFetchProfilingSearchConditions() {
  return function (dispatch) {
    dispatch(onFetchProfilingSearchConditionsLoading());
    return fetchProfilingSearchConditions().then(
      (success) => dispatch(onFetchProfilingSearchConditionsSuccess(success)),
      (error) => dispatch(onFetchProfilingSearchConditionsFailure(error))
    );
  };
}

function onSelectEntity(entity) {
  return { type: ON_SELECT_ENTITY, entity };
}

function fetchCustomerWiseTransactionSummary(data, channel) {
  return client({
    method: 'POST',
    url: `casereview/${channel}/entity/cases/summary`,
    data
  });
}

function onFetchCustomerWiseTransactionSummaryLoading(calledBy) {
  return { type: ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING, calledBy };
}

function onFetchCustomerWiseTransactionSummarySuccess(response, calledBy) {
  return {
    type: ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS,
    response,
    calledBy
  };
}

function onFetchCustomerWiseTransactionSummaryFailure(response, calledBy) {
  return {
    type: ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE,
    response,
    calledBy
  };
}

function onFetchCustomerWiseTransactionSummary(data, channel, calledBy) {
  return function (dispatch) {
    dispatch(onFetchCustomerWiseTransactionSummaryLoading(calledBy));
    return fetchCustomerWiseTransactionSummary(data, channel).then(
      (success) => dispatch(onFetchCustomerWiseTransactionSummarySuccess(success, calledBy)),
      (error) => dispatch(onFetchCustomerWiseTransactionSummaryFailure(error, calledBy))
    );
  };
}

export {
  onFetchEntityList,
  onFetchCustomerWiseTransactionSummary,
  onSelectEntity,
  onFetchProfilingSearchConditions
};
