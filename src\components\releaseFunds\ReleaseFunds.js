/* eslint-disable react/prop-types */
import { faUnlock } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import $ from 'jquery';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import ReactTable from 'react-table';
import { Button, Form, FormGroup, Label, Input, ButtonGroup } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import TableLoader from 'components/loader/TableLoader';

const ReleaseFunds = ({ toggle, userCreds, releaseFunds, releaseFundsActions, toggleActions }) => {
  const [selectedToBulkRelease, setSelectedToBulkRelease] = useState([]);
  const [checked, setChecked] = useState([]);
  const [subChecked, setSubChecked] = useState([]);
  const [isBulkRelease, setIsBulkRelease] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [releaseComment, setReleaseComment] = useState('');
  const [modalType, setModalType] = useState('');
  const [currentCaseRefNo, setCurrentCaseRefNo] = useState('');
  const [currentTxnId, setCurrentTxnId] = useState('');
  const [isHeaderCheckboxDisabled, setIsHeaderCheckboxDisabled] = useState(false);

  useEffect(() => {
    document.title = 'BANKiQ FRC | Release Funds';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  useEffect(() => {
    if (_.isEmpty(releaseFunds.fundsToBeRelease.list)) {
      releaseFundsActions.onFetchReleaseFundsList(userCreds);
      handleCheckboxCheck(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCheckboxCheck = (val) => {
    const checkedCopy = [];
    let caseWithDocRequiredCount = 0;
    _.each(releaseFunds.fundsToBeRelease.list, (fund) => {
      checkedCopy.push(val);
      if (fund.docVerified === 1) caseWithDocRequiredCount++;
    });

    setIsHeaderCheckboxDisabled(
      !_.isEmpty(releaseFunds.fundsToBeRelease.list) &&
        releaseFunds.fundsToBeRelease.list.length === caseWithDocRequiredCount
    );

    setChecked(checkedCopy);
  };

  const handleAggregatedCheckboxCheck = (val) => {
    const subCheckedCopy = [];
    const expandableHead = $('.rt-expandable');
    _.each(Array.from(expandableHead), () => {
      subCheckedCopy.push(val);
    });
    setSubChecked(subCheckedCopy);
  };

  const handleHeaderCheckboxChange = () => {
    const selectAllCopy = !selectAll;

    handleCheckboxCheck(selectAllCopy);
    handleAggregatedCheckboxCheck(selectAllCopy);

    setSelectAll(selectAllCopy);
    setIsBulkRelease(selectAllCopy);
    setSelectedToBulkRelease(selectAllCopy ? releaseFunds.fundsToBeRelease.list : []);
  };

  const handleSingleCheckboxChange = (row) => {
    const checkedCopy = checked;
    const subCheckedCopy = subChecked;
    const selectedToBulkReleaseCopy = selectedToBulkRelease;
    const subidx = row.nestingPath[0];

    if (row.groupedByPivot) {
      _.each(row.subRows, (item) => {
        const idx = item._index;
        checkedCopy[idx] = !subChecked[subidx];
      });
      subCheckedCopy[subidx] = !subChecked[subidx];

      if (!subCheckedCopy[subidx]) {
        const unchekedTxnIdArr = row.subRows.map((ele) => ele._original.txnId);
        const newSelectedToBulkReleaseCopy = _.filter(
          selectedToBulkReleaseCopy,
          (item) => !unchekedTxnIdArr.includes(item.txnId)
        );
        setSelectAll(false);
        setSelectedToBulkRelease(newSelectedToBulkReleaseCopy);
      } else {
        const chekedTxnIdArr = selectedToBulkReleaseCopy.map((ele) => ele.txnId);

        _.each(row.subRows, (item) => {
          !chekedTxnIdArr.includes(item._original.txnId) &&
            selectedToBulkReleaseCopy.push(item._original);
        });
        setSelectedToBulkRelease(selectedToBulkReleaseCopy);
      }
    } else {
      const idx = row.index;
      checkedCopy[idx] = !checked[idx];
      if (!checkedCopy[idx]) {
        subCheckedCopy[subidx] = false;
        const newSelectedToBulkReleaseCopy = _.filter(
          selectedToBulkReleaseCopy,
          (item) => row.original.txnId !== item.txnId
        );
        setSelectAll(false);
        setSelectedToBulkRelease(newSelectedToBulkReleaseCopy);
      } else {
        selectedToBulkReleaseCopy.push(row.original);
        setSelectedToBulkRelease(selectedToBulkReleaseCopy);
      }
    }

    setChecked(checkedCopy);
    setSubChecked(subCheckedCopy);
    setIsBulkRelease(checkedCopy.includes(true));
  };

  const toggleReleaseFundsModal = (modalType, row) => {
    setModalType(modalType);
    setReleaseComment('');
    if (modalType === 'single') {
      setCurrentCaseRefNo(row.caseRefNo);
      setCurrentTxnId(row.txnId);
    } else {
      setCurrentCaseRefNo('');
      setCurrentTxnId('');
    }
    toggleActions.onToggleReleaseFundsModal();
  };

  const handleReleaseFundsSubmit = (e) => {
    e.preventDefault();
    if (modalType === 'single') {
      const formData = {
        caseRefNo: currentCaseRefNo,
        txnId: currentTxnId,
        comment: releaseComment,
        userId: userCreds.userId
      };
      releaseFundsActions.onReleaseSingleFund(formData, userCreds);
    } else if (modalType === 'bulk') {
      const releaseObj = [];

      const filteredSlectedToBulkRelease = _.filter(
        selectedToBulkRelease,
        (item) => item.docVerified !== 1
      );

      _.each(filteredSlectedToBulkRelease, (item, idx) => {
        if (idx === 0) {
          const newObj = {
            merchantId: item.merchantId,
            releaseDesc: releaseComment,
            txnIds: [item.txnId]
          };
          releaseObj.push(newObj);
        } else {
          let existInReleaseObj = false;
          let existAt = '';
          _.each(releaseObj, (obj, index) => {
            if (obj.merchantId === item.merchantId) {
              existInReleaseObj = true;
              existAt = index;
            }
          });

          if (existInReleaseObj) releaseObj[existAt].txnIds.push(item.txnId);
          else {
            const newObj = {
              merchantId: item.merchantId,
              releaseDesc: releaseComment,
              txnIds: [item.txnId]
            };
            releaseObj.push(newObj);
          }
        }
      });

      const formData = {
        releaseObj
      };
      releaseFundsActions.onReleaseFundsInBulk(formData, userCreds);
      handleCheckboxCheck(false);
      handleAggregatedCheckboxCheck(false);
      setSelectAll(false);
      setIsBulkRelease(false);
      setSelectedToBulkRelease([]);
    }
  };

  const handleSubheaderDisabled = (row) => {
    let docRequiredCaseCount = 0;
    _.each(row.subRows, (subROw) => {
      if (subROw._original.docVerified === 1) docRequiredCaseCount++;
    });

    return docRequiredCaseCount === row.subRows.length;
  };

  const tableHeader = [
    {
      Header: (
        <input
          type="checkbox"
          disabled={isHeaderCheckboxDisabled}
          onChange={handleHeaderCheckboxChange}
          checked={selectAll}
        />
      ),
      Aggregated: (row) => (
        <input
          type="checkbox"
          checked={subChecked[row.nestingPath[0]]}
          disabled={handleSubheaderDisabled(row)}
          onChange={() => handleSingleCheckboxChange(row)}
        />
      ),
      Cell: (row) => (
        <input
          type="checkbox"
          checked={checked[row.index]}
          title={
            row.original && row.original.docVerified === 1 ? 'Verification documents required' : ''
          }
          disabled={row.original && row.original.docVerified === 1}
          onChange={() => handleSingleCheckboxChange(row)}
        />
      ),
      sortable: false,
      filterable: false,
      searchable: false,
      width: '40'
    },
    {
      Header: 'Action',
      searchable: false,
      sortable: false,
      filterable: false,
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            size="sm"
            color="warning"
            className="me-3"
            title={
              row.original && row.original.docVerified === 1
                ? 'Verification documents required'
                : 'Release fund'
            }
            disabled={row.original && row.original.docVerified === 1}
            onClick={() => toggleReleaseFundsModal('single', row.original)}>
            <FontAwesomeIcon icon={faUnlock} />
          </Button>
        </span>
      )
    },
    {
      Header: 'Merchant ID',
      accessor: 'merchantId'
    },
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      aggregate: (vals) => _.sum(vals),
      Aggregated: (row) => <span>{row.value}</span>
    },
    {
      Header: 'Transaction ID',
      accessor: 'txnId'
    },
    {
      Header: 'Time',
      accessor: 'txnTimestamp',
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        Moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    {
      Header: 'IFRM Pre Auth Action',
      accessor: 'response',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    {
      Header: 'IFRM Post Auth Action',
      accessor: 'ifrmPostauthVerdictName',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    {
      Header: 'Case Verdict',
      accessor: 'verdict',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>Confirmed Fraud</option>
          <option>Undetermined</option>
          <option>Confirmed Genuine</option>
          <option>Assumed Genuine</option>
        </select>
      )
    },
    {
      Header: 'Liability',
      accessor: 'liability',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>Bank</option>
          <option>Customer</option>
          <option>Insurance</option>
          <option>Not Applicable</option>
        </select>
      )
    },
    {
      Header: 'Fraud Type',
      accessor: 'fraudType',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>NRI NRC</option>
          <option>Account Takeover</option>
          <option>Internet MOTO</option>
          <option>Application Fraud</option>
          <option>Identity Theft</option>
          <option>Lost Stolen</option>
          <option>Skimming Counterfeit</option>
          <option>Unknown</option>
        </select>
      )
    },
    { Header: 'Explanation', accessor: 'explanation' }
  ];

  return (
    <div className="content-wrapper">
      <CardContainer
        title="Release Funds"
        action={
          <ButtonGroup>
            <Button
              color="primary"
              className="me-3"
              disabled={!isBulkRelease || isHeaderCheckboxDisabled}
              size="sm"
              onClick={() => toggleReleaseFundsModal('bulk')}>
              Bulk release
            </Button>
          </ButtonGroup>
        }>
        {(() => {
          if (releaseFunds.fundsToBeRelease.loader) return <TableLoader />;

          if (releaseFunds.fundsToBeRelease.error)
            return <div className="no-data-div">{releaseFunds.fundsToBeRelease.errorMessage}</div>;

          if (_.isEmpty(releaseFunds.fundsToBeRelease.list))
            return <div className="no-data-div">No past cases</div>;

          return (
            <ReactTable
              filterable
              pivotBy={['merchantId']}
              data={releaseFunds.fundsToBeRelease.list}
              pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              defaultPageSize={5}
              showPaginationTop={true}
              showPaginationBottom={false}
              noDataText="No case history"
              columns={tableHeader}
              className="-highlight -striped"
              freezeWhenExpanded={true}
              defaultFilterMethod={(filter, row) => {
                if (_.isEmpty(row?._subRows))
                  return (
                    row[filter.id] &&
                    row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
                  );
                else return true;
              }}
            />
          );
        })()}
      </CardContainer>

      <ModalContainer
        theme={toggle.theme}
        isOpen={toggle.releaseFundsModal}
        header="Release transaction"
        size="md"
        toggle={() => toggleReleaseFundsModal('')}>
        <Form onSubmit={handleReleaseFundsSubmit}>
          <FormGroup>
            <Label>Description</Label>
            <Input
              type="textarea"
              name="description"
              rows="5"
              value={releaseComment}
              onChange={(event) => setReleaseComment(event.target.value)}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button size="sm" type="submit" color="success" disabled={releaseComment === ''}>
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </div>
  );
};

ReleaseFunds.propTypes = {
  toggle: PropTypes.object.isRequired,
  userCreds: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  releaseFunds: PropTypes.object.isRequired,
  releaseFundsActions: PropTypes.object.isRequired
};

export default ReleaseFunds;
