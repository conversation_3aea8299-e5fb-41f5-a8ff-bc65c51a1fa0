import _ from 'lodash';
import React, { useState } from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';
import SandboxResultContainer from 'containers/ruleEngine/SandboxResultContainer';

const SandboxHistoryTable = ({ sandboxHistory, ruleName }) => {
  const [displayResult, setDisplayResult] = useState(null);

  const sandboxHistoryHeader = [
    {
      id: 'displayResult',
      Header: 'Action',
      accessor: 'result',
      filterable: false,
      sortable: false,
      // eslint-disable-next-line react/no-multi-comp, react/prop-types
      Cell: (row) => (
        <Button
          size="sm"
          color="warning"
          title="View result"
          disabled={row.original.status != 'Done' || row.value.length == 0}
          onClick={() => setDisplayResult(row.value)}>
          <FontAwesomeIcon icon={faEye} />
        </Button>
      )
    },
    { Header: 'Run ID', accessor: 'sandboxRunId' },
    { Header: 'Run By', accessor: 'runBy' },
    {
      Header: 'Start Time',
      accessor: 'startTime',
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A')
    },
    { Header: 'Status', accessor: 'status' }
  ];

  return (
    !_.isEmpty(sandboxHistory.list) && (
      <>
        <ReactTable
          columns={sandboxHistoryHeader}
          data={_.filter(sandboxHistory.list, (d) => d.ruleName === ruleName)}
          pageSizeOptions={[5, 10, 20]}
          defaultPageSize={5}
          minRows={3}
          showPaginationTop={true}
          showPaginationBottom={false}
        />
        <ModalContainer
          title="Sandbox result"
          toggle={() => setDisplayResult(null)}
          isOpen={!_.isEmpty(displayResult)}>
          <SandboxResultContainer rules={[ruleName]} result={displayResult} />
        </ModalContainer>
      </>
    )
  );
};

SandboxHistoryTable.propTypes = {
  ruleName: PropTypes.string.isRequired,
  sandboxHistory: PropTypes.object.isRequired
};

export default SandboxHistoryTable;
