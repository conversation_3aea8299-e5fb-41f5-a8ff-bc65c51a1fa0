import React from 'react';
import { isEmpty, camelCase } from 'lodash';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col, Button, Collapse } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretRight, faCaretDown } from '@fortawesome/free-solid-svg-icons';
import HelpIcon from 'components/common/HelpIcon';

function ConfigFormWrapper({
  highlightText,
  configTitle,
  handleSaveConfigurations,
  handleInputChange,
  data,
  children,
  handleResetConfigurations,
  configType,
  toggleRow,
  openRows,
  configDesc,
  activationId = ''
}) {
  return (
    <tr className="scp-table">
      <th className="searchable" onClick={() => toggleRow(configType)}>
        <Button size="sm" className="setting-expand-button">
          {openRows[configType] ? (
            <FontAwesomeIcon icon={faCaretDown} />
          ) : (
            <FontAwesomeIcon icon={faCaretRight} />
          )}
        </Button>
        <span className="ms-2">
          {highlightText(configTitle)}
          {!isEmpty(configDesc) && <HelpIcon
            size="lg"
            id={camelCase(configTitle)}
            text={configDesc}
          />}
        </span>
      </th>
      <td>
        <Collapse isOpen={!!openRows[configType]}>
          <form onSubmit={(e) => handleSaveConfigurations(e)}>
            {!isEmpty(activationId) && (
              <FormGroup switch row>
                <Label sm={4} md={3} lg={2} for={activationId} className="searchable">
                  {highlightText('Activate configuration')}
                  {!isEmpty(data?.activation?.desc) && <HelpIcon
                    size="lg"
                    id={camelCase(configTitle+'Activate configuration')}
                    text={data?.activation?.desc}
                  />}
                </Label>
                <Col sm={4} md={3} lg={2} className="setting-input-padding">
                  <Input
                    type="switch"
                    role="switch"
                    id={activationId}
                    name="activation"
                    value={data?.activation?.value}
                    onChange={(e) => handleInputChange(e)}
                    checked={data?.activation?.value === 'enabled'}
                  />
                </Col>
              </FormGroup>
            )}
            {children || ''}
            <FormGroup className={`${data?.isEdited ? '' : 'visibility-hide'}`} row>
              <Col sm={4} md={3} lg={2} />
              <Col sm={6} md={5} lg={4} className="setting-input-padding mt-4">
                <Button size="sm" color="primary" title="Save" className="me-2" type="submit">
                  Save
                </Button>
                <Button color="warning" title="Reset" size="sm" onClick={handleResetConfigurations}>
                  Reset
                </Button>
              </Col>
            </FormGroup>
          </form>
        </Collapse>
      </td>
    </tr>
  );
}

ConfigFormWrapper.propTypes = {
  highlightText: PropTypes.func.isRequired,
  handleSaveConfigurations: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  configTitle: PropTypes.string.isRequired,
  activationId: PropTypes.string,
  handleInputChange: PropTypes.func.isRequired,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]),
  handleResetConfigurations: PropTypes.func.isRequired,
  configType: PropTypes.string.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired,
  configDesc: PropTypes.string.isRequired
};

export default ConfigFormWrapper;
