/* eslint-disable react/prop-types */

'use strict';
import { faTrash, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import ReactTable from 'react-table';
import { Row, Col, FormGroup, Label, Input, Button, FormFeedback } from 'reactstrap';

const DynamicCountersForm = ({
  channel,
  formName,
  submit,
  dynamicCountersAction,
  dynamicCounters
}) => {
  const [identifier, setIdentifier] = useState('');
  const [invalidIdentifier, setInvalidIdentifier] = useState(false);
  const [description, setDescription] = useState('');
  const [period, setPeriod] = useState('Monthly');
  const [ttl, setTtl] = useState('');
  const [invalidTtl, setInvalidTtl] = useState(false);
  const [isSingleAttribute, setIsSingleAttribute] = useState(1);
  const [primaryAttribute, setPrimaryAttribute] = useState('');
  const [secondaryAttribute, setSecondaryAttribute] = useState('');
  const [field, setField] = useState({});
  const [operator, setOperator] = useState('');
  const [value, setValue] = useState('');
  const [invalidValue, setInvalidValue] = useState(false);
  const [invalidValueMessage, setInvalidValueMessage] = useState('');
  const [conditionsList, setConditionsList] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isPostAuth, setIsPostAuth] = useState(0);

  const operators = [
    { key: '=', value: 'equalTo', datatype: ['text', 'int', 'numeric'] },
    { key: '>', value: 'greaterThan', datatype: ['int', 'numeric'] },
    { key: '<', value: 'lessThan', datatype: ['int', 'numeric'] },
    { key: '>=', value: 'greaterThanEqualTo', datatype: ['int', 'numeric'] },
    { key: '<=', value: 'lessThanEqualTo', datatype: ['int', 'numeric'] },
    { key: 'IN', value: 'in', datatype: ['text', 'int', 'numeric'] },
    { key: '!=', value: 'notEqualTo', datatype: ['text', 'int', 'numeric'] },
    { key: 'NOT IN', value: 'notIn', datatype: ['text', 'int', 'numeric'] },
    { key: '%', value: 'mod', datatype: ['int', 'numeric'] }
  ];

  const pattern = {
    alphanumeric: /^[\w\-\s]+$/,
    commaSeperatedAlphanumeric: /^[\w\-\s]+(,[\w\-\s]+)+$/,
    integer: /^[+-]?[\d]+$/,
    commaSeperatedInteger: /^[+-]?[\d]+(,[+-]?[\d]+)+$/,
    decimal: /^[+-]?\d+(\.\d+)?$/,
    commaSeperatedDecimal: /^[+-]?\d+(\.\d+)?(,[+-]?\d+(\.\d+)?)+$/,
    default: /^$/
  };

  useEffect(() => {
    if (_.isEmpty(dynamicCounters.conditionalAttributes.list))
      dynamicCountersAction.onFetchConditionalAttributes(channel);
    if (_.isEmpty(dynamicCounters.allAttributes.list))
      dynamicCountersAction.onFetchAllAttributes(channel);
    if (_.isEmpty(dynamicCounters.subAttributes.list))
      dynamicCountersAction.onFetchSubAttributes(channel);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleValueValidationProps = () => {
    switch (true) {
      case field?.datatype === 'text' && operator !== 'IN' && operator !== 'NOT IN':
        return {
          pattern: pattern.alphanumeric,
          title: 'Please enter an alphanumeric value'
        };
      case field?.datatype === 'text' && (operator === 'IN' || operator === 'NOT IN'):
        return {
          pattern: pattern.commaSeperatedAlphanumeric,
          title: 'Comma seperated alphanumeric values allowed'
        };
      case field?.datatype === 'int' && operator !== 'IN' && operator !== 'NOT IN':
        return {
          pattern: pattern.integer,
          title: 'Please enter an integer'
        };
      case field?.datatype === 'int' && (operator === 'IN' || operator === 'NOT IN'):
        return {
          pattern: pattern.commaSeperatedInteger,
          title: 'Comma seperated integer values without space are allowed'
        };
      case field?.datatype === 'numeric' && operator !== 'IN' && operator !== 'NOT IN':
        return {
          pattern: pattern.decimal,
          title: 'Please enter a decimal'
        };
      case field?.datatype === 'numeric' && (operator === 'IN' || operator === 'NOT IN'):
        return {
          pattern: pattern.commaSeperatedDecimal,
          title: 'Comma seperated decimal values without space are allowed'
        };
      default:
        return {
          pattern: pattern.default,
          title: ''
        };
    }
  };

  const validateValue = (value) => {
    const applicableRegex = handleValueValidationProps();
    setInvalidValue(!applicableRegex.pattern.test(value));
    setInvalidValueMessage(applicableRegex.title);
  };

  const operatorOptions = operators
    .filter((operator) => _.includes(operator.datatype, _.toLower(field?.datatype)))
    .map((operator) => (
      <option key={operator.value} value={operator.key}>
        {operator.value}
      </option>
    ));

  const validateIdentifier = (identifier) => {
    const existingIdentifier =
      dynamicCounters.counters.list &&
      dynamicCounters.counters.list.filter(
        (counter) => counter.identifier.toLowerCase() === identifier.toLowerCase()
      );
    !_.isEmpty(existingIdentifier) ? setInvalidIdentifier(true) : setInvalidIdentifier(false);
  };

  const validateTtl = (ttl) => {
    const isInvalidTtl = ttl > 366 || ttl < 1;
    setInvalidTtl(isInvalidTtl);
  };

  const handleFieldChange = (e) => {
    const selectedField = dynamicCounters.conditionalAttributes.list.filter(
      (d) => d.value === e.target.value
    );
    setField(selectedField[0]);
    setOperator('');
    setValue('');
  };

  const handleCommaSeparatedText = (value) => {
    const trimmedValues = _.split(value, ',').map((val) => _.trim(val));
    const commaSeparatedText = trimmedValues.toString();

    return commaSeparatedText.replace(/,/g, "','");
  };

  const handleAddConditions = () => {
    let formattedValue;

    if (operator === 'IN' || operator === 'NOT IN')
      if (field?.datatype === 'text') formattedValue = `('${handleCommaSeparatedText(value)}')`;
      else formattedValue = `(${value})`;
    else if (field?.datatype === 'text') formattedValue = `'${value}'`;
    else formattedValue = value;

    const condition = { field: field?.value, operator, value: formattedValue };
    setConditionsList((conditionsList) => [...conditionsList, condition]);
    setField({});
    setOperator('');
    setValue('');
  };

  const handleRemoveConditions = (selectedCondition) => {
    if (confirm('Are you sure you wish to remove this condition ?')) {
      const newConditionsList =
        conditionsList &&
        conditionsList.filter((condition) => !_.isEqual(condition, selectedCondition));
      setConditionsList(newConditionsList);
    }
  };

  const conditionsTableheader = [
    {
      Header: 'Field',
      accessor: 'field',
      Cell: ({ value }) => <span>{value}</span>
    },
    { Header: 'Operator', accessor: 'operator' },
    { Header: 'Values', accessor: 'value' },
    {
      Header: 'Actions',
      filterable: false,
      sortable: false,
      maxWidth: 80,
      Cell: (row) => (
        <Button
          size="sm"
          color="danger"
          title="Delete"
          onClick={() => handleRemoveConditions(row.original)}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    }
  ];

  const conditionalAttributesOptions =
    dynamicCounters.conditionalAttributes.list &&
    dynamicCounters.conditionalAttributes.list.map((attribute) => (
      <option key={attribute.key} value={attribute.value}>
        {attribute.key}
      </option>
    ));

  const primaryAttributeOptions =
    dynamicCounters.allAttributes.list &&
    dynamicCounters.allAttributes.list.map((attribute) => (
      <option key={attribute.key} value={attribute.value}>
        {attribute.key}
      </option>
    ));

  const secondaryAttributeOptions =
    dynamicCounters.subAttributes.list &&
    dynamicCounters.subAttributes.list
      .filter((attribute) => attribute.value !== primaryAttribute)
      .map((attribute) => (
        <option key={attribute.key} value={attribute.value}>
          {attribute.key}
        </option>
      ));

  const onSubmit = (e) => {
    e.preventDefault();
    setSubmitLoading(true);
    if (invalidIdentifier || (period === 'Custom Days' && invalidTtl)) return false;
    else submit(e, conditionsList);
    setSubmitLoading(false);
  };

  return (
    <form name={formName} onSubmit={onSubmit}>
      <FormGroup>
        <Label>Identifier</Label>
        <Input
          type="text"
          name="identifier"
          value={identifier}
          onChange={(event) => {
            validateIdentifier(event.target.value);
            setIdentifier(event.target.value);
          }}
          spellCheck={false}
          invalid={invalidIdentifier}
          required
        />
        <FormFeedback>Counter with same identifier already exists.</FormFeedback>
      </FormGroup>
      <FormGroup>
        <Label>Description</Label>
        <Input
          type="textarea"
          name="description"
          rows="2"
          value={description}
          onChange={(event) => {
            setDescription(event.target.value);
          }}
          required
        />
      </FormGroup>

      <FormGroup>
        <Label>Period</Label>
        <Row className="period-radio">
          <Col md="3" sm="3" xs="12">
            <FormGroup check>
              <Label check>
                <Input
                  type="radio"
                  name="period"
                  value="Monthly"
                  checked={period === 'Monthly'}
                  onChange={(e) => {
                    setPeriod(e.target.value);
                    setTtl('');
                  }}
                />
                Monthly
              </Label>
            </FormGroup>
          </Col>
          <Col md="3" sm="3" xs="12">
            <FormGroup check>
              <Label check>
                <Input
                  type="radio"
                  name="period"
                  value="Custom Days"
                  checked={period === 'Custom Days'}
                  onChange={(e) => setPeriod(e.target.value)}
                />
                Custom Days
              </Label>
            </FormGroup>
          </Col>
          {period === 'Custom Days' && (
            <Col md="3" sm="3" xs="12">
              <FormGroup>
                <Input
                  type="number"
                  name="ttl"
                  value={ttl}
                  placeholder="Days"
                  min="1"
                  invalid={invalidTtl}
                  required
                  onChange={(e) => {
                    validateTtl(e.target.value);
                    setTtl(e.target.value);
                  }}
                />
                <FormFeedback>Only 1 to 366 day&apos;s are allowed.</FormFeedback>
              </FormGroup>
            </Col>
          )}
        </Row>
      </FormGroup>

      <FormGroup>
        <Label># Of Core Attributes</Label>
        <Row>
          <Col md="6" sm="6" xs="12">
            <FormGroup check>
              <Label check>
                <Input
                  type="radio"
                  name="isSingleAttribute"
                  value={1}
                  checked={isSingleAttribute === 1}
                  onChange={(e) => {
                    setIsSingleAttribute(e.target.value);
                    setSecondaryAttribute('');
                  }}
                />
                Single Attribute
              </Label>
            </FormGroup>
          </Col>
          <Col md="6" sm="6" xs="12">
            <FormGroup check>
              <Label check>
                <Input
                  type="radio"
                  name="isSingleAttribute"
                  value={0}
                  checked={isSingleAttribute === 0}
                  onChange={(e) => setIsSingleAttribute(e.target.value)}
                />
                Dual Attributes
              </Label>
            </FormGroup>
          </Col>
        </Row>
        <Row>
          <Col md="6" sm="6" xs="12">
            <FormGroup>
              <Label>Select Primary Attribute </Label>
              <Input
                type="select"
                name="primaryAttribute"
                value={primaryAttribute}
                onChange={(e) => setPrimaryAttribute(e.target.value)}
                required>
                <option value="">-- Select --</option>
                {primaryAttributeOptions}
              </Input>
            </FormGroup>
          </Col>
          {isSingleAttribute === 0 && (
            <Col md="6" sm="6" xs="12">
              <FormGroup>
                <Label>Select Secondary Attribute </Label>
                <Input
                  type="select"
                  name="secondaryAttribute"
                  value={secondaryAttribute}
                  onChange={(e) => setSecondaryAttribute(e.target.value)}
                  required
                  disabled={primaryAttribute === ''}>
                  <option value="">-- Select --</option>
                  {secondaryAttributeOptions}
                </Input>
              </FormGroup>
            </Col>
          )}
        </Row>
      </FormGroup>

      <FormGroup>
        <Label>Conditions</Label>
        <Row>
          <Col md="4" sm="4" xs="12">
            <FormGroup>
              <Label>Select Field </Label>
              <Input
                type="select"
                name="field"
                value={!_.isEmpty(field) ? field?.value : ''}
                onChange={(e) => handleFieldChange(e)}>
                <option value="">-- Select --</option>
                {conditionalAttributesOptions}
              </Input>
            </FormGroup>
          </Col>
          <Col md="4" sm="4" xs="12">
            <FormGroup>
              <Label>Select Operator </Label>
              <Input
                type="select"
                name="operator"
                value={operator}
                onChange={(e) => {
                  setOperator(e.target.value);
                  setValue('');
                }}
                disabled={_.isEmpty(field)}>
                <option value="">-- Select --</option>
                {operatorOptions}
              </Input>
            </FormGroup>
          </Col>
          <Col md="3" sm="3" xs="12">
            <FormGroup>
              <Label>Value </Label>
              <Input
                placeholder="value"
                type="text"
                name="value"
                value={value}
                onChange={(e) => {
                  validateValue(e.target.value);
                  setValue(e.target.value);
                }}
                disabled={_.isEmpty(field) || _.isEmpty(operator)}
                spellCheck={false}
                invalid={!_.isEmpty(value) && invalidValue}
              />
              <FormFeedback>{invalidValueMessage}</FormFeedback>
            </FormGroup>
          </Col>
          <Col md="1" sm="1" xs="12">
            <FormGroup className="d-flex justify-content-end">
              <Button
                color="primary"
                size="sm"
                className="add-condition-btn"
                disabled={
                  invalidValue || _.isEmpty(field) || _.isEmpty(value) || _.isEmpty(operator)
                }
                onClick={handleAddConditions}>
                Add
              </Button>
            </FormGroup>
          </Col>
        </Row>
      </FormGroup>

      {!_.isEmpty(conditionsList) && (
        <ReactTable
          filterable={false}
          defaultFilterMethod={(filter, row) =>
            row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
          }
          columns={conditionsTableheader}
          data={conditionsList}
          defaultPageSize={20}
          minRows={2}
          showPaginationTop={false}
          showPaginationBottom={false}
          className="-highlight  -striped"
        />
      )}
      <FormGroup>
        <Row>
          <Col md="6" sm="6" xs="12">
            <FormGroup className="create-counter-btn">
              <Label for="isPostAuth">
                <Input
                  type="checkbox"
                  id="isPostAuth"
                  name="isPostAuth"
                  value={isPostAuth}
                  checked={isPostAuth === 1}
                  onChange={() => setIsPostAuth(isPostAuth === 1 ? 0 : 1)}
                />{' '}
                Is Post Auth
              </Label>
            </FormGroup>
          </Col>
          <Col md="6" sm="6" xs="12">
            <FormGroup className="d-flex justify-content-end create-counter-btn">
              <Button
                color="primary"
                type="submit"
                size="sm"
                disabled={invalidIdentifier || (period === 'Custom Days' && invalidTtl)}>
                {submitLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className="loader fa-spin" />
                ) : (
                  'Create counter'
                )}
              </Button>
            </FormGroup>
          </Col>
        </Row>
      </FormGroup>
    </form>
  );
};

DynamicCountersForm.propTypes = {
  theme: PropTypes.string.isRequired,
  submit: PropTypes.func.isRequired,
  formName: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  formData: PropTypes.object,
  dynamicCountersAction: PropTypes.object.isRequired,
  dynamicCounters: PropTypes.object.isRequired
};

export default DynamicCountersForm;
