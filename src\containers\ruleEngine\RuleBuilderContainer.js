import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import RuleBuilder from 'components/ruleEngine/RuleBuilder';

const mapStateToProps = (state) => {
  return {
    helperList: state.ruleCreation.helperList,
    validation: state.ruleCreation.validation,
    prefilterLists: state.prefiltersList.allLists,
    moduleType: state.auth.moduleType
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch)
  };
};

const RuleBuilderContainer = connect(mapStateToProps, mapDispatchToProps)(RuleBuilder);

export default RuleBuilderContainer;
