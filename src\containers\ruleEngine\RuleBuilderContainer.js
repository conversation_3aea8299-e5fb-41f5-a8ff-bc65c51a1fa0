import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as ruleCreationActions from 'actions/ruleCreationActions';
import RuleBuilder from 'components/ruleEngine/RuleBuilder';
import { getValidation } from 'selectors/ruleEngineSelectors';

// Optimized mapStateToProps using memoized selectors
const mapStateToProps = (state) => ({
  validation: getValidation(state)
});

const mapDispatchToProps = (dispatch) => ({
  ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch)
});

const RuleBuilderContainer = connect(mapStateToProps, mapDispatchToProps)(RuleBuilder);

export default RuleBuilderContainer;
