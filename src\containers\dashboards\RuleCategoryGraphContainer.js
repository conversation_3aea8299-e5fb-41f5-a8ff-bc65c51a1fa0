import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleCategoryTrend } from 'actions/businessDashboardActions';
import RuleCategoryGraph from 'components/dashboards/RuleCategoryGraph';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleCategoryTrend: state.businessDashboard.ruleCategoryTrend
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleCategoryTrend: bindActionCreators(onFetchRuleCategoryTrend, dispatch)
  };
};

const RuleCategoryGraphContainer = connect(mapStateToProps, mapDispatchToProps)(RuleCategoryGraph);

export default RuleCategoryGraphContainer;
