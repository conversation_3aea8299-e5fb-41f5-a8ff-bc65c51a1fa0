import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/userManagementActions';
import responses from 'mocks/responses';

const { uam } = responses;

describe('user management actions', () => {
  it('should fetch userslist', () => {
    const expectedActions = [{ type: types.ON_SUCCESSFUL_USER_LIST_FETCH, response: uam.users }];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchUsersList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch roles', () => {
    const expectedActions = [{ type: types.ON_SUCCESSFUL_FETCH_ROLES, response: uam.roles }];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchRoles()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch shifts', () => {
    const expectedActions = [{ type: types.ON_SUCCESSFUL_FETCH_SHIFTS, response: uam.shifts }];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchShifts()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch channels', () => {
    const expectedActions = [{ type: types.ON_SUCCESSFUL_FETCH_CHANNELS, response: uam.channels }];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchChannels()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add user', () => {
    const formData = {
      type: 'user',
      userData: {
        email: 'user',
        userName: 'user',
        firstName: 'user',
        lastName: 'user',
        channelRoles: []
      }
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'User added successfully' } },
      { type: types.ON_TOGGLE_ADD_USER_MODAL },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onAddUser(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update user', () => {
    const formData = {
      type: 'user',
      userData: {
        email: 'user',
        userName: 'user',
        firstName: 'user',
        lastName: 'user',
        channelRoles: []
      }
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_UPDATE_USER_ROLES, user: formData.userData },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'User roles updated successfully' } },
      { type: types.ON_TOGGLE_UPDATE_USER_ROLES_MODAL },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onUpdateUserRoles(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add shift', () => {
    const formData = { shiftName: 'Morning', fromTime: '07:00:00', toTime: '16:00:00' };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_SHIFT_MODAL },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Shift added successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onAddShift(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should assign shift to user', () => {
    const formData = { userId: 13, shiftIds: [2], shifts: ['Afternoon'] };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_USER_ASSIGN_SHIFTS, userShifts: formData },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Shift assigned successfully' } },
      { type: types.ON_TOGGLE_ASSIGN_SHIFT_MODAL },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onUserAssignShifts(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Stages', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_STAGES,
        response: [
          { id: 1, roleName: 'Maker' },
          { id: 2, roleName: 'Checker' }
        ]
      }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchStages()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch PartnerId List', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST,
        response: [
          {
            id: 1,
            partnerName: 'SHIVALIK BANK',
            isActive: 1,
            registrationDate: '2023-09-18 04:55:31.347395'
          },
          {
            id: 2,
            partnerName: 'YES BANK',
            isActive: 1,
            registrationDate: '2023-09-18 06:24:06.0'
          },
          {
            id: 3,
            partnerName: 'CO-OP BANK',
            isActive: 1,
            registrationDate: '2023-09-18 06:24:52.0'
          }
        ]
      }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchPartnerIdList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch External CheckerList', () => {
    const expectedActions = [
      {
        type: types.ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS,
        response: responses.uam.externalChecker
      }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchExternalCheckerList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Case Criteria AttributesList', () => {
    const expectedActions = [
      { type: types.ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING },
      {
        type: types.ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS,
        response: responses.uam.caseCriteriaAttributes
      }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchCaseCriteriaAttributesList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should User Assign CaseCriteria', () => {
    const formData = {
      userId: 1,
      caseCriteriaInfo: [
        {
          userRole: 'checker',
          caseCriteria: 'checker'
        }
      ]
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS, response: formData },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Case criteria assigned successfully' }
      },
      { type: types.ON_TOGGLE_USER_CASE_CRITERIA_MODAL },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onUserAssignCaseCriteria(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should User Toggle Auto Case', () => {
    const formData = { userId: 1, userName: 'abc', pauseAutoCase: 1 };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_USER_TOGGLE_AUTO_CASE_SUCCESS,
        response: {
          pauseAutoCase: 1,
          userId: 1,
          userName: 'abc'
        }
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Auto case assignment PAUSED for user - abc` }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onUserToggleAutoCase(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Configurations', () => {
    const expectedActions = [
      {
        type: types.ON_FETCH_CONFIGURATIONS_SUCCESS,
        response: responses.configurations
      }
    ];
    const store = mockStore({ user: {} });

    return store.dispatch(actions.onFetchConfigurations()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
