import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onParkCase } from 'actions/caseAssignmentActions';
import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import * as communicationActions from 'actions/communicationActions';
import * as oneViewActions from 'actions/oneViewActions';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import { onClearSearch } from 'actions/transactionHistorySearchActions';
import CaseOneView from 'components/caseReview/CaseOneView';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  userId: state.auth.userCreds.userId,
  userName: state.auth.userCreds.userName,
  prefiltersList: state.prefiltersList,
  txnDetails: state.transactionDetails.details,
  oneViewData: state.oneView,
  communicationLogs: state.customerCommunication.logs,
  closeCaseBuckets: state.caseAssignment.closeCaseBuckets
});

const mapDispatchToProps = (dispatch) => ({
  parkCase: bindActionCreators(onParkCase, dispatch),
  clearHistory: bindActionCreators(onClearSearch, dispatch),
  oneViewActions: bindActionCreators(oneViewActions, dispatch),
  prefilterActions: bindActionCreators(prefiltersListAction, dispatch),
  communicationActions: bindActionCreators(communicationActions, dispatch),
  fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
});

const CaseOneViewContainer = connect(mapStateToProps, mapDispatchToProps)(CaseOneView);

export default CaseOneViewContainer;
