import _ from 'lodash';
import {
  ON_PRE_EXISTING_CASE,
  ON_SUCCESSFUL_CASE_CREATION,
  ON_FETCH_CLOSURE_CASES_LOADING,
  ON_FETCH_CLOSURE_CASES_SUCCESS,
  ON_FETCH_CLOSURE_CASES_FAILURE,
  ON_CLEAR_BULK_CLOSURE_SEARCH
} from 'constants/actionTypes';
import { onFetchBuckets, onFetchPastInvestigations } from 'actions/caseReviewActions';
import { onToggleLoader, onToggleCreateCaseModal } from 'actions/toggleActions';
import { onFetchCaseDetails, onUpdateCaseDetails } from 'actions/caseDetailsActions';
import { onFetchCaseLogs, onFetchEntityLogs } from 'actions/logsActions';
import {
  onAdvanceSearchTxn,
  onRemoveSearchTxn,
  onSearchTxn
} from 'actions/transactionHistorySearchActions';
import { onShowF<PERSON>ure<PERSON><PERSON>t, onShowSuccessAlert } from 'actions/alertActions';
import { onReviewerParkCase } from 'actions/oneViewActions';
import client from 'utility/apiClient';

function createCaseAndAssign(formData) {
  return client({
    method: 'POST',
    url: `uds/${formData.channel}/createalert`,
    data: formData,
    badRequestMessage: 'Unable to create case'
  });
}

function onSuccessfulCaseAssignment(assignedTo, assignedBy) {
  return {
    type: ON_SUCCESSFUL_CASE_CREATION,
    assignedTo,
    assignedBy
  };
}

function onPreexistingCase() {
  return { type: ON_PRE_EXISTING_CASE };
}

function onCreateCaseAndAssign(formData, searchFormData, selectedCase) {
  return function (dispatch, getState) {
    dispatch(onToggleLoader(true));
    return createCaseAndAssign(formData)
      .then(
        (success) => {
          const { roles } = getState().auth.userCreds;
          dispatch(
            onShowSuccessAlert({
              message: success[0].isSuccess ? 'Successfully opened a new case.' : success[0].reason
            })
          );
          _.has(formData, 'agencyType') && dispatch(onToggleCreateCaseModal());
          if (!_.isEmpty(searchFormData)) {
            dispatch(onRemoveSearchTxn([formData.txnId], formData.channel)),
              roles === 'reviewer' &&
                _.has(searchFormData.formData, 'criterion') &&
                dispatch(
                  onSearchTxn(
                    searchFormData.entityCategory,
                    searchFormData.formData,
                    searchFormData.channel
                  )
                ),
              !_.isEmpty(searchFormData.filters) && dispatch(onAdvanceSearchTxn(searchFormData));
          } else {
            success[0].isSuccess
              ? dispatch(onSuccessfulCaseAssignment(formData.assignedTo, formData.assignedBy))
              : dispatch(onPreexistingCase()),
              dispatch(onFetchBuckets(roles, formData.channel)),
              !_.isEmpty(selectedCase) &&
                (dispatch(onFetchCaseDetails(selectedCase, formData.channel)),
                dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo)),
                dispatch(
                  onFetchEntityLogs(selectedCase.entityId, selectedCase.channel, {
                    pageNo: 1,
                    pageRecords: 10,
                    filterCondition: []
                  })
                ));
          }
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function reassignCase(formData, channel, isBulk = false) {
  return client({
    method: 'POST',
    url: `casereview/case/${channel}/user/reassign${isBulk ? '/bulk' : ''}`,
    data: formData,
    badRequestMessage: 'Unable to reassign case'
  });
}

function onReassignCase(formData, isBulk, channel) {
  return function (dispatch, getState) {
    const pastInvestigationConfig = getState().caseAssignment.pastInvestigations[channel]
      .pastInvestigationConfig;
    const data = {
      pageNo: 1,
      pageRecords: 5,
      filterCondition: pastInvestigationConfig?.filterCondition
    };
    dispatch(onToggleLoader(true));
    return reassignCase(formData, channel, isBulk)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Successfully reassigned case(s) to ' + formData.assignedToName
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, channel));
          // re fetch latest investigation
          pastInvestigationConfig &&
            dispatch(
              onFetchPastInvestigations(
                {
                  channel: pastInvestigationConfig?.channel,
                  entityId: pastInvestigationConfig?.entityId,
                  data
                },
                'byDate'
              )
            );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function reassignCaseToSupervisor(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/user/approval/supervisor/reassign`,
    data: formData,
    badRequestMessage: 'Unable to reassign case'
  });
}

function onReassignCaseToSupervisor(formData, selectedCase) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return reassignCaseToSupervisor(formData)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Successfully reassigned case to ' + formData.supervisorToBeReassignedToName
            })
          );
          dispatch(onFetchCaseDetails(selectedCase, selectedCase.channel));
          dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo));
          selectedCase.entityId &&
            dispatch(
              onFetchEntityLogs(selectedCase.entityId, selectedCase.channel, {
                pageNo: 1,
                pageRecords: 10,
                filterCondition: []
              })
            );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function assignNewCase(formData, channel, isBulk = false) {
  return client({
    method: 'POST',
    url: `casereview/case/${channel}/system/assign/user${isBulk ? '/bulk' : ''}`,
    data: formData,
    badRequestMessage: 'Unable to assign NEW case'
  });
}

function onAssignNewCase(formData, isBulk, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return assignNewCase(formData, channel, isBulk)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Successfully assigned case(s) to ' + formData.assignedToName
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function escalateCase(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/user/escalate`,
    data: formData,
    badRequestMessage: 'Unable to escalate case'
  });
}

function onEscalateCase(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return escalateCase(formData)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Successfully assigned case to maker ' + formData.escalatedTo
            })
          );
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, 'frm'));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function acknowledgeCase(caseRefNo, channel) {
  return client({
    method: 'POST',
    url: `casereview/case/${caseRefNo}/${channel}/acknowledge`,
    data: { isAcknowledged: 1 },
    badRequestMessage: 'Unable to acknowledge case'
  });
}

function onAcknowledgeCase(selectedCase, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return acknowledgeCase(selectedCase.caseRefNo, channel)
      .then(
        () => {
          dispatch(onFetchCaseDetails(selectedCase, channel));
          dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo));
          channel === 'frm' &&
            selectedCase.entityId &&
            dispatch(
              onFetchEntityLogs(selectedCase.entityId, channel, {
                pageNo: 1,
                pageRecords: 10,
                filterCondition: []
              })
            );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCasesForClosure(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/fetch/cases`,
    data: formData,
    badRequestMessage: 'Unable to fetch case for closure'
  });
}

function onFetchCasesForClosureLoading() {
  return { type: ON_FETCH_CLOSURE_CASES_LOADING };
}

function onFetchCasesForClosureSuccess(response) {
  return {
    type: ON_FETCH_CLOSURE_CASES_SUCCESS,
    response
  };
}

function onFetchCasesForClosureFailure(response) {
  return {
    type: ON_FETCH_CLOSURE_CASES_FAILURE,
    response
  };
}

function onFetchCasesForClosure(formData) {
  return function (dispatch) {
    dispatch(onFetchCasesForClosureLoading());
    return fetchCasesForClosure(formData).then(
      (success) => dispatch(onFetchCasesForClosureSuccess(success)),
      (error) => onFetchCasesForClosureFailure({ message: error.responseJSON.detail })
    );
  };
}

function onClearBulkClosureSearch() {
  return function (dispatch) {
    dispatch({ type: ON_CLEAR_BULK_CLOSURE_SEARCH });
  };
}

function reOpenCase(formData, userId, channel) {
  return client({
    url: `casereview/case/${userId}/${channel}/reopen`,
    method: 'PUT',
    data: formData,
    badRequestMessage: 'Unable to re-open case.'
  });
}

function onReOpenCase(formData, channel, selectedCase, currentFormDataAdvanceSearch) {
  return function (dispatch, getState) {
    const { userId } = getState().auth.userCreds;
    dispatch(onToggleLoader(true));
    return reOpenCase(formData, userId, channel)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Case re-opened successfully' }));
          !_.isEmpty(currentFormDataAdvanceSearch)
            ? (dispatch(onRemoveSearchTxn([formData.caseRefNo], channel)),
              dispatch(onAdvanceSearchTxn(currentFormDataAdvanceSearch)))
            : dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function parkCase(formData, userId, channel) {
  return client({
    method: 'PUT',
    url: `casereview/case/${userId}/${channel}/park`,
    data: formData,
    badRequestMessage: 'Unable to park case.'
  });
}

function onParkCase(formData, selectedCase, channel) {
  return function (dispatch, getState) {
    const { roles, userId } = getState().auth.userCreds;
    dispatch(onToggleLoader(true));
    return parkCase(formData, userId, channel)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Case parked successfully' }));
          roles !== 'reviewer' &&
            dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, channel));
          roles === 'reviewer' && dispatch(onReviewerParkCase());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function unparkCase(formData, userId) {
  return client({
    method: 'PUT',
    url: `casereview/case/${userId}/unpark`,
    data: formData,
    badRequestMessage: 'Unable to unpark case.'
  });
}

function onUnparkCase(formData, selectedCase, channel) {
  return function (dispatch, getState) {
    const { userId } = getState().auth.userCreds;
    dispatch(onToggleLoader(true));
    return unparkCase(formData, userId)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Case unparked successfully' }));
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onEscalateCase,
  onReassignCase,
  onAssignNewCase,
  onCreateCaseAndAssign,
  onReassignCaseToSupervisor,
  onAcknowledgeCase,
  onFetchCasesForClosure,
  onClearBulkClosureSearch,
  onReOpenCase,
  onParkCase,
  onUnparkCase
};
