/**
 * Mock for ECharts library to avoid ES module issues in Jest tests
 */

// Mock chart components
export const LineChart = jest.fn();
export const BarChart = jest.fn();
export const PieChart = jest.fn();
export const ScatterChart = jest.fn();

// Mock components
export const GridComponent = jest.fn();
export const TooltipComponent = jest.fn();
export const LegendComponent = jest.fn();
export const TitleComponent = jest.fn();
export const ToolboxComponent = jest.fn();
export const DataZoomComponent = jest.fn();
export const DatasetComponent = jest.fn();
export const MarkPointComponent = jest.fn();

// Mock renderers
export const CanvasRenderer = jest.fn();
export const SVGRenderer = jest.fn();

// Mock core functionality
export const init = jest.fn(() => ({
  setOption: jest.fn(),
  resize: jest.fn(),
  dispose: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  getWidth: jest.fn(() => 400),
  getHeight: jest.fn(() => 300),
  getDom: jest.fn(() => document.createElement('div')),
  getOption: jest.fn(() => ({})),
  clear: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn()
}));

export const use = jest.fn();
export const registerTheme = jest.fn();
export const registerMap = jest.fn();
export const getInstanceByDom = jest.fn();
export const connect = jest.fn();
export const disconnect = jest.fn();
export const dispose = jest.fn();

// Default export for echarts/core
const echarts = {
  init,
  use,
  registerTheme,
  registerMap,
  getInstanceByDom,
  connect,
  disconnect,
  dispose
};

export default echarts;
