server {
         listen 80 default_server;
         listen [::]:80 default_server;

         root /var/www/ifrm-ui/dist;

         index index.html index.htm index.nginx-debian.html;

         server_name bankiq.com;

         access_log /var/log/bankiq/ifrm/nginx/ifrm-access.log;
         error_log /var/log/bankiq/ifrm/nginx/ifrm-error.log;

         location / {
                 try_files $uri /index.html;
         }
         location /api {
	    proxy_pass http://127.0.0.1:80/;
         }

 }
