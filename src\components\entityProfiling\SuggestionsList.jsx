/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

import React from 'react';
import PropTypes from 'prop-types';
import { ListGroupItem, ListGroupItemText, ListGroupItemHeading } from 'reactstrap';
import Loader from 'components/loader/Loader';

const SuggestionsList = ({ loader, error, errorMessage, list, cursor, listEl, selectEntity }) => {
  if (loader) {
    return (
      <ListGroupItem>
        <Loader show={true} />
      </ListGroupItem>
    );
  }

  if (error) {
    return (
      <ListGroupItem>
        <ListGroupItemText>{errorMessage}</ListGroupItemText>
      </ListGroupItem>
    );
  }

  return list.map((entity, i) => (
    <li
      key={entity.id}
      ref={(el) => {
        listEl.current[i] = el;
      }}
      className={i === cursor ? 'list-group-item active' : 'list-group-item'}
      onClick={() => selectEntity(entity)}>
      <ListGroupItemHeading>{entity.name}</ListGroupItemHeading>
      <ListGroupItemText>{entity.entityCategory + ' - ' + entity.id}</ListGroupItemText>
      <ListGroupItemText>{entity.accountNumber + ' | ' + entity.mobileNumber}</ListGroupItemText>
    </li>
  ));
};

SuggestionsList.propTypes = {
  errorMessage: PropTypes.string,
  list: PropTypes.array.isRequired,
  cursor: PropTypes.number.isRequired,
  listEl: PropTypes.object.isRequired,
  selectEntity: PropTypes.func.isRequired,
  loader: PropTypes.bool,
  error: PropTypes.bool
};

export default SuggestionsList;
