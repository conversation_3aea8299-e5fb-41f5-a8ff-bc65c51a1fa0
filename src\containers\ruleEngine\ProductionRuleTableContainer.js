import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as ruleConfiguratorActions from 'actions/ruleConfiguratorActions';
import * as snoozeActions from 'actions/ruleSnoozeActions';

import { onClearValidation } from 'actions/ruleCreationActions';
import { onToggleRuleDuplicateModal } from 'actions/toggleActions';
import ProductionRuleTable from 'components/ruleEngine/ProductionRuleTable';
import {
  getToggleState,
  getUserRoles,
  getProductionRules,
  getSnoozeRulesList,
  getModuleType,
  getSandboxHistory,
  getHasSandbox
} from 'selectors/ruleEngineSelectors';

// Optimized mapStateToProps using memoized selectors
const mapStateToProps = (state) => ({
  toggle: getToggleState(state),
  role: getUserRoles(state),
  ruleList: getProductionRules(state),
  snoozelist: getSnoozeRulesList(state),
  moduleType: getModuleType(state),
  sandboxHistory: getSandboxHistory(state),
  hasSandbox: getHasSandbox(state)
});

const mapDispatchToProps = (dispatch) => ({
  toggleRuleDuplicateModal: bindActionCreators(onToggleRuleDuplicateModal, dispatch),
  ruleConfiguratorActions: bindActionCreators(ruleConfiguratorActions, dispatch),
  clearValidation: bindActionCreators(onClearValidation, dispatch),
  snoozeActions: bindActionCreators(snoozeActions, dispatch)
});

const ProductionRuleTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ProductionRuleTable);

export default ProductionRuleTableContainer;
