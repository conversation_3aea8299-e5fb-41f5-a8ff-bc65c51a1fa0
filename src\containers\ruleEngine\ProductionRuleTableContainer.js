import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onToggleRuleDuplicateModal } from 'actions/toggleActions';
import { onClearValidation } from 'actions/ruleCreationActions';
import * as ruleConfiguratorActions from 'actions/ruleConfiguratorActions';
import ProductionRuleTable from 'components/ruleEngine/ProductionRuleTable';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    role: state.auth.userCreds.roles,
    ruleList: state.ruleConfigurator.productionRules,
    snoozelist: state.snoozeRules.list,
    moduleType: state.auth.moduleType,
    sandboxHistory: state.sandboxing.testHistory,
    hasSandbox: state.user.configurations.sandbox
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleRuleDuplicateModal: bindActionCreators(onToggleRuleDuplicateModal, dispatch),
    ruleConfiguratorActions: bindActionCreators(ruleConfiguratorActions, dispatch),
    clearValidation: bindActionCreators(onClearValidation, dispatch)
  };
};

const ProductionRuleTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ProductionRuleTable);

export default ProductionRuleTableContainer;
