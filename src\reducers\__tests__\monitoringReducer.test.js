import monitoringReducer from 'reducers/monitoringReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';

describe('Monitor reducer', () => {
  it('should return the intial state', () => {
    expect(monitoringReducer(undefined, {})).toEqual(initialState.monitor);
  });

  it('should handle ON_SUCCESSFUL_FETCH_MONITORING_DATA', () => {
    const response = {
      transactions: { 3: [], 2: [], 1: [] },
      flaggedTransactions: 0,
      identifiedFrauds: 0
    };
    expect(
      monitoringReducer(
        {
          transactions: [],
          totalCount: {
            rpsl: 0
          },
          flaggedCount: 0,
          fraudCount: 0
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_MONITORING_DATA,
          response
        }
      )
    ).toEqual({
      transactions: [],
      totalCount: {
        rpsl: 0
      },
      flaggedCount: response.flaggedTransactions,
      fraudCount: response.identifiedFrauds
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT', () => {
    const response = 12;
    expect(
      monitoringReducer(
        {
          transactions: [],
          totalCount: {
            rpsl: 0
          },
          flaggedCount: 0,
          fraudCount: 0
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT,
          response,
          channel: 'rpsl'
        }
      )
    ).toEqual({
      transactions: [],
      totalCount: {
        rpsl: response
      },
      flaggedCount: 0,
      fraudCount: 0
    });
  });

  it('should handle ON_FETCH_MONITORING_DATA_FAILURE', () => {
    expect(
      monitoringReducer(
        {
          transactions: [],
          totalCount: {
            frm: 0,
            str: 0
          },
          flaggedCount: 0,
          fraudCount: 0
        },
        {
          type: types.ON_FETCH_MONITORING_DATA_FAILURE
        }
      )
    ).toEqual({
      transactions: [],
      totalCount: {
        frm: 0,
        str: 0
      },
      flaggedCount: 0,
      fraudCount: 0
    });
  });

  it('should handle ON_FETCH_TRANSACTION_COUNT_FAILURE', () => {
    expect(
      monitoringReducer(
        {
          transactions: [],
          totalCount: {
            frm: 0,
            str: 0
          },
          flaggedCount: 0,
          fraudCount: 0
        },
        {
          type: types.ON_FETCH_TRANSACTION_COUNT_FAILURE
        }
      )
    ).toEqual({
      transactions: [],
      totalCount: {
        frm: 0,
        str: 0
      },
      flaggedCount: 0,
      fraudCount: 0
    });
  });
});
