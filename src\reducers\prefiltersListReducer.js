import { remove, isEmpty, has, unionBy, uniqBy, mapValues, isDate } from 'lodash';
import moment from 'moment';
import objectAssign from 'object-assign';

import {
  ON_FETCH_SPECIALIZED_LIST_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_LOADING,
  ON_FETCH_SPECIALIZED_LIST_SUCCESS,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING,
  ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS,
  ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS,
  ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM,
  ON_FETCH_LIMIT_LIST_LOADING,
  ON_FETCH_LIMIT_LIST_SUCCESS,
  ON_<PERSON>ETCH_LIMIT_LIST_FAILURE,
  ON_FETCH_LIMIT_TYPE_LOADING,
  ON_FETCH_LIMIT_TYPE_SUCCESS,
  ON_FETCH_LIMIT_TYPE_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE,
  ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING,
  ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS,
  ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM,
  ON_FETCH_ALL_LISTS_LOADING,
  ON_FETCH_ALL_LISTS_SUCCESS,
  ON_FETCH_ALL_LISTS_FAILURE,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS,
  ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE,
  ON_RESET_LIMIT_LIST_WITH_PAGINATION,
  ON_ADD_TO_LIST_ITEM_CLICK,
  ON_LIST_BULK_UPLOAD_INITIATED,
  ON_LIST_BULK_UPLOAD_COMPLETED,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS,
  ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE
} from 'constants/actionTypes';
import { isCooperative } from 'constants/publicKey';

import initialState from './initialState';

const updateItemStatus = (data, formData) =>
  data.map((item) => {
    const updatedFormData = objectAssign(
      {},
      item,
      mapValues(formData, (value, key) => {
        if (isDate(value) || key === 'startDate' || key === 'endDate')
          return moment(value).format('YYYY-MM-DDTHH:mm:ss.SSS'); // Formatting as per original

        return value;
      })
    );

    return item.identifier === formData.identifier &&
      (!isCooperative || item.partnerId === formData.partnerId)
      ? updatedFormData
      : item;
  });

const removeItem = (data, entity, listType) => {
  const isSpecializedListItem = (item, entity, isCooperative) => {
    const baseMatch =
      item.identifier === entity.identifier && item.categoryName === entity.categoryName;
    return isCooperative ? baseMatch && item.partnerId === entity.partnerId : baseMatch;
  };

  const isMerchantIdItem = (item, entity) =>
    item[entity.keyName] === entity.type && item.limitType === entity.merchantlimitType;

  const isGenericItem = (item, entity) => item[entity.keyName] === entity.type;

  remove(data, function (item) {
    if (listType === 'specializedList') return isSpecializedListItem(item, entity, isCooperative);
    else if (entity.keyName === 'merchantId') return isMerchantIdItem(item, entity);
    else return isGenericItem(item, entity);
  });
  return data;
};

const addDataToMerchantLimit = (data, resp, currentId) => {
  if (has(resp, 'merchantdaily')) {
    const filteredResp = unionBy(data.merchantdaily, resp.merchantdaily, 'merchantId');
    return objectAssign({}, resp, {
      merchantdaily: filteredResp
    });
  } else {
    const updatedResp = data?.merchant?.filter((item) => item.id !== currentId);
    const filteredResp = unionBy(updatedResp, resp.merchant, 'id');
    return objectAssign({}, resp, {
      merchant: filteredResp
    });
  }
};

export default function prefiltersListReducer(state = initialState.prefiltersList, action) {
  switch (action.type) {
    case ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING:
      return objectAssign({}, state, {
        specializedListTypes: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE:
      return objectAssign({}, state, {
        specializedListTypes: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS:
      return objectAssign({}, state, {
        specializedListTypes: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SPECIALIZED_LIST_LOADING:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_SPECIALIZED_LIST_FAILURE:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          conf: action.conf,
          data:
            !action.hydrate ||
            state.specializedList.conf.listName !== action.conf.listName ||
            state.specializedList.conf.categoryName !== action.conf.categoryName ||
            state.specializedList.conf.identifier !== action.conf.identifier
              ? initialState.prefiltersList.specializedList.data
              : state.specializedList.data,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_SPECIALIZED_LIST_SUCCESS:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          conf: action.conf,
          data:
            !action.hydrate ||
            state.specializedList.conf.listName !== action.conf.listName ||
            state.specializedList.conf.categoryName !== action.conf.categoryName ||
            state.specializedList.conf.identifier !== action.conf.identifier
              ? action.response
              : {
                  ...action.response,
                  listInfo: unionBy(
                    state.specializedList.data.listInfo,
                    action.response.listInfo,
                    'id'
                  )
                },
          loader: false
        })
      });
    case ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING:
      return objectAssign({}, state, {
        category: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE:
      return objectAssign({}, state, {
        category: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS:
      return objectAssign({}, state, {
        category: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          data: objectAssign({}, state.specializedList.data, {
            listInfo: updateItemStatus(state.specializedList.data.listInfo, action.formData)
          })
        })
      });
    case ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          data: objectAssign({}, state.specializedList.data, {
            listInfo: removeItem(
              state.specializedList.data.listInfo,
              action.formData,
              'specializedList'
            )
          })
        })
      });
    case ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM:
      return objectAssign({}, state, {
        limitList: objectAssign({}, state.limitList, {
          data: action.entity.isWithPagination
            ? removeItem(
                state.limitListWithPagination.data[action.entity.dataKey],
                action.entity,
                'limitList'
              )
            : removeItem(state.limitList.data, action.entity, 'limitList')
        })
      });
    case ON_FETCH_LIMIT_LIST_LOADING:
      return objectAssign({}, state, {
        limitList: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_LIMIT_LIST_FAILURE:
      return objectAssign({}, state, {
        limitList: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_LIMIT_LIST_SUCCESS:
      return objectAssign({}, state, {
        limitList: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_LIMIT_TYPE_LOADING:
      return objectAssign({}, state, {
        limitType: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_LIMIT_TYPE_FAILURE:
      return objectAssign({}, state, {
        limitType: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_LIMIT_TYPE_SUCCESS:
      return objectAssign({}, state, {
        limitType: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_LISTS_LOADING:
      return objectAssign({}, state, {
        allLists: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_LISTS_FAILURE:
      return objectAssign({}, state, {
        allLists: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_ALL_LISTS_SUCCESS:
      return objectAssign({}, state, {
        allLists: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING:
      return objectAssign({}, state, {
        limitListWithPagination: objectAssign({}, state.limitListWithPagination, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE:
      return objectAssign({}, state, {
        limitListWithPagination: objectAssign({}, state.limitListWithPagination, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS:
      return objectAssign({}, state, {
        limitListWithPagination: {
          data:
            isEmpty(state.limitListWithPagination.data) ||
            (action.fetchFrom && action.fetchFrom === 'search')
              ? action.response
              : addDataToMerchantLimit(
                  state.limitListWithPagination.data,
                  action.response,
                  action.currentId
                ),
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_RESET_LIMIT_LIST_WITH_PAGINATION:
      return objectAssign({}, state, {
        limitListWithPagination: {
          data: {},
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_ADD_TO_LIST_ITEM_CLICK:
      return objectAssign({}, state, {
        addToListCurrentItems: {
          currentListInfo: action.currentItemInfo.currentListInfo,
          currentIndex: action.currentItemInfo.currentIndex,
          currentCategoryName: action.currentItemInfo.currentCategoryName,
          currentIdentifier: action.currentItemInfo.currentIdentifier,
          currentPartnerId: action.currentItemInfo.currentPartnerId
        }
      });
    case ON_LIST_BULK_UPLOAD_INITIATED:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          fileUpload: action.response
        })
      });
    case ON_LIST_BULK_UPLOAD_COMPLETED:
      return objectAssign({}, state, {
        specializedList: objectAssign({}, state.specializedList, {
          fileUpload: initialState.prefiltersList.specializedList.fileUpload
        })
      });
    case ON_FETCH_BLOCKED_LIST_IDENTIFIER_LOADING:
      return objectAssign({}, state, {
        blockedIdentifiers: objectAssign({}, state.blockedIdentifiers, {
          loader: true,
          txnId: action.txnId,
          ...(state.blockedIdentifiers.txnId !== action.txnId && {
            data: [],
            error: false,
            errorMessage: []
          })
        })
      });
    case ON_FETCH_BLOCKED_LIST_IDENTIFIER_SUCCESS:
      return objectAssign({}, state, {
        blockedIdentifiers: objectAssign({}, state.blockedIdentifiers, {
          loader: false,
          data: action.response?.id
            ? uniqBy([...state.blockedIdentifiers.data, action.response], 'id')
            : state.blockedIdentifiers.data
        })
      });
    case ON_FETCH_BLOCKED_LIST_IDENTIFIER_FAILURE:
      return objectAssign({}, state, {
        blockedIdentifiers: objectAssign({}, state.blockedIdentifiers, {
          loader: false,
          error: true,
          errorMessage: [...state.blockedIdentifiers.errorMessage, action.response.message]
        })
      });
    default:
      return state;
  }
}
