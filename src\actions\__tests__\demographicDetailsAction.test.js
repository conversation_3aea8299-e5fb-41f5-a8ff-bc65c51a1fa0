import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/demographicDetailsAction';
import responses from 'mocks/responses';

describe('demographic details actions', () => {
  it('should fetch merchant demographic details', () => {
    const merchantId = '123';

    const expectedActions = [
      { type: types.ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS,
        response: responses.demographics.merchant
      }
    ];
    const store = mockStore({ demographicDetails: {} });

    return store.dispatch(actions.onFetchMerchantDemographicDetails(merchantId)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch agent demographic details', () => {
    const agentId = '123';

    const expectedActions = [
      { type: types.ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS,
        response: responses.demographics.merchant
      }
    ];
    const store = mockStore({ demographicDetails: {} });

    return store.dispatch(actions.onFetchAgentDemographicDetails(agentId)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
