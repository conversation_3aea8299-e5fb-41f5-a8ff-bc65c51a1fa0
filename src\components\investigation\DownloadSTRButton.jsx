import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGavel } from '@fortawesome/free-solid-svg-icons';
import { BASE_URL, URL_CONTEXT } from 'constants/applicationConstants';

const DownloadSTRButton = ({ caseRefNo, entityId, getSTRLogs }) => {
  const downloadReport = (caseRefNo, entityId) => {
    const a = document.createElement('a');
    a.download = 'STR-' + caseRefNo + '.zip';
    a.href = BASE_URL + URL_CONTEXT + `casereview/case/${caseRefNo}/merchant/${entityId}/download`;
    a.target = '_blank';
    a.rel = 'noopener noreferrer';
    a.click();
  };

  function downloadReportAndFetchLogs(caseRefNo, entityId) {
    downloadReport(caseRefNo, entityId);
    getSTRLogs(caseRefNo);
  }

  return (
    <Button
      outline
      size="sm"
      color="success"
      className="ms-1"
      title="Suggest Action"
      onClick={() => downloadReportAndFetchLogs(caseRefNo, entityId)}>
      <FontAwesomeIcon icon={faGavel} /> Download STR
    </Button>
  );
};

DownloadSTRButton.propTypes = {
  entityId: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  getSTRLogs: PropTypes.func.isRequired
};

export default DownloadSTRButton;
