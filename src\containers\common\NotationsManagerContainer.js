import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as notationActions from 'actions/notationActions';
import NotationsManager from 'components/common/NotationsManager';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    notations: state.notations.master
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    notationActions: bindActionCreators(notationActions, dispatch)
  };
};

const NotationsManagerContainer = connect(mapStateToProps, mapDispatchToProps)(NotationsManager);

export default NotationsManagerContainer;
