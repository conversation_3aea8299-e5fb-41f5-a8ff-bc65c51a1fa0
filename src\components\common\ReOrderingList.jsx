import { faCaretDown, faCaretUp } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { chain, sortBy } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { Button, ListGroup, ListGroupItem } from 'reactstrap';

function ReOrderingList({ idKey, list, submitUpdatedOrder, formatter = (d) => d.label }) {
  const [order, setOrder] = useState([]);
  const [updatedValues, setUpdatedValues] = useState([]);

  useEffect(() => {
    setOrder(sortBy(list, 'order'));
    setUpdatedValues([]);
  }, [list]);

  useEffect(() => {
    const updates = order?.filter(
      (obj1) => !list.some((obj2) => obj1?.order === obj2?.order && obj1?.[idKey] === obj2?.[idKey])
    );
    setUpdatedValues(updates);
  }, [order]);

  function onChangeOrder(list, item, direction) {
    let newList = [];
    const newOrder = direction === 'up' ? item.order - 1 : item.order + 1;
    newList = chain(list)
      .map((d) => {
        if (d.order === newOrder) return { ...d, order: item.order };
        if (d.order === item.order) return { ...d, order: newOrder };
        return d;
      })
      .sortBy('order')
      .value();
    setOrder(() => newList);
  }

  return (
    <>
      <ListGroup className="mt-2">
        {order?.map((d, i, order) => (
          <ListGroupItem key={d?.[idKey]} className="d-flex justify-content-between">
            {i !== 0 ? (
              <Button
                color="link"
                size="sm"
                className="me-3"
                title="Move up in order"
                onClick={() => onChangeOrder(order, d, 'up')}>
                <FontAwesomeIcon icon={faCaretUp} />
              </Button>
            ) : (
              <span size="sm" className="me-5" />
            )}
            <span className="flex-grow-1">{formatter(d)}</span>
            {order.length !== i + 1 && (
              <Button
                color="link"
                size="sm"
                className="ms-3"
                title="Move up in order"
                onClick={() => onChangeOrder(order, d, 'down')}>
                <FontAwesomeIcon icon={faCaretDown} title="Move down in order" className="ms-4" />
              </Button>
            )}
          </ListGroupItem>
        ))}
      </ListGroup>
      {updatedValues.length > 0 && (
        <div className="d-flex mt-3 justify-content-end">
          <Button size="sm" color="primary" onClick={() => submitUpdatedOrder(updatedValues)}>
            Save
          </Button>
        </div>
      )}
    </>
  );
}

ReOrderingList.propTypes = {
  formatter: PropTypes.func,
  idKey: PropTypes.string.isRequired,
  list: PropTypes.array.isRequired,
  submitUpdatedOrder: PropTypes.func.isRequired
};

export default ReOrderingList;
