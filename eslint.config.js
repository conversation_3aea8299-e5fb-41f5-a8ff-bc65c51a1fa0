const babelParser = require('@babel/eslint-parser');
const { fixupConfigRules, fixupPluginRules } = require('@eslint/compat');
const { FlatCompat } = require('@eslint/eslintrc');
const js = require('@eslint/js');
const { defineConfig } = require('eslint/config');
const _import = require('eslint-plugin-import');
const prettier = require('eslint-plugin-prettier');
const react = require('eslint-plugin-react');
const reactHooks = require('eslint-plugin-react-hooks');
const unusedImports = require('eslint-plugin-unused-imports');
const globals = require('globals');

// FlatCompat setup
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended
});

module.exports = defineConfig([
  {
    files: ['**/*.js', '**/*.jsx'],

    extends: fixupConfigRules(
      compat.extends(
        'eslint:recommended',
        'plugin:import/recommended',
        'plugin:import/errors',
        'plugin:import/warnings',
        'plugin:react/recommended',
        'plugin:jsx-a11y/recommended',
        'plugin:prettier/recommended'
      )
    ),

    plugins: {
      react: fixupPluginRules(react),
      import: fixupPluginRules(_import),
      prettier: fixupPluginRules(prettier),
      'unused-imports': fixupPluginRules(unusedImports),
      'react-hooks': fixupPluginRules(reactHooks)
    },

    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.jest
      },
      parser: babelParser,
      ecmaVersion: 2021,
      sourceType: 'module',
      parserOptions: {
        requireConfigFile: false,
        sourceType: 'module',
        babelOptions: {
          presets: ['@babel/preset-env', '@babel/preset-react']
        },
        ecmaFeatures: { jsx: true }
      }
    },

    settings: {
      react: {
        version: 'detect'
      },
      'import/resolver': {
        node: {
          paths: ['src'],
          extensions: ['.js', '.jsx']
        }
      }
    },
    rules: {
      'no-console': 'warn',
      'no-debugger': 'warn',
      'no-unused-vars': 'off',
      'no-var': 'error',
      'prefer-const': 'error',
      'prefer-template': 'warn',
      'no-duplicate-imports': 'error',
      eqeqeq: ['error', 'always'],
      curly: ['error', 'multi'],
      'no-multi-spaces': 'error',
      'object-shorthand': ['warn', 'always'],
      'arrow-body-style': ['warn', 'as-needed'],
      'no-nested-ternary': 'warn',

      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_'
        }
      ],
      'import/extensions': ['off', 'ignorePackages'],
      'import/no-unresolved': ['error', { ignore: ['^mocks/.+'] }],
      'import/order': [
        'warn',
        {
          groups: ['builtin', 'external', 'internal', ['parent', 'sibling', 'index']],
          'newlines-between': 'always',
          alphabetize: { order: 'asc', caseInsensitive: true }
        }
      ],

      'react/display-name': 'off',
      'react/prop-types': 'warn',
      'react/jsx-uses-react': 'warn',
      'react/jsx-uses-vars': 'warn',
      'react/no-unknown-property': 'warn',
      'react/self-closing-comp': 'warn',
      'react/no-danger': 'warn',
      'react/no-direct-mutation-state': 'error',
      'react/prefer-es6-class': 'warn',
      'react/react-in-jsx-scope': 'warn',
      'react/jsx-no-bind': ['warn', { allowArrowFunctions: true }],
      'react/jsx-curly-brace-presence': ['warn', { props: 'never', children: 'never' }],
      'react/jsx-no-useless-fragment': 'warn',

      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': [
        'warn',
        {
          additionalHooks: '(useEffect|useLayoutEffect)'
        }
      ],

      'jsx-a11y/anchor-is-valid': ['warn', { components: ['Link'], specialLink: ['to'] }],
      'jsx-a11y/alt-text': 'warn',
      'jsx-a11y/no-autofocus': 'warn',
      'jsx-a11y/label-has-associated-control': [
        'warn',
        {
          assert: 'either',
          depth: 3
        }
      ],

      'prettier/prettier': ['warn', {}, { usePrettierrc: true }]
    }
  }
]);
