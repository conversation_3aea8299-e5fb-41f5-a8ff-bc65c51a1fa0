import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

function ProvisionalFieldsValue({ attrName, provisionalFields = [] }) {
  return (
    <>
      {_.chain(provisionalFields)
        .filter((data) => data.attrName == attrName)
        .map((data) => data.attrValue)
        .head()
        .value()}
    </>
  );
}

ProvisionalFieldsValue.propTypes = {
  attrName: PropTypes.string.isRequired,
  provisionalFields: PropTypes.array.isRequired
};

export default ProvisionalFieldsValue;
