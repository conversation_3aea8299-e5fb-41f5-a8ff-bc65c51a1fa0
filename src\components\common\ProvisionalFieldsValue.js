import _ from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';

function ProvisionalFieldsValue({ attrName, provisionalFields = [] }) {
  return (
    <>
      {_.chain(provisionalFields)
        .filter((data) => data.attrName === attrName)
        .map((data) => data.attrValue)
        .head()
        .value()}
    </>
  );
}

ProvisionalFieldsValue.propTypes = {
  attrName: PropTypes.string.isRequired,
  provisionalFields: PropTypes.array.isRequired
};

export default ProvisionalFieldsValue;
