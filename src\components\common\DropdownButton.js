import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { ButtonDropdown, DropdownToggle, DropdownMenu } from 'reactstrap';

const DropdownButton = ({ disabled, name, children, color, title }) => {
  const [toggle, setToggle] = useState(false);
  return (
    <ButtonDropdown
      size="sm"
      toggle={() => setToggle(!toggle)}
      disabled={disabled}
      isOpen={toggle}
      className="ms-1"
      title={title || undefined}>
      <DropdownToggle outline caret disabled={disabled} color={color || 'primary'} size="sm">
        {name}
      </DropdownToggle>
      <DropdownMenu>{children}</DropdownMenu>
    </ButtonDropdown>
  );
};

DropdownButton.propTypes = {
  disabled: PropTypes.bool,
  name: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
  color: PropTypes.string,
  title: PropTypes.string,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]).isRequired
};

export default DropdownButton;
