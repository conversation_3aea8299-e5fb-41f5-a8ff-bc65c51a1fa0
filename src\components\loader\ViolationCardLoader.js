import React from 'react';
import { Row, Col } from 'reactstrap';
import { range } from 'lodash';

const ViolationCardLoader = () => {
  return (
    <Row className="txn-info">
      {range(2).map((d) => (
        <Col key={d} md="6" sm="12" xs="12">
          <p className="card-subtitle-placeholder">{''}</p>
          <br />
          <br />
          <span className="violation-badge-placeholder">{''}</span>
          <span className="violation-text-placeholder">{''}</span>
          <span className="violation-text-placeholder">{''}</span>
        </Col>
      ))}
    </Row>
  );
};

export default ViolationCardLoader;
