import { groupBy } from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_FILTER_FAILURE,
  ON_FETCH_FILTER_LOADING,
  ON_FETCH_FILTER_SUCCESS,
  ON_FETCH_TPS_LIMIT_FAILURE,
  ON_FETCH_TPS_LIMIT_LOADING,
  ON_FETCH_TPS_LIMIT_SUCCESS,
  ON_FETCH_FILTER_CATEGORY_FAILURE,
  ON_FETCH_FILTER_CATEGORY_LOADING,
  ON_FETCH_FILTER_CATEGORY_SUCCESS
} from 'constants/actionTypes';

import initialState from './initialState';

const groupByChannel = (list) => groupBy(list, 'channelName');

export default function prefilterReducer(state = initialState.prefilter, action) {
  switch (action.type) {
    case ON_FETCH_FILTER_LOADING:
      return objectAssign({}, state, {
        filter: objectAssign({}, state.filter, { loader: true })
      });
    case ON_FETCH_FILTER_FAILURE:
      return objectAssign({}, state, {
        filter: objectAssign({}, state.filter, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_FILTER_SUCCESS:
      return objectAssign({}, state, {
        filter: objectAssign({}, state.filter, {
          loader: false,
          list: groupByChannel(action.response)
        })
      });
    case ON_FETCH_TPS_LIMIT_LOADING:
      return objectAssign({}, state, {
        tps: objectAssign({}, state.tps, { loader: true })
      });
    case ON_FETCH_TPS_LIMIT_FAILURE:
      return objectAssign({}, state, {
        tps: objectAssign({}, state.tps, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_TPS_LIMIT_SUCCESS:
      return objectAssign({}, state, {
        tps: objectAssign({}, state.tps, {
          loader: false,
          list: action.response
        })
      });
    case ON_FETCH_FILTER_CATEGORY_LOADING:
      return objectAssign({}, state, {
        category: objectAssign({}, state.category, { loader: true })
      });
    case ON_FETCH_FILTER_CATEGORY_FAILURE:
      return objectAssign({}, state, {
        category: objectAssign({}, state.category, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_FILTER_CATEGORY_SUCCESS:
      return objectAssign({}, state, {
        category: objectAssign({}, state.category, {
          loader: false,
          list: groupByChannel(action.response)
        })
      });
    default:
      return state;
  }
}
