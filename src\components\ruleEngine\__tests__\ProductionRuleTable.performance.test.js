import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { createStore } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import ProductionRuleTable from '../ProductionRuleTable';

// Mock dependencies
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: () => ({ pathname: '/rule-engine' })
}));

jest.mock('containers/ruleEngine/RuleTableContainer', () =>
  jest.fn(({ data, channel }) => (
    <div data-testid="rule-table">
      Rule Table - {channel} - {data?.list?.[channel]?.length || 0} rules
    </div>
  ))
);

const createMockStore = () => createStore(() => ({}));

describe('ProductionRuleTable Performance Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const createLargeRuleList = (count) =>
    Array.from({ length: count }, (_, i) => ({
      code: `RULE_${i}`,
      name: `Rule ${i}`,
      logic: `TXN.AMOUNT > ${1000 + i}`,
      order: i + 1,
      status: i % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
      createdBy: `user${i % 5}`,
      createdTimestamp: new Date(Date.now() - i * 86400000).toISOString()
    }));

  const defaultProps = {
    channel: 'frm',
    role: 'checker',
    ruleList: {
      list: { frm: createLargeRuleList(10) },
      loader: false,
      error: false
    },
    snoozelist: { frm: [] },
    moduleType: 'acquirer',
    sandboxHistory: { list: [] },
    hasSandbox: 1,
    toggle: {
      ruleDuplicateModal: { frm: false },
      theme: 'light'
    },
    ruleConfiguratorActions: {
      onFetchProductionRulesList: jest.fn(),
      onToggleRuleStatus: jest.fn(),
      onDeleteRule: jest.fn(),
      onFetchRuleNames: jest.fn()
    },
    onClearValidation: jest.fn(),
    onToggleRuleDuplicateModal: jest.fn()
  };

  const renderWithProviders = (component) =>
    render(
      <Provider store={store}>
        <BrowserRouter>{component}</BrowserRouter>
      </Provider>
    );

  it('should render large rule lists efficiently', () => {
    const largeRuleProps = {
      ...defaultProps,
      ruleList: {
        ...defaultProps.ruleList,
        list: { frm: createLargeRuleList(500) }
      }
    };

    performanceMonitor.startTiming('large_rule_list_render');

    renderWithProviders(<ProductionRuleTable {...largeRuleProps} />);

    const duration = performanceMonitor.endTiming('large_rule_list_render');
    expect(duration).toBeLessThan(300);
    expect(screen.getByText('Rule Table - frm - 500 rules')).toBeInTheDocument();
  });

  it('should handle filtering efficiently', () => {
    renderWithProviders(<ProductionRuleTable {...defaultProps} />);

    const filterInput = screen.getByPlaceholderText('Filter by name...');

    performanceMonitor.startTiming('filter_operation');

    fireEvent.change(filterInput, { target: { value: 'Rule 1' } });

    const duration = performanceMonitor.endTiming('filter_operation');
    expect(duration).toBeLessThan(50);
  });

  it('should handle select all operations efficiently', () => {
    renderWithProviders(<ProductionRuleTable {...defaultProps} />);

    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i });

    performanceMonitor.startTiming('select_all_operation');

    fireEvent.click(selectAllCheckbox);

    const duration = performanceMonitor.endTiming('select_all_operation');
    expect(duration).toBeLessThan(100);
  });

  it('should memoize filtered rule list correctly', () => {
    const { rerender } = renderWithProviders(<ProductionRuleTable {...defaultProps} />);

    // Set up filter
    const filterInput = screen.getByPlaceholderText('Filter by name...');
    fireEvent.change(filterInput, { target: { value: 'Rule 1' } });

    performanceMonitor.startTiming('memoized_filter_rerender');

    // Re-render with same props
    rerender(
      <Provider store={store}>
        <BrowserRouter>
          <ProductionRuleTable {...defaultProps} />
        </BrowserRouter>
      </Provider>
    );

    const duration = performanceMonitor.endTiming('memoized_filter_rerender');
    expect(duration).toBeLessThan(30);
  });

  it('should handle bulk operations efficiently', async () => {
    const mockToggleStatus = jest.fn().mockResolvedValue({});
    const propsWithBulkActions = {
      ...defaultProps,
      ruleConfiguratorActions: {
        ...defaultProps.ruleConfiguratorActions,
        onToggleRuleStatus: mockToggleStatus
      }
    };

    renderWithProviders(<ProductionRuleTable {...propsWithBulkActions} />);

    // Select multiple rules
    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i });
    fireEvent.click(selectAllCheckbox);

    performanceMonitor.startTiming('bulk_status_toggle');

    const bulkToggleButton = screen.getByText('Toggle Status');
    fireEvent.click(bulkToggleButton);

    const duration = performanceMonitor.endTiming('bulk_status_toggle');
    expect(duration).toBeLessThan(50);

    await waitFor(() => {
      expect(mockToggleStatus).toHaveBeenCalled();
    });
  });

  it('should not cause memory leaks during sorting operations', () => {
    renderWithProviders(<ProductionRuleTable {...defaultProps} />);

    performanceMonitor.takeMemorySnapshot('before_sorting');

    // Perform multiple sort operations
    const sortableHeaders = ['Name', 'Order', 'Status'];
    sortableHeaders.forEach((header) => {
      const headerElement = screen.getByText(header);
      fireEvent.click(headerElement);
      fireEvent.click(headerElement); // Reverse sort
    });

    performanceMonitor.takeMemorySnapshot('after_sorting');
    performanceMonitor.checkMemoryLeaks();

    expect(true).toBe(true);
  });

  it('should handle rapid filter changes without performance degradation', () => {
    renderWithProviders(<ProductionRuleTable {...defaultProps} />);

    const filterInput = screen.getByPlaceholderText('Filter by name...');

    performanceMonitor.startTiming('rapid_filter_changes');

    // Simulate rapid typing
    const searchTerms = ['R', 'Ru', 'Rul', 'Rule', 'Rule ', 'Rule 1'];
    searchTerms.forEach((term) => {
      fireEvent.change(filterInput, { target: { value: term } });
    });

    const duration = performanceMonitor.endTiming('rapid_filter_changes');
    expect(duration).toBeLessThan(100);
  });

  it('should maintain performance with complex rule data', () => {
    const complexRuleList = Array.from({ length: 100 }, (_, i) => ({
      code: `COMPLEX_RULE_${i}`,
      name: `Complex Rule ${i} with very long name and description`,
      logic: `(TXN.AMOUNT > ${1000 + i} AND CARD.TYPE == "CREDIT") OR (MERCHANT.CATEGORY == "HIGH_RISK" AND TXN.CURRENCY != "USD")`,
      order: i + 1,
      status: i % 3 === 0 ? 'ACTIVE' : i % 3 === 1 ? 'INACTIVE' : 'PENDING',
      createdBy: `user${i % 10}`,
      createdTimestamp: new Date(Date.now() - i * 86400000).toISOString(),
      tags: [`tag${i % 5}`, `category${i % 3}`],
      metadata: {
        complexity: 'HIGH',
        riskLevel: i % 4,
        lastModified: new Date().toISOString()
      }
    }));

    const complexProps = {
      ...defaultProps,
      ruleList: {
        ...defaultProps.ruleList,
        list: { frm: complexRuleList }
      }
    };

    performanceMonitor.startTiming('complex_data_render');

    renderWithProviders(<ProductionRuleTable {...complexProps} />);

    const duration = performanceMonitor.endTiming('complex_data_render');
    expect(duration).toBeLessThan(400);
  });
});
