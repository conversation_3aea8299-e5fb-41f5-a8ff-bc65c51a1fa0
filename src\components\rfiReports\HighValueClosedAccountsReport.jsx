import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Row, Col, CardTitle, CardSubtitle } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

import RFIReportsTable from './RFIReportsTable';

function HighValueClosedAccountsReport({
  highValueClosedAccount,
  fetchHighValueClosedAccountCount,
  fetchHighValueClosedAccountData
}) {
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  useEffect(() => {
    fetchHighValueClosedAccountCount();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePageChange = (page) => {
    fetchHighValueClosedAccountData({ pageNo: page + 1, pageSize });
    setPageNo(page);
  };

  const headers = [
    {
      Header: 'Account Closed On',
      accessor: 'accountClosingDate',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    {
      Header: 'Credit',
      columns: [
        {
          Header: 'Amount',
          accessor: 'totalCreditTxnAmount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnAmount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalCreditTxnCount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnCount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    },
    {
      Header: 'Debit',
      columns: [
        {
          Header: 'Amount',
          accessor: 'totalDebitTxnAmount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalDebitTxnAmount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalDebitTxnCount',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalDebitTxnCount'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  return (
    <Row>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{highValueClosedAccount.count?.value ?? 0}</CardTitle>
          <CardSubtitle># High Volume Closed Accounts</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="High Volume Closed Accounts">
          {highValueClosedAccount.count?.value > 0 ? (
            <RFIReportsTable
              count={highValueClosedAccount.count?.value}
              additionalHeaders={headers}
              data={highValueClosedAccount.data}
              fetchData={fetchHighValueClosedAccountData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div"> No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

HighValueClosedAccountsReport.propTypes = {
  highValueClosedAccount: PropTypes.object.isRequired,
  fetchHighValueClosedAccountCount: PropTypes.func.isRequired,
  fetchHighValueClosedAccountData: PropTypes.func.isRequired
};

export default HighValueClosedAccountsReport;
