import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import incidentsReducer from 'reducers/incidentsReducer';
import initialState from 'reducers/initialState';

describe('incidents Reducer', () => {
  it('should return the intial state', () => {
    expect(incidentsReducer(undefined, {})).toEqual(initialState.incidents);
  });

  it('should handle ON_FETCH_ALL_INCIDENTS_LOADING', () => {
    expect(
      incidentsReducer(
        {
          allIncidents: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_INCIDENTS_LOADING
        }
      )
    ).toEqual({
      allIncidents: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_INCIDENTS_SUCCESS', () => {
    expect(
      incidentsReducer(
        {
          allIncidents: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_INCIDENTS_SUCCESS,
          response: responses.incidents.allIncidents
        }
      )
    ).toEqual({
      allIncidents: {
        list: responses.incidents.allIncidents,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_INCIDENTS_FAILURE', () => {
    expect(
      incidentsReducer(
        {
          allIncidents: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_INCIDENTS_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      allIncidents: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_INCIDENT_BY_ID_LOADING', () => {
    expect(
      incidentsReducer(
        {
          incident: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_INCIDENT_BY_ID_LOADING
        }
      )
    ).toEqual({
      incident: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_INCIDENT_BY_ID_SUCCESS', () => {
    expect(
      incidentsReducer(
        {
          incident: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_INCIDENT_BY_ID_SUCCESS,
          response: responses.incidents.incident
        }
      )
    ).toEqual({
      incident: {
        data: responses.incidents.incident,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_INCIDENT_BY_ID_FAILURE', () => {
    expect(
      incidentsReducer(
        {
          incident: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_INCIDENT_BY_ID_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      incident: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_MASTERS_LOADING', () => {
    expect(
      incidentsReducer(
        {
          masters: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_MASTERS_LOADING
        }
      )
    ).toEqual({
      masters: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_MASTERS_SUCCESS', () => {
    expect(
      incidentsReducer(
        {
          masters: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_MASTERS_SUCCESS,
          response: responses.incidents.masters
        }
      )
    ).toEqual({
      masters: {
        data: responses.incidents.masters,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_MASTERS_FAILURE', () => {
    expect(
      incidentsReducer(
        {
          masters: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_MASTERS_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      masters: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_CASE_CLOSED_CPIFR_LOADING', () => {
    expect(
      incidentsReducer(
        {
          closedList: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_CLOSED_CPIFR_LOADING
        }
      )
    ).toEqual({
      closedList: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS', () => {
    expect(
      incidentsReducer(
        {
          closedList: {
            conf: {
              sortBy: 'weight',
              sortOrder: 'desc',
              filterCondition: []
            },
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS,
          response: responses.incidents.closedList,
          conf: {
            sortBy: 'weight',
            sortOrder: 'desc',
            filterCondition: []
          }
        }
      )
    ).toEqual({
      closedList: {
        conf: {
          sortBy: 'weight',
          sortOrder: 'desc',
          filterCondition: []
        },
        list: responses.incidents.closedList,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_CLOSED_CPIFR_FAILURE', () => {
    expect(
      incidentsReducer(
        {
          closedList: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_CLOSED_CPIFR_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      closedList: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_REPORT_HISTORY_LOADING', () => {
    expect(
      incidentsReducer(
        {
          history: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_REPORT_HISTORY_LOADING
        }
      )
    ).toEqual({
      history: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS', () => {
    expect(
      incidentsReducer(
        {
          history: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS,
          response: responses.incidents.history
        }
      )
    ).toEqual({
      history: {
        list: responses.incidents.history,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE', () => {
    expect(
      incidentsReducer(
        {
          history: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      history: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_CLEAR_SELECTED_INCIDENT', () => {
    expect(
      incidentsReducer(
        {
          incident: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_CLEAR_SELECTED_INCIDENT
        }
      )
    ).toEqual({
      incident: {
        data: {},
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });
});
