import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import violatedRulesReducer from 'reducers/violatedRulesReducer';

describe('Violated Rules reducer', () => {
  it('should return the intial state', () => {
    expect(violatedRulesReducer(undefined, {})).toEqual(initialState.violatedRules);
  });

  it('should handle ON_VIOLATED_RULES_FETCH_LOADING', () => {
    expect(
      violatedRulesReducer(
        {},
        {
          type: types.ON_VIOLATED_RULES_FETCH_LOADING
        }
      )
    ).toEqual({
      list: [],
      loader: true,
      error: false,
      errorMessage: '',
      transactionLoader: false
    });
  });

  it('should handle ON_SUCCESSFUL_VIOLATED_RULES_FETCH', () => {
    expect(
      violatedRulesReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_VIOLATED_RULES_FETCH,
          response: {
            transactionId: 1,
            list: responses.caseAssignment.violatedRules
          }
        }
      )
    ).toEqual({
      list: responses.caseAssignment.violatedRules,
      loader: false,
      error: false,
      errorMessage: '',
      transactionLoader: false
    });
  });

  it('should handle ON_FAILURE_VIOLATED_RULES_FETCH', () => {
    expect(
      violatedRulesReducer(
        {},
        {
          type: types.ON_FAILURE_VIOLATED_RULES_FETCH,
          response: { message: 'Unable to fetch violated rules for transaction.' }
        }
      )
    ).toEqual({
      list: [],
      loader: false,
      error: true,
      errorMessage: 'Unable to fetch violated rules for transaction.',
      transactionLoader: false
    });
  });

  it('should handle ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING', () => {
    expect(
      violatedRulesReducer(
        {},
        {
          type: types.ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING
        }
      )
    ).toEqual({
      transactionLoader: true
    });
  });

  it('should handle ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS', () => {
    expect(
      violatedRulesReducer(
        {
          list: responses.caseAssignment.violatedRules,
          loader: false,
          error: false,
          errorMessage: '',
          transactionLoader: false
        },
        {
          type: types.ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS,
          response: responses.caseAssignment.violatedRules,
          ruleCode: '125467'
        }
      )
    ).toEqual({
      list: responses.caseAssignment.violatedRules,
      loader: false,
      error: false,
      errorMessage: '',
      transactionLoader: false
    });
  });

  it('should handle ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE', () => {
    expect(
      violatedRulesReducer(
        {
          list: responses.caseAssignment.violatedRules,
          loader: false,
          error: false,
          errorMessage: '',
          transactionLoader: false
        },
        {
          type: types.ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      list: responses.caseAssignment.violatedRules,
      loader: false,
      error: false,
      errorMessage: '',
      transactionLoader: false
    });
  });
});
