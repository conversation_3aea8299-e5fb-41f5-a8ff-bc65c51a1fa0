import {
  ON_<PERSON><PERSON><PERSON>_ALL_INCIDENTS_LOADING,
  ON_<PERSON>ET<PERSON>_ALL_INCIDENTS_SUCCESS,
  ON_FETCH_ALL_INCIDENTS_FAILURE,
  ON_FETCH_INCIDENT_BY_ID_LOADING,
  ON_FETCH_INCIDENT_BY_ID_SUCCESS,
  ON_FETCH_INCIDENT_BY_ID_FAILURE,
  ON_FETCH_CPIFR_MASTERS_LOADING,
  ON_FETCH_CPIFR_MASTERS_SUCCESS,
  ON_FETCH_CPIFR_MASTERS_FAILURE,
  ON_FETCH_CASE_CLOSED_CPIFR_LOADING,
  ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS,
  ON_FETCH_CASE_CLOSED_CPIFR_FAILURE,
  ON_FETCH_CPIFR_REPORT_HISTORY_LOADING,
  ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS,
  ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE,
  ON_CLEAR_SELECTED_INCIDENT,
  ON_FETCH_UNVERIFIED_CASES_LOADING,
  ON_FETCH_UNVERIFIED_CASES_SUCCESS,
  ON_FETCH_UNVERIFIED_CASES_FAILURE,
  ON_REMOVE_CASES_FROM_UNVERIFIED_LIST,
  ON_FETCH_PRIORITY_CASES_FAILURE,
  ON_FETCH_PRIORITY_CASES_LOADING,
  ON_FETCH_PRIORITY_CASES_SUCCESS
} from 'constants/actionTypes';
import initialState from './initialState';
import objectAssign from 'object-assign';
import _ from 'lodash';

export default function incidentsReducer(state = initialState.incidents, action) {
  switch (action.type) {
    case ON_FETCH_ALL_INCIDENTS_LOADING:
      return objectAssign({}, state, {
        allIncidents: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_INCIDENTS_SUCCESS:
      return objectAssign({}, state, {
        allIncidents: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_INCIDENTS_FAILURE:
      return objectAssign({}, state, {
        allIncidents: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_INCIDENT_BY_ID_LOADING:
      return objectAssign({}, state, {
        incident: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_INCIDENT_BY_ID_SUCCESS:
      return objectAssign({}, state, {
        incident: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_INCIDENT_BY_ID_FAILURE:
      return objectAssign({}, state, {
        incident: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_CLEAR_SELECTED_INCIDENT:
      return objectAssign({}, state, {
        incident: initialState.incidents.incident
      });
    case ON_FETCH_CPIFR_MASTERS_LOADING:
      return objectAssign({}, state, {
        masters: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CPIFR_MASTERS_SUCCESS:
      return objectAssign({}, state, {
        masters: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CPIFR_MASTERS_FAILURE:
      return objectAssign({}, state, {
        masters: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_CASE_CLOSED_CPIFR_LOADING:
      return objectAssign({}, state, {
        closedList: objectAssign({}, state.closedList, {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS:
      return objectAssign({}, state, {
        closedList: objectAssign({}, state.closedList, {
          list: action.response,
          conf: action.conf,
          loader: false
        })
      });
    case ON_FETCH_CASE_CLOSED_CPIFR_FAILURE:
      return objectAssign({}, state, {
        closedList: objectAssign({}, state.closedList, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_CPIFR_REPORT_HISTORY_LOADING:
      return objectAssign({}, state, {
        history: objectAssign({}, state.history, {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS:
      return objectAssign({}, state, {
        history: objectAssign({}, state.history, {
          list: action.response,
          conf: action.conf,
          loader: false
        })
      });
    case ON_FETCH_CPIFR_REPORT_HISTORY_FAILURE:
      return objectAssign({}, state, {
        history: objectAssign({}, state.history, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_UNVERIFIED_CASES_LOADING:
      return objectAssign({}, state, {
        unverifiedList: objectAssign({}, state.unverifiedList, { loader: true })
      });
    case ON_FETCH_UNVERIFIED_CASES_SUCCESS:
      return objectAssign({}, state, {
        unverifiedList: objectAssign({}, state.unverifiedList, {
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          list:
            state.unverifiedList.conf.sortBy !== action.conf.sortBy ||
            state.unverifiedList.conf.sortOrder !== action.conf.sortOrder ||
            state.unverifiedList.conf.filterCondition !== action.conf.filterCondition
              ? action.response.cases
              : _.unionBy(state.unverifiedList.list, action.response.cases, 'txnId'),
          conf: action.conf,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_UNVERIFIED_CASES_FAILURE:
      return objectAssign({}, state, {
        unverifiedList: objectAssign({}, state.unverifiedList, {
          count: state.unverifiedList.conf == action.conf ? state.unverifiedList.count : 0,
          list:
            state.unverifiedList.conf.sortBy !== action.conf.sortBy ||
            state.unverifiedList.conf.sortOrder !== action.conf.sortOrder ||
            state.unverifiedList.conf.filterCondition !== action.conf.filterCondition
              ? []
              : state.unverifiedList.list,
          conf: action.conf,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_REMOVE_CASES_FROM_UNVERIFIED_LIST:
      return objectAssign({}, state, {
        unverifiedList: objectAssign({}, state.unverifiedList, {
          list: _.filter(
            state.unverifiedList.list,
            (listItem) => !_.includes(action.cases, listItem.caseRefNo)
          )
        })
      });
    case ON_FETCH_PRIORITY_CASES_LOADING:
      return objectAssign({}, state, {
        priorityList: objectAssign({}, state.priorityList, { loader: true })
      });
    case ON_FETCH_PRIORITY_CASES_SUCCESS:
      return objectAssign({}, state, {
        priorityList: objectAssign({}, state.priorityList, {
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          list:
            state.priorityList.conf.sortBy !== action.conf.sortBy ||
            state.priorityList.conf.sortOrder !== action.conf.sortOrder ||
            state.priorityList.conf.filterCondition !== action.conf.filterCondition
              ? action.response.cases
              : _.unionBy(state.priorityList.list, action.response.cases, 'txnId'),
          conf: action.conf,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_PRIORITY_CASES_FAILURE:
      return objectAssign({}, state, {
        priorityList: objectAssign({}, state.priorityList, {
          count: state.priorityList.conf == action.conf ? state.priorityList.count : 0,
          list:
            state.priorityList.conf.sortBy !== action.conf.sortBy ||
            state.priorityList.conf.sortOrder !== action.conf.sortOrder ||
            state.priorityList.conf.filterCondition !== action.conf.filterCondition
              ? []
              : state.priorityList.list,
          conf: action.conf,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
