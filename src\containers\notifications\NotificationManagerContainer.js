import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchNotifications } from 'actions/notificationActions';
import NotificationManager from 'components/notifications/NotificationManager';

const mapStateToProps = (state) => ({
  notifications: state.notifications
});

const mapDispatchToProps = (dispatch) => ({
  fetchNotifications: bindActionCreators(onFetchNotifications, dispatch)
});

const NotificationManagerContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NotificationManager);

export default NotificationManagerContainer;
