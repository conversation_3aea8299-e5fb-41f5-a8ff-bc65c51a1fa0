import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchNotifications } from 'actions/notificationActions';
import NotificationManager from 'components/notifications/NotificationManager';

const mapStateToProps = (state) => {
  return {
    notifications: state.notifications
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchNotifications: bindActionCreators(onFetchNotifications, dispatch)
  };
};

const NotificationManagerContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NotificationManager);

export default NotificationManagerContainer;
