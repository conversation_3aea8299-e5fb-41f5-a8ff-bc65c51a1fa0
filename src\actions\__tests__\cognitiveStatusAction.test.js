import * as actions from 'actions/cognitiveStatusAction';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('cognitive status actions', () => {
  it('should fetch cognitive status', () => {
    const channel = 'frm';

    const response = true;

    const expectedActions = [
      { type: types.ON_FETCH_COGNITIVE_STATUS_LOADING },
      { type: types.ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS, channel, response }
    ];
    const store = mockStore({ cognitiveStatus: {} });

    return store.dispatch(actions.onFetchCognitiveStatus(channel)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should toggle cognitive status', () => {
    const channel = 'rpsl';
    const currentStatus = false;

    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Cognitive System Activated' } },
      { type: types.ON_FETCH_COGNITIVE_STATUS_LOADING }
    ];
    const store = mockStore({ cognitiveStatus: {} });

    return store.dispatch(actions.onToggleCognitiveStatus(channel, currentStatus)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
