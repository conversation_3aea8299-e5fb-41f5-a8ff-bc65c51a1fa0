import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, CardTitle, CardSubtitle } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import RFIReportsTable from './RFIReportsTable';

function HighValueNewAccountsReport({
  highValueNewAccount,
  fetchHighValueNewAccountCount,
  fetchHighValueNewAccountData
}) {
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(() => {
    fetchHighValueNewAccountCount();
  }, []);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchHighValueNewAccountData({ pageNo: page + 1, pageSize });
    setPageNo(page);
  };

  const columnHeaders = [
    {
      Header: 'Previous Month',
      columns: [
        {
          Header: 'Amount',
          accessor: 'txnAmountInPrevMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types, react/no-multi-comp
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'txnAmountInPrevMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'txnCountInPrevMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types, react/no-multi-comp
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'txnCountInPrevMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  return (
    <Row>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{highValueNewAccount.count?.value ?? 0}</CardTitle>
          <CardSubtitle># High Value New Accounts</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="High Value New Accounts">
          {highValueNewAccount.count?.value > 0 ? (
            <RFIReportsTable
              count={highValueNewAccount.count?.value}
              additionalHeaders={columnHeaders}
              data={highValueNewAccount.data}
              fetchData={fetchHighValueNewAccountData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div"> No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

HighValueNewAccountsReport.propTypes = {
  highValueNewAccount: PropTypes.object.isRequired,
  fetchHighValueNewAccountCount: PropTypes.func.isRequired,
  fetchHighValueNewAccountData: PropTypes.func.isRequired
};

export default HighValueNewAccountsReport;
