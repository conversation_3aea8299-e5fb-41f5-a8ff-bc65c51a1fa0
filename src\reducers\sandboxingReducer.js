import { isEmpty, isEqual, unionBy } from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_SANDBOX_DATE_RANGE_LOADING,
  ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS,
  ON_FETCH_SANDBOX_DATE_RANGE_FAILURE,
  ON_TEST_SANDBOX_RULES_LOADING,
  ON_TEST_SANDBOX_RULES_SUCCESS,
  ON_TEST_SANDBOX_RULES_FAILURE,
  ON_FETCH_SANDBOX_STATUS_SUCCESS,
  ON_FETCH_SANDBOX_STATUS_FAILURE,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE,
  ON_FETCH_SANDBOX_HISTORY_LOADING,
  ON_<PERSON>ET<PERSON>_SANDBOX_HISTORY_SUCCESS,
  ON_FETCH_SANDBOX_HISTORY_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function sandboxing(state = initialState.sandboxing, action) {
  switch (action.type) {
    case ON_FETCH_SANDBOX_DATE_RANGE_LOADING:
      return objectAssign({}, state, {
        dateRange: objectAssign({}, state.dateRange, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS:
      return objectAssign({}, state, {
        dateRange: objectAssign({}, state.dateRange, {
          loader: false,
          data: action.response
        })
      });
    case ON_FETCH_SANDBOX_DATE_RANGE_FAILURE:
      return objectAssign({}, state, {
        dateRange: objectAssign({}, state.dateRange, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_TEST_SANDBOX_RULES_LOADING:
      return objectAssign({}, state, {
        testing: objectAssign({}, state.testing, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_TEST_SANDBOX_RULES_SUCCESS:
      return objectAssign({}, state, {
        testing: objectAssign({}, state.testing, {
          status: 'STARTED',
          ruleName: action.ruleName,
          ruleCode: action.ruleCode,
          testId: action.response.testId
        })
      });
    case ON_TEST_SANDBOX_RULES_FAILURE:
      return objectAssign({}, state, {
        testing: objectAssign({}, state.testing, {
          status: 'FAILURE',
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_SANDBOX_STATUS_SUCCESS:
      return objectAssign({}, state, {
        testing: objectAssign({}, state.testing, {
          status: !isEmpty(action.response) ? 'COMPLETE' : 'PENDING',
          data: !isEmpty(action.response) ? action.response : [],
          loader: isEmpty(action.response)
        })
      });
    case ON_FETCH_SANDBOX_STATUS_FAILURE:
      return objectAssign({}, state, {
        testing: objectAssign({}, state.testing, {
          status: 'FAILED',
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING:
      return objectAssign({}, state, {
        violationDetails: objectAssign({}, state.violationDetails, {
          loader: true
        })
      });
    case ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        violationDetails: {
          date: action.date,
          list: isEqual(action.date, state.violationDetails.date)
            ? unionBy(state.violationDetails.list, action.response.records, 'txnId')
            : action.response.records,
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE:
      return objectAssign({}, state, {
        violationDetails: {
          date: action.date,
          list: isEqual(action.date, state.violationDetails.date)
            ? state.violationDetails.list
            : [],
          count: isEqual(action.date, state.filterCondition) ? state.count : 0,
          isLastPage: isEqual(action.date, state.violationDetails.date)
            ? state.violationDetails.isLastPage
            : true,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_SANDBOX_HISTORY_LOADING:
      return objectAssign({}, state, {
        testHistory: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SANDBOX_HISTORY_SUCCESS:
      return objectAssign({}, state, {
        testHistory: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SANDBOX_HISTORY_FAILURE:
      return objectAssign({}, state, {
        testHistory: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    default:
      return state;
  }
}
