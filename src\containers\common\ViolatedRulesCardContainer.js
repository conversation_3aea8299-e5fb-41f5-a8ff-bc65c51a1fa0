import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as violatedRulesActions from 'actions/violatedRulesActions';
import ViolatedRulesCard from 'components/common/ViolatedRulesCard';

const mapStateToProps = (state) => {
  return {
    violations: state.violatedRules,
    theme: state.toggle.theme,
    channels: state.auth.userCreds.channels,
    role: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(violatedRulesActions, dispatch)
  };
};

const ViolatedRulesCardContainer = connect(mapStateToProps, mapDispatchToProps)(ViolatedRulesCard);

export default ViolatedRulesCardContainer;
