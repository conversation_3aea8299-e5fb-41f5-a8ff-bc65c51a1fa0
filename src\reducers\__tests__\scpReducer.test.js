import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import scpReducer from 'reducers/scpReducer';

describe('SCP reducer', () => {
  it('should return the intial state', () => {
    expect(scpReducer(undefined, {})).toEqual(initialState.scp);
  });

  it('should handle ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING', () => {
    expect(
      scpReducer(
        {},
        {
          type: types.ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING
        }
      )
    ).toEqual({
      configurationsData: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH', () => {
    const response = [
      {
        configType: 'autoClosure',
        configPoints: {
          timeOfDay: '',
          activation: 'Disabled',
          jobInterval: '',
          fraudTypeId: '',
          autoClosureDays: '',
          explanation: '',
          liabilityId: '',
          verdictTypeId: ''
        }
      }
    ];
    expect(
      scpReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
          response
        }
      )
    ).toEqual({
      configurationsData: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE', () => {
    expect(
      scpReducer(
        {},
        {
          type: types.ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      configurationsData: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
