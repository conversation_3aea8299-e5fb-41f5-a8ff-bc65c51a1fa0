import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchHRCTrxnsCount, onFetchHRCTrxnsData } from 'actions/rfiReportActions';
import HRCReport from 'components/rfiReports/HRCReport';

const mapStateToProps = (state) => ({
  hrcTrxns: state.rfiReports.hrcTrxns
});

const mapDispatchToProps = (dispatch) => ({
  fetchHRCTrxnsCount: bindActionCreators(onFetchHRCTrxnsCount, dispatch),
  fetchHRCTrxnsData: bindActionCreators(onFetchHRCTrxnsData, dispatch)
});

const HRCReportContainer = connect(mapStateToProps, mapDispatchToProps)(HRCReport);

export default HRCReportContainer;
