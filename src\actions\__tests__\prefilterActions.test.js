import responses from 'mocks/responses';

import * as actions from 'actions/prefilterActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('pre-filter actions', () => {
  it('should fetch TPS', () => {
    const expectedActions = [
      { type: types.ON_FETCH_TPS_LIMIT_LOADING },
      { type: types.ON_FETCH_TPS_LIMIT_SUCCESS, response: responses.prefilter.tps }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onFetchTps()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add TPS', () => {
    const formData = {
      tpsLimit: 10,
      channelId: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_TPS_LIMIT_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'TPS limit set successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onSetTps(formData, 'POST')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update TPS', () => {
    const formData = {
      tpsLimit: 10,
      channelId: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_TPS_LIMIT_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'TPS limit set successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onSetTps(formData, 'PUT')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should delete TPS', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_TPS_LIMIT_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'TPS limit removed successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onDeleteTps(1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Prefilter categories', () => {
    const expectedActions = [
      { type: types.ON_FETCH_FILTER_CATEGORY_LOADING },
      { type: types.ON_FETCH_FILTER_CATEGORY_SUCCESS, response: responses.prefilter.category }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onFetchCategory()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Prefilter filter', () => {
    const expectedActions = [
      { type: types.ON_FETCH_FILTER_LOADING },
      { type: types.ON_FETCH_FILTER_SUCCESS, response: responses.prefilter.filter }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onFetchFilter()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add Prefilter', () => {
    const formData = {
      channelId: 1,
      categoryId: 2,
      identifier: 'terminalid@1',
      isActive: true
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_FILTER_LOADING },
      { type: types.ON_TOGGLE_PREFILTER_MODAL, channel: 'rpsl' },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Filter added successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onSetFilter(formData, 'POST', 'rpsl')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update Prefilter', () => {
    const formData = {
      channelId: 1,
      categoryId: 2,
      identifier: 'terminalid@1',
      isActive: true
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_FILTER_LOADING },
      { type: types.ON_TOGGLE_PREFILTER_MODAL, channel: 'rpsl' },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Filter updated successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onSetFilter(formData, 'PUT', 'rpsl')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should delete Prefilter', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_FILTER_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Filter removed successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ prefilter: {} });

    return store.dispatch(actions.onDeleteFilter(1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
