import _ from 'lodash';
import React, { useEffect } from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import { Card, Row, Col } from 'reactstrap';
import objectAssign from 'object-assign';

import CardContainer from 'components/common/CardContainer';
import CustomerInfoLoader from 'components/loader/CustomerInfoLoader';
import EntityRiskScoreBadge from 'components/common/EntityRiskScoreBadge';
import CustomerAccountsTable from 'components/common/CustomerAccountsTable';
import StatusLogTableContainer from 'containers/common/StatusLogTableContainer';
import FacctumDetailsContainer from 'containers/common/FacctumDetailsContainer';
import { dataColumn, renderDataColumnList } from 'utility/customRenders';
import {
  countFormatter,
  getDateDifference,
  checkValue,
  formatMobile,
  addItemToList
} from 'constants/functions';

const CustomerInfoCard = ({
  data,
  isFacctum,
  customerId,
  fetchDetails,
  demographicDetails,
  channel
}) => {
  const { details, vulnerability, loader, error, errorMessage } = data;

  useEffect(() => {
    customerId && fetchDetails(customerId);
  }, [customerId]);

  const scoreBadge = checkValue(vulnerability, 'riskScore') && (
    <EntityRiskScoreBadge vulnerability={vulnerability} />
  );

  const customerInfo = details;
  const txnCounterResponse = customerInfo?.customerTxnCounterResponse;

  const customerInfoLoader = loader && <CustomerInfoLoader />;
  const errorComponent = error && _.isEmpty(customerInfo) && (
    <div className="no-data-div">{errorMessage}</div>
  );
  const noDataComponent = _.isEmpty(customerInfo) &&
    _.isEmpty(demographicDetails?.customer?.details?.add_on) && (
      <div className="no-data-div">No customer information available</div>
    );

  const renderCustomerDemographics = () => {
    let customerDemographics = [];

    addItemToList(customerInfo?.name, 'Name', customerInfo.name, customerDemographics);
    addItemToList(
      customerInfo?.dob,
      'Age',
      getDateDifference(customerInfo?.dob),
      customerDemographics
    );
    addItemToList(customerInfo?.gender, 'Gender', customerInfo?.gender, customerDemographics);
    addItemToList(
      customerInfo?.mobileNo?.value,
      'Mobile No',
      formatMobile(customerInfo?.mobileNo?.value),
      customerDemographics,
      objectAssign({}, customerInfo?.mobileNo, {
        value: formatMobile(customerInfo?.mobileNo?.value)
      })
    );
    addItemToList(customerInfo?.pan, 'PAN', customerInfo.pan, customerDemographics);
    addItemToList(customerInfo?.aadhar, 'Aadhar', customerInfo.aadhar, customerDemographics);
    addItemToList(
      customerInfo?.occupation,
      'Occupation',
      customerInfo.occupation,
      customerDemographics
    );
    addItemToList(
      customerInfo?.incomeRange,
      'Income Range',
      customerInfo.incomeRange,
      customerDemographics
    );
    addItemToList(
      customerInfo?.address?.address1 ||
        customerInfo?.address?.address2 ||
        customerInfo?.address?.address3,
      'Home Location',
      `${customerInfo?.address?.address1 || ''} ${
        customerInfo?.address?.address2 ? ', ' + customerInfo?.address?.address2 : ''
      } ${customerInfo?.address?.address3 ? ', ' + customerInfo?.address?.address3 : ''}`,
      customerDemographics
    );
    addItemToList(
      customerInfo?.address?.city,
      'City',
      customerInfo?.address?.city,
      customerDemographics
    );
    addItemToList(
      customerInfo?.address?.state,
      'State',
      customerInfo?.address?.state,
      customerDemographics
    );
    addItemToList(
      customerInfo?.address?.pin,
      'PinCode',
      customerInfo?.address?.pin,
      customerDemographics
    );

    return (
      <div>
        <b>Demographic Details</b>
        {renderDataColumnList(customerDemographics, txnCounterResponse?.partnerId)}
      </div>
    );
  };

  const renderCustomerInfo = () => {
    const customerInfoList = [];

    addItemToList(
      customerInfo?.cif?.value,
      'Customer ID',
      customerInfo?.cif?.value,
      customerInfoList,
      customerInfo?.cif
    );
    addItemToList(
      customerInfo?.custRefCif,
      'Customer CIF',
      customerInfo?.custRefCif,
      customerInfoList
    );
    addItemToList(customerInfo?.email, 'Email', customerInfo.email, customerInfoList);
    addItemToList(
      customerInfo?.createdOn,
      'Customer Since',
      getDateDifference(customerInfo.createdOn),
      customerInfoList
    );
    addItemToList(
      customerInfo?.account?.accountOpeningDate,
      'Account Opening Date',
      Moment(customerInfo?.account?.accountOpeningDate).format('YYYY-MM-DD'),
      customerInfoList
    );
    addItemToList(customerInfo?.category, 'Category', customerInfo.category, customerInfoList);
    addItemToList(
      customerInfo?.address?.isUrbanRural,
      'Type',
      customerInfo?.address?.isUrbanRural ? 'Urban' : 'Rural',
      customerInfoList
    );
    addItemToList(
      customerInfo?.isStaff,
      'Staff',
      customerInfo?.isStaff ? 'Yes' : 'No',
      customerInfoList
    );
    addItemToList(customerInfo?.loopType, 'Loop Type', customerInfo.loopType, customerInfoList);
    addItemToList(
      customerInfo?.activity,
      'Activity',
      customerInfo?.activity?.replace('_', ' '),
      customerInfoList
    );
    addItemToList(
      customerInfo?.totalTxns,
      'Transactions',
      customerInfo.totalTxns,
      customerInfoList
    );
    addItemToList(
      customerInfo?.totalAmount,
      'Amount',
      countFormatter(customerInfo.totalAmount),
      customerInfoList
    );

    return (
      <div>
        <b>Account Details</b>
        {renderDataColumnList(customerInfoList, txnCounterResponse?.partnerId)}
      </div>
    );
  };

  const renderCustomerTxnCounterResponse = () => {
    if (_.has(customerInfo, 'customerTxnCounterResponse')) {
      const txnCounterResponseData = [
        { label: 'Transaction Count', value: countFormatter(txnCounterResponse?.txnCount) || 0 },
        { label: 'Transaction Amount', value: countFormatter(txnCounterResponse?.txnAmount) || 0 },
        {
          label: 'Average Transaction',
          value: countFormatter(txnCounterResponse?.averageTransaction) || 0
        },
        { label: 'Fraud Count', value: countFormatter(txnCounterResponse?.fraudCount) || 0 },
        { label: 'Fraud Amount', value: countFormatter(txnCounterResponse?.fraudAmount) || 0 },
        { label: 'Average Fraud', value: countFormatter(txnCounterResponse?.averageFraud) || 0 }
      ];

      return (
        <div>
          <b>Activity</b>
          {renderDataColumnList(txnCounterResponseData, txnCounterResponse?.partnerId)}
        </div>
      );
    }
    return null;
  };

  let addonCustomerDemographicDetails =
    _.has(demographicDetails?.customer?.details, 'add_on') &&
    demographicDetails.customer.details.add_on.map((item) => dataColumn(item.key, item.val));

  const customerInfoComponent = (
    <div>
      <Row>
        {channel === 'str' && isFacctum === 1 && (
          <Col lg="3" md="4" sm="2">
            <FacctumDetailsContainer
              name={customerInfo?.name}
              pan={customerInfo?.pan}
              aadhar={customerInfo?.aadhar}
            />
          </Col>
        )}
        <Col lg="3" md="4" sm="2">
          {renderCustomerDemographics()}
        </Col>
        <Col lg="3" md="4" sm="2">
          {renderCustomerInfo()}
        </Col>
        <Col lg="3" md="4" sm="2">
          {renderCustomerTxnCounterResponse()}
        </Col>
        {addonCustomerDemographicDetails.length > 0 && <Col>{addonCustomerDemographicDetails}</Col>}
        <Col sm="12">
          <span className="section-title p-1">Account Details: </span>
          <CustomerAccountsTable
            accountsData={customerInfo?.account}
            partnerId={txnCounterResponse?.partnerId || ''}
          />
        </Col>
      </Row>
    </div>
  );

  return (
    <CardContainer
      withAddToList={true}
      title={<span>Customer Information {scoreBadge}</span>}
      action={
        <StatusLogTableContainer
          id={customerId}
          name={customerInfo?.name || ' '}
          channel={channel}
        />
      }>
      <Card>
        {customerInfoLoader}
        {errorComponent}
        {noDataComponent}
        {customerInfoComponent}
      </Card>
    </CardContainer>
  );
};

CustomerInfoCard.propTypes = {
  data: PropTypes.object.isRequired,
  isFacctum: PropTypes.number.isRequired,
  customerId: PropTypes.string.isRequired,
  fetchDetails: PropTypes.func.isRequired,
  demographicDetails: PropTypes.object,
  channel: PropTypes.string.isRequired
};

export default CustomerInfoCard;
