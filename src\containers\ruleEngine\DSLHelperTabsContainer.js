import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchDSLHelpers } from 'actions/ruleCreationActions';
import DSLHelperTabs from 'components/ruleEngine/DSLHelperTabs';
import { getHelperList, getModuleType } from 'selectors/ruleEngineSelectors';

// Optimized mapStateToProps using memoized selectors
const mapStateToProps = (state) => ({
  helperList: getHelperList(state),
  prefilterLists: state.prefiltersList.allLists,
  moduleType: getModuleType(state)
});

const mapDispatchToProps = (dispatch) => ({
  fetchDSLHelpers: bindActionCreators(onFetchDSLHelpers, dispatch)
});

const DSLHelperTabsContainer = connect(mapStateToProps, mapDispatchToProps)(DSLHelperTabs);

export default DSLHelperTabsContainer;
