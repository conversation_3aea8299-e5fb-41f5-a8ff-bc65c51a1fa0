@use 'colors' as *;

.dark
  color: $text-high-emphasis-dark !important

  ::-webkit-scrollbar-track
    background: $scroll-track-dark

  ::-webkit-scrollbar-thumb
    background: $scroll-thumb-dark

  .required-star
    color: $color-error-dark

  /* ------------------------------------- */
  // Color and Text
  /* ------------------------------------- */
  .color-primary
    color: $color-primary-dark

  .color-success
    color: $color-success-dark

  .color-info
    color: $color-info-dark

  .color-warning
    color: $color-warning-dark

  .color-danger
    color: $color-error-dark

  .text-success
    color: $color-success-dark !important

  .text-danger
    color: $color-error-dark !important

  .text-disabled
    color: $text-disabled-dark !important

  .text-medium-emphasis
    color: $text-medium-emphasis-dark !important

  /* ------------------------------------- */
  // Badge
  /* ------------------------------------- */
  .badge
    color: $surface-dark !important
    &.bg-success
      background-color: $color-success-dark
    &.bg-primary
      background-color: $color-primary-dark
      color: $text-high-emphasis-dark !important
    &.bg-warning
      background-color: $color-warning-dark
    &.bg-danger
      background-color: $color-error-dark
    &.bg-secondary
      background-color: $color-secondary-dark
      color: $text-high-emphasis-dark !important
    &.bg-info
      background-color: $color-info-dark

  /* ------------------------------------- */
  // Buttons
  /* ------------------------------------- */
  .-btn
    color: $text-high-emphasis-dark
    &:focus
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-primary
    background-color: $color-primary-dark !important
    color: $surface-dark !important
    border-color: $color-primary-dark !important
    &:hover
      background-color: $surface-dark !important
      color: $color-primary-dark !important
      border-color: $color-primary-dark !important

  .btn-outline-primary
    background-color: $surface-dark !important
    color: $color-primary-dark !important
    border-color: $color-primary-dark !important
    &:hover
      background-color: $color-primary-dark !important
      color: $surface-dark !important
    &:active, &.active
      color: $surface-dark !important
      background-color: $color-primary-dark !important
    &:focus
      color: $surface-dark !important
      background-color: $color-primary-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-success
    background-color: $color-success-dark !important
    color: $surface-dark !important
    border-color: $color-success-dark !important
    &:hover
      background: $surface-dark !important
      color: $color-success-dark !important
      border-color: $color-success-dark !important

  .btn-outline-success
    color: $color-success-dark !important
    border-color: $color-success-dark !important
    &:hover
      background-color: $color-success-dark !important
      color: $surface-dark !important
    &:active, &.active
      color: $surface-dark !important
      background-color: $color-success-dark !important
    &:focus
      color: $surface-dark !important
      background-color: $color-success-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-info
    background-color: $color-info-dark !important
    color: $surface-dark !important
    border-color: $color-info-dark !important
    &:hover
      background: $surface-dark !important
      color: $color-info-dark !important
      border-color: $color-info-dark !important

  .btn-outline-info
    color: $surface-dark !important
    border-color: $color-info-dark !important
    &:hover
      background-color: $color-info-dark !important
      color: $surface-dark !important
      border-color: $color-info-dark !important
    &:active, &.active
      color: $surface-dark !important
      background-color: $color-info-dark !important
      border-color: $color-info-dark !important
    &:focus
      color: $surface-light !important
      background-color: $color-info-dark !important
      border-color: $color-info-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-warning
    background-color: $color-warning-dark !important
    color: $surface-dark !important
    border-color: $color-warning-dark !important
    &:hover
      background-color: $surface-dark !important
      color: $color-warning-dark !important
      border-color: $color-warning-dark !important

  .btn-outline-warning
    color: $color-warning-dark !important
    border-color: $color-warning-dark !important
    &:hover
      background-color: $color-warning-dark !important
      color: $surface-dark !important
    &:active, &.active
      color: $surface-dark !important
      background-color: $color-warning-dark !important
    &:focus
      color: $surface-dark !important
      background-color: $color-warning-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-danger
    background-color: $color-error-dark !important
    color: $surface-dark !important
    border-color: $color-error-dark !important
    &:hover
      background: $surface-dark !important
      color: $color-error-dark !important
      border-color: $color-error-dark !important

  .btn-outline-danger
    color: $color-error-dark !important
    border-color: $color-error-dark !important
    &:hover
      background-color: $color-error-dark !important
      color: $surface-dark !important
      border-color: $color-error-dark !important
    &:active, &.active
      color: $surface-dark !important
      background-color: $color-error-dark !important
      border-color: $color-error-dark !important
    &:focus
      color: $surface-dark !important
      background-color: $color-error-dark !important
      border-color: $color-error-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  .btn-secondary
    background-color: $color-secondary-dark !important
    color: $surface-dark !important
    border-color: $color-secondary-dark !important
    &:hover
      background: $surface-dark !important
      color: $color-secondary-dark !important
      border-color: $color-secondary-dark !important

  .btn-outline-secondary
    color: $color-secondary-dark !important
    border-color: $color-secondary-dark !important
    &:hover
      color: $surface-dark !important
      background: $color-secondary-dark !important
      border-color: $color-secondary-dark !important
    &:active, &.active
      color: $surface-dark !important
      background: $color-secondary-dark !important
      border-color: $color-secondary-dark !important
    &:focus
      color: $surface-dark !important
      background: $color-secondary-dark !important
      border-color: $color-secondary-dark !important
      box-shadow: 0 0 0 0.2rem $focus-ring-dark

  // Disabled states
  .btn:disabled, .btn.disabled
    color: $disabled-dark !important
    background-color: $surface-dark !important
    border-color: $disabled-dark !important

  .btn-close
    color: $color-error-dark !important

  .rule-upload-btn
    color: $color-primary-dark !important
    border-color: $color-primary-dark !important
    background-color: $background-dark !important
    &:hover
      color: $background-dark !important
      border-color: $background-dark !important
      background-color: $color-primary-dark !important

  .download-csv-btn
    a
      color: $color-primary-dark !important
      background-color: $surface-dark !important
      &:hover
        color: $surface-dark !important
        background-color: $color-primary-dark !important

  .btn.useful-active-no-hover-focus
    &:hover, &:focus
      color: $text-high-emphasis-dark !important
      border-color: $color-success-dark !important
      background-color: $color-success-dark !important

  .btn.useful-inactive-no-hover-focus
    color: $text-medium-emphasis-dark !important
    border-color: $text-medium-emphasis-dark !important
    background-color: $surface-dark !important
    &:hover, &:focus
      color: $text-medium-emphasis-dark !important
      border-color: $text-medium-emphasis-dark !important
      background-color: $surface-dark !important

  .btn.not-useful-active-no-hover-focus
    &:hover, &:focus
      color: $text-high-emphasis-dark !important
      border-color: $color-error-dark !important
      background-color: $color-error-dark !important

  .btn.not-useful-inactive-no-hover-focus
    color: $text-medium-emphasis-dark !important
    border-color: $text-medium-emphasis-dark !important
    background-color: $surface-dark !important
    &:hover, &:focus
      color: $text-medium-emphasis-dark !important
      border-color: $text-medium-emphasis-dark !important
      background-color: $surface-dark !important

  .feedback-buttons:nth-child(odd)
    border-color: $divider-dark

  .notation-action button
    border-color: $hover-dark !important

  .border-left::before
    background-color: $text-high-emphasis-dark !important

  /* ------------------------------------- */
  // Input
  /* ------------------------------------- */
  input:not([type="checkbox"]), select, textarea, .input-group-text, .table,
  .sidebar-primary-title, .sidebar-secondary-title,
  .primary-menu-list li, .secondary-menu-list li,
  .content-placeholder, .modal-body .table,
  .graph-subtitle, .no-comment-text
    color: $text-high-emphasis-dark !important
    &:focus
      box-shadow: 0 0 0 0.2rem $focus-ring-dark
      border-color: $color-primary-dark !important

  select option
    background: $background-dark
    border-color: $border-dark
    &:hover
      background-color: $hover-dark
    &.custom
      background: $color-primary-dark !important
      color: $text-high-emphasis-dark

  input:not([type="checkbox"]), select, textarea, .input-group-text
    background-color: $surface-dark !important
    border: 1px solid $border-dark !important
    &::placeholder
      color: $text-medium-emphasis-dark !important

  .form-check-input:checked ~ .custom-control-label::before
    color: $text-high-emphasis-dark
    border-color: $color-primary-dark
    background-color: $color-primary-dark

  // Enhanced form states
  .form-control:disabled, .form-control[readonly]
    background-color: $surface-variant-dark !important
    color: $disabled-dark !important
    border-color: $disabled-dark !important

  /* ------------------------------------- */
  // Toast notifications
  /* ------------------------------------- */
  .toast, .toast-header, .toast-body
    background-color: $surface-dark
    color: $text-high-emphasis-dark
    border: 1px solid $border-dark

  .toast-header
    border-bottom-color: $divider-dark

  /* ------------------------------------- */
  // Modal
  /* ------------------------------------- */
  .modal-header, .modal-body
    background-color: $surface-dark
    border-color: $border-dark

  .modal-header
    border-bottom-color: $divider-dark

  .close, .close:hover
    color: $color-error-dark

  /* ------------------------------------- */
  // Card
  /* ------------------------------------- */
  .card
    background-color: $surface-dark
    border: 1px solid $border-dark
    color: $text-high-emphasis-dark

  .card-main, .citation-card
    background-color: $surface-dark
    -webkit-box-shadow: 8px 8px 12px -4px $shadow-dark
    -moz-box-shadow: 8px 8px 12px -4px $shadow-dark
    box-shadow: 8px 8px 12px -4px $shadow-dark

  .kpi-background
    background: $surface-dark
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  .kpi-overlap
    color: $text-high-emphasis-dark

  /* ------------------------------------- */
  // Dropdown
  /* ------------------------------------- */
  .dropdown-toggle
    background-color: $surface-dark
  .dropdown-menu
    background-color: $surface-dark
    border: 1px solid $border-dark
    -webkit-box-shadow: 6px 6px 6px 2px $shadow-dark
    -moz-box-shadow: 6px 6px 6px 2px $shadow-dark
    box-shadow: 6px 6px 6px 2px $shadow-dark
    .dropdown-header
      color: $text-medium-emphasis-dark
    .dropdown-item
      color: $text-high-emphasis-dark !important
      &:hover, &:focus
        background-color: $hover-dark
      &.active
        background-color: $color-primary-dark
        color: $surface-dark !important

  .dropdown-card-title, .dropdown-card-title:hover
    background-color: $surface-dark !important
    border: none
    color: $text-high-emphasis-dark !important

  /* ------------------------------------- */
  // React-table
  /* ------------------------------------- */
  .ReactTable
    .-pagination .-btn
        background: $background-dark
        color: $text-medium-emphasis-dark
        &:hover
          background: $hover-dark
          color: $text-high-emphasis-dark

    .rt-thead
      .rt-th
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-dark !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-dark !important
      .rt-td
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-dark !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-dark !important
      &.-filters
        border-bottom: 1px solid $border-dark
    &.-headerGroups
      .rt-th:not(:last-of-type)
        border-left: 2px solid $border-dark !important

    .rt-tbody .rt-tr-group
      border-bottom: 1px solid $border-dark

    &.-striped .rt-tbody .rt-tr.-odd
      background-color: $surface-dark
    &.-striped .rt-tbody .rt-tr.-even
      background: $surface-variant-dark

    &.-highlight
      .rt-tbody .rt-tr:not(.-padRow):hover
        background: $hover-dark


  /* ------------------------------------- */
  // MultiSelect
  /* ------------------------------------- */
  .rmsc
    --rmsc-main: #{$color-primary-dark}
    --rmsc-hover: #{$hover-dark}
    --rmsc-selected: #{$background-dark}
    --rmsc-border: #{$border-dark}
    --rmsc-gray: #{$text-medium-emphasis-dark}
    --rmsc-bg: #{$surface-dark}

  /* ------------------------------------- */
  // React Date-Time
  /* ------------------------------------- */
  .rdt
    .rdtPicker
      border-color: $border-dark
      background: $surface-dark
      thead th
        border-bottom-color: $border-dark
      td:hover, .rdtPrev:hover, .rdtNext:hover, .rdtSwitch:hover
        background: $background-dark
      .rdtBtn:hover
        background: $surface-dark
      tfoot
        border-top-color: $border-dark

  .rdtPicker td.rdtActive,
  .rdtPicker td.rdtActive:hover
    background-color: $color-primary-dark

  /* ------------------------------------- */
  //loaders
  /* ------------------------------------- */
  .loader
    color: $color-primary-dark

  .customer-graph-placeholder, .detail-placeholder, .comment-text-placeholder, .card-subtitle-placeholder, .violation-badge-placeholder, .violation-text-placeholder, .graph-loader span
    background: linear-gradient(to right, $surface-variant-dark 20%, $text-disabled-dark 50%, $surface-variant-dark 80%)

  .table-loading
    background-color: $surface-variant-dark !important
    tr
      border-bottom: 1px solid $divider-dark
      td
        &.td-2 span, &.td-5 span
          background-color: $surface-variant-dark
        &.td-3 span, &.td-4 span
          background: linear-gradient(to right, $surface-variant-dark 20%, $text-disabled-dark 50%, $surface-variant-dark 80%)

  /* ------------------------------------- */
  // Navbar
  /* ------------------------------------- */
  .nav-tab
    .active-tab
      border-bottom: 3px solid $color-primary-dark
      color: $color-primary-dark !important

  .nav-pills
    .active-tab
      background: $color-primary-dark
      color: $text-high-emphasis-dark !important

  .navbar-toggler
    .navbar-toggler-icon, .navbar-toggler-icon::before, .navbar-toggler-icon::after
      background: $text-high-emphasis-dark

  .nav-link
    color: $text-high-emphasis-dark !important

  .tab-header.vertical-tab
    border-right: 1px solid $border-dark

  /* ------------------------------------- */
  // Notations
  /* ------------------------------------- */
  .comment
    background: $surface-dark
    border-color: $border-dark
    color: $text-high-emphasis-dark

  .comment-separator
    background: $divider-dark

  .comment-title-placeholder, .comment-timestamp-placeholder
    background-color: $border-dark

  /* ------------------------------------- */
  // Header
  /* ------------------------------------- */
  .header-container
    background-color: $surface-dark
    -webkit-box-shadow: 0px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 0px 5px 2px -3px $shadow-dark
    box-shadow: 0px 5px 2px -3px $shadow-dark

    a, .header-logo
      color: $text-high-emphasis-dark

    ul.navbar-nav .active
      border-bottom: 3px solid $color-primary-dark
      color: $color-primary-dark !important

  /* ------------------------------------- */
  // Sidebar
  /* ------------------------------------- */
  .sidebar-container
    background-color: $sidebar-background-dark
    .sidebar-primary, .sidebar-secondary
      background-color: $surface-dark
      -webkit-box-shadow: 5px 2px 2px -3px $shadow-dark
      -moz-box-shadow: 5px 2px 2px -3px $shadow-dark
      box-shadow: 5px 2px 2px -3px $shadow-dark

    .primary-menu-list li.active, .secondary-menu-list li.active
      border-left: 5px solid $color-success-dark
      background-color: $background-dark

    .sidebar-pagination
      background: $background-dark
      color: $text-high-emphasis-dark
      border-color: $border-dark
      &:hover
        background: $hover-dark
        color: $text-high-emphasis-dark
        border-color: $border-dark

    .card, .case-box
      background: $background-dark

    a
      color: $color-primary-dark
      &:hover
        color: $color-info-dark

  /* ------------------------------------- */
  // Page Content
  /* ------------------------------------- */
  .content-wrapper
    background-color: $background-dark

  /* ------------------------------------- */
  // Login
  /* ------------------------------------- */
  .loginWrap
    background-color: $background-dark

  .login-form-flex
    background-color: $surface-dark
    border-color: $hover-dark !important
    color: $text-high-emphasis-dark !important
    .form-control
      background-color: $background-dark !important
      border: 1px solid $border-dark !important
      color: $text-high-emphasis-dark !important

  .login-form-container
    background-color: $surface-dark
    border: 1px solid $border-dark
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  /* ------------------------------------- */
  // Rule Engine
  /* ------------------------------------- */
  .dsl-list-group
    .list-group-item
      background-color: $background-dark !important
      border: 1px solid $border-dark
      color: $text-high-emphasis-dark
      &:hover
        background-color: $surface-dark
      small
        color: $text-medium-emphasis-dark

  /* ------------------------------------- */
  // Logs
  /* ------------------------------------- */
  .log-list-item
    border-color: $border-dark
    color: $text-high-emphasis-dark
    &.active
      background: $surface-variant-dark

  /* ------------------------------------- */
  // Citations
  /* ------------------------------------- */
  .citation-comments > .list-group-item
    background-color: $surface-dark !important
    border: 0
    border-bottom: 1px solid $divider-dark
    color: $text-high-emphasis-dark
    &:last-child
      border-bottom: 0

  /* ------------------------------------- */
  // Prefilters
  /* ------------------------------------- */
  .prefilter-list, .add-list
    border: 1px solid $hover-dark !important
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  .add-to-list
    .btn:not(.btn-sm):not(.btn-xs)
      color: $hover-dark !important
      &.with-list
        color: $color-info-dark !important
    button.active-item
      background-color: $hover-dark

  /* ------------------------------------- */
  // Notifications
  /* ------------------------------------- */
  .notification-counter > .badge
    border-color: $surface-dark

  .notifications
    .list-group-flush
      .list-group-item
        background-color: inherit
        color: $text-high-emphasis-dark
        &.active
          background-color: $background-dark
          border-color: $hover-dark
        &:hover
          background-color: $hover-dark

  /* ------------------------------------- */
  // SCP
  /* ------------------------------------- */
  .highlight-scp-search
    background-color: $color-primary-dark
    color: $text-high-emphasis-dark

  .scp-page
    .danger-implied-verdict
      color: $color-error-dark

    .secondary-implied-verdict
      color: $color-success-dark

    .form-check-input:checked
      background-color: $color-primary-dark
      border-color: $color-primary-dark

    .form-range
      &:focus
        box-shadow: $color-primary-dark 0 0 0 0 !important

    .setting-expand-button
      &.btn-secondary
        svg
          color: $text-high-emphasis-dark

  .editableDiv:empty:before
    color: $text-disabled-dark

  .call-container
    background-color: $surface-variant-dark

  // Form stepper - dark theme
  .steps-row::before
    background-color: $border-dark
