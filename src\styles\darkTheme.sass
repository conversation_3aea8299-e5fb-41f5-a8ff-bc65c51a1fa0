@import 'colors'

.dark
  color: $font-primary-dark

  ::-webkit-scrollbar-track
    background: $scroll-track-dark

  ::-webkit-scrollbar-thumb
      background: $scroll-thumb-dark

  .required-star
    color: $btn-error-dark

  // Header
  .header-container
    background-color: $foreground-dark
    -webkit-box-shadow: 0px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 0px 5px 2px -3px $shadow-dark
    box-shadow: 0px 5px 2px -3px $shadow-dark

    a, .header-logo
      color: $font-primary-dark

    ul.navbar-nav .active
      color: $btn-primary-dark !important
      border-bottom: 3px solid $btn-primary-dark

  // Page Content
  .content-wrapper
    background-color: $background-dark

  // Color and Text
  .color-danger
    color: $btn-error-dark

  .color-warning
    color: $btn-warning-dark

  .color-info, .color-primary
    color: $btn-info-dark

  .color-success
    color: $btn-success-dark

  .text-danger
    color: $btn-error-dark !important

  .text-success
    color: $btn-success-dark !important

  // Buttons
  .-btn
    color: $font-primary-dark

  .btn-outline-warning
    color: $btn-warning-dark
    border-color: $btn-warning-dark
    &:hover
      background-color: $btn-warning-dark
      color: $foreground-dark

  .btn-warning
    background-color: $btn-warning-dark
    color: $foreground-dark
    border-color: $btn-warning-dark
    &:hover
      background-color: $foreground-dark
      color: $btn-warning-dark
      border-color: $btn-warning-dark

  .btn-outline-primary
    color: $btn-info-dark !important
    border-color: $btn-info-dark !important
    &:hover
      background-color: $btn-info-dark !important
      color: $font-primary-dark !important

  .btn-outline-primary.dropdown-toggle
    background-color: $foreground-dark

  .btn-primary
    background-color: $btn-info-dark
    color: $font-primary-dark
    border-color: $btn-info-dark
    &:hover
      background-color: $foreground-dark
      color: $btn-info-dark
      border-color: $btn-info-dark

  .btn-outline-secondary
    background: inherit
    color: $btn-secondary-dark
    border-color: $btn-secondary-dark
    &:hover
      color: $background-dark
      background: $btn-secondary-dark
      border-color: $btn-secondary-dark

  .btn-secondary
    background-color: $btn-secondary-dark !important
    color: $background-dark !important
    border-color: $btn-secondary-dark !important
    &:hover
      background: $background-dark !important
      color: $btn-secondary-dark !important
      border-color: $btn-secondary-dark !important

  .btn-danger
    background-color: $btn-error-dark !important
    color: $foreground-dark !important
    border-color: $btn-error-dark !important
    &:hover
      background: $foreground-dark !important
      color: $btn-error-dark !important
      border-color: $btn-error-dark !important

  .btn-outline-success
    color: $btn-success-dark !important
    border-color: $btn-success-dark !important
    &:hover
      background-color: $btn-success-dark !important
      color: $font-primary-dark !important

  .btn-success
    background-color: $btn-success-dark !important
    color: $foreground-dark !important
    border-color: $btn-success-dark !important
    &:hover
      background: $foreground-dark !important
      color: $btn-success-dark !important
      border-color: $btn-success-dark !important

  .btn-primary:not(:disabled):not(.disabled).active
    background-color: $btn-success-dark !important
    border-color: $btn-success-dark !important
    color: $background-dark !important

  .advance-search-button, .statistics-button .btn
    background-color: $shadow-light !important
    color: $font-primary-dark !important
    border-color: $shadow-light !important
    &:hover
      background-color: $shadow-light !important
      color: $font-primary-dark !important
      border-color: $shadow-light !important
    &.active
      background-color: $background-dark !important
      color: $font-primary-dark !important
      border-color: $shadow-light !important

  .notation-action button
    border-color: $hover-dark !important

  // Input
  input:not([type="checkbox"]), select, textarea, .input-group-text, .table,
  .sidebar-primary-title, .sidebar-secondary-title,
  .primary-menu-list li, .secondary-menu-list li,
  .content-placeholder, .modal-body .table,
  .graph-subtitle, .no-comment-text
    color: $font-primary-dark !important
    &:focus
      box-shadow: $btn-info-dark 0 0 0 2px
      border-color: $btn-info-dark

  select option
    background: $background-dark
    border-color: $background-dark
    &:hover
      background-color: $background-dark
    &.custom
      background: $btn-primary-dark !important
      color: $foreground-light

  input:not([type="checkbox"]), select, textarea, .input-group-text
    background-color: $foreground-dark !important
    border: 1px solid $hover-dark !important

  .form-check-input:checked ~ .custom-control-label::before
    color: $font-primary-dark
    border-color: $btn-info-dark
    background-color: $btn-info-dark

  // React-table
  .ReactTable
    .-pagination .-btn
        background: $background-dark
        color: $font-primary-dark
        &:hover
          background: $hover-dark

    .rt-thead
      .rt-th
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-dark !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-dark !important
      .rt-td
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-dark !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-dark !important
      &.-header
        border-bottom: 2px solid $hover-dark
      &.-filters
        border-bottom: 1px solid $hover-dark
    &.-headerGroups
      .rt-th:not(:last-of-type)
        border-left: 2px solid $hover-dark !important

    .rt-tbody .rt-tr-group
      border-bottom: 1px solid $hover-dark

    &.-striped .rt-tbody .rt-tr.-odd
      background-color: $foreground-dark
    &.-striped .rt-tbody .rt-tr.-even
      background: $background-dark

    &.-highlight
      .rt-tbody .rt-tr:not(.-padRow):hover
        background: $hover-dark

  // Sidebar
  .sidebar-container
    background-color: $sidebar-background-dark
    .sidebar-primary, .sidebar-secondary
      background-color: $foreground-dark
      -webkit-box-shadow: 5px 2px 2px -3px $shadow-dark
      -moz-box-shadow: 5px 2px 2px -3px $shadow-dark
      box-shadow: 5px 2px 2px -3px $shadow-dark

    .primary-menu-list li.active, .secondary-menu-list li.active
      border-left: 5px solid $btn-success-dark
      background-color: $background-dark

    .sidebar-pagination
      background: $background-dark
      color: $font-primary-dark
      border-color: $background-dark
      &:hover
        background: $hover-dark
        color: $font-primary-dark
        border-color: $hover-dark

    .card, .case-box
      background: $background-dark

    a
      color: $btn-info-dark

  // Dropdown
  .dropdown-menu
    background-color: $background-dark
    -webkit-box-shadow: 6px 6px 6px 2px $shadow-dark
    -moz-box-shadow: 6px 6px 6px 2px $shadow-dark
    box-shadow: 6px 6px 6px 2px $shadow-dark
    .dropdown-header
      color: $font-primary-dark
    .dropdown-item
      color: $font-primary-dark !important
      &:hover, &:focus
        background-color: $hover-dark

  .dropdown-card-title, .dropdown-card-title:hover
    background-color: $foreground-dark !important
    border: none
    color: $font-primary-dark !important

  // Badge
  .badge
    color: $foreground-dark !important
    &.bg-success
      background-color: $btn-success-dark
    &.bg-primary
      background-color: $btn-info-dark
      color: $font-primary-dark !important
    &.bg-warning
      background-color: $btn-warning-dark
    &.bg-danger
      background-color: $btn-error-dark
    &.bg-secondary
      color: $font-primary-dark !important

  .highlight-scp-search
    background-color: $btn-info-dark
    color: $foreground-dark

  // Modal
  .modal-header, .modal-body
    background-color: $foreground-dark

  .modal-header
    border-bottom-color: $hover-dark

  .close, .close:hover
    color: $btn-error-dark

  // List
  .dsl-list-group
    .list-group-item
      background-color: $background-dark !important
      border: 1px solid $hover-dark
      &:hover
        background-color: $foreground-dark
      small
        color: $btn-primary-dark

  .comment
    background: $background-dark
    border-color: $hover-dark

  .comment-separator
    background: $font-primary-dark

  .log-list-item
    border-color: $hover-dark
    &.active
      background: $background-dark

  .citation-comments > .list-group-item
    background-color: $foreground-dark !important
    border: 0
    border-bottom: 1px solid $shadow-dark
    &:last-child
      border-bottom: 0

  .prefilter-list, .add-list
    border: 1px solid $hover-dark !important
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  .add-to-list
    .btn:not(.btn-sm):not(.btn-xs)
      color: $hover-dark !important
      &.with-list
        color: $btn-info-dark !important
    button.active-item
      background-color: $hover-dark


  // React Date-Time
  .rdt
    .rdtPicker
      border-color: $hover-dark
      background: $foreground-dark
      thead th
        border-bottom-color: $hover-dark
      td:hover, .rdtPrev:hover, .rdtNext:hover, .rdtSwitch:hover
        background: $background-dark
      .rdtBtn:hover
        background: $foreground-dark
      tfoot
        border-top-color: $hover-dark

  .rdtPicker td.rdtActive,
  .rdtPicker td.rdtActive:hover
    background-color: $btn-info-dark

  // Card
  .card
    background-color: $foreground-dark
    border: 1px solid $hover-dark

  .card-main
    -webkit-box-shadow: 8px 8px 12px -4px $shadow-dark
    -moz-box-shadow: 8px 8px 12px -4px $shadow-dark
    box-shadow: 8px 8px 12px -4px $shadow-dark

  .kpi-background
    background: $foreground-dark
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  .kpi-overlap
    color: $font-primary-dark

  // Toast
  .toast, .toast-header, .toast-body
    background-color: $background-dark

  .toast-header
    border-color: $hover-dark

  // Navbar
  .nav-tab
    .active-tab
      border-bottom: 3px solid $font-primary-dark

  .nav-pills
    .active-tab
      background: $btn-primary-dark

  .navbar-toggler
    .navbar-toggler-icon, .navbar-toggler-icon::before, .navbar-toggler-icon::after
      background: $font-primary-dark

  .nav-link
    color: $font-primary-dark !important

  .tab-header.vertical-tab
    border-right: 1px solid $hover-dark

  // MultiSelect
  .rmsc
    --rmsc-main: #{$btn-info-dark}
    --rmsc-hover: #{$hover-dark}
    --rmsc-selected: #{$background-dark}
    --rmsc-border: #{$hover-dark}
    --rmsc-gray: #{$font-primary-dark}
    --rmsc-bg: #{$foreground-dark}

  // Login
  .loginWrap
    background-color: $background-dark

  .login-form-flex
    background-color: $foreground-dark
    color: $font-primary-dark !important
    .form-control
      background-color: $background-dark !important
      border: 1px solid $hover-dark !important
      color: $font-primary-dark !important

  .login-form-container
    background-color: $foreground-dark
    border: 1px solid $background-dark
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-dark
    -moz-box-shadow: 5px 5px 2px -3px $shadow-dark
    box-shadow: 5px 5px 2px -3px $shadow-dark

  .editableDiv:empty:before
    color: $shadow-dark

  .call-container
    background-color: $hover-dark

  .rule-upload-btn
    color: $btn-primary-dark !important
    border-color: $btn-primary-dark !important
    background-color: $background-dark !important
    &:hover
      color: $background-dark !important
      border-color: $background-dark !important
      background-color: $btn-primary-dark !important

  .download-csv-btn
    a
      color: $btn-primary-dark !important
      background-color: $background-dark !important
      &:hover
        color: $background-dark !important
        background-color: $btn-primary-dark !important

  .btn.useful-active-no-hover-focus
    &:hover, &:focus
      color: $foreground-dark !important
      border-color: $foreground-dark !important
      background-color: $btn-success-dark !important

  .btn.useful-inactive-no-hover-focus
    color: $font-secondary-dark !important
    border-color: $font-secondary-dark !important
    background-color: $foreground-dark !important
    &:hover, &:focus
      color: $font-secondary-dark !important
      border-color: $font-secondary-dark !important
      background-color: $foreground-dark !important

  .btn.not-useful-active-no-hover-focus
    &:hover, &:focus
      color: $foreground-dark !important
      border-color: $foreground-dark !important
      background-color: $btn-error-dark !important

  .btn.not-useful-inactive-no-hover-focus
    color: $font-secondary-dark !important
    border-color: $font-secondary-dark !important
    background-color: $foreground-dark!important
    &:hover, &:focus
      color: $font-secondary-dark !important
      border-color: $font-secondary-dark !important
      background-color: $foreground-dark!important

  .feedback-buttons
    &:nth-child(odd)
      border-color: $shadow-dark

  .notification-counter > .badge
    border-color: $foreground-dark

  .notifications
    .list-group-flush
      .list-group-item
        background-color: inherit
        color: $font-primary-dark
        &.active
          background-color: $background-dark
          border-color: $hover-dark
        &:hover
          background-color: $hover-dark

  .scp-page
    .danger-implied-verdict
      color: $btn-error-dark

    .secondary-implied-verdict
      color: $btn-success-dark

    .form-check-input:checked
      background-color: $btn-primary-dark
      border-color: $btn-primary-dark

    .form-range
      &:focus
        box-shadow: $btn-primary-dark 0 0 0 0 !important

    .setting-expand-button
      &.btn-secondary
        svg
          color: $font-primary-dark
