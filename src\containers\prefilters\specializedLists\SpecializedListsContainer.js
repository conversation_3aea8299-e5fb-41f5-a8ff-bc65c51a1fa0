import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SpecializedLists from 'components/prefilters/specializedLists/SpecializedLists';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    prefiltersList: state.prefiltersList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const SpecializedListsContainer = connect(mapStateToProps, mapDispatchToProps)(SpecializedLists);

export default SpecializedListsContainer;
