import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import SpecializedLists from 'components/prefilters/specializedLists/SpecializedLists';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  prefiltersList: state.prefiltersList
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const SpecializedListsContainer = connect(mapStateToProps, mapDispatchToProps)(SpecializedLists);

export default SpecializedListsContainer;
