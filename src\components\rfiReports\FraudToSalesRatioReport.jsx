/* eslint-disable react/no-multi-comp */
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { CardTitle, CardSubtitle, Row, Col, Input, Button, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import RFIReportsTable from './RFIReportsTable';

function FraudToSalesRatioReport({
  fraudToSaleRatio,
  fetchFraudToSalesRatioCount,
  fetchFraudToSalesRatioData
}) {
  const [cummValue, setCummValue] = useState(fraudToSaleRatio.cummValue);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchFraudToSalesRatioData({ pageNo: page + 1, pageSize, cummValue });
    setPageNo(page);
  };

  const columnHeaders = [
    {
      Header: 'Ratio',
      accessor: 'f2sRatio',
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step={0.01}
          placeholder="Amount greater than"
          value={_.find(tableFilters, ['id', 'f2sRatio'])?.value}
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Fraud Amount',
      accessor: 'totalFraudAmount',
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          placeholder="Amount greater than"
          value={_.find(tableFilters, ['id', 'totalFraudAmount'])?.value}
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Amount',
      accessor: 'totalTxnAmount',
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          placeholder="Amount greater than"
          value={_.find(tableFilters, ['id', 'totalTxnAmount'])?.value}
          onChange={(event) => onChange(event.target.value)}
        />
      )
    }
  ];

  const onSubmit = (e) => {
    e.preventDefault();
    if (cummValue !== fraudToSaleRatio.cummValue && !isNaN(cummValue))
      fetchFraudToSalesRatioCount({ cummValue });
  };

  return (
    <Row>
      <Col md="12" className="mb-3">
        <form onSubmit={onSubmit}>
          <Row>
            <Col xs="6" md="3">
              <Label htmlFor="cummValue">Total transaction amount</Label>
              <Input
                type="number"
                id="cummValue"
                name="cummValue"
                value={cummValue}
                placeholder="Enter value to generate report"
                onChange={(e) => setCummValue(+e.target.value)}
                min={0}
                max={99999999999}
                pattern="\d{1,11}"
                title="Please input valid amount"
                required
              />
            </Col>
            <Col className="d-flex align-items-end">
              <Button outline size="sm" color="primary">
                Search
              </Button>
            </Col>
          </Row>
        </form>
      </Col>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{fraudToSaleRatio.count?.value ?? 0}</CardTitle>
          <CardSubtitle># High Fraud To Sale Ratio</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="High Fraud To Sale Ratio">
          {fraudToSaleRatio.count?.value > 0 ? (
            <RFIReportsTable
              count={fraudToSaleRatio.count?.value}
              additionalHeaders={columnHeaders}
              data={fraudToSaleRatio.data}
              filter={{ cummValue }}
              fetchData={fetchFraudToSalesRatioData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div">No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

FraudToSalesRatioReport.propTypes = {
  fraudToSaleRatio: PropTypes.object.isRequired,
  fetchFraudToSalesRatioCount: PropTypes.func.isRequired,
  fetchFraudToSalesRatioData: PropTypes.func.isRequired
};

export default FraudToSalesRatioReport;
