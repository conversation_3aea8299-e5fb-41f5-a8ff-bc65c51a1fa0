import mockResponses from 'mocks/responses';

import {
  onFetchCaseDetails,
  onRequestForInformation,
  onUpdateCaseDetails
} from 'actions/caseDetailsActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const conf = {
  bucket: 'OpenCases',
  role: 'checker',
  pageNo: 1,
  pageRecords: 10,
  sortBy: 'weight',
  sortOrder: 'desc',
  filterCondition: []
};

const mockedStore = {
  auth: {
    userCreds: {
      roles: 'checker'
    }
  },
  caseAssignment: {
    cases: {
      str: {
        list: mockResponses.caseAssignment.cases.cases,
        count: mockResponses.caseAssignment.cases.count,
        isLastPage: mockResponses.caseAssignment.cases.isLastPage,
        conf
      }
    },
    selectedCase: mockResponses.caseAssignment.caseDetail
  }
};

describe('transaction details actions', () => {
  it('should fetch case details for channel-txn', () => {
    const formData = {
      channel: 'frm',
      txnId: '123'
    };

    const expectedActions = [
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_CASE_DETAIL,
        response: mockResponses.caseAssignment.caseDetail
      }
    ];
    const store = mockStore({ caseDetails: {} });

    return store.dispatch(onFetchCaseDetails(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should request for information', () => {
    const formData = {
      caseId: 553061,
      caseRefNo: '0976d132-09e5-4b08-b48f-4201ee698ac0',
      requestedInfo: 'some string'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Requested further information successfully' }
      },
      { type: types.ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL },
      {
        type: types.ON_REMOVE_CASES_FROM_LIST,
        cases: ['0976d132-09e5-4b08-b48f-4201ee698ac0'],
        channel: 'str'
      },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'str' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'str', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(onRequestForInformation(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update case details', () => {
    const expectedActions = [
      {
        type: types.ON_REMOVE_CASES_FROM_LIST,
        cases: ['0976d132'],
        channel: 'str'
      },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'str' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'str', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING }
    ];

    const store = mockStore(mockedStore);

    store.dispatch(onUpdateCaseDetails('0976d132', undefined, 'str'));
    expect(store.getActions()).toEqual(expectedActions);
  });
});
