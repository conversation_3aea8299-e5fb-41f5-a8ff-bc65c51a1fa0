'use strict';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import objectAssign from 'object-assign';
import { Button, ButtonGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes, faChartBar, faPencil } from '@fortawesome/free-solid-svg-icons';

import RuleApprovalModal from 'components/ruleEngine/RuleApprovalModal';
import RuleTableContainer from 'containers/ruleEngine/RuleTableContainer';
import { getFilterValue } from 'utility/utils';


const NonProductionRuleTable = ({
  role,
  toggle,
  channel,
  hasSandbox,
  toggleEditModal,
  nonProductionRules,
  ruleCreationActions
}) => {
  const [ruleApproval, setRuleApproval] = useState({
    showModal: false,
    ruleApprovalId: '',
    isApproved: false,
    comments: ''
  });
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(() => {
    ruleCreationActions.onFetchNonProductionRulesList(channel, role);
  }, []);

  const toggleApprovalModal = (approvalId = '', approvalStatus = false) => {
    setRuleApproval({
      showModal: !ruleApproval.showModal,
      ruleApprovalId: approvalId,
      isApproved: approvalStatus,
      comments: ''
    });
  };

  const setComment = (comment) =>
    setRuleApproval(objectAssign({}, ruleApproval, { comments: comment }));

  const submitRuleApproval = (e) => {
    e.preventDefault();
    ruleCreationActions.onRuleApproval(channel, role, ruleApproval);
    toggleApprovalModal();
  };

  const actionHeaders = [
    {
      Header: 'Action',
      filterable: false,
      sortable: false,
      Cell: (row) => {
        switch (true) {
          case ['checker', 'investigator'].includes(role) &&
            row.original.approvalStatus !== 'Approved':
            return (
              <ButtonGroup>
                {channel !== 'str' && hasSandbox === 1 && (
                  <Button
                    size="sm"
                    color="success"
                    title="Sandbox testing"
                    onClick={() => toggleEditModal(row.original, 'nonProduction', true)}>
                    <FontAwesomeIcon icon={faChartBar} />
                  </Button>
                )}

                {['Pending', 'Rejected'].includes(row.original.approvalStatus) && (
                  <Button
                    outline
                    size="sm"
                    color="warning"
                    title="Edit rule"
                    onClick={() => toggleEditModal(row.original, 'nonProduction')}>
                    <FontAwesomeIcon icon={faPencil} />
                  </Button>
                )}
              </ButtonGroup>
            );
          case role == 'supervisor' && row.original.approvalStatus == 'Pending':
            return (
              <ButtonGroup>
                <Button
                  size="sm"
                  color="success"
                  title="Approve rule"
                  onClick={() => toggleApprovalModal(row.original.approvalId, true)}>
                  <FontAwesomeIcon icon={faCheck} />
                </Button>
                <Button
                  size="sm"
                  color="danger"
                  title="Reject rule"
                  className="ml-1"
                  onClick={() => toggleApprovalModal(row.original.approvalId, false)}>
                  <FontAwesomeIcon icon={faTimes} />
                </Button>
              </ButtonGroup>
            );
          default:
            return null;
        }
      }
    }
  ];

  const typeHeaders = [
    {
      Header: 'Approval Status',
      accessor: 'approvalStatus',
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('approvalStatus', tableFilters)}>
          <option value="">All</option>
          <option>Pending</option>
          <option>Rejected</option>
        </select>
      )
    },
    { Header: 'Comments', accessor: 'comments', minWidth: 400 }
  ];

  return (
    <>
      <RuleTableContainer
        channel={channel}
        data={nonProductionRules}
        actionHeaders={actionHeaders}
        typeHeaders={typeHeaders}
        tableFilters={tableFilters}
        setTableFilters={setTableFilters}
        defaultSort={[
          {
            id: 'approvalStatus',
            desc: role == 'supervisor' ? false : true
          },
          {
            id: 'order',
            asc: true
          }
        ]}
      />

      <RuleApprovalModal
        theme={toggle.theme}
        ruleApproval={ruleApproval}
        setComment={setComment}
        submitAction={submitRuleApproval}
        toggleApprovalModal={toggleApprovalModal}
      />
    </>
  );
};

NonProductionRuleTable.propTypes = {
  role: PropTypes.string.isRequired,
  toggle: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  toggleEditModal: PropTypes.func.isRequired,
  nonProductionRules: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired
};

export default NonProductionRuleTable;
