import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Suspense, lazy, useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Row, Col, TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import TransactionRiskScoreInfo from 'components/common/TransactionRiskScoreInfo';
import Loader from 'components/loader/Loader';
import { isCooperative } from 'constants/publicKey';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import RuleFeedbackFormContainer from 'containers/common/RuleFeedbackFormContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import CPFIRFormContainer from 'containers/incidents/CPIFRFormContainer';
import InvestigationActionsContainer from 'containers/investigation/InvestigationActionsContainer';
import CaseNotificationContainer from 'containers/notifications/CaseNotificationContainer';

const AgentInfoCardContainer = lazy(() => import('containers/common/AgentInfoCardContainer'));
const CustomerInfoCardContainer = lazy(() => import('containers/common/CustomerInfoCardContainer'));
const MerchantInfoCardContainer = lazy(() => import('containers/common/MerchantInfoCardContainer'));
const Log = lazy(() => import('containers/common/LogContainer'));
const NotationContainer = lazy(() => import('containers/common/NotationContainer'));
const VerdictModalContainer = lazy(() => import('containers/common/VerdictModalContainer'));
const HistoryTxnTableContainer = lazy(() => import('containers/common/HistoryTxnTableContainer'));
const ViolatedRulesCardContainer = lazy(
  () => import('containers/common/ViolatedRulesCardContainer')
);
const SimilarTxnsTableContainer = lazy(
  () => import('containers/investigation/SimilarTxnsTableContainer')
);
const PastInvestigationCardContainer = lazy(
  () => import('containers/common/PastInvestigationCardContainer')
);
const TransactionTimeAmountTrendContainer = lazy(
  () => import('containers/common/TransactionTimeAmountTrendContainer')
);
const CPIFRDownloadHistoryContainer = lazy(
  () => import('containers/incidents/CPIFRDownloadHistoryContainer')
);
const CustomerEventsTableContainer = lazy(
  () => import('containers/investigation/CustomerEventsTableContainer')
);

const Indepth = ({
  role,
  txnDetails,
  selectedCase,
  transactionHistorySearchActions,
  documentStatus,
  selectCase,
  fetchRulesWithConditions
}) => {
  const tabList = ['Details', 'Trend', 'History', 'Similar'];
  const history = useHistory();
  const { txnId, channel } = useParams();
  const tabRef = useRef(null);
  const [renderedTab, setRenderedTab] = useState([]);
  const {
    caseId,
    caseRefNo,
    reViolatedRules,
    currentStatus,
    currentStage,
    partnerId,
    internalId,
    investigationVerdict,
    investigatorVerdict,
    txnAmount
  } = selectedCase;

  const violatedRules = txnDetails?.details?.reViolatedRules || reViolatedRules || [];
  const stage = _.toLower(currentStage);
  const shouldAddBucketName =
    isCooperative && !_.isEmpty(investigationVerdict) && _.isEmpty(investigatorVerdict);
  let bucketName = null;
  if (shouldAddBucketName)
    if (stage === 'reviewer') bucketName = 'Unverified';
    else if (stage === 'investigator') bucketName = 'Priority';

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'frm' });
    tabRef?.current?.changeTab(0);
  }, [selectCase, txnId]);

  useEffect(() => {
    if (!_.isEmpty(selectedCase)) {
      document.title = `BANKiQ FRC | Investigation - ${selectedCase.caseRefNo}`;
      transactionHistorySearchActions.onClearSearch();
    }

    !_.isEmpty(channel) &&
      !_.isEmpty(violatedRules) &&
      fetchRulesWithConditions({ rulesCodes: violatedRules }, channel);

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabChangeAction = (tabId) => {
    if (!_.includes(renderedTab, tabId)) setRenderedTab((prev) => [...prev, tabId]);
  };

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  currentStatus !== 'READY_TO_OPEN' && tabList.push('Logs');

  const investigationAction = <InvestigationActionsContainer history={history} channel="frm" />;
  return (
    <div className="content-wrapper">
      <Tabs
        pills
        ref={tabRef}
        tabNames={tabList}
        action={investigationAction}
        tabChangeAction={tabChangeAction}>
        <TabPane tabId={0}>
          <Row className="ms-0">
            <Col md="12" sm="12">
              {isCooperative && <CaseNotificationContainer txnId={txnId} channel="frm" />}
              <Row className="ms-0">
                {currentStatus !== 'READY_TO_OPEN' ? (
                  <Col lg={!_.isEmpty(txnDetails?.details?.riskDetails) ? 7 : 12}>
                    <CaseDetailCard
                      caseDetails={selectedCase}
                      documentStatus={documentStatus}
                      channel="frm"
                    />
                  </Col>
                ) : null}

                {!_.isEmpty(txnDetails?.details?.riskDetails) && (
                  <Col lg={currentStatus !== 'READY_TO_OPEN' ? 5 : 12}>
                    <TransactionRiskScoreInfo details={txnDetails?.details} />
                  </Col>
                )}
              </Row>
              <TransactionDetailCardContainer channel="frm" />
              <Suspense fallback={<Loader show={true} />}>
                {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
                  <MerchantInfoCardContainer merchantId={entityId} channel="frm" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
                  <CustomerInfoCardContainer customerId={entityId} channel="frm" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
                  <AgentInfoCardContainer agentId={entityId} channel="frm" />
                ) : null}
              </Suspense>
            </Col>

            <Col xl={caseId ? '8' : '12'} xs="12">
              <Suspense fallback={<Loader show={true} />}>
                {!_.isEmpty(txnDetails.details) && (
                  <ViolatedRulesCardContainer
                    transactionId={txnId}
                    txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
                    reViolatedRules={violatedRules}
                    channel="frm"
                  />
                )}
                <PastInvestigationCardContainer
                  role={role}
                  transactionId={txnId}
                  entityId={entityId}
                  channel="frm"
                  contextKey="pastInvestigationIndepth"
                />
              </Suspense>
            </Col>

            {caseId && (
              <Col xl="4" xs="12">
                <Suspense fallback={<Loader show={true} />}>
                  <NotationContainer
                    caseId={caseId}
                    caseRefNo={caseRefNo}
                    disableComment={currentStatus === 'READY_TO_OPEN'}
                    channel="frm"
                  />
                </Suspense>
              </Col>
            )}
          </Row>
        </TabPane>

        <TabPane tabId={1}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 1) && (
            <Suspense fallback={<Loader show={true} />}>
              <TransactionTimeAmountTrendContainer
                txnId={txnId}
                entityId={entityId}
                txnAmount={txnAmount}
                txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
              />
            </Suspense>
          )}
        </TabPane>

        <TabPane tabId={2}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 2) && (
            <div>
              <Suspense fallback={<Loader show={true} />} className="mb-3">
                <CustomerEventsTableContainer
                  customerId={entityId}
                  custAccountNumber={txnDetails?.details?.identifiers?.custAccountNumber || ''}
                />
              </Suspense>
              <Suspense fallback={<Loader show={true} />}>
                <HistoryTxnTableContainer
                  entityId={entityId}
                  entityCategory={txnDetails.details.entityCategory || ''}
                  channel="frm"
                  txnDetails={txnDetails.details}
                  showSearchFilter={true}
                />
              </Suspense>
            </div>
          )}
        </TabPane>

        <TabPane tabId={3}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 3) && (
            <Suspense fallback={<Loader show={true} />}>
              <SimilarTxnsTableContainer
                txnId={txnId}
                txnDetails={txnDetails.details}
                channel="frm"
              />
            </Suspense>
          )}
        </TabPane>

        <TabPane tabId={4}>
          {!_.isEmpty(selectedCase) && _.includes(renderedTab, 4) && (
            <Suspense fallback={<Loader show={true} />}>
              <Log module="Case" id={caseRefNo || ''} />
              {internalId && <CPIFRDownloadHistoryContainer internalId={internalId} />}
            </Suspense>
          )}
        </TabPane>
      </Tabs>

      <Suspense fallback={<Loader show={true} />}>
        <VerdictModalContainer
          caseId={caseRefNo}
          channel="frm"
          caseDetails={selectedCase}
          bulkCaseIds={shouldAddBucketName && [caseRefNo]}
          partnerId={partnerId}
          violatedRules={txnDetails.details?.reViolatedRules}
          bucket={bucketName}
        />
      </Suspense>

      <CPFIRFormContainer allowClose={true} />
      {!_.isEmpty(violatedRules) && <RuleFeedbackFormContainer rulesCodes={violatedRules} />}
    </div>
  );
};

Indepth.propTypes = {
  role: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  transactionHistorySearchActions: PropTypes.object.isRequired,
  documentStatus: PropTypes.object.isRequired,
  selectCase: PropTypes.func.isRequired,
  fetchRulesWithConditions: PropTypes.func.isRequired
};

export default Indepth;
