import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { map, isEmpty } from 'lodash';

import * as echarts from 'echarts/core';
import ReactEcharts from 'echarts-for-react/lib/core';
import { Card, CardBody, CardTitle, Col, Row } from 'reactstrap';
import { PieChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  DatasetComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent,
  ToolboxComponent,
  MarkLineComponent
} from 'echarts/components';

import lightTheme from 'constants/chartLightTheme';
import darkTheme from 'constants/chartDarkTheme';

echarts.registerTheme('chart-theme-light', lightTheme);
echarts.registerTheme('chart-theme-dark', darkTheme);

function RuleFeedbackStats({ theme, period, ruleFeedbacks, fetchRuleFeedback, rule }) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleFeedback({
        ruleCode: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const chartTheme = {
    light: 'chart-theme-light',
    dark: 'chart-theme-dark'
  };

  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DataZoomComponent,
    DatasetComponent,
    LegendComponent,
    MarkPointComponent,
    MarkLineComponent,
    PieChart,
    ToolboxComponent
  ]);

  const getOption = (title, data, type) => ({
    title: {
      text: title,
      bottom: 'bottom',
      left: 'center',
      textStyle: {
        width: 300,
        overflow: 'break',
        fontSize: 15
      }
    },
    tooltip: {
      show: false
    },
    legend: {
      orient: 'horizontal',
      top: 'top',
      selectedMode: false,
      show: type == 'rule'
    },
    series: [
      {
        name: 'Count',
        type: 'pie',
        radius: ['40%', '70%'],
        bottom: '24%',
        top: '12%',
        label: {
          formatter: ' {b|{b}} {a|{a}} {abg|}\n{hr|}\n {c}  {per|{d}%}  ',
          backgroundColor: '#F6F8FC',
          borderColor: '#8C8D8E',
          borderWidth: 1,
          borderRadius: 4,
          show: false,
          rich: {
            a: {
              color: '#4C5058',
              fontSize: 12,
              fontWeight: 'bold',
              lineHeight: 20
            },
            hr: {
              borderColor: '#8C8D8E',
              width: '100%',
              borderWidth: 1,
              height: 0
            },
            b: {
              color: '#4C5058',
              fontSize: 12,
              fontWeight: 'bold',
              lineHeight: 20
            },
            per: {
              color: '#fff',
              backgroundColor: '#4C5058',
              padding: [2, 2],
              borderRadius: 4
            }
          }
        },
        emphasis: {
          label: {
            show: true
          }
        },
        data
      }
    ],
    color: ['#3ba272', '#c1232b']
  });
  return (
    <Card className={'d-flex card-main '}>
      <CardBody className="p-3 card-container">
        <CardTitle className={'d-flex align-items-center justify-content-between '}>
          {'Rule Feedback Analysis'}
        </CardTitle>
        <Card>
          {ruleFeedbacks?.loader ? (
            <div className="graph-loader">
              <span>{''}</span>
            </div>
          ) : ruleFeedbacks?.error ? (
            <div className="no-data-div">{ruleFeedbacks?.errorMessage}</div>
          ) : isEmpty(ruleFeedbacks.data) ? (
            <div className="no-data-div">No data to display</div>
          ) : (
            <Row className={'d-flex align-items-center justify-content-between '}>
              {(ruleFeedbacks.data.ruleRatingPositive !== 0 ||
                ruleFeedbacks.data.ruleRatingNegative !== 0) && (
                <Col md="4">
                  <ReactEcharts
                    echarts={echarts}
                    option={getOption(
                      ruleFeedbacks.data.name,
                      [
                        { value: ruleFeedbacks.data.ruleRatingPositive, name: 'Positive' },
                        { value: ruleFeedbacks.data.ruleRatingNegative, name: 'Negative' }
                      ],
                      'rule'
                    )}
                    notMerge={true}
                    lazyUpdate={true}
                    theme={chartTheme[theme]}
                    style={{ height: '500px' }}
                  />
                </Col>
              )}
              <Col
                md={
                  ruleFeedbacks.data.ruleRatingPositive !== 0 ||
                  ruleFeedbacks.data.ruleRatingNegative !== 0
                    ? '8'
                    : '12'
                }
                className="d-flex flex-row flex-wrap">
                {map(ruleFeedbacks.data.ruleCondition, (condition) => (
                  <Col className="mb-3" md="6">
                    <ReactEcharts
                      echarts={echarts}
                      option={getOption(
                        condition.condition,
                        [
                          { value: condition.conditionRatingPositive, name: 'Positive' },
                          { value: condition.conditionRatingNegative, name: 'Negative' }
                        ],
                        'condition'
                      )}
                      notMerge={true}
                      lazyUpdate={true}
                      theme={chartTheme[theme]}
                      style={{ height: '250px' }}
                    />
                  </Col>
                ))}
              </Col>
            </Row>
          )}
        </Card>
      </CardBody>
    </Card>
  );
}

RuleFeedbackStats.propTypes = {
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  rule: PropTypes.string.isRequired,
  ruleFeedbacks: PropTypes.object.isRequired,
  fetchRuleFeedback: PropTypes.func.isRequired
};

export default RuleFeedbackStats;
