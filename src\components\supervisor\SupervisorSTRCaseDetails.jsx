import _ from 'lodash';
import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { TabPane, ButtonGroup, Button } from 'reactstrap';
import { useHistory, useParams } from 'react-router-dom';

import Tabs from 'components/common/Tabs';
import LogContainer from 'containers/common/LogContainer';
import NotationContainer from 'containers/common/NotationContainer';
import CitationContainer from 'containers/common/CitationContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import STRReportContainer from 'containers/investigation/STRReportContainer';
import AgentInfoCardContainer from 'containers/common/AgentInfoCardContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import STRReportLogContainer from 'containers/investigation/STRReportLogContainer';
import DownloadSTRButton from 'containers/investigation/DownloadSTRButtonContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import MerchantInfoCardContainer from 'containers/common/MerchantInfoCardContainer';
import CustomerInfoCardContainer from 'containers/common/CustomerInfoCardContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import DocumentManager from 'containers/investigation/DocumentManagerContainer';
import { isCooperative } from 'constants/publicKey';

const SupervisorSTRCaseDetails = ({
  role,
  txnDetails,
  selectCase,
  selectedCase,
  clearSelectedCase
}) => {
  const history = useHistory();
  const { txnId } = useParams();

  const tabRef = useRef(null);

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'str' });
    tabRef?.current?.changeTab(0);
  }, [txnId]);

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  useEffect(() => {
    if (!_.isEmpty(selectedCase)) {
      document.title = 'BANKiQ FRC | Investigation - ' + selectedCase.caseRefNo;
    }

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabList = ['Details', 'Citations', 'Supporting'];
  selectedCase.currentStage !== 'Maker' &&
    selectedCase.currentStatus !== 'READY_TO_OPEN' &&
    tabList.push('Report');
  selectedCase.currentStatus !== 'READY_TO_OPEN' && tabList.push('Logs');

  const investigationAction =
    selectedCase?.makerAction === 'File STR' && role == 'principal-officer' ? (
      selectedCase?.currentStatus == 'Closed' && !selectedCase?.parentTxn ? (
        <DownloadSTRButton caseRefNo={selectedCase?.caseRefNo} entityId={entityId} />
      ) : selectedCase?.currentStatus == 'Pending' ? (
        <ApprovalModalContainer
          caseId={selectedCase?.caseRefNo || ''}
          status={selectedCase?.currentStatus || ''}
          caseDetails={selectedCase}
          channel={'str'}
          singleType=""
        />
      ) : null
    ) : (selectedCase?.currentStatus == 'New' ||
        selectedCase?.currentStatus == 'Open' ||
        selectedCase?.currentStatus == 'Rejected') &&
      !isCooperative ? (
      <UserListDropdownContainer
        bucket={selectedCase?.currentStatus}
        caseId={selectedCase?.caseRefNo}
        channel={'str'}
        showText
      />
    ) : null;

  const actions = (
    <ButtonGroup>
      {investigationAction}
      <Button
        outline
        size="sm"
        color="secondary"
        className="ms-1"
        onClick={() => {
          clearSelectedCase();
          history.goBack();
        }}>
        Back
      </Button>
    </ButtonGroup>
  );

  return (
    <div className={'content-wrapper'}>
      <Tabs ref={tabRef} action={actions} tabNames={tabList} pills>
        <TabPane tabId={0}>
          <CaseDetailCard caseDetails={selectedCase} channel={'str'} />
          <TransactionDetailCardContainer channel="str" />
          {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
            <MerchantInfoCardContainer merchantId={entityId} channel="str" />
          ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
            <CustomerInfoCardContainer customerId={entityId} channel="str" />
          ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
            <AgentInfoCardContainer agentId={entityId} channel="str" />
          ) : null}
          {!_.isEmpty(txnDetails.details) && (
            <ViolatedRulesCardContainer
              transactionId={txnId}
              txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
              reViolatedRules={txnDetails?.details?.reViolatedRules || []}
              channel={'str'}
            />
          )}
          {_.has(selectedCase, 'caseId') && (
            <NotationContainer
              caseId={selectedCase.caseId}
              caseRefNo={selectedCase.caseRefNo}
              channel={'str'}
            />
          )}
        </TabPane>
        <TabPane tabId={1}>
          {selectedCase.caseRefNo && (
            <CitationContainer
              caseRefNo={selectedCase.caseRefNo}
              txnId={txnId}
              disable={_.includes(['READY_TO_OPEN'], selectedCase.currentStatus)}
            />
          )}
        </TabPane>
        <TabPane tabId={2}>
          <DocumentManager caseRefNo={selectedCase.caseRefNo} canUpload={false} />
        </TabPane>
        {selectedCase.currentStage !== 'Maker' &&
          selectedCase.currentStatus !== 'READY_TO_OPEN' && (
            <TabPane tabId={3}>
              <STRReportContainer caseRefNo={selectedCase?.caseRefNo} />
            </TabPane>
          )}

        <TabPane tabId={tabList.indexOf('Logs')}>
          <LogContainer module="Case" id={selectedCase.caseRefNo} />
          <STRReportLogContainer caseRefNo={selectedCase.caseRefNo} />
        </TabPane>
      </Tabs>
    </div>
  );
};

SupervisorSTRCaseDetails.propTypes = {
  role: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  selectCase: PropTypes.func.isRequired,
  clearSelectedCase: PropTypes.func.isRequired
};

export default SupervisorSTRCaseDetails;
