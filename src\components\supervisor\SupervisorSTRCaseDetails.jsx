import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useRef } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { TabPane, ButtonGroup, Button } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import { isCooperative } from 'constants/publicKey';
import AgentInfoCardContainer from 'containers/common/AgentInfoCardContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import CitationContainer from 'containers/common/CitationContainer';
import CustomerInfoCardContainer from 'containers/common/CustomerInfoCardContainer';
import LogContainer from 'containers/common/LogContainer';
import MerchantInfoCardContainer from 'containers/common/MerchantInfoCardContainer';
import NotationContainer from 'containers/common/NotationContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';
import DocumentManager from 'containers/investigation/DocumentManagerContainer';
import DownloadSTRButton from 'containers/investigation/DownloadSTRButtonContainer';
import STRReportContainer from 'containers/investigation/STRReportContainer';
import STRReportLogContainer from 'containers/investigation/STRReportLogContainer';

const SupervisorSTRCaseDetails = ({
  role,
  txnDetails,
  selectCase,
  selectedCase,
  clearSelectedCase
}) => {
  const history = useHistory();
  const { txnId } = useParams();

  const tabRef = useRef(null);

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'str' });
    tabRef?.current?.changeTab(0);
  }, [selectCase, txnId]);

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  useEffect(() => {
    if (!_.isEmpty(selectedCase))
      document.title = `BANKiQ FRC | Investigation - ${selectedCase.caseRefNo}`;

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabList = ['Details', 'Citations', 'Supporting'];
  selectedCase.currentStage !== 'Maker' &&
    selectedCase.currentStatus !== 'READY_TO_OPEN' &&
    tabList.push('Report');
  selectedCase.currentStatus !== 'READY_TO_OPEN' && tabList.push('Logs');

  const getInvestigationAction = () => {
    // Principal officer with File STR action
    if (selectedCase?.makerAction === 'File STR' && role === 'principal-officer') {
      if (selectedCase?.currentStatus === 'Closed' && !selectedCase?.parentTxn)
        return <DownloadSTRButton caseRefNo={selectedCase?.caseRefNo} entityId={entityId} />;

      if (selectedCase?.currentStatus === 'Pending')
        return (
          <ApprovalModalContainer
            caseId={selectedCase?.caseRefNo || ''}
            status={selectedCase?.currentStatus || ''}
            caseDetails={selectedCase}
            channel="str"
            singleType=""
          />
        );

      return null;
    }

    // Other cases with specific statuses and non-cooperative
    const isValidStatus =
      selectedCase?.currentStatus === 'New' ||
      selectedCase?.currentStatus === 'Open' ||
      selectedCase?.currentStatus === 'Rejected';

    if (isValidStatus && !isCooperative)
      return (
        <UserListDropdownContainer
          bucket={selectedCase?.currentStatus}
          caseId={selectedCase?.caseRefNo}
          channel="str"
          showText
        />
      );

    return null;
  };

  const actions = (
    <ButtonGroup>
      {getInvestigationAction()}
      <Button
        outline
        size="sm"
        color="secondary"
        className="ms-1"
        onClick={() => {
          clearSelectedCase();
          history.goBack();
        }}>
        Back
      </Button>
    </ButtonGroup>
  );

  return (
    <div className="content-wrapper">
      <Tabs ref={tabRef} action={actions} tabNames={tabList} pills>
        <TabPane tabId={0}>
          <CaseDetailCard caseDetails={selectedCase} channel="str" />
          <TransactionDetailCardContainer channel="str" />
          {(() => {
            const entityCategory = _.lowerCase(txnDetails.details.entityCategory);

            if (entityCategory === 'merchant')
              return <MerchantInfoCardContainer merchantId={entityId} channel="str" />;

            if (entityCategory === 'customer')
              return <CustomerInfoCardContainer customerId={entityId} channel="str" />;

            if (entityCategory === 'agent')
              return <AgentInfoCardContainer agentId={entityId} channel="str" />;

            return null;
          })()}
          {!_.isEmpty(txnDetails.details) && (
            <ViolatedRulesCardContainer
              transactionId={txnId}
              txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
              reViolatedRules={txnDetails?.details?.reViolatedRules || []}
              channel="str"
            />
          )}
          {_.has(selectedCase, 'caseId') && (
            <NotationContainer
              caseId={selectedCase.caseId}
              caseRefNo={selectedCase.caseRefNo}
              channel="str"
            />
          )}
        </TabPane>
        <TabPane tabId={1}>
          {selectedCase.caseRefNo && (
            <CitationContainer
              caseRefNo={selectedCase.caseRefNo}
              txnId={txnId}
              disable={_.includes(['READY_TO_OPEN'], selectedCase.currentStatus)}
            />
          )}
        </TabPane>
        <TabPane tabId={2}>
          <DocumentManager caseRefNo={selectedCase.caseRefNo} canUpload={false} />
        </TabPane>
        {selectedCase.currentStage !== 'Maker' &&
          selectedCase.currentStatus !== 'READY_TO_OPEN' && (
            <TabPane tabId={3}>
              <STRReportContainer caseRefNo={selectedCase?.caseRefNo} />
            </TabPane>
          )}

        <TabPane tabId={tabList.indexOf('Logs')}>
          <LogContainer module="Case" id={selectedCase.caseRefNo} />
          <STRReportLogContainer caseRefNo={selectedCase.caseRefNo} />
        </TabPane>
      </Tabs>
    </div>
  );
};

SupervisorSTRCaseDetails.propTypes = {
  role: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  selectCase: PropTypes.func.isRequired,
  clearSelectedCase: PropTypes.func.isRequired
};

export default SupervisorSTRCaseDetails;
