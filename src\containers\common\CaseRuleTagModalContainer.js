import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onAddRulesToCase } from 'actions/caseReviewActions';
import CaseRuleTagModal from 'components/common/CaseRuleTagModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    prefiltersList: state.prefiltersList,
    role: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    addRulesToCase: bindActionCreators(onAddRulesToCase, dispatch)
  };
};

const CaseRuleTagModalContainer = connect(mapStateToProps, mapDispatchToProps)(CaseRuleTagModal);

export default CaseRuleTagModalContainer;
