/* eslint-disable react/prop-types */
import { render, screen } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import RuleEngine from '../RuleEngine';

// Fix React import for performance monitor
global.React = React;

// Mock child components
jest.mock('containers/ruleEngine/CognitiveStatusCardContainer', () =>
  jest.fn(() => <div data-testid="cognitive-status">Cognitive Status</div>)
);

jest.mock('containers/ruleEngine/RuleListTableContainer', () =>
  jest.fn(() => <div data-testid="rule-list-table">Rule List Table</div>)
);

jest.mock('containers/ruleEngine/DynamicCountersListTableContainer', () =>
  jest.fn(() => <div data-testid="dynamic-counters">Dynamic Counters</div>)
);

// Mock store
const mockStore = createStore(() => ({}));

describe('RuleEngine Performance Tests', () => {
  beforeEach(() => {
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const defaultProps = {
    channels: ['frm'],
    userRoles: 'checker',
    hasCognitive: 0,
    hasSandbox: 1,
    fetchSandboxHistory: jest.fn()
  };

  const renderWithProvider = (component) =>
    render(<Provider store={mockStore}>{component}</Provider>);

  it('should not re-render unnecessarily with same props', () => {
    const { rerender } = renderWithProvider(<RuleEngine {...defaultProps} />);

    // Clear initial render tracking
    performanceMonitor.reset();

    // Re-render with same props
    rerender(
      <Provider store={mockStore}>
        <RuleEngine {...defaultProps} />
      </Provider>
    );

    const report = performanceMonitor.getReport();
    expect(report.renderCounts.RuleEngine).toBeUndefined();
  });

  it('should memoize tabNames computation', () => {
    const propsWithMultipleChannels = {
      ...defaultProps,
      channels: ['frm', 'str', 'rpsl']
    };

    const { rerender } = renderWithProvider(<RuleEngine {...propsWithMultipleChannels} />);

    // Re-render with same channels
    rerender(
      <Provider store={mockStore}>
        <RuleEngine {...propsWithMultipleChannels} />
      </Provider>
    );

    // Should not cause expensive re-computation
    expect(screen.getAllByText('Rule List Table')).toHaveLength(3);
  });

  it('should handle single channel efficiently', () => {
    performanceMonitor.startTiming('single_channel_render');

    renderWithProvider(<RuleEngine {...defaultProps} />);

    const duration = performanceMonitor.endTiming('single_channel_render');
    expect(duration).toBeLessThan(50); // Should render quickly
  });

  it('should handle multiple channels efficiently', () => {
    const multiChannelProps = {
      ...defaultProps,
      channels: ['frm', 'str', 'rpsl']
    };

    performanceMonitor.startTiming('multi_channel_render');

    renderWithProvider(<RuleEngine {...multiChannelProps} />);

    const duration = performanceMonitor.endTiming('multi_channel_render');
    expect(duration).toBeLessThan(100); // Should still render reasonably quickly
  });

  it('should only call fetchSandboxHistory when conditions are met', () => {
    const fetchSandboxHistory = jest.fn();

    // Test with frm channel and sandbox enabled
    renderWithProvider(
      <RuleEngine
        {...defaultProps}
        channels={['frm']}
        hasSandbox={1}
        fetchSandboxHistory={fetchSandboxHistory}
      />
    );

    expect(fetchSandboxHistory).toHaveBeenCalledTimes(1);

    // Test with different channel
    fetchSandboxHistory.mockClear();
    renderWithProvider(
      <RuleEngine
        {...defaultProps}
        channels={['str']}
        hasSandbox={1}
        fetchSandboxHistory={fetchSandboxHistory}
      />
    );

    expect(fetchSandboxHistory).not.toHaveBeenCalled();
  });

  it('should not cause memory leaks on multiple renders', () => {
    const { rerender } = renderWithProvider(<RuleEngine {...defaultProps} />);

    // Take initial memory snapshot
    performanceMonitor.takeMemorySnapshot('initial');

    // Perform multiple re-renders
    for (let i = 0; i < 10; i++)
      rerender(
        <Provider store={mockStore}>
          <RuleEngine {...defaultProps} channels={[`channel_${i}`]} />
        </Provider>
      );

    // Take final memory snapshot
    performanceMonitor.takeMemorySnapshot('final');

    // Check for memory leaks (this is a basic check)
    performanceMonitor.checkMemoryLeaks();

    // The test passes if no warnings are logged
    expect(true).toBe(true);
  });

  it('should efficiently handle cognitive status visibility changes', () => {
    const { rerender } = renderWithProvider(
      <RuleEngine {...defaultProps} userRoles="supervisor" hasCognitive={1} />
    );

    expect(screen.getByTestId('cognitive-status')).toBeInTheDocument();

    performanceMonitor.startTiming('cognitive_toggle');

    rerender(
      <Provider store={mockStore}>
        <RuleEngine {...defaultProps} userRoles="checker" hasCognitive={1} />
      </Provider>
    );

    const duration = performanceMonitor.endTiming('cognitive_toggle');
    expect(duration).toBeLessThan(20);
    expect(screen.queryByTestId('cognitive-status')).not.toBeInTheDocument();
  });

  it('should maintain stable references for memoized values', () => {
    let capturedTabNames = null;
    let capturedShowCognitive = null;

    // Mock the component to capture memoized values
    const TestWrapper = (props) => {
      const tabNames = React.useMemo(
        () => props.channels.map((tab) => tab.toUpperCase()),
        [props.channels]
      );
      const showCognitive = React.useMemo(
        () => props.userRoles === 'supervisor' && props.hasCognitive === 1,
        [props.userRoles, props.hasCognitive]
      );

      capturedTabNames = tabNames;
      capturedShowCognitive = showCognitive;

      return <div>Test</div>;
    };

    const { rerender } = render(<TestWrapper {...defaultProps} />);
    const initialTabNames = capturedTabNames;
    const initialShowCognitive = capturedShowCognitive;

    // Re-render with same props
    rerender(<TestWrapper {...defaultProps} />);

    // References should be stable
    expect(capturedTabNames).toBe(initialTabNames);
    expect(capturedShowCognitive).toBe(initialShowCognitive);
  });
});
