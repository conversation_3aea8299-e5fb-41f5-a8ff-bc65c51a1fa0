import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as actions from 'actions/authActions';
import { onToggleTheme, onToggleResetPasswordModal } from 'actions/toggleActions';
import Header from 'components/common/Header';

const mapStateToProps = (state) => ({
  authDetails: state.auth,
  theme: state.toggle.theme,
  displayResetPasswordModal: state.toggle.resetPasswordModal,
  hasHoldAndRelease: state.user.configurations.holdAndRelease,
  hasKnowageReports: state.user.configurations.knowageReport,
  hasLocalAuthentication: state.user.configurations.localAuthentication
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(actions, dispatch),
  toggleTheme: bindActionCreators(onToggleTheme, dispatch),
  toggleResetPasswordModal: bindActionCreators(onToggleResetPasswordModal, dispatch)
});

const HeaderContainer = connect(mapStateToProps, mapDispatchToProps)(Header);

export default HeaderContainer;
