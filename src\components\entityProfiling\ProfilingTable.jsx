import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isEmpty, debounce } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import ReactTable from 'react-table';
import { Card, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import DurationSelector from 'components/common/DurationSelector';
import TableLoader from 'components/loader/TableLoader';
import { PROFILING_TABLE_HEADER } from 'constants/applicationConstants';

const ProfilingTable = ({
  header,
  customerTransactionSummary,
  onClickSearchProfile,
  onClickRuleLabel,
  onPageSizeChange,
  onSetPageNo,
  pageNo,
  pageRecords,
  contextKey
}) => {
  const [tableFilters, setTableFilters] = useState([]);

  const { list, loader, error, errorMessage, count } = customerTransactionSummary;

  const filterRuleLabel = (filter, row) => {
    const ruleLabels = Object.entries(row.ruleLabel)
      .map(([key, value]) => `${key} - ${value}`)
      .join(' ')
      .toLowerCase();

    return ruleLabels.includes(filter.value.toLowerCase());
  };

  const actionColumn = useMemo(
    () => [
      {
        Header: '',
        searchable: false,
        sortable: false,
        filterable: false,

        // eslint-disable-next-line react/prop-types
        Cell: ({ original }) => (
          <Button
            size="sm"
            type="submit"
            color="primary"
            className="me-3"
            onClick={() => onClickSearchProfile(original)}>
            <FontAwesomeIcon icon={faSearch} />
          </Button>
        )
      }
    ],
    [onClickSearchProfile]
  );

  const ruleLabelColumn = useMemo(
    () => [
      {
        Header: 'Rule Label',
        accessor: 'ruleLabel',
        width: 350,
        filterMethod: filterRuleLabel,
        Cell: (row) => (
          <>
            {Object.entries(row.original.ruleLabel).map(([key, value]) => (
              <button
                onClick={() => onClickRuleLabel(key, row.original)}
                className="btn btn-secondary m-1 badge-pill"
                key={key}
                type="button">
                {key[0].toUpperCase() + key.slice(1)} - {value}
              </button>
            ))}
          </>
        )
      }
    ],
    [onClickRuleLabel]
  );

  const profileTableHeader = useMemo(
    () => [...actionColumn, ...PROFILING_TABLE_HEADER, ...ruleLabelColumn],
    [actionColumn, ruleLabelColumn]
  );

  const tablePageCountProp = isEmpty(tableFilters)
    ? { pages: count / pageRecords > 1 ? Math.ceil(count / pageRecords) : 1 }
    : {};

  useEffect(() => {
    const debouncedSetPageNo = debounce(() => {
      onSetPageNo(1);
    }, 500);

    debouncedSetPageNo();

    return () => {
      debouncedSetPageNo.cancel();
    };
  }, [tableFilters]);

  const renderTable = useCallback(() => {
    if (loader) return <TableLoader />;
    if (error) return <div className="no-data-div no-data-card-padding">{errorMessage}</div>;

    return (
      <Card className="mt-3">
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id]?.toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          data={list}
          columns={profileTableHeader}
          noDataText="No entity details found"
          filterable
          showPaginationTop
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={5}
          showPageJump={false}
          filtered={tableFilters}
          onFilteredChange={setTableFilters}
          className="-highlight -striped"
          page={pageNo - 1}
          pageSize={pageRecords}
          onPageChange={(page) => onSetPageNo(page + 1)}
          onPageSizeChange={(pageSize, page) => onPageSizeChange(pageSize, page + 1)}
          {...tablePageCountProp}
        />
      </Card>
    );
  }, [loader, error, errorMessage, list, profileTableHeader, tableFilters]);

  const action = useMemo(() => <DurationSelector contextKey={contextKey} />, []);

  return (
    <CardContainer title={header} action={action}>
      {renderTable()}
    </CardContainer>
  );
};

ProfilingTable.propTypes = {
  header: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  customerTransactionSummary: PropTypes.object.isRequired,
  onClickSearchProfile: PropTypes.func.isRequired,
  onClickRuleLabel: PropTypes.func.isRequired,
  onPageSizeChange: PropTypes.func.isRequired,
  onSetPageNo: PropTypes.func.isRequired,
  pageNo: PropTypes.number.isRequired,
  pageRecords: PropTypes.number.isRequired
};

export default ProfilingTable;
