import _ from 'lodash';
import React, { Suspense, lazy, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { TabPane, Row, Col } from 'reactstrap';
import { useHistory, useParams } from 'react-router-dom';

import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import CitationContainer from 'containers/common/CitationContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import InvestigationActionsContainer from 'containers/investigation/InvestigationActionsContainer';

const AgentInfoCardContainer = lazy(() => import('containers/common/AgentInfoCardContainer'));
const CustomerInfoCardContainer = lazy(() => import('containers/common/CustomerInfoCardContainer'));
const MerchantInfoCardContainer = lazy(() => import('containers/common/MerchantInfoCardContainer'));
const Log = lazy(() => import('containers/common/LogContainer'));
const NotationContainer = lazy(() => import('containers/common/NotationContainer'));
const HistoryTxnTableContainer = lazy(() => import('containers/common/HistoryTxnTableContainer'));
const ViolatedRulesCardContainer = lazy(
  () => import('containers/common/ViolatedRulesCardContainer')
);
const PastInvestigationCardContainer = lazy(
  () => import('containers/common/PastInvestigationCardContainer')
);
const STRReportLogContainer = lazy(() => import('containers/investigation/STRReportLogContainer'));
const DocumentManager = lazy(() => import('containers/investigation/DocumentManagerContainer'));
const STRReportContainer = lazy(() => import('containers/investigation/STRReportContainer'));
const AddCaseCitationContainer = lazy(() => import('containers/common/AddCaseCitationContainer'));

const IndepthSTR = ({
  role,
  userName,
  txnDetails,
  selectCase,
  selectedCase,
  addChildTxn,
  transactionHistorySearchActions
}) => {
  const history = useHistory();
  const { txnId } = useParams();
  const tabRef = useRef(null);
  const [renderedTab, setRenderedTab] = useState([]);

  useEffect(() => {
    txnId && selectCase({ txnId, channel: 'str' });
    tabRef?.current?.changeTab(0);
  }, [txnId]);

  useEffect(() => {
    if (!_.isEmpty(selectedCase)) {
      document.title = 'BANKiQ FRC | Investigation - ' + selectedCase.caseRefNo;
      transactionHistorySearchActions.onClearSearch();
    }

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const tabChangeAction = (tabId) => {
    if (!_.includes(renderedTab, tabId)) setRenderedTab((prev) => [...prev, tabId]);
  };

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details.entityId.value : '';

  const tabList = ['Details', 'History', 'Investigations', 'Citations', 'Supporting'];
  selectedCase.currentStage !== 'Maker' &&
    selectedCase.currentStatus !== 'READY_TO_OPEN' &&
    tabList.push('Report');
  selectedCase.currentStatus !== 'READY_TO_OPEN' && tabList.push('Logs');

  const investigationAction = <InvestigationActionsContainer history={history} channel="str" />;

  return (
    <div className={'content-wrapper'}>
      <Tabs
        pills
        ref={tabRef}
        tabNames={tabList}
        action={investigationAction}
        tabChangeAction={tabChangeAction}>
        <TabPane tabId={0}>
          <Row className="ms-0">
            <Col md="12" sm="12">
              {selectedCase.currentStatus !== 'READY_TO_OPEN' && (
                <CaseDetailCard caseDetails={selectedCase} channel="str" />
              )}
              <TransactionDetailCardContainer channel="str" />
              <Suspense fallback={<Loader show={true} />}>
                {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
                  <MerchantInfoCardContainer merchantId={entityId} channel="str" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
                  <CustomerInfoCardContainer customerId={entityId} channel="str" />
                ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
                  <AgentInfoCardContainer agentId={entityId} channel="str" />
                ) : null}
              </Suspense>
            </Col>

            <Col xl={_.has(selectedCase, 'caseId') ? '8' : '12'} xs="12">
              <Suspense fallback={<Loader show={true} />}>
                {!_.isEmpty(txnDetails.details) && (
                  <ViolatedRulesCardContainer
                    transactionId={txnId}
                    txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
                    reViolatedRules={txnDetails?.details?.reViolatedRules || []}
                    channel="str"
                  />
                )}
                {!_.isEmpty(txnDetails.details) && (
                  <PastInvestigationCardContainer
                    key={'selectedAlerts'}
                    entityId={entityId}
                    channel={'str'}
                    transactionId={txnId}
                    caseStatus={['New', 'Open']}
                    childTxns={selectedCase.childTxns}
                    addToCase={addChildTxn}
                    showActions={
                      selectedCase.currentlyAssignedTo == userName &&
                      selectedCase.currentStatus == 'Open' &&
                      selectedCase.isAcknowledged === 1
                    }
                    contextKey="pastInvestigationSelectedAlerts"
                  />
                )}
              </Suspense>
            </Col>

            {_.has(selectedCase, 'caseId') && (
              <Col xl="4" xs="12">
                <Suspense fallback={<Loader show={true} />}>
                  <NotationContainer
                    caseId={selectedCase.caseId}
                    caseRefNo={selectedCase.caseRefNo}
                    channel="str"
                    disableComment={_.includes(
                      ['READY_TO_OPEN', 'Closed'],
                      selectedCase.currentStatus
                    )}
                  />
                </Suspense>
              </Col>
            )}
          </Row>
        </TabPane>
        <TabPane tabId={1}>
          {_.includes(renderedTab, 1) && (
            <Suspense fallback={<Loader show={true} />}>
              <HistoryTxnTableContainer
                entityId={entityId}
                entityCategory={txnDetails.details.entityCategory || ''}
                channel="str"
              />
            </Suspense>
          )}
        </TabPane>
        <TabPane tabId={2}>
          {_.includes(renderedTab, 2) && (
            <Suspense fallback={<Loader show={true} />}>
              <PastInvestigationCardContainer
                key={'allAlerts'}
                role={role}
                transactionId={txnId}
                entityId={entityId}
                channel="str"
                contextKey="pastInvestigationAllAlerts"
              />
            </Suspense>
          )}
        </TabPane>
        <TabPane tabId={3}>
          {selectedCase.caseRefNo && _.includes(renderedTab, 3) && (
            <Suspense fallback={<Loader show={true} />}>
              <div>
                {!_.includes(['READY_TO_OPEN', 'New', 'Closed'], selectedCase.currentStatus) && (
                  <span className="d-flex justify-content-end mb-2">
                    <AddCaseCitationContainer caseRefNo={selectedCase.caseRefNo} channel="str" />
                  </span>
                )}
                <CitationContainer
                  caseRefNo={selectedCase.caseRefNo}
                  txnId={txnId}
                  disable={_.includes(
                    ['READY_TO_OPEN', 'New', 'Closed'],
                    selectedCase.currentStatus
                  )}
                />
              </div>
            </Suspense>
          )}
        </TabPane>

        <TabPane tabId={4}>
          {_.includes(renderedTab, 4) && (
            <Suspense fallback={<Loader show={true} />}>
              <DocumentManager
                caseRefNo={selectedCase.caseRefNo}
                canUpload={
                  role === 'maker' &&
                  ['Open', 'Rejected'].includes(selectedCase?.currentStatus) &&
                  selectedCase?.isAcknowledged == 1 &&
                  selectedCase?.currentlyAssignedTo == userName
                }
              />
            </Suspense>
          )}
        </TabPane>

        {selectedCase.currentStage !== 'Maker' &&
          selectedCase.currentStatus !== 'READY_TO_OPEN' && (
            <TabPane tabId={5}>
              {_.includes(renderedTab, 5) && (
                <Suspense fallback={<Loader show={true} />}>
                  <STRReportContainer caseRefNo={selectedCase?.caseRefNo} />
                </Suspense>
              )}
            </TabPane>
          )}

        <TabPane tabId={tabList.indexOf('Logs')}>
          {_.includes(renderedTab, tabList.indexOf('Logs')) && (
            <Suspense fallback={<Loader show={true} />}>
              <Log module="Case" id={selectedCase.caseRefNo} />
              <STRReportLogContainer caseRefNo={selectedCase.caseRefNo} />
            </Suspense>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};

IndepthSTR.propTypes = {
  role: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  transactionHistorySearchActions: PropTypes.object.isRequired,
  addChildTxn: PropTypes.func.isRequired,
  selectCase: PropTypes.func.isRequired
};

export default IndepthSTR;
