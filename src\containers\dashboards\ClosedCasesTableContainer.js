import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCases } from 'actions/caseReviewActions';
import ClosedCasesTable from 'components/dashboards/ClosedCasesTable';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    conf: state.caseAssignment.cases.frm.conf
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchCases, dispatch)
  };
};

const ClosedCasesTableContainer = connect(mapStateToProps, mapDispatchToProps)(ClosedCasesTable);

export default ClosedCasesTableContainer;
