import prefilterReducer from 'reducers/prefilterReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import responses from 'mocks/responses';
import { groupBy } from 'lodash';

describe('prefilter Reducer', () => {
  it('should return the intial state', () => {
    expect(prefilterReducer(undefined, {})).toEqual(initialState.prefilter);
  });

  it('should handle ON_FETCH_FILTER_LOADING', () => {
    expect(
      prefilterReducer(
        {
          filter: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_LOADING
        }
      )
    ).toEqual({
      filter: {
        list: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FILTER_SUCCESS', () => {
    expect(
      prefilterReducer(
        {
          filter: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_SUCCESS,
          response: responses.prefilter.filter
        }
      )
    ).toEqual({
      filter: {
        list: groupBy(responses.prefilter.filter, 'channelName'),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FILTER_FAILURE', () => {
    expect(
      prefilterReducer(
        {
          filter: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      filter: {
        list: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_TPS_LIMIT_LOADING', () => {
    expect(
      prefilterReducer(
        {
          tps: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_TPS_LIMIT_LOADING
        }
      )
    ).toEqual({
      tps: {
        list: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_TPS_LIMIT_SUCCESS', () => {
    expect(
      prefilterReducer(
        {
          tps: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_TPS_LIMIT_SUCCESS,
          response: responses.prefilter.tps
        }
      )
    ).toEqual({
      tps: {
        list: responses.prefilter.tps,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_TPS_LIMIT_FAILURE', () => {
    expect(
      prefilterReducer(
        {
          tps: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_TPS_LIMIT_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      tps: {
        list: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_FILTER_CATEGORY_LOADING', () => {
    expect(
      prefilterReducer(
        {
          category: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_CATEGORY_LOADING
        }
      )
    ).toEqual({
      category: {
        list: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FILTER_CATEGORY_SUCCESS', () => {
    expect(
      prefilterReducer(
        {
          category: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_CATEGORY_SUCCESS,
          response: responses.prefilter.category
        }
      )
    ).toEqual({
      category: {
        list: groupBy(responses.prefilter.category, 'channelName'),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FILTER_CATEGORY_FAILURE', () => {
    expect(
      prefilterReducer(
        {
          category: {
            list: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FILTER_CATEGORY_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      category: {
        list: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
