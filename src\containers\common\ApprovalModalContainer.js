import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onSupervisorApproval, onFetchFraudTypesWithBuckets } from 'actions/caseReviewActions';
import { onFetchSTRReportMasters } from 'actions/strReportActions';
import { onToggleApprovalModal } from 'actions/toggleActions';
import ApprovalModal from 'components/common/ApprovalModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    userList: state.user.userslist,
    role: state.auth.userCreds.roles,
    display: state.toggle.approvalModal,
    strReportMasters: state.strReport.masters,
    fraudTypesWithBuckets: state.caseAssignment.fraudTypesWithBuckets
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggle: bindActionCreators(onToggleApprovalModal, dispatch),
    caseApproval: bindActionCreators(onSupervisorApproval, dispatch),
    getMasters: bindActionCreators(onFetchSTRReportMasters, dispatch),
    fetchFraudTypesWithBuckets: bindActionCreators(onFetchFraudTypesWithBuckets, dispatch)
  };
};

const ApprovalModalContainer = connect(mapStateToProps, mapDispatchToProps)(ApprovalModal);

export default ApprovalModalContainer;
