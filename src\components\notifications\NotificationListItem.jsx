import { faCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import moment from 'moment';
import PropTypes from 'prop-types';
import React from 'react';
import { useHistory } from 'react-router-dom';
import { Row, Col, ListGroupItem } from 'reactstrap';

function NotificationListItem({ notification }) {
  const history = useHistory();

  return (
    <ListGroupItem
      key={notification.id}
      onClick={() => history.push(`/investigation/frm/${notification.txnId}`)}
      active={!notification.isAcknowledged}>
      <Row>
        <Col className="text-danger" md="1">
          {!notification.isAcknowledged && <FontAwesomeIcon icon={faCircle} className="fa-xs" />}
        </Col>
        <Col md="1">{notification?.module}</Col>
        <Col md="2">{notification.customerId}</Col>
        <Col md="5">{notification.message}</Col>
        <Col md="1">
          <em>{moment(notification.timestamp).format('hh:mm A')}</em>
        </Col>
        <Col md="2">
          {notification?.acknowledgedBy} <br />
          <small>
            <em>
              {notification?.acknowledgedOn
                ? moment(notification?.acknowledgedOn).format('YYYY-MM-DD HH:mm a')
                : ''}
            </em>
          </small>
        </Col>
      </Row>
    </ListGroupItem>
  );
}

NotificationListItem.propTypes = {
  notification: PropTypes.object.isRequired
};

export default NotificationListItem;
