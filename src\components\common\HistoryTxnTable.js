import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';

import CardContainer from 'components/common/CardContainer';
import DurationSelector from 'components/common/DurationSelector';
import TxnSearchFilterForm from 'components/common/TxnSearchFilterForm';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';
import { useDateRange } from 'context/DateRangeContext';
import { findValueInTxnDetail } from 'utility/utils';

const HistoryTxnTable = ({
  entityCategory,
  historyData,
  fetchHistoryTransactions,
  channel,
  txnDetails,
  entityId,
  contextKey,
  showSearchFilter = false
}) => {
  const { startDate, endDate } = useDateRange(contextKey);

  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);
  const [txnCategory, setTxnCategory] = useState([{ value: 'entityId', label: 'entityId' }]);

  const historyTxnCategoryList = ['entityId', 'payeeVpa', 'pspId', 'payeeName'];

  // To track the first render for date and pagination changes
  const isFirstRenderForDate = useRef(true);
  const isFirstRenderForPagination = useRef(true);

  useEffect(() => {
    searchTxnHistory(entityCategory, channel, startDate, endDate);
  }, [channel]);

  // Handle pagination change (pageNo, pageRecords)
  useEffect(() => {
    // Skip the API call on the first render for pagination
    if (isFirstRenderForPagination.current) {
      isFirstRenderForPagination.current = false;
      return;
    }
    searchTxnHistory(entityCategory, channel, startDate, endDate);
  }, [pageNo, pageRecords]);

  // Handle date change (startDate, endDate)
  useEffect(() => {
    // Skip the API call on the first render for date changes
    if (isFirstRenderForDate.current) {
      isFirstRenderForDate.current = false;
      return;
    }
    searchHistoryForDate();
  }, [startDate, endDate]);

  useEffect(() => {
    const debouncedSetPageNo = _.debounce(() => {
      setPageNo(0);
    }, 500);

    debouncedSetPageNo();

    return () => {
      debouncedSetPageNo.cancel();
    };
  }, [tableFilters]);

  const tablePageCountProp = useMemo(
    () =>
      _.isEmpty(tableFilters)
        ? {
            pages:
              historyData[channel].count / pageRecords > 1
                ? Math.ceil(historyData[channel].count / pageRecords)
                : 1
          }
        : {},
    [tableFilters, historyData, channel, pageRecords]
  );

  const searchHistoryForDate = () => {
    setPageNo(0);
    searchTxnHistory(entityCategory, channel, startDate, endDate);
  };

  const searchTxnHistory = useCallback(
    (entityCategory, channel, startDate, endDate) => {
      const criterion = [
        {
          condition: 'BETWEEN',
          key: 'txn_timestamp',
          values: [
            `${Moment(startDate).format('YYYY-MM-DD HH:mm:ss')}Z`,
            `${Moment(endDate).format('YYYY-MM-DD HH:mm:ss')}Z`
          ]
        }
      ];

      // Add more criteria based on selected txnCategory
      txnCategory.forEach((category) => {
        const key = category.value; // Get the category value (e.g., 'entityId',, etc.)
        let value;

        // If key is 'entityId' and showSearchFilter is false, use entityId value
        if (key === 'entityId' && !showSearchFilter) value = entityId;
        else value = findValueInTxnDetail(key, txnDetails);

        criterion.push({
          condition: 'EQUAL',
          key: _.snakeCase(key),
          values: [value ?? '']
        });
      });

      const formData = {
        criterion,
        pageNo: pageNo + 1,
        pageSize: pageRecords
      };

      if (startDate && endDate && !_.isEmpty(entityCategory))
        fetchHistoryTransactions(entityCategory, formData, channel);
    },
    [
      txnCategory,
      pageNo,
      pageRecords,
      fetchHistoryTransactions,
      showSearchFilter,
      entityId,
      txnDetails
    ]
  );

  const action = useMemo(() => <DurationSelector contextKey={contextKey} />, []);

  return (
    <CardContainer title="Transaction History" action={action}>
      {showSearchFilter && channel === 'frm' && (
        <TxnSearchFilterForm
          id="historyTxnFilterForm"
          txnCategory={txnCategory}
          setTxnCategory={setTxnCategory}
          categoryList={historyTxnCategoryList}
          onClickSearchBtn={searchHistoryForDate}
          txnDetails={txnDetails}
        />
      )}
      <TransactionTableContainer
        page={pageNo}
        pageSize={pageRecords}
        filtered={tableFilters}
        data={historyData[channel]}
        onPageChange={(page) => setPageNo(page)}
        onPageSizeChange={(pageSize, page) => {
          setPageNo(page);
          setPageRecords(pageSize);
        }}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        {...tablePageCountProp}
      />
    </CardContainer>
  );
};

HistoryTxnTable.propTypes = {
  entityCategory: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  historyData: PropTypes.object,
  fetchHistoryTransactions: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  showSearchFilter: PropTypes.bool,
  entityId: PropTypes.string.isRequired
};

export default HistoryTxnTable;
