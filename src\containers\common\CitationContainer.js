import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as citationActions from 'actions/citationActions';
import Citation from 'components/common/Citation';

const mapStateToProps = (state) => {
  return {
    citations: state.citations,
    userCreds: state.auth.userCreds
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    citationActions: bindActionCreators(citationActions, dispatch)
  };
};

const CitationContainer = connect(mapStateToProps, mapDispatchToProps)(Citation);

export default CitationContainer;
