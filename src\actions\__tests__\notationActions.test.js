import responses from 'mocks/responses';

import * as actions from 'actions/notationActions';
import {
  ON_TOGGLE_LOADER,
  ON_ADD_CASE_NOTATION_SUCCESS,
  ON_FETCH_CASE_NOTATION_LIST_LOADING,
  ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
  ON_FETCH_SELECTED_CASE_LOGS_LOADING,
  ON_FETCH_SELECTED_ENTITY_LOGS_LOADING,
  ON_FETCH_NOTATIONS_LIST_LOADING,
  ON_FETCH_NOTATIONS_LIST_SUCCESS,
  ON_SUCCESS_ALERT,
  ON_UPDATE_NOTATION_SUCCESS,
  ON_DELETE_NOTATION_SUCCESS
} from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('customer actions', () => {
  it('should fetch notations list for caseId', () => {
    const expectedActions = [
      { type: ON_FETCH_CASE_NOTATION_LIST_LOADING },
      {
        type: ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
        response: responses.caseAssignment.notations.case
      }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onFetchCaseNotationList(1, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should post notation for caseId', () => {
    const formData = {
      caseId: '1',
      UserName: 'ojjol',
      notationComment: 'created notation',
      caseRefNo: '123'
    };

    const expectedActions = [
      { type: ON_TOGGLE_LOADER, state: true },
      { type: ON_ADD_CASE_NOTATION_SUCCESS, notation: formData },
      { type: ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({
      notations: {},
      caseAssignment: { selectedCase: responses.caseAssignment.selectedCase }
    });

    return store.dispatch(actions.onAddCaseNotation(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Notations List', () => {
    const expectedActions = [
      { type: ON_FETCH_NOTATIONS_LIST_LOADING },
      { type: ON_FETCH_NOTATIONS_LIST_SUCCESS, response: responses.caseAssignment.notations.master }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onFetchNotationsList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Add Notation To Master', () => {
    const formData = {
      caseId: '1',
      notationUserId: 4,
      notationUserName: 'testname',
      notationTimestamp: '2019-03-27T10:15:30',
      notationComment: 'created notation'
    };
    const expectedActions = [
      { type: ON_TOGGLE_LOADER, state: true },
      {
        type: ON_SUCCESS_ALERT,
        response: { message: 'Notation added successfully!' }
      },
      { type: ON_FETCH_NOTATIONS_LIST_LOADING },
      { type: ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onAddNotationToMaster(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Update Notation From Master', () => {
    const formData = {
      caseId: '1',
      notationUserId: 4,
      notationUserName: 'testname',
      notationTimestamp: '2019-03-27T10:15:30',
      notationComment: 'created notation'
    };
    const expectedActions = [
      { type: ON_TOGGLE_LOADER, state: true },
      {
        type: ON_SUCCESS_ALERT,
        response: { message: 'Notation updated successfully!' }
      },
      {
        type: ON_UPDATE_NOTATION_SUCCESS,
        response: {
          caseId: '1',
          notationUserId: 4,
          notationUserName: 'testname',
          notationTimestamp: '2019-03-27T10:15:30',
          notationComment: 'created notation'
        }
      },
      { type: ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onUpdateNotationFromMaster(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Delete Notation From Master', () => {
    const expectedActions = [
      { type: ON_TOGGLE_LOADER, state: true },
      {
        type: ON_SUCCESS_ALERT,
        response: { message: 'Notation deleted successfuully!' }
      },
      { type: ON_DELETE_NOTATION_SUCCESS, id: 4 },
      { type: ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ notations: {} });

    return store.dispatch(actions.onDeleteNotationFromMaster(4)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
