import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import udsReducer from 'reducers/udsReducer';

describe('UDS Reducer', () => {
  it('should return the intial state', () => {
    expect(udsReducer(undefined, {})).toEqual(initialState.uds);
  });

  it('should handle ON_FETCH_UDS_ENTITY_DETAILS_LOADING', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING,
          entity: 'agent'
        }
      )
    ).toEqual({
      agent: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
          response: responses.demographics.merchant,
          entity: 'agent'
        }
      )
    ).toEqual({
      agent: {
        details: responses.demographics.merchant,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_UDS_ENTITY_DETAILS_FAILURE', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_DETAILS_FAILURE,
          response: { message: 'error message', entity: 'agent' }
        }
      )
    ).toEqual({
      agent: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_UDS_ENTITY_SCORES_LOADING', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_SCORES_LOADING,
          entity: 'merchant'
        }
      )
    ).toEqual({
      merchant: {
        riskScore: {
          details: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UDS_ENTITY_SCORES_SUCCESS', () => {
    const response = [
      {
        scoreName: 'OnBoarding Score',
        scoreValue: 'MEDIUM'
      },
      {
        scoreName: 'Monitoring Score',
        scoreValue: 'High'
      }
    ];
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_SCORES_SUCCESS,
          response,
          entity: 'merchant'
        }
      )
    ).toEqual({
      merchant: {
        riskScore: {
          details: response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UDS_ENTITY_SCORES_FAILURE', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_UDS_ENTITY_SCORES_FAILURE,
          response: { message: 'error message', entity: 'merchant' }
        }
      )
    ).toEqual({
      merchant: {
        riskScore: {
          details: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_FACCTUM_DETAILS_LOADING', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_FACCTUM_DETAILS_LOADING
        }
      )
    ).toEqual({
      facctumData: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FACCTUM_DETAILS_SUCCESS', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_FACCTUM_DETAILS_SUCCESS,
          response: {}
        }
      )
    ).toEqual({
      facctumData: {
        details: {},
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FACCTUM_DETAILS_FAILURE', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_FACCTUM_DETAILS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      facctumData: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS,
          response: {}
        }
      )
    ).toEqual({
      customer: {
        vulnerability: {
          riskScore: {},
          details: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE', () => {
    expect(
      udsReducer(
        {},
        {
          type: types.ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      customer: {
        vulnerability: {
          details: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });
});
