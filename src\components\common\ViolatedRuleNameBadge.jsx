import React, { useEffect } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { Badge } from 'reactstrap';
import { getCombinedRuleNames } from 'constants/functions';

const ViolatedRuleNameBadge = ({
  ruleNames,
  channels,
  fetchRuleNamesList,
  violatedRulesList = '',
  taggedRulesList = ''
}) => {
  useEffect(() => {
    channels.map((channel) => {
      if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
    });
  }, []);

  let combinedRuleNames = getCombinedRuleNames(channels, ruleNames);

  const createRuleString = (ruleList, color) =>
    !_.isEmpty(ruleList)
      ? _.map(ruleList, (rule) => (
          <Badge key={rule.name} className="ms-2 mb-2" color={color}>
            {rule.name}
          </Badge>
        ))
      : null;

  let violatedRules = createRuleString(
    _.filter(combinedRuleNames, (rule) => _.includes(violatedRulesList, rule?.code)),
    'primary'
  );

  let taggedRules = createRuleString(
    _.filter(combinedRuleNames, (rule) => _.includes(taggedRulesList, rule.code)),
    'secondary'
  );

  return (
    <div className="m-2 ms-5">
      {!_.isEmpty(violatedRules) && <span>{violatedRules}</span>}
      {!_.isEmpty(taggedRules) && <span>{taggedRules}</span>}
    </div>
  );
};

ViolatedRuleNameBadge.propTypes = {
  taggedRulesList: PropTypes.string.isRequired,
  violatedRulesList: PropTypes.string.isRequired,
  ruleNames: PropTypes.object.isRequired,
  channels: PropTypes.array.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired
};

export default ViolatedRuleNameBadge;
