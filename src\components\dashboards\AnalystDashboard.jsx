import { capitalize, lowerCase } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState, lazy, useMemo, useRef, Suspense, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { TabPane } from 'reactstrap';

import DurationSelector from 'components/common/DurationSelector';
import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import { isCooperative } from 'constants/publicKey';
import RuleEfficiencyDashboard from 'containers/dashboards/RuleEfficiencyDashboardContainer';
import { useDateRange } from 'context/DateRangeContext';

const BusinessDashboard = lazy(() => import('components/dashboards/BusinessDashboard'));
const InvestigationsReport = lazy(() => import('components/incidents/IncidentsHomePage'));
const OperationsDashboard = lazy(() => import('components/dashboards/OperationsDashboard'));

const AnalystDashboard = ({ role, channels, hasKnowageReport, moduleType, contextKey }) => {
  const history = useHistory();
  const { startDate, endDate } = useDateRange(contextKey);
  const [currentTab, setCurrentTab] = useState([0]);
  const dashboardRef = useRef(null);

  useEffect(() => {
    if (role !== 'investigator' && role !== 'checker') history.goBack();

    const originalTitle = document.title;
    document.title = 'BANKiQ FRC | Dashboard';
    return () => {
      document.title = originalTitle;
    };
  }, [history, role]);

  const period = useMemo(() => ({ startDate, endDate }), [startDate, endDate]);

  const dashboardComponents = useMemo(
    () => ({
      ruleEfficiency: (
        <RuleEfficiencyDashboard dashboardRef={dashboardRef} period={period} history={history} />
      ),
      compliance: <InvestigationsReport period={period} />,
      business: <BusinessDashboard dashboardRef={dashboardRef} period={period} />,
      operations: <OperationsDashboard period={period} />
    }),
    [period, history]
  );

  const tabKeys = useMemo(() => {
    const tabs = [];

    if (role === 'investigator') tabs.push('compliance', 'business');
    else if (role === 'checker')
      if (isCooperative || channels[0] === 'str') tabs.push('ruleEfficiency');
      else if (moduleType === 'acquirer') tabs.push('ruleEfficiency', 'business');
      else tabs.push('ruleEfficiency', 'compliance', 'business');

    if (!isCooperative && hasKnowageReport === 1) tabs.push('operations');

    return tabs;
  }, [role, channels, moduleType, hasKnowageReport]);

  const tabNames = useMemo(() => tabKeys.map((tab) => capitalize(lowerCase(tab))), [tabKeys]);

  const tabPanes = useMemo(
    () =>
      tabKeys.map((key, index) => (
        <TabPane tabId={index} key={key}>
          {currentTab.includes(index) ? (
            <Suspense fallback={<Loader show />}>{dashboardComponents[key]}</Suspense>
          ) : null}
        </TabPane>
      )),
    [tabKeys, currentTab, dashboardComponents]
  );

  const handleTabChange = useCallback(
    (tabId) => {
      if (!currentTab.includes(tabId)) setCurrentTab((prev) => [...prev, tabId]);
    },
    [currentTab]
  );

  return (
    <div className="content-wrapper">
      <div id="dashboard-content" ref={dashboardRef}>
        <Tabs
          tabNames={tabNames}
          action={<DurationSelector contextKey={contextKey} />}
          getCurrentTab={handleTabChange}>
          {tabPanes}
        </Tabs>
      </div>
    </div>
  );
};

AnalystDashboard.propTypes = {
  role: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired,
  contextKey: PropTypes.string.isRequired,
  hasKnowageReport: PropTypes.number.isRequired,
  moduleType: PropTypes.string.isRequired
};

export default AnalystDashboard;
