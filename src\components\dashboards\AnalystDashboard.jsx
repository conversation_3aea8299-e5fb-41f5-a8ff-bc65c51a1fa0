import React, { useEffect, useState, Suspense, lazy, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import Loader from 'components/loader/Loader';
import DurationSelector from 'components/common/DurationSelector';
import { isCooperative } from 'constants/publicKey';
import { useDateRange } from 'context/DateRangeContext';
import RuleEfficiencyDashboard from 'containers/dashboards/RuleEfficiencyDashboardContainer';
const BusinessDashboard = lazy(() => import('components/dashboards/BusinessDashboard'));
const InvestigationsReport = lazy(() => import('components/incidents/IncidentsHomePage'));
const OperationsDashboard = lazy(() => import('components/dashboards/OperationsDashboard'));

function AnalystDashboard({ role, channels, hasKnowageReport, moduleType, contextKey }) {
  const history = useHistory();
  const { startDate, endDate } = useDateRange(contextKey);
  const [currentTab, setCurrentTab] = useState([0]);
  const dashboardRef = useRef(null);

  useEffect(() => {
    if (role !== 'investigator' && role !== 'checker') history.goBack();
    document.title = 'BANKiQ FRC | Dashboard';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const ruleEfficiencyTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          <RuleEfficiencyDashboard
            dashboardRef={dashboardRef}
            period={{ startDate, endDate }}
            history={history}
          />
        </TabPane>
      ),
      [startDate, endDate]
    );

  const complianceTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <InvestigationsReport period={{ startDate, endDate }} />
            </Suspense>
          )}
        </TabPane>
      ),
      [currentTab]
    );

  const businessTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          <Suspense fallback={<Loader show={true} />}>
            <BusinessDashboard dashboardRef={dashboardRef} period={{ startDate, endDate }} />
          </Suspense>
        </TabPane>
      ),
      [startDate, endDate]
    );

  const operationsTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <OperationsDashboard period={{ startDate, endDate }} />
            </Suspense>
          )}
        </TabPane>
      ),
      [startDate, endDate, currentTab]
    );

  const investigatorTabs = ['Compliance', 'Business'];
  !isCooperative && hasKnowageReport === 1 && investigatorTabs.push('Operations');

  let checkerTabs =
    isCooperative || channels[0] === 'str'
      ? ['Rule Efficiency']
      : moduleType === 'acquirer'
      ? ['Rule Efficiency', 'Business']
      : ['Rule Efficiency', 'Compliance', 'Business'];

  !isCooperative && hasKnowageReport === 1 && checkerTabs.push('Operations');

  let tabs =
    role == 'investigator'
      ? [complianceTab(0), businessTab(1)]
      : isCooperative
      ? [ruleEfficiencyTab(0)]
      : moduleType === 'acquirer'
      ? [ruleEfficiencyTab(0), businessTab(1)]
      : [ruleEfficiencyTab(0), complianceTab(1), businessTab(2)];

  hasKnowageReport === 1 &&
    (role === 'investigator' || moduleType === 'acquirer'
      ? tabs.push(operationsTab(2))
      : !isCooperative
      ? tabs.push(operationsTab(3))
      : null);

  const updateCurrentTab = (tabId) =>
    !currentTab.includes(tabId) && setCurrentTab((prev) => [...prev, tabId]);

  return (
    <div className="content-wrapper">
      <div id="dashboard-content" ref={dashboardRef}>
        <Tabs
          tabNames={role == 'investigator' ? investigatorTabs : checkerTabs}
          action={<DurationSelector contextKey={contextKey} />}
          getCurrentTab={updateCurrentTab}>
          {tabs}
        </Tabs>
      </div>
    </div>
  );
}

AnalystDashboard.propTypes = {
  role: PropTypes.string.isRequired,
  channels: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  hasKnowageReport: PropTypes.number.isRequired,
  moduleType: PropTypes.string.isRequired
};

export default AnalystDashboard;
