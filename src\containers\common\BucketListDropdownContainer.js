import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCases, onFetchBuckets } from 'actions/caseReviewActions';
import BucketListDropdown from 'components/common/BucketListDropdown';

const mapStateToProps = (state, ownProps) => {
  return {
    buckets: state.caseAssignment.buckets[ownProps.channel]
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchCases, dispatch),
    fetchBuckets: bindActionCreators(onFetchBuckets, dispatch)
  };
};

const BucketListDropdownContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(BucketListDropdown);

export default BucketListDropdownContainer;
