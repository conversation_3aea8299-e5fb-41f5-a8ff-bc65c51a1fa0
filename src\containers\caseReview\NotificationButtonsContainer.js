import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as communicationActions from 'actions/communicationActions';
import NotificationButtons from 'components/caseReview/NotificationButtons';

const mapStateToProps = (state) => ({
  communicationLogs: state.customerCommunication.logs
});

const mapDispatchToProps = (dispatch) => ({
  communicationActions: bindActionCreators(communicationActions, dispatch)
});

const NotificationButtonsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NotificationButtons);

export default NotificationButtonsContainer;
