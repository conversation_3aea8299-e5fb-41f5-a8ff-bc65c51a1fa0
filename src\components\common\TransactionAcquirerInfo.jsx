import React from 'react';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getAcquirerInfoList = (details) => {
  let acquirerInfoList = [];

  addItemToList(
    details?.masterFields?.acquirerName || details?.identifiers?.acquirerId?.value,
    'Name',
    details?.masterFields?.acquirerName || details?.identifiers?.acquirerId?.value,
    acquirerInfoList,
    details?.identifiers?.acquirerId?.value || null,
    true
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.acqInstCountryCode,
    'Country Code',
    details?.masterFields?.txnAdditionalFields?.acqInstCountryCode,
    acquirerInfoList
  );

  addItemToList(
    details?.identifiers?.initiatorId?.value,
    'Initiator ID',
    details?.identifiers?.initiatorId?.value,
    acquirerInfoList,
    details?.identifiers?.initiatorId
  );

  addItemToList(
    details?.masterFields?.sourceInstitutionName,
    'Source Institution Name',
    details?.masterFields?.sourceInstitutionName,
    acquirerInfoList,
    null,
    true
  );

  addItemToList(
    details?.identifiers?.agentId?.value,
    'Agent ID',
    details?.identifiers?.agentId?.value,
    acquirerInfoList,
    details?.identifiers?.agentId
  );

  addItemToList(
    details?.identifiers?.subAgentId?.value,
    'Sub Agent ID',
    details?.identifiers?.subAgentId?.value,
    acquirerInfoList,
    details?.identifiers?.subAgentId
  );

  addItemToList(
    details?.identifiers?.initiatorMobile?.value,
    'Initiator Mobile',
    details?.identifiers?.initiatorMobile?.value,
    acquirerInfoList,
    details?.identifiers?.initiatorMobile
  );

  addItemToList(
    details?.identifiers?.initiatorType,
    'Initiator Type',
    details?.identifiers?.initiatorType,
    acquirerInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.pspRefNo,
    'PSP Ref No',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.pspRefNo,
    acquirerInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.cardAcceptorIdCode,
    'Card Acceptor ID',
    details?.masterFields?.txnAdditionalFields?.cardAcceptorIdCode,
    acquirerInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.cardAcceptorBusinessCode,
    'Card Acceptor Business Code',
    details?.masterFields?.txnAdditionalFields?.cardAcceptorBusinessCode,
    acquirerInfoList
  );

  addItemToList(
    details?.masterFields?.cardAcceptorNameAndLoc,
    'Card Acceptor',
    details?.masterFields?.cardAcceptorNameAndLoc,
    acquirerInfoList
  );

  addItemToList(
    details?.masterFields?.responseCodeName,
    'Response Code',
    details?.masterFields?.responseCodeName,
    acquirerInfoList,
    null,
    true
  );

  addItemToList(details?.isLien, 'Is Lien', details?.isLien, acquirerInfoList);

  return acquirerInfoList;
};

function TransactionAcquirerInfo({ details }) {
  const acquirerInfoList = getAcquirerInfoList(details);
  if (acquirerInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Acquirer Details</b>
      {renderDataColumnList(acquirerInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionAcquirerInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionAcquirerInfo;
