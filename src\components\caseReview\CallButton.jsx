import React, { useState, useEffect } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { forEach, isEmpty, lowerCase, filter } from 'lodash';
import { Button, PopoverBody, PopoverHeader, Popover } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone } from '@fortawesome/free-solid-svg-icons';

function CallButton({
  channel,
  caseRefNo,
  entityId,
  callCustomer,
  communicationLogs,
  disabled = false
}) {
  const [outgoingCount, setOutgoingCount] = useState(0);
  const [lastCall, setLastCall] = useState('');
  const [displayPopover, setDisplayPopover] = useState(false);
  const callLogs = filter(communicationLogs.data, ['mode', 'OUTGOING']);

  useEffect(() => {
    let outgoingCount = 0;
    let lastCall = '';
    forEach(callLogs, (d) => {
      if (d.media === 'CALL') {
        if (d.mode === 'OUTGOING' && lowerCase(d.commStatus) == 'call placed') outgoingCount++;
        if (lastCall == '' || moment(d.timestamp) > moment(lastCall)) lastCall = d.timestamp;
      }
    });

    if (outgoingCount > 0) setOutgoingCount(outgoingCount);
    if (!isEmpty(lastCall)) setLastCall(moment(lastCall));
  }, [caseRefNo, communicationLogs.data]);

  const lastCallFormat = () => {
    const dayDiff = moment().diff(lastCall, 'days');
    return (
      (dayDiff == 0 ? 'Today' : dayDiff == 1 ? 'Yesterday' : lastCall.format('MMM D')) +
      ', ' +
      lastCall.format('h A')
    );
  };

  const popoverDisplay = () => setDisplayPopover(true);
  const popOverHide = () => setDisplayPopover(false);

  return (
    <>
      <div
        id="CallStatus"
        className="call-container"
        onFocus={popoverDisplay}
        onMouseOver={popoverDisplay}
        onMouseOut={popOverHide}
        onBlur={popOverHide}>
        <Button
          color="success"
          className="btn-circle"
          onClick={() => !disabled && callCustomer(channel, caseRefNo, entityId)}
          disabled={disabled}>
          <FontAwesomeIcon icon={faPhone} />
        </Button>
        {!isEmpty(lastCall) && <span className="last-call-text">Last call {lastCallFormat()}</span>}
      </div>
      {!isEmpty(lastCall) && (
        <Popover
          target="CallStatus"
          trigger="focus"
          isOpen={displayPopover}
          toggle={() => setDisplayPopover(!displayPopover)}>
          <PopoverHeader>Call counts</PopoverHeader>
          <PopoverBody>
            <p>Outgoing calls: {outgoingCount}</p>
            <p>Last call status: {callLogs[0]?.commStatus}</p>
          </PopoverBody>
        </Popover>
      )}
    </>
  );
}

CallButton.propTypes = {
  disabled: PropTypes.bool,
  channel: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  callCustomer: PropTypes.func.isRequired,
  communicationLogs: PropTypes.object.isRequired
};

export default CallButton;
