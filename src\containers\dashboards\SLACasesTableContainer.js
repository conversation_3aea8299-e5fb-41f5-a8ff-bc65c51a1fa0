import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSlaBreachCases } from 'actions/slaDashboardActions';
import SLACasesTable from 'components/dashboards/SLACasesTable';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  slaBreachCases: state.slaDashboard.slaBreachCases
});

const mapDispatchToProps = (dispatch) => ({
  fetchSlaBreachCases: bindActionCreators(onFetchSlaBreachCases, dispatch)
});

const SLACasesTableContainer = connect(mapStateToProps, mapDispatchToProps)(SLACasesTable);

export default SLACasesTableContainer;
