import React, { useEffect } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Card, Button } from 'reactstrap';
import DownloadFile from 'js-file-download';

import TableLoader from 'components/loader/TableLoader';
import CardContainer from 'components/common/CardContainer';

function CPIFRDownloadHistory({ internalId, data, fetchDownloadHistory }) {
  useEffect(() => {
    if (internalId) fetchDownloadHistory({ internalIdList: [internalId] });
  }, [internalId]);

  const headers = [
    {
      Header: 'Internal Id',
      accessor: 'internalId'
    },
    {
      Header: 'Report Version',
      accessor: 'fileVersion',
      Cell: ({ value }) => 'Version ' + value
    },
    { Header: 'Downloaded By', accessor: 'userName' },
    {
      Header: 'Uploaded On',
      accessor: 'uploadDate',
      Cell: ({ value }) => value ? moment(value).format('YYYY-MM-DD') : null
    },
    {
      Header: 'Download Report',
      accessor: 'xmlFileContent',
      filterable: false,
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <Button
          color="primary"
          size="sm"
          onClick={() =>
            DownloadFile(
              row.original.fileContent,
              row.original.internalId + '-v' + row.original.fileVersion + '.txt'
            )
          }>
          Download Report
        </Button>
      )
    }
  ];

  return (
    <CardContainer title="Incident Log">
      {data.loader ? (
        <TableLoader />
      ) : data.error ? (
        <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>
      ) : data.list.length === 0 ? (
        <div className="no-data-div no-data-card-padding">No transactions found</div>
      ) : (
        <Card className="mt-3">
          <ReactTable
            columns={headers}
            data={data.list}
            noDataText="No logs found"
            filterable
            showPaginationTop={true}
            showPaginationBottom={false}
            minRows={5}
            showPageJump={false}
            pageSizeOptions={[5, 10, 20, 30, 40, 50]}
            className={'-highlight -striped'}
          />
        </Card>
      )}
    </CardContainer>
  );
}

CPIFRDownloadHistory.propTypes = {
  internalId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  fetchDownloadHistory: PropTypes.func.isRequired
};

export default CPIFRDownloadHistory;
