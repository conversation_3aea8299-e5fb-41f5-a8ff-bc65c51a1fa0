import DownloadFile from 'js-file-download';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import ReactTable from 'react-table';
import { Card, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';

function CPIFRDownloadHistory({ internalId, data, fetchDownloadHistory }) {
  useEffect(() => {
    if (internalId) fetchDownloadHistory({ internalIdList: [internalId] });
  }, [internalId]);

  const headers = [
    {
      Header: 'Internal Id',
      accessor: 'internalId'
    },
    {
      Header: 'Report Version',
      accessor: 'fileVersion',
      Cell: ({ value }) => `Version ${value}`
    },
    { Header: 'Downloaded By', accessor: 'userName' },
    {
      Header: 'Uploaded On',
      accessor: 'uploadDate',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD') : null)
    },
    {
      Header: 'Download Report',
      accessor: 'xmlFileContent',
      filterable: false,

      Cell: (row) => (
        <Button
          color="primary"
          size="sm"
          onClick={() =>
            DownloadFile(
              row.original.fileContent,
              `${row.original.internalId}-v${row.original.fileVersion}.txt`
            )
          }>
          Download Report
        </Button>
      )
    }
  ];

  const renderContent = () => {
    if (data.loader) return <TableLoader />;

    if (data.error)
      return <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>;

    if (data.list.length === 0)
      return <div className="no-data-div no-data-card-padding">No transactions found</div>;

    return (
      <Card className="mt-3">
        <ReactTable
          columns={headers}
          data={data.list}
          noDataText="No logs found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          minRows={5}
          showPageJump={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          className="-highlight -striped"
        />
      </Card>
    );
  };

  return <CardContainer title="Incident Log">{renderContent()}</CardContainer>;
}

CPIFRDownloadHistory.propTypes = {
  internalId: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  fetchDownloadHistory: PropTypes.func.isRequired
};

export default CPIFRDownloadHistory;
