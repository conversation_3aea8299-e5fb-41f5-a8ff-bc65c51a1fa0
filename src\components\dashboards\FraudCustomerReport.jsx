import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Table } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretRight } from '@fortawesome/free-solid-svg-icons';

import CardContainer from 'components/common/CardContainer';

function FraudCustomerReport({ startDate, endDate, highAlertCustomers, fetchHighAlertCustomer }) {
  useEffect(() => {
    if (highAlertCustomers.data.length < 1) fetchHighAlertCustomer({ startDate, endDate });
  }, []);

  const tableRows =
    highAlertCustomers.data.length > 0 ? (
      highAlertCustomers.data?.map((d) => (
        <tr key={d?.customerId}>
          <td>{d?.customerName}</td>
          <td>{d?.count}</td>
          <td>{d?.amount}</td>
          <td>
            <Link
              to={{
                pathname: '/search',
                state: {
                  entityId: d?.customerId,
                  startDate,
                  endDate
                }
              }}>
              <FontAwesomeIcon size="lg" icon={faCaretRight} />
            </Link>
          </td>
        </tr>
      ))
    ) : (
      <tr>
        <td colSpan={5}>
          <center>No data found</center>
        </td>
      </tr>
    );

  return (
    <CardContainer className="card-height-500" title="Customers w/ most reported frauds">
      <Table>
        <thead>
          <tr>
            <th>Customer Name</th>
            <th>Count</th>
            <th>Amount</th>
            <th> </th>
          </tr>
        </thead>
        <tbody>{tableRows}</tbody>
      </Table>
    </CardContainer>
  );
}

FraudCustomerReport.propTypes = {
  endDate: PropTypes.string.isRequired,
  startDate: PropTypes.string.isRequired,
  highAlertCustomers: PropTypes.object.isRequired,
  fetchHighAlertCustomer: PropTypes.func.isRequired
};

export default FraudCustomerReport;
