import {
  ON_<PERSON>ET<PERSON>_BUSINESS_KPI_LOADING,
  ON_<PERSON>ETCH_BUSINESS_KPI_SUCCESS,
  ON_FETCH_BUSINESS_KPI_FAILURE,
  ON_FETCH_ACTIONS_SHARE_LOADING,
  ON_FETCH_ACTIONS_SHARE_SUCCESS,
  ON_FETCH_ACTIONS_SHARE_FAILURE,
  ON_FETCH_NO_VIOLATION_FRAUD_LOADING,
  ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS,
  ON_FETCH_NO_VIOLATION_FRAUD_FAILURE,
  ON_FETCH_RULE_CATEGORY_TREND_LOADING,
  ON_FETCH_RULE_CATEGORY_TREND_SUCCESS,
  ON_FETCH_RULE_CATEGORY_TREND_FAILURE,
  ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING,
  ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS,
  ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchBusinessKpis(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/business/${channel}/count`,
    data: formData
  });
}

function onFetchBusinessKpisLoading() {
  return { type: ON_FETCH_BUSINESS_KPI_LOADING };
}

function onFetchBusinessKpisSuccess(response) {
  return { type: ON_FETCH_BUSINESS_KPI_SUCCESS, response };
}

function onFetchBusinessKpisFailure(response) {
  return { type: ON_FETCH_BUSINESS_KPI_FAILURE, response };
}

function onFetchBusinessKpis(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchBusinessKpisLoading());
    return fetchBusinessKpis(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchBusinessKpisSuccess(success)),
      (error) => dispatch(onFetchBusinessKpisFailure(error))
    );
  };
}

function fetchActionShare(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/business/${channel}/txn/actions`,
    data: formData
  });
}

function onFetchActionShareLoading() {
  return { type: ON_FETCH_ACTIONS_SHARE_LOADING };
}

function onFetchActionShareSuccess(response) {
  return { type: ON_FETCH_ACTIONS_SHARE_SUCCESS, response };
}

function onFetchActionShareFailure(response) {
  return { type: ON_FETCH_ACTIONS_SHARE_FAILURE, response };
}

function onFetchActionShare(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchActionShareLoading());
    return fetchActionShare(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchActionShareSuccess(success)),
      (error) => dispatch(onFetchActionShareFailure(error))
    );
  };
}

function fetchHighAlertCustomer(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/business/${channel}/customers/reports/frauds`,
    data: formData
  });
}

function onFetchHighAlertCustomerLoading() {
  return { type: ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING };
}

function onFetchHighAlertCustomerSuccess(response) {
  return { type: ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS, response };
}

function onFetchHighAlertCustomerFailure(response) {
  return { type: ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE, response };
}

function onFetchHighAlertCustomer(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchHighAlertCustomerLoading());
    return fetchHighAlertCustomer(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchHighAlertCustomerSuccess(success)),
      (error) => dispatch(onFetchHighAlertCustomerFailure(error))
    );
  };
}

function fetchNoViolationFraud(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/${channel}/fraud/cases`,
    data: formData
  });
}

function onFetchNoViolationFraudLoading() {
  return { type: ON_FETCH_NO_VIOLATION_FRAUD_LOADING };
}

function onFetchNoViolationFraudSuccess(response) {
  return { type: ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS, response };
}

function onFetchNoViolationFraudFailure(response) {
  return { type: ON_FETCH_NO_VIOLATION_FRAUD_FAILURE, response };
}

function onFetchNoViolationFraud(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchNoViolationFraudLoading());
    return fetchNoViolationFraud(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchNoViolationFraudSuccess(success)),
      (error) => dispatch(onFetchNoViolationFraudFailure(error))
    );
  };
}

function fetchRuleCategoryTrend(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/business/${channel}/rule/category/trend`,
    data: formData
  });
}

function onFetchRuleCategoryTrendLoading() {
  return { type: ON_FETCH_RULE_CATEGORY_TREND_LOADING };
}

function onFetchRuleCategoryTrendSuccess(response) {
  return { type: ON_FETCH_RULE_CATEGORY_TREND_SUCCESS, response };
}

function onFetchRuleCategoryTrendFailure(response) {
  return { type: ON_FETCH_RULE_CATEGORY_TREND_FAILURE, response };
}

function onFetchRuleCategoryTrend(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchRuleCategoryTrendLoading());
    return fetchRuleCategoryTrend(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleCategoryTrendSuccess(success)),
      (error) => dispatch(onFetchRuleCategoryTrendFailure(error))
    );
  };
}

export {
  onFetchBusinessKpis,
  onFetchActionShare,
  onFetchHighAlertCustomer,
  onFetchNoViolationFraud,
  onFetchRuleCategoryTrend
};
