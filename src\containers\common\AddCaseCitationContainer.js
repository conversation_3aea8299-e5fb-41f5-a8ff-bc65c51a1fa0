import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onAddCaseCitation } from 'actions/citationActions';
import AddCaseCitation from 'components/common/AddCaseCitation';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  addCaseCitation: bindActionCreators(onAddCaseCitation, dispatch)
});

const AddCaseCitationContainer = connect(mapStateToProps, mapDispatchToProps)(AddCaseCitation);

export default AddCaseCitationContainer;
