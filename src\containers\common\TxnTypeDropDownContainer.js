import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchTxnTypes } from 'actions/caseReviewActions';
import TxnTypeDropDown from 'components/common/TxnTypeDropDown';

const mapStateToProps = (state) => ({
  txnTypeData: state.caseAssignment.txnType
});

const mapDispatchToProps = (dispatch) => ({
  getTxnTypes: bindActionCreators(onFetchTxnTypes, dispatch)
});

const TxnTypeDropDownContainer = connect(mapStateToProps, mapDispatchToProps)(TxnTypeDropDown);

export default TxnTypeDropDownContainer;
