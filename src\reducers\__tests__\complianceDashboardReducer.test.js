import complianceDashboardReducer from 'reducers/complianceDashboardReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import responses from 'mocks/responses';

describe('compliance Dashboard Reducer', () => {
  it('should return the intial state', () => {
    expect(complianceDashboardReducer(undefined, {})).toEqual(initialState.complianceDashboard);
  });

  it('should handle ON_FETCH_FMR_REPORTED_CASE_LOADING', () => {
    expect(
      complianceDashboardReducer(
        {
          fmrReportedCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FMR_REPORTED_CASE_LOADING
        }
      )
    ).toEqual({
      fmrReportedCases: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FMR_REPORTED_CASE_SUCCESS', () => {
    expect(
      complianceDashboardReducer(
        {
          fmrReportedCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FMR_REPORTED_CASE_SUCCESS,
          response: responses.complianceDashboard
        }
      )
    ).toEqual({
      fmrReportedCases: {
        list: responses.complianceDashboard,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FMR_REPORTED_CASE_FAILURE', () => {
    expect(
      complianceDashboardReducer(
        {
          fmrReportedCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FMR_REPORTED_CASE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      fmrReportedCases: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
