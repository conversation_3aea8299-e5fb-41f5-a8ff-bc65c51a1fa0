import notationsReducer from 'reducers/notationsReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';

describe('notations Reducer', () => {
  it('should return the intial state', () => {
    expect(notationsReducer(undefined, {})).toEqual(initialState.notations);
  });

  it('should handle ON_FETCH_NOTATIONS_LIST_LOADING', () => {
    expect(
      notationsReducer(
        {
          master: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NOTATIONS_LIST_LOADING
        }
      )
    ).toEqual({
      master: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_NOTATIONS_LIST_SUCCESS', () => {
    expect(
      notationsReducer(
        {
          master: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FMR_REPORTED_CASE_SUCCESS,
          response: []
        }
      )
    ).toEqual({
      master: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_UPDATE_NOTATION_SUCCESS', () => {
    expect(
      notationsReducer(
        {
          master: {
            list: [
              {
                caseId: '1',
                notationUserId: 4,
                notationUserName: 'ojjol',
                notationTimestamp: '2019-03-27T10:15:30',
                notationComment: 'created notation'
              }
            ],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_UPDATE_NOTATION_SUCCESS,
          response: {
            caseId: '1',
            notationUserId: 4,
            notationUserName: 'testname',
            notationTimestamp: '2019-03-27T10:15:30',
            notationComment: 'created notation'
          }
        }
      )
    ).toEqual({
      master: {
        list: [
          {
            caseId: '1',
            notationUserId: 4,
            notationUserName: 'testname',
            notationTimestamp: '2019-03-27T10:15:30',
            notationComment: 'created notation'
          }
        ],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_DELETE_NOTATION_SUCCESS', () => {
    expect(
      notationsReducer(
        {
          master: {
            list: [
              {
                caseId: '1',
                notationUserId: 4,
                notationUserName: 'ojjol',
                notationTimestamp: '2019-03-27T10:15:30',
                notationComment: 'created notation'
              }
            ],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_DELETE_NOTATION_SUCCESS,
          id: 4
        }
      )
    ).toEqual({
      master: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_NOTATIONS_LIST_FAILURE', () => {
    expect(
      notationsReducer(
        {
          master: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NOTATIONS_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      master: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_CASE_NOTATION_LIST_LOADING', () => {
    expect(
      notationsReducer(
        {
          case: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_NOTATION_LIST_LOADING
        }
      )
    ).toEqual({
      case: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_NOTATION_LIST_SUCCESS', () => {
    expect(
      notationsReducer(
        {
          case: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_NOTATION_LIST_SUCCESS,
          response: []
        }
      )
    ).toEqual({
      case: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_NOTATION_LIST_FAILURE', () => {
    expect(
      notationsReducer(
        {
          case: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CASE_NOTATION_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      case: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_ADD_CASE_NOTATION_SUCCESS', () => {
    expect(
      notationsReducer(
        {
          case: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_ADD_CASE_NOTATION_SUCCESS,
          notation: {
            caseId: '1',
            userName: 'ojjol',
            notationComment: 'created notation',
            caseRefNo: '123'
          }
        }
      )
    ).toEqual({
      case: {
        list: [
          {
            caseId: '1',
            notationUserName: 'ojjol',
            notationTimestamp: new Date(),
            notationComment: 'created notation'
          }
        ],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });
});
