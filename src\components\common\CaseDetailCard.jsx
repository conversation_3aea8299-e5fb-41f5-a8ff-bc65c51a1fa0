import React, { useEffect } from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import { Card, Badge, Button } from 'reactstrap';
import _ from 'lodash';
import { useHistory, withRouter } from 'react-router-dom';

import CardContainer from 'components/common/CardContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';
import { dataColumn } from 'utility/customRenders';
import { isCooperative } from 'constants/publicKey';
import { getScreen } from 'constants/functions';

const CaseDetailCard = ({
  role,
  channel,
  caseDetails,
  action,
  documentStatus,
  closeCaseBuckets,
  fetchCloseCaseBuckets
}) => {
  const history = useHistory();

  useEffect(() => {
    if (channel == 'frm' && _.isEmpty(closeCaseBuckets.list) && !closeCaseBuckets.loader)
      fetchCloseCaseBuckets();
  }, []);

  const bucket =
    channel == 'frm' &&
    caseDetails?.bucketId &&
    closeCaseBuckets?.list.find((d) => d.id == caseDetails.bucketId);

  const getDocVerificationStatus = (docStatus) => {
    switch (true) {
      case docStatus == 0:
        return 'Not Required';
      case docStatus == 1:
        return 'Required';
      case docStatus == 2:
        return 'Verified';
      default:
        return 'Rejected';
    }
  };

  const documentStatusBadge = channel == 'frm' && documentStatus && (
    <Badge
      color={
        documentStatus.details.holdStatus == 'OnHold' ||
        documentStatus.details.holdStatus == 'On Hold'
          ? 'warning'
          : documentStatus.details.holdStatus == 'Declined'
          ? 'danger'
          : 'success'
      }>
      {documentStatus.details.holdStatus}
    </Badge>
  );

  const caseTypeBadge = channel == 'str' &&
    !_.isEmpty(caseDetails?.agencyType) &&
    caseDetails?.agencyType !== 'NA' && <Badge color="secondary">External</Badge>;

  const reversalTxnBadge = caseDetails?.reversalTxn == 1 && <Badge color="primary">Reversal</Badge>;

  return (
    <CardContainer
      action={action}
      title={
        <span>
          <span>{_.upperCase(channel)} Case Detail </span>
          <span className="ms-3">[{caseDetails?.caseRefNo || ''}]</span>
          <span className="ms-3">
            {documentStatusBadge} {caseTypeBadge} {reversalTxnBadge}
          </span>
        </span>
      }>
      <Card>
        <div className="case-details">
          <div className="d-flex justify-content-start flex-wrap flex-grow-1 detail-cols">
            {channel == 'str' && caseDetails?.agencyType && (
              <div>
                <b>Details</b>
                {caseDetails?.agencyType &&
                  dataColumn(
                    'Agency',
                    caseDetails?.agencyType == 'fiu'
                      ? 'FIU-IND'
                      : caseDetails?.agencyType == 'ed'
                      ? 'Enforcement Directorate'
                      : _.capitalize(caseDetails?.agencyType)
                  )}
                {caseDetails?.enquiryDetails &&
                  dataColumn('Enquiry Details', caseDetails.enquiryDetails)}
                {caseDetails?.caseReopenReason &&
                  dataColumn('Reopen Reason', caseDetails.caseReopenReason)}
                {caseDetails?.batchId && dataColumn('Original Batch ID', caseDetails.batchId)}
              </div>
            )}
            <div>
              <b>Status</b>
              {caseDetails.caseId && dataColumn('Case Id', caseDetails.caseId)}
              {caseDetails.currentStatus && dataColumn('Status', caseDetails.currentStatus)}
              {caseDetails.currentStage && dataColumn('Stage', caseDetails.currentStage)}
              {caseDetails.lastActionName && dataColumn('Activity', caseDetails.lastActionName)}
              {caseDetails.lastUpdatedTimestamp &&
                (caseDetails.currentStatus == 'Closed'
                  ? dataColumn(
                      'Closed Date',
                      Moment(caseDetails.lastUpdatedTimestamp).format('YYYY-MM-DD hh:mm A')
                    )
                  : caseDetails.currentStatus == 'New'
                  ? dataColumn(
                      'Creation Date',
                      Moment(caseDetails.lastUpdatedTimestamp).format('YYYY-MM-DD hh:mm A')
                    )
                  : dataColumn(
                      'Assignment Date',
                      Moment(caseDetails.lastUpdatedTimestamp).format('YYYY-MM-DD hh:mm A')
                    ))}
              {caseDetails.lastActivityBy &&
                (caseDetails.currentStatus != 'Closed'
                  ? dataColumn('Assigned By', caseDetails.lastActivityBy)
                  : dataColumn('Closed By', caseDetails.lastActivityBy))}
              {caseDetails.currentlyAssignedTo &&
                caseDetails.currentStatus != 'Closed' &&
                dataColumn('Assigned To', caseDetails.currentlyAssignedTo)}
            </div>
            {channel == 'frm' && caseDetails.investigationVerdict !== 'NA' && (
              <div>
                <b>{isCooperative ? 'Reviewer' : ''} Verdict</b>
                {caseDetails.investigationVerdict &&
                  dataColumn('Verdict', caseDetails.investigationVerdict)}
                {caseDetails.fraudType && dataColumn('Fraud Type', caseDetails.fraudType)}
                {caseDetails.remarks && dataColumn('Explanation', caseDetails.remarks)}
                {caseDetails.bucketId && bucket && dataColumn('Bucket', bucket.name)}
                {((caseDetails.channel == 'frm' &&
                  documentStatus &&
                  !_.isEmpty(documentStatus.details)) ||
                  (caseDetails.channel == 'str' && _.has(caseDetails, 'docStatus'))) &&
                  dataColumn(
                    'Document Verification Status',
                    getDocVerificationStatus(
                      caseDetails.channel == 'frm'
                        ? documentStatus.details.status
                        : caseDetails.docStatus
                    )
                  )}
              </div>
            )}
            {(caseDetails?.childTxns?.length > 0 || caseDetails?.parentTxn) && (
              <div>
                <b>Related Transactions</b>
                <ul>
                  {_.map(caseDetails?.childTxns, (d) => (
                    <li key={d}>{d}</li>
                  ))}
                </ul>
                <span>
                  {caseDetails?.parentTxn && (
                    <Button
                      color="link"
                      onClick={() =>
                        history.push(`${getScreen(role)}/${channel}/${caseDetails?.parentTxn}`)
                      }>
                      {caseDetails?.parentTxn}
                    </Button>
                  )}
                </span>
              </div>
            )}
            {channel == 'str' && caseDetails.makerAction && (
              <div>
                <b>Maker</b>
                {dataColumn('Suggested Action', caseDetails.makerAction)}
                {dataColumn('Notation', caseDetails.remarks)}
                {dataColumn('Date', Moment(caseDetails.makerTimestamp).format('YYYY-MM-DD'))}
                {dataColumn('Time', Moment(caseDetails.makerTimestamp).format('hh:mm A'))}
              </div>
            )}
            {channel == 'str' && caseDetails.checkerAction && (
              <div>
                <b>Checker</b>
                {dataColumn('Response', caseDetails.checkerAction)}
                {dataColumn('Notation', caseDetails.checkerRemark)}
                {dataColumn('Date', Moment(caseDetails.checkerTimestamp).format('YYYY-MM-DD'))}
                {dataColumn('Time', Moment(caseDetails.checkerTimestamp).format('hh:mm A'))}
              </div>
            )}
            {channel == 'str' && caseDetails?.poAction && (
              <div>
                <b>Principal Officer</b>
                {dataColumn('Response', caseDetails.poAction)}
                {dataColumn('Notation', caseDetails.poRemark)}
                {dataColumn('Date', Moment(caseDetails.poTimestamp).format('YYYY-MM-DD'))}
                {dataColumn('Time', Moment(caseDetails.poTimestamp).format('hh:mm A'))}
              </div>
            )}
            {channel == 'frm' && isCooperative && !_.isEmpty(caseDetails?.investigatorVerdict) && (
              <div>
                <b>Investigator Verdict</b>
                {caseDetails?.investigatorVerdict &&
                  dataColumn('Verdict', caseDetails.investigatorVerdict)}
                {caseDetails?.investigatorFraudType &&
                  dataColumn('Fraud Type', caseDetails.investigatorFraudType)}
                {caseDetails?.investigatorRemarks &&
                  dataColumn('Explanation', caseDetails.investigatorRemarks)}
              </div>
            )}
          </div>
          {channel == 'frm' && !_.isEmpty(caseDetails?.taggedRule) && (
            <div>
              <b>Tagged Rules</b>
              <ViolatedRuleNameBadgeContainer taggedRulesList={caseDetails.taggedRule || ''} />
            </div>
          )}
        </div>
      </Card>
    </CardContainer>
  );
};

CaseDetailCard.propTypes = {
  action: PropTypes.object,
  documentStatus: PropTypes.object,
  role: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  caseDetails: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired
};

export default withRouter(CaseDetailCard);
