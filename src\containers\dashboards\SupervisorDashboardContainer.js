import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchShifts } from 'actions/userManagementActions';
import SupervisorDashboard from 'components/dashboards/SupervisorDashboard';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    role: state.auth.userCreds.roles,
    shiftDetails: state.slaDashboard.shiftDetails,
    shiftslist: state.user.shifts,
    userslist: state.user.userslist,
    hasKnowageReport: state.user.configurations.knowageReport,
    moduleType: state.auth.moduleType
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchShifts: bindActionCreators(onFetchShifts, dispatch)
  };
};

const SupervisorDashboardContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)((props) => (
  <DateRangeProvider contextKey="supervisorDashboard">
    <SupervisorDashboard {...props} contextKey="supervisorDashboard" />
  </DateRangeProvider>
));

export default SupervisorDashboardContainer;
