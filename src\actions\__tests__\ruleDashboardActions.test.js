import responses from 'mocks/responses';

import * as actions from 'actions/ruleDashboardActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  auth: { userCreds },
  ruleDashboard: {}
};

describe('rule dashboard actions', () => {
  it('should Fetch Rule Stats', () => {
    const formData = {
      ruleId: 'rule1',
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_STATS_LOADING },
      {
        type: types.ON_FETCH_RULE_STATS_SUCCESS,
        response: responses.ruleDashboard.stats
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleStats(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Rule Efficacy', () => {
    const formData = {
      ruleId: 'rule1',
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_EFFICACY_LOADING },
      {
        type: types.ON_FETCH_RULE_EFFICACY_SUCCESS,
        response: responses.ruleDashboard.efficacy
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleEfficacy(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Rule Efficiency', () => {
    const formData = {
      ruleId: 'rule1',
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_EFFICIENCY_LOADING },
      {
        type: types.ON_FETCH_RULE_EFFICIENCY_SUCCESS,
        response: responses.ruleDashboard.efficiency
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleEfficiency(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Rule Effectiveness', () => {
    const formData = {
      ruleId: 'rule1',
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_EFFECTIVENESS_LOADING },
      {
        type: types.ON_FETCH_RULE_EFFECTIVENESS_SUCCESS,
        response: responses.ruleDashboard.effectiveness
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleEffectiveness(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Rule Behaviour', () => {
    const formData = {
      ruleId: 'rule1',
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_BEHAVIOUR_LOADING },
      {
        type: types.ON_FETCH_RULE_BEHAVIOUR_SUCCESS,
        response: responses.ruleDashboard.behaviour
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleBehaviour(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
