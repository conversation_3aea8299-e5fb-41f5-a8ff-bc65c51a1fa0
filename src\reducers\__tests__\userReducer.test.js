import _ from 'lodash';
import objectAssign from 'object-assign';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import userReducer from 'reducers/userReducer';

const userslist = [
  {
    id: 14,
    userName: '<EMAIL>',
    email: '<EMAIL>',
    firstName: 'bkbkj',
    lastName: 'jbkbkjb',
    channelRoles: ['rpsl:checker'],
    channels: ['rpsl'],
    shiftNames: [],
    roles: ['checker'],
    partnerId: 0,
    partnerName: ''
  },
  {
    id: 13,
    userName: '<EMAIL>',
    email: '<EMAIL>',
    firstName: 'bkjbkj',
    lastName: 'bkjbkjb',
    channelRoles: ['rpsl:maker'],
    channels: ['rpsl'],
    shiftNames: ['afternoon', 'morning'],
    roles: ['maker']
  }
];

const getRoles = (list) =>
  list.map((user) => {
    let roles = user.channelRoles.map((value) => {
      if (value === 'admin') return 'admin';
      const arr = _.split(value, ':');
      return arr[1];
    });

    roles = _.uniq(roles);
    return { ...user, roles };
  });

const updateUserRoles = (userlist, newData) => {
  let roles = newData.channelRoles.map((value) => {
    if (value === 'admin') return 'admin';
    const arr = _.split(value, ':');
    return arr[1];
  });
  roles = _.uniq(roles);
  return userlist.map((user) =>
    user.id === newData.userId
      ? objectAssign({}, user, {
          channelRoles: newData.channelRoles,
          roles
        })
      : user
  );
};

const updateUserShifts = (userslist, userShifts) =>
  userslist.map((user) =>
    user.id === userShifts.userId ? objectAssign({}, user, { shiftNames: userShifts.shifts }) : user
  );

describe('User reducer', () => {
  it('should return the intial state', () => {
    expect(userReducer(undefined, {})).toEqual(initialState.user);
  });

  it('should handle ON_SUCCESSFUL_FETCH_ROLES', () => {
    const response = [
      { id: '1', name: 'checker' },
      { id: '2', name: 'supervisor' },
      { id: '7', name: 'maker' }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_ROLES,
          response
        }
      )
    ).toEqual({
      roles: response
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_SHIFTS', () => {
    const response = [
      { id: 1, shiftName: 'Morning', fromTime: '07:00:00', toTime: '16:00:00' },
      { id: 2, shiftName: 'Afternoon', fromTime: '13:00:00', toTime: '21:00:00' }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_SHIFTS,
          response
        }
      )
    ).toEqual({
      shifts: response
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CHANNELS', () => {
    const response = [{ id: '1', name: 'rpsl' }];
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CHANNELS,
          response
        }
      )
    ).toEqual({
      channels: response
    });
  });

  it('should handle ON_SUCCESSFUL_USER_LIST_FETCH', () => {
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_USER_LIST_FETCH,
          response: userslist
        }
      )
    ).toEqual({
      hasMakerChecker: true,
      userslist: getRoles(userslist)
    });
  });

  it('should handle ON_SUCCESSFUL_UPDATE_USER_ROLES', () => {
    const user = {
      userName: '<EMAIL>',
      userId: 14,
      channelRoles: ['rpsl:checker']
    };
    expect(
      userReducer(
        {
          userslist
        },
        {
          type: types.ON_SUCCESSFUL_UPDATE_USER_ROLES,
          user
        }
      )
    ).toEqual({
      hasMakerChecker: true,
      userslist: updateUserRoles(userslist, user)
    });
  });

  it('should handle ON_SUCCESSFUL_USER_ASSIGN_SHIFTS', () => {
    const userShifts = {
      userId: 14,
      shiftIds: 1,
      shifts: 'Morning'
    };
    expect(
      userReducer(
        {
          userslist
        },
        {
          type: types.ON_SUCCESSFUL_USER_ASSIGN_SHIFTS,
          userShifts
        }
      )
    ).toEqual({
      userslist: updateUserShifts(userslist, userShifts)
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_STAGES', () => {
    const response = [
      { id: 1, roleName: 'Maker' },
      { id: 2, roleName: 'Checker' }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_STAGES,
          response
        }
      )
    ).toEqual({
      stages: response
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST', () => {
    const response = [
      {
        id: 1,
        partnerName: 'SHIVALIK BANK',
        isActive: 1,
        registrationDate: '2023-09-18 04:55:31.347395'
      },
      {
        id: 2,
        partnerName: 'YES BANK',
        isActive: 1,
        registrationDate: '2023-09-18 06:24:06.0'
      },
      {
        id: 3,
        partnerName: 'CO-OP BANK',
        isActive: 1,
        registrationDate: '2023-09-18 06:24:52.0'
      }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST,
          response
        }
      )
    ).toEqual({
      partnerIdList: response
    });
  });

  it('should handle ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS', () => {
    const response = [
      {
        id: 75,
        partnerId: 2,
        userName: 'user1',
        email: '<EMAIL>',
        firstName: 'user',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 6,
        partnerId: 1,
        userName: 'akashm',
        email: '<EMAIL>',
        firstName: 'akash',
        lastName: 'gulbhile',
        channelRoles: ['frm:checker', 'frm:maker'],
        shifts: [
          {
            id: 20,
            shiftName: 'SecondShift',
            fromTime: '14:00:00',
            toTime: '23:00:00'
          },
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          },
          {
            id: 14,
            shiftName: 'fullShift01',
            fromTime: '05:00:00',
            toTime: '23:59:00'
          },
          {
            id: 6,
            shiftName: 'firstShift',
            fromTime: '06:30:00',
            toTime: '14:00:00'
          },
          {
            id: 1,
            shiftName: 'firstShift',
            fromTime: '06:00:00',
            toTime: '12:00:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 63,
        partnerId: 2,
        userName: 'tsuper',
        email: '<EMAIL>',
        firstName: 'tsuper',
        lastName: 'dsuper',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 25,
        partnerId: 1,
        userName: 'testuba',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'uba',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 24,
        partnerId: 2,
        userName: 'testsbi',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'sbi',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 43,
        partnerId: 1,
        userName: 'demo_maker',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'maker',
        channelRoles: ['frm:maker'],
        shifts: [
          {
            id: 14,
            shiftName: 'fullShift01',
            fromTime: '05:00:00',
            toTime: '23:59:00'
          },
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 72,
        partnerId: 1,
        userName: 'user_ub',
        email: '<EMAIL>',
        firstName: 'ubuser',
        lastName: 'test',
        channelRoles: ['frm:checker'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 62,
        partnerId: 2,
        userName: 'tadmin',
        email: '<EMAIL>',
        firstName: 'Tushar',
        lastName: 'Dhane',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 33,
        partnerId: 1,
        userName: 'testusera',
        email: 'testusera',
        firstName: 'test',
        lastName: 'usera',
        channelRoles: ['frm:checker', 'frm:maker'],
        shifts: [
          {
            id: 20,
            shiftName: 'SecondShift',
            fromTime: '14:00:00',
            toTime: '23:00:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 31,
        partnerId: 1,
        userName: 'testubc',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'ubc',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 86,
        partnerId: 1,
        userName: 'strsuper',
        email: '<EMAIL>',
        firstName: 'str',
        lastName: 'super',
        channelRoles: ['str:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 35,
        partnerId: 2,
        userName: 'testsbiuser',
        email: 'testsbiuser',
        firstName: 'test',
        lastName: 'sbiuser',
        channelRoles: ['frm:checker', 'frm:maker'],
        shifts: [
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 91,
        partnerId: 1,
        userName: 'iucauser',
        email: '<EMAIL>',
        firstName: 'iuca',
        lastName: 'user',
        channelRoles: ['frm:checker', 'frm:maker'],
        shifts: [
          {
            id: 27,
            shiftName: 'Dshift',
            fromTime: '04:00:00',
            toTime: '12:30:00'
          },
          {
            id: 30,
            shiftName: 'full day',
            fromTime: '01:00:00',
            toTime: '23:55:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 23,
        partnerId: 1,
        userName: 'testub',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'ub',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 49,
        partnerId: 2,
        userName: 'demo_s_checker',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'schecker',
        channelRoles: ['frm:checker'],
        shifts: [
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 27,
        partnerId: 1,
        userName: 'testsupera',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'supera',
        channelRoles: [
          'frm:maker',
          'frm:supervisor',
          'frm:ext-checker',
          'str:maker',
          'frm:checker',
          'str:ext-checker',
          'str:checker'
        ],
        shifts: [],
        loginStatus: false
      },
      {
        id: 2,
        partnerId: 1,
        userName: 'tushar',
        email: '<EMAIL>',
        firstName: 'tushar',
        lastName: 'dhane',
        channelRoles: ['frm:maker', 'frm:checker', 'frm:supervisor'],
        shifts: [
          {
            id: 1,
            shiftName: 'firstShift',
            fromTime: '06:00:00',
            toTime: '12:00:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 90,
        partnerId: 1,
        userName: 'demouser',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'user',
        channelRoles: ['frm:checker', 'frm:maker', 'str:checker', 'str:maker'],
        shifts: [
          {
            id: 30,
            shiftName: 'full day',
            fromTime: '01:00:00',
            toTime: '23:55:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 77,
        partnerId: 2,
        userName: 'user2',
        email: '<EMAIL>',
        firstName: 'user',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 89,
        partnerId: 1,
        userName: 'struser',
        email: '<EMAIL>',
        firstName: 'str',
        lastName: 'user',
        channelRoles: ['str:maker', 'str:checker'],
        shifts: [
          {
            id: 2,
            shiftName: 'generalShift',
            fromTime: '09:00:00',
            toTime: '17:00:00'
          },
          {
            id: 3,
            shiftName: 'afternoon',
            fromTime: '12:00:00',
            toTime: '21:00:00'
          },
          {
            id: 30,
            shiftName: 'full day',
            fromTime: '01:00:00',
            toTime: '23:55:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 60,
        partnerId: 2,
        userName: 'u_sbi',
        email: '<EMAIL>',
        firstName: 'utest',
        lastName: 'sbi',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 56,
        partnerId: 2,
        userName: 'sbi_supervisor',
        email: '<EMAIL>',
        firstName: 'sbi',
        lastName: 'super',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 74,
        partnerId: 1,
        userName: 'test_admin',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'admin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 80,
        partnerId: 2,
        userName: 'user4',
        email: '<EMAIL>',
        firstName: 'user',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 66,
        partnerId: 1,
        userName: 'ub_user',
        email: '<EMAIL>',
        firstName: 'ubtest',
        lastName: 'user',
        channelRoles: ['frm:maker', 'frm:checker'],
        shifts: [
          {
            id: 27,
            shiftName: 'Dshift',
            fromTime: '04:00:00',
            toTime: '12:30:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 81,
        partnerId: 1,
        userName: 'ub_admin',
        email: '<EMAIL>',
        firstName: 'ubtest',
        lastName: 'admin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 20,
        partnerId: 1,
        userName: 'NA',
        email: '<EMAIL>',
        firstName: 'NA',
        lastName: 'NA',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 3,
        partnerId: 1,
        userName: 'agadmin',
        email: '<EMAIL>',
        firstName: 'akash',
        lastName: 'gulbhile',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 52,
        partnerId: 1,
        userName: 'system',
        email: '<EMAIL>',
        firstName: 'system',
        lastName: 'system',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 46,
        partnerId: 2,
        userName: 'demo_sbi_admin',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'sbiadmin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 21,
        partnerId: 1,
        userName: 'demochecker',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'checker',
        channelRoles: ['frm:checker'],
        shifts: [
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          },
          {
            id: 12,
            shiftName: 'demomorning1',
            fromTime: '11:00:00',
            toTime: '17:00:00'
          },
          {
            id: 9,
            shiftName: 'demo3',
            fromTime: '18:20:00',
            toTime: '20:20:00'
          },
          {
            id: 6,
            shiftName: 'firstShift',
            fromTime: '06:30:00',
            toTime: '14:00:00'
          },
          {
            id: 3,
            shiftName: 'afternoon',
            fromTime: '12:00:00',
            toTime: '21:00:00'
          },
          {
            id: 11,
            shiftName: 'morningdemo1',
            fromTime: '07:00:00',
            toTime: '11:00:00'
          },
          {
            id: 8,
            shiftName: 'demo2',
            fromTime: '15:40:00',
            toTime: '16:00:00'
          },
          {
            id: 5,
            shiftName: 'demoutcnoon',
            fromTime: '12:00:00',
            toTime: '15:00:00'
          },
          {
            id: 2,
            shiftName: 'generalShift',
            fromTime: '09:00:00',
            toTime: '17:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          },
          {
            id: 13,
            shiftName: 'demofirstShift',
            fromTime: '06:00:00',
            toTime: '12:01:00'
          },
          {
            id: 10,
            shiftName: 'mornigdemo',
            fromTime: '05:30:00',
            toTime: '05:50:00'
          },
          {
            id: 7,
            shiftName: 'demo1',
            fromTime: '14:20:00',
            toTime: '16:20:00'
          },
          {
            id: 4,
            shiftName: 'eveni',
            fromTime: '15:00:00',
            toTime: '18:30:00'
          },
          {
            id: 1,
            shiftName: 'firstShift',
            fromTime: '06:00:00',
            toTime: '12:00:00'
          },
          {
            id: 14,
            shiftName: 'fullShift01',
            fromTime: '05:00:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 32,
        partnerId: 1,
        userName: 'testsuperb',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'superb',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 82,
        partnerId: 1,
        userName: 'ub_supervisor',
        email: '<EMAIL>',
        firstName: 'ubtest',
        lastName: 'supervisor',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 85,
        partnerId: 1,
        userName: 'Automation_Checker_DoNotUse',
        email: '<EMAIL>',
        firstName: 'Automation',
        lastName: 'Checker',
        channelRoles: ['frm:checker'],
        shifts: [
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 47,
        partnerId: 2,
        userName: 'demo_s_supervisor',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'ssupervisor',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 79,
        partnerId: 0,
        userName: 'user3',
        email: '<EMAIL>',
        firstName: 'user',
        lastName: 'user',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 64,
        partnerId: 2,
        userName: 'tsuper1',
        email: '<EMAIL>',
        firstName: 'tsuper',
        lastName: 'dsuper',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 92,
        partnerId: 1,
        userName: 'demosuper',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'super',
        channelRoles: ['frm:supervisor', 'str:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 5,
        partnerId: 1,
        userName: 'akash',
        email: '<EMAIL>',
        firstName: 'akash',
        lastName: 'gulbhile',
        channelRoles: ['frm:checker', 'frm:supervisor', 'frm:maker'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 83,
        partnerId: 1,
        userName: 'ub_makar',
        email: 'ub_user',
        firstName: 'ubtest',
        lastName: 'user',
        channelRoles: ['frm:maker'],
        shifts: [
          {
            id: 20,
            shiftName: 'SecondShift',
            fromTime: '14:00:00',
            toTime: '23:00:00'
          },
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 26,
        partnerId: 2,
        userName: 'testsbia',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'sbia',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 45,
        partnerId: 1,
        userName: 'demo_ub_admin',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'ubadmin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 70,
        partnerId: 1,
        userName: 'uu_user',
        email: '<EMAIL>',
        firstName: 'uutest',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      },
      {
        id: 84,
        partnerId: 1,
        userName: 'Automation_DoNotUse',
        email: '<EMAIL>',
        firstName: 'DoNotUse',
        lastName: 'Automation',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 88,
        partnerId: 1,
        userName: 'iucasuper',
        email: '<EMAIL>',
        firstName: 'iuca',
        lastName: 'super',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 44,
        partnerId: 1,
        userName: 'demo_checker',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'checker',
        channelRoles: ['frm:checker'],
        shifts: [
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 15,
            shiftName: 'EndToEnd',
            fromTime: '07:00:00',
            toTime: '23:59:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 55,
        partnerId: 2,
        userName: 'sbi_admin',
        email: '<EMAIL>',
        firstName: 'sbi',
        lastName: 'admin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 48,
        partnerId: 2,
        userName: 'demo_s_maker',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'smaker',
        channelRoles: ['frm:maker'],
        shifts: [
          {
            id: 17,
            shiftName: 'NightShift',
            fromTime: '21:00:00',
            toTime: '09:00:00'
          },
          {
            id: 16,
            shiftName: 'ETEShift',
            fromTime: '00:01:00',
            toTime: '23:59:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 65,
        partnerId: 1,
        userName: 'ub_super',
        email: '<EMAIL>',
        firstName: 'ubtest',
        lastName: 'super',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 78,
        partnerId: 1,
        userName: 'admin1',
        email: '<EMAIL>',
        firstName: 'admin',
        lastName: 'admin',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 59,
        partnerId: 2,
        userName: 's_sbi',
        email: '<EMAIL>',
        firstName: 'stest',
        lastName: 'sbi',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 57,
        partnerId: 2,
        userName: 'sbi_user',
        email: '<EMAIL>',
        firstName: 'sbi',
        lastName: 'user',
        channelRoles: ['frm:maker', 'frm:checker'],
        shifts: [
          {
            id: 18,
            shiftName: 'test',
            fromTime: '10:00:00',
            toTime: '22:00:00'
          }
        ],
        loginStatus: false
      },
      {
        id: 58,
        partnerId: 2,
        userName: 'super__sbi',
        email: '<EMAIL>',
        firstName: 'super',
        lastName: 'sbi',
        channelRoles: ['admin'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 34,
        partnerId: 2,
        userName: 'testsbisuper',
        email: '<EMAIL>',
        firstName: 'test',
        lastName: 'sbisuper',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 87,
        partnerId: 1,
        userName: 'allsuper',
        email: '<EMAIL>',
        firstName: 'all',
        lastName: 'super',
        channelRoles: ['str:supervisor', 'frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 42,
        partnerId: 1,
        userName: 'demo_supervisor',
        email: '<EMAIL>',
        firstName: 'demo',
        lastName: 'supervisor',
        channelRoles: ['frm:supervisor'],
        shifts: [],
        loginStatus: false
      },
      {
        id: 68,
        partnerId: 1,
        userName: 'ubnew_user',
        email: '<EMAIL>',
        firstName: 'ubnew',
        lastName: 'user',
        channelRoles: [],
        shifts: [],
        loginStatus: false
      }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS,
          response
        }
      )
    ).toEqual({
      externalCheckers: {
        list: response,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE', () => {
    expect(
      userReducer(
        {},
        {
          type: types.ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      externalCheckers: {
        list: [],
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING', () => {
    expect(
      userReducer(
        {},
        {
          type: types.ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING
        }
      )
    ).toEqual({
      attributesList: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS', () => {
    const response = [
      {
        key: 'txnAmount',
        value: 'txnAmount',
        dataType: 'numeric'
      },
      {
        key: 'payerId',
        value: 'payerId',
        dataType: 'text'
      },
      {
        key: 'payeeId',
        value: 'payeeId',
        dataType: 'text'
      },
      {
        key: 'txnType',
        value: 'txnType',
        dataType: 'text'
      },
      {
        key: 'xchannelId',
        value: 'xchannelId',
        dataType: 'text'
      }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS,
          response
        }
      )
    ).toEqual({
      attributesList: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE', () => {
    expect(
      userReducer(
        {},
        {
          type: types.ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      attributesList: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS', () => {
    const formData = {
      userId: '123'
    };
    expect(
      userReducer(
        {},
        {
          type: types.ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS,
          userShifts: formData
        }
      )
    ).toEqual({
      userslist: []
    });
  });

  it('should handle ON_USER_TOGGLE_AUTO_CASE_SUCCESS', () => {
    const response = [
      {
        key: 'txnAmount',
        value: 'txnAmount',
        dataType: 'numeric'
      },
      {
        key: 'payerId',
        value: 'payerId',
        dataType: 'text'
      },
      {
        key: 'payeeId',
        value: 'payeeId',
        dataType: 'text'
      },
      {
        key: 'txnType',
        value: 'txnType',
        dataType: 'text'
      },
      {
        key: 'xchannelId',
        value: 'xchannelId',
        dataType: 'text'
      }
    ];
    expect(
      userReducer(
        {},
        {
          type: types.ON_USER_TOGGLE_AUTO_CASE_SUCCESS,
          response
        }
      )
    ).toEqual({
      userslist: []
    });
  });
});
