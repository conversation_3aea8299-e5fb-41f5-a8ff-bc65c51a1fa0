import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onShowFailureAlert } from 'actions/alertActions';
import { onSelectCase } from 'actions/caseReviewActions';
import * as investigationActions from 'actions/investigationActions';
import { onFetchRulesWithConditions } from 'actions/ruleConfiguratorActions';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import Indepth from 'components/investigation/Indepth';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  role: state.auth.userCreds.roles,
  investigationData: state.investigation,
  txnDetails: state.transactionDetails,
  selectedCase: state.caseAssignment.selectedCase,
  documentStatus: state.releaseFunds.documentStatus
});

const mapDispatchToProps = (dispatch) => ({
  transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
  investigationActions: bindActionCreators(investigationActions, dispatch),
  showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
  selectCase: bindActionCreators(onSelectCase, dispatch),
  fetchRulesWithConditions: bindActionCreators(onFetchRulesWithConditions, dispatch)
});

const IndepthContainer = connect(mapStateToProps, mapDispatchToProps)(Indepth);

export default IndepthContainer;
