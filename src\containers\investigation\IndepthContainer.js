import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import * as investigationActions from 'actions/investigationActions';
import { onSelectCase } from 'actions/caseReviewActions';
import { onShowFailureAlert } from 'actions/alertActions';
import { onFetchRulesWithConditions } from 'actions/ruleConfiguratorActions';
import Indepth from 'components/investigation/Indepth';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    role: state.auth.userCreds.roles,
    investigationData: state.investigation,
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase,
    documentStatus: state.releaseFunds.documentStatus
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
    investigationActions: bindActionCreators(investigationActions, dispatch),
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    selectCase: bindActionCreators(onSelectCase, dispatch),
    fetchRulesWithConditions: bindActionCreators(onFetchRulesWithConditions, dispatch)
  };
};

const IndepthContainer = connect(mapStateToProps, mapDispatchToProps)(Indepth);

export default IndepthContainer;
