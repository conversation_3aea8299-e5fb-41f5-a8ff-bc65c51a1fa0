import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRuleEfficacy } from 'actions/ruleDashboardActions';
import RuleEfficacyGraph from 'components/dashboards/RuleEfficacyGraph';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  ruleEfficacy: state.ruleDashboard.efficacy
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleEfficacy: bindActionCreators(onFetchRuleEfficacy, dispatch)
});

const RuleEfficacyGraphContainer = connect(mapStateToProps, mapDispatchToProps)(RuleEfficacyGraph);

export default RuleEfficacyGraphContainer;
