import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleEfficacy } from 'actions/ruleDashboardActions';
import RuleEfficacyGraph from 'components/dashboards/RuleEfficacyGraph';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleEfficacy: state.ruleDashboard.efficacy
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleEfficacy: bindActionCreators(onFetchRuleEfficacy, dispatch)
  };
};

const RuleEfficacyGraphContainer = connect(mapStateToProps, mapDispatchToProps)(RuleEfficacyGraph);

export default RuleEfficacyGraphContainer;
