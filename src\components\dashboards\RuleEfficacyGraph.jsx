import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

import GraphContainer from 'components/common/GraphContainer';
import HelpIcon from 'components/common/HelpIcon';

function RuleEfficacyGraph({ theme, rule, period, ruleEfficacy, fetchRuleEfficacy }) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleEfficacy({
        ruleId: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const config = {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        startAngle: 200,
        endAngle: -20,
        min: 0,
        max: 100,
        splitNumber: 5,
        progress: {
          show: true,
          width: 30
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 30
          }
        },
        axisTick: {
          show: false,
          distance: -45,
          splitNumber: 5,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        splitLine: {
          show: false,
          distance: -52,
          length: 14,
          lineStyle: {
            width: 3,
            color: '#999'
          }
        },
        axisLabel: {
          distance: 10,
          color: '#999'
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          fontWeight: 'bolder',
          formatter: '{value}',
          color: 'inherit'
        },
        data: [
          {
            value: ruleEfficacy.data ?? 0
          }
        ]
      }
    ]
  };

  const subtitle = (
    <HelpIcon
      size="lg"
      id="efficacyHelp"
      key="efficacyHelp"
      text={<small>Score from the sandbox testing result. Requires sandbox testing on rule.</small>}
    />
  );

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title="Rule Efficacy"
      subtitle={subtitle}
      noData={false}
      loader={ruleEfficacy.loader}
      error={{ flag: ruleEfficacy.error, errorMessage: ruleEfficacy.errorMessage }}
    />
  );
}

RuleEfficacyGraph.propTypes = {
  rule: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  ruleEfficacy: PropTypes.object.isRequired,
  fetchRuleEfficacy: PropTypes.func.isRequired
};

export default RuleEfficacyGraph;
