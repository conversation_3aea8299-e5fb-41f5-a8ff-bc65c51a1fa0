import PropTypes from 'prop-types';
import React from 'react';
import { Button } from 'reactstrap';

const UnfreezeAccountButton = ({
  custAccNo,
  entityId,
  txnId,
  channel,
  customerInfo,
  unfreezeAccountDebit,
  hasDebitFreeze = 0
}) => {
  if (hasDebitFreeze === 0) return null;

  if (!customerInfo.account?.find((d) => d.accountNo.value === custAccNo)?.isDebitFreeze)
    return null;

  return (
    <Button
      outline
      size="sm"
      color="warning"
      className="ms-1"
      onClick={() =>
        unfreezeAccountDebit({
          entityId,
          txnId,
          channel,
          custAcNo: custAccNo
        })
      }>
      Debit Unfreeze
    </Button>
  );
};

UnfreezeAccountButton.propTypes = {
  hasDebitFreeze: PropTypes.number,
  txnId: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  custAccNo: PropTypes.string.isRequired,
  customerInfo: PropTypes.object.isRequired,
  unfreezeAccountDebit: PropTypes.func.isRequired
};

export default UnfreezeAccountButton;
