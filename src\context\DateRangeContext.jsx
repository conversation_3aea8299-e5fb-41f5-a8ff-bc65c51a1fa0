import React, { createContext, useState, useContext, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';

const DateRangeContext = createContext(undefined);

export const DateRangeProvider = ({ children, contextKey }) => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [duration, setDuration] = useState(1);

  const updateDateRange = useCallback(
    (start, end) => {
      setStartDate(start);
      setEndDate(end);
    },
    [setStartDate, setEndDate]
  );

  const updateDuration = useCallback(
    (newDuration) => {
      setDuration(newDuration);
    },
    [setDuration]
  );

  const value = useMemo(
    () => ({
      startDate,
      endDate,
      duration,
      updateDateRange,
      updateDuration
    }),
    [startDate, endDate, duration, updateDateRange, updateDuration]
  );

  return (
    <DateRangeContext.Provider value={{ contextKey, ...value }}>
      {children}
    </DateRangeContext.Provider>
  );
};

export const useDateRange = (contextKey) => {
  const context = useContext(DateRangeContext);
  if (!context) {
    throw new Error('useDateRange must be used within a DateRangeProvider');
  }

  // Return only the values for the matching key
  if (contextKey && context.contextKey !== contextKey) {
    throw new Error('Context key mismatch');
  }

  return context;
};

DateRangeProvider.propTypes = {
  children: PropTypes.node.isRequired,
  contextKey: PropTypes.string // Unique key for this context instance
};
