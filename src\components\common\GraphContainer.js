import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pi<PERSON><PERSON><PERSON>ar<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lle<PERSON><PERSON><PERSON>
} from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  DatasetComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent,
  ToolboxComponent,
  MarkLineComponent,
  GeoComponent
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import ReactEcharts from 'echarts-for-react/lib/core';
import PropTypes from 'prop-types';
import React from 'react';
import { Card, CardBody, CardTitle } from 'reactstrap';

import darkTheme from 'constants/chartDarkTheme';
import lightTheme from 'constants/chartLightTheme';

echarts.registerTheme('chart-theme-light', lightTheme);
echarts.registerTheme('chart-theme-dark', darkTheme);

const Graph<PERSON>ontainer = ({
  title,
  subtitle,
  config,
  theme,
  noData,
  loader,
  error,
  events,
  graphForm,
  className
}) => {
  const chartTheme = {
    light: 'chart-theme-light',
    dark: 'chart-theme-dark'
  };

  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DataZoomComponent,
    DatasetComponent,
    LegendComponent,
    BarChart,
    ScatterChart,
    CanvasRenderer,
    EffectScatterChart,
    MarkPointComponent,
    MarkLineComponent,
    PictorialBarChart,
    PieChart,
    ToolboxComponent,
    FunnelChart,
    MapChart,
    GeoComponent,
    GaugeChart,
    ParallelChart
  ]);

  let displayGraph;
  if (loader)
    displayGraph = (
      <div className="graph-loader">
        <span />
      </div>
    );
  else if (error.flag) displayGraph = <div className="no-data-div">{error.errorMessage}</div>;
  else if (noData) displayGraph = <div className="no-data-div">No data to display</div>;
  else
    displayGraph = (
      <ReactEcharts
        echarts={echarts}
        option={config}
        notMerge={true}
        lazyUpdate={true}
        theme={chartTheme[theme]}
        onEvents={events}
        className={className}
      />
    );

  return (
    <div>
      <Card className={`d-flex card-main ${className}`}>
        <CardBody className="p-3 card-container">
          <CardTitle className="d-flex align-items-center justify-content-between ">
            {title}
            <span className="graph-subtitle">{subtitle}</span>
          </CardTitle>
          <Card>
            {graphForm !== undefined && graphForm}
            {displayGraph}
          </Card>
        </CardBody>
      </Card>
    </div>
  );
};

GraphContainer.defaultProps = {
  noData: true,
  loader: false,
  error: {
    flag: false,
    errorMessage: ''
  },
  subtitle: '',
  events: {}
};

GraphContainer.propTypes = {
  noData: PropTypes.bool,
  loader: PropTypes.bool,
  error: PropTypes.object,
  events: PropTypes.object,
  className: PropTypes.string,
  subtitle: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
    PropTypes.element,
    PropTypes.node
  ]),
  config: PropTypes.object.isRequired,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
  theme: PropTypes.string.isRequired,
  graphForm: PropTypes.element
};

export default GraphContainer;
