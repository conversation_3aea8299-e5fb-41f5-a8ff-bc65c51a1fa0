import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onCloseCase, onRequestSupervisorApproval } from 'actions/caseReviewActions';
import { onFetchSTRReportDetails } from 'actions/strReportActions';
import VerdictModalSTR from 'components/common/VerdictModalSTR';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  userList: state.user.userslist,
  verdictModal: state.toggle.verdictModal.str,
  citations: state.citations,
  gosDetails: state.strReport.details
});

const mapDispatchToProps = (dispatch) => ({
  closeCase: bindActionCreators(onCloseCase, dispatch),
  fileStr: bindActionCreators(onRequestSupervisorApproval, dispatch),
  getGosDetails: bindActionCreators(onFetchSTRReportDetails, dispatch)
});

const VerdictModalSTRContainer = connect(mapStateToProps, mapDispatchToProps)(VerdictModalSTR);

export default VerdictModalSTRContainer;
