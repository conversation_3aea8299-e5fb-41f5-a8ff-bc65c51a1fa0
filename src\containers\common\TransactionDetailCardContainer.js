import { connect } from 'react-redux';
import TransactionDetailCard from 'components/common/TransactionDetailCard';

const mapStateToProps = (state) => {
  return {
    data: state.transactionDetails,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const TransactionDetailCardContainer = connect(mapStateToProps, null)(TransactionDetailCard);

export default TransactionDetailCardContainer;
