import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faPencil } from '@fortawesome/free-solid-svg-icons';

import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import ConfirmAlert from 'components/common/ConfirmAlert';

function ShiftsTable({
  theme,
  shiftslist,
  showShiftModal,
  toggleShiftModal,
  fetchShifts,
  addShift,
  deleteShift,
  updateShift
}) {
  const [shiftName, setShiftName] = useState('');
  const [fromTime, setFromTime] = useState('');
  const [toTime, setToTime] = useState('');
  const [deleteMode, setDeleteMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedShift, setSelectedShift] = useState({});
  const [shiftId, setShiftId] = useState(0);

  function cancelDelete() {
    setDeleteMode(false);
    setSelectedShift({});
  }

  function confirmDelete() {
    deleteShift(selectedShift);
    setSelectedShift({});
    setDeleteMode(false);
  }

  const shiftTableHeader = [
    {
      Header: 'Actions',
      minWidth: 80,
      filterable: false,
      sortable: false,
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="Edit"
            className="me-2"
            onClick={() => {
              handleToggleShiftModal('edit', row.original);
              setEditMode(true);
            }}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            onClick={() => {
              setSelectedShift(row.original);
              setDeleteMode(true);
            }}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    { Header: 'Shift Name', accessor: 'shiftName' },
    { Header: 'Start Time', accessor: 'fromTime' },
    { Header: 'End Time', accessor: 'toTime' }
  ];

  useEffect(() => {
    fetchShifts();
  }, []);

  function resetForm() {
    setShiftName('');
    setFromTime('');
    setToTime('');
    setShiftId(0);
  }

  const setUpdatedValue = (data) => {
    setShiftName(data.shiftName);
    setFromTime(data.fromTime);
    setToTime(data.toTime);
    setShiftId(data.id);
  };

  function addShiftSubmit(e) {
    e.preventDefault();
    let formData = { shiftName, fromTime, toTime };
    editMode ? updateShift({ shiftName, fromTime, toTime, id: shiftId }) : addShift(formData);
    resetForm();
  }

  function handleToggleShiftModal(type, data) {
    type == 'edit' ? setUpdatedValue(data) : resetForm();
    toggleShiftModal();
    setEditMode(false);
  }

  let shiftAddAction = (
    <Button size="sm" color="primary" title="Edit" onClick={() => handleToggleShiftModal('add')}>
      Add shift
    </Button>
  );

  return (
    <CardContainer title={'Shifts'} action={shiftAddAction}>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        data={shiftslist}
        columns={shiftTableHeader}
        noDataText="No shift found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        defaultPageSize={5}
        minRows={3}
        className={'-highlight  -striped'}
      />

      <ModalContainer
        theme={theme}
        header={`${editMode ? 'Edit ' : 'Add '} shift`}
        isOpen={showShiftModal}
        size="md"
        toggle={() => handleToggleShiftModal('close')}>
        <form onSubmit={addShiftSubmit}>
          <FormGroup>
            <Label>Name</Label>
            <Input
              type="text"
              id="shiftName"
              name="shiftName"
              value={shiftName}
              onChange={(e) => setShiftName(e.target.value)}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label>Start Time</Label>
            <Input
              type="time"
              id="fromTime"
              name="fromTime"
              value={fromTime}
              onChange={(e) => setFromTime(e.target.value)}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label>End Time</Label>
            <Input
              type="time"
              id="toTime"
              name="toTime"
              value={toTime}
              onChange={(e) => setToTime(e.target.value)}
              required
            />
          </FormGroup>
          <Button size="sm" color="primary" className="d-flex ms-auto">
            Submit
          </Button>
        </form>
      </ModalContainer>
      <ConfirmAlert
        theme={theme}
        confirmAlertModal={deleteMode}
        toggleConfirmAlertModal={cancelDelete}
        confirmationAction={confirmDelete}
        confirmAlertTitle={`Are you sure you want to delete ${selectedShift.shiftName} shift ?`}
      />
    </CardContainer>
  );
}

ShiftsTable.propTypes = {
  theme: PropTypes.string.isRequired,
  shiftslist: PropTypes.array.isRequired,
  showShiftModal: PropTypes.bool.isRequired,
  toggleShiftModal: PropTypes.func.isRequired,
  fetchShifts: PropTypes.func.isRequired,
  addShift: PropTypes.func.isRequired,
  deleteShift: PropTypes.func.isRequired,
  updateShift: PropTypes.func.isRequired
};

export default ShiftsTable;
