import { faClock, faPaperPlane } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  ListGroup,
  InputGroup,
  ListGroupItem,
  ListGroupItemText,
  Card,
  CardBody,
  CardTitle,
  CardHeader,
  CardFooter,
  FormFeedback
} from 'reactstrap';

const Citation = ({ citations, citationActions, userCreds, caseRefNo, txnId, disable = true }) => {
  const [submittedForm, setSubmittedForm] = useState(null);
  const { userId, userName, roles } = userCreds;

  useEffect(() => {
    caseRefNo && citationActions.onFetchCaseCitations(caseRefNo);
  }, [caseRefNo, citationActions]);

  const addComment = (e, index) => {
    e.preventDefault();
    setSubmittedForm(index);
    const form = e.target;
    const { comment, citationId } = form;
    const formData = {
      txnId,
      caseRefNo,
      userId: userId.toString(),
      userName,
      userRole: roles,
      response: comment.value,
      citationId: +citationId.value
    };
    citationActions.onAddCitationComment(formData);
    form.reset();
  };

  // Handle loading state
  if (citations.loader) return <div className="no-data-div">Fetching citations...</div>;

  // Handle error state
  if (citations.error) return <div className="no-data-div">{citations.errorMessage}</div>;

  // Render citations list
  return _.map(citations.data.list, (citation, i) => (
    <Card key={citation.citationId} className="citation-card">
      <CardHeader>
        <CardTitle>{citation.citationName}</CardTitle>
      </CardHeader>
      <CardBody className="comment-list-container">
        <ListGroup flush className="citation-comments no-padding">
          {citation.citationResponses
            .filter((comment) => !_.isEmpty(comment))
            .map((commentObj) => {
              const comment = commentObj?.ruleCitationResponse;
              const userRole = _.split(comment.userRole, ':');
              return (
                <ListGroupItem key={citation.citationId + comment.creationDate}>
                  <ListGroupItemText className="d-flex justify-content-between flex-wrap">
                    <b>
                      {comment.userName} ({_.capitalize(userRole)})
                    </b>
                    <span className="comment-timestamp">
                      <FontAwesomeIcon icon={faClock} />{' '}
                      {Moment(comment.creationDate).format('YYYY-MM-DD hh:mm A')}
                    </span>
                  </ListGroupItemText>
                  <ListGroupItemText>{comment.response}</ListGroupItemText>
                </ListGroupItem>
              );
            })}
        </ListGroup>
      </CardBody>
      <CardFooter>
        <Form onSubmit={(e) => addComment(e, i)} className="comment-form">
          <input type="hidden" name="citationId" defaultValue={citation.citationId} />
          <InputGroup className="notation-action ">
            <Input
              type="text"
              name="comment"
              placeholder="Reply..."
              required
              disabled={citations.data.loader || disable}
              {...(submittedForm === i && citations.data.error && { invalid: true })}
            />
            <Button outline size="sm" color="success" disabled={citations.data.loader || disable}>
              <FontAwesomeIcon icon={faPaperPlane} />
            </Button>
          </InputGroup>
          <FormFeedback>
            {submittedForm === i && citations.data.error && citations.data.errorMessage}
          </FormFeedback>
        </Form>
      </CardFooter>
    </Card>
  ));
};

Citation.propTypes = {
  disable: PropTypes.bool,
  txnId: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  userCreds: PropTypes.object.isRequired,
  citations: PropTypes.object.isRequired,
  citationActions: PropTypes.object.isRequired
};

export default Citation;
