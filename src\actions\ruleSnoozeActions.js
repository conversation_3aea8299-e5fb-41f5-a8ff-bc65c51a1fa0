import { onShowF<PERSON>ure<PERSON>lert, onShowSuccessAlert } from 'actions/alertActions';
import {
  ON_FETCH_SNOOZE_RULE_LIST_LOADING,
  ON_FETCH_SNOOZE_RULE_LIST_SUCCESS,
  ON_FETCH_SNOOZE_RULE_LIST_FAILURE,
  ON_FETCH_SNOOZE_ATTRIBUTES_LOADING,
  ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS,
  ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchSnoozeAttributesList(channel) {
  return client({ url: `${channel}/ruleengine/fetch/snooze/attributes` });
}

function onFetchSnoozeAttributesListLoading() {
  return { type: ON_FETCH_SNOOZE_ATTRIBUTES_LOADING };
}

function onFetchSnoozeAttributesListSuc<PERSON>(response) {
  return {
    type: ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS,
    response
  };
}

function onFetchSnoozeAttributesListFailure(response) {
  return {
    type: ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE,
    response
  };
}

function onFetchSnoozeAttributesList(channel) {
  return function (dispatch) {
    dispatch(onFetchSnoozeAttributesListLoading());
    return fetchSnoozeAttributesList(channel).then(
      (success) => dispatch(onFetchSnoozeAttributesListSuccess(success)),
      (error) => dispatch(onFetchSnoozeAttributesListFailure(error))
    );
  };
}

function snoozeRule(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/rule/recommend/snooze`,
    data: formData,
    badRequestMessage: 'Rule snooze requested failed!'
  });
}

function onSnoozeRule(formData, channel) {
  return function (dispatch) {
    return snoozeRule(formData, channel).then(
      () => dispatch(onShowSuccessAlert({ message: 'Rule snooze requested successfully!' })),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchSnoozeRulesList(channel) {
  return client({ url: `${channel}/ruleengine/rules/all/snooze` });
}

function onFetchSnoozeRulesListLoading() {
  return { type: ON_FETCH_SNOOZE_RULE_LIST_LOADING };
}

function onFetchSnoozeRulesListSuccess(channel, response) {
  return {
    type: ON_FETCH_SNOOZE_RULE_LIST_SUCCESS,
    channel,
    response
  };
}

function onFetchSnoozeRulesListFailure(response) {
  return {
    type: ON_FETCH_SNOOZE_RULE_LIST_FAILURE,
    response
  };
}

function onFetchSnoozeRulesList(channel) {
  return function (dispatch) {
    dispatch(onFetchSnoozeRulesListLoading());
    return fetchSnoozeRulesList(channel).then(
      (success) => dispatch(onFetchSnoozeRulesListSuccess(channel, success)),
      (error) => dispatch(onFetchSnoozeRulesListFailure(error))
    );
  };
}

function snoozeRuleResponse(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/rule/process/snooze`,
    data: formData,
    badRequestMessage: `Rule snooze ${formData.isApproved === 1 ? 'approval' : 'rejection'} failed!`
  });
}

function onSnoozeRuleResponse(formData, channel) {
  return function (dispatch) {
    return snoozeRuleResponse(formData, channel).then(
      () => {
        dispatch(
          onShowSuccessAlert({
            message: `Rule snooze ${
              formData.isApproved === 1 ? 'approved' : 'rejected'
            } successfully!`
          })
        );
        dispatch(onFetchSnoozeRulesList(channel));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function unsnoozeRule(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/rule/process/unsnooze`,
    data: formData,
    badRequestMessage: `Rule unsnooze failed!`
  });
}

function onUnsnoozeRule(formData, channel) {
  return function (dispatch) {
    return unsnoozeRule(formData, channel).then(
      () => {
        dispatch(onShowSuccessAlert({ message: `Rule unsnoozed successfully!` }));
        dispatch(onFetchSnoozeRulesList(channel));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export {
  onFetchSnoozeAttributesList,
  onSnoozeRule,
  onFetchSnoozeRulesList,
  onSnoozeRuleResponse,
  onUnsnoozeRule
};
