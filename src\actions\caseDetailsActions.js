import {
  ON_FETCH_CASE_DETAIL_LOADING,
  ON_FETCH_CASE_DETAIL_FAILURE,
  ON_SUCCESSFUL_FETCH_CASE_DETAIL
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleRequestForInformationModal, onToggleLoader } from 'actions/toggleActions';
import { onFetchBuckets, onFetchCases, onRemoveCasesFromList } from 'actions/caseReviewActions';
import { onFetchCaseLogs, onFetchEntityLogs } from 'actions/logsActions';
import { isEmpty, lowerCase } from 'lodash';
import client from 'utility/apiClient';
import { onFetchUDSEntityDetails, onFetchCustomerDetailsWithAllAccounts } from './udsActions';
import { onFetchReviewerCase } from './oneViewActions';

function fetchCaseDetails(txnId, channel) {
  return client({
    url: `casereview/channel/${channel}/txnId/${txnId}/case`
  });
}

function onFetchCaseDetailsLoading() {
  return { type: ON_FETCH_CASE_DETAIL_LOADING };
}

function onSuccessfulFetchCaseDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CASE_DETAIL,
    response
  };
}

function onFetchCaseDetailsFailure(caseDetails) {
  return {
    type: ON_FETCH_CASE_DETAIL_FAILURE,
    caseDetails
  };
}

function onFetchCaseDetails(selectedCase, channel) {
  return function (dispatch) {
    dispatch(onFetchCaseDetailsLoading());
    return fetchCaseDetails(selectedCase.txnId, channel).then(
      (success) => dispatch(onSuccessfulFetchCaseDetails(success)),
      () =>
        dispatch(
          onFetchCaseDetailsFailure({
            ...selectedCase,
            currentStatus: 'READY_TO_OPEN'
          })
        )
    );
  };
}

function requestForInformation(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/additional/info`,
    data: formData
  });
}

function onRequestForInformation(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return requestForInformation(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Requested further information successfully' }));
          dispatch(onToggleRequestForInformationModal());
          dispatch(onUpdateCaseDetails(formData?.caseRefNo, formData?.caseRefList, 'str'));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onUpdateCaseDetails(caseRefNo, caseRefList, channel) {
  return function (dispatch, getState) {
    const { cases, selectedCase } = getState().caseAssignment;
    const { roles, userName, userId } = getState().auth.userCreds;
    if (roles == 'reviewer') {
      dispatch(onFetchReviewerCase({ userId, userName, channel }));
    }

    if (!['reviewer', 'investigator'].includes(roles)) {
      dispatch(onRemoveCasesFromList(caseRefList || [caseRefNo], channel));
      dispatch(onFetchBuckets(roles, channel));
      dispatch(onFetchCases(cases[channel].conf, channel, true));
    }

    if (!caseRefList && !isEmpty(selectedCase)) {
      dispatch(onFetchCaseDetails(selectedCase, channel));
      selectedCase?.entityId &&
        selectedCase?.entityCategory &&
        dispatch(
          lowerCase(selectedCase.entityCategory) === 'customer'
            ? onFetchCustomerDetailsWithAllAccounts(selectedCase.entityId)
            : onFetchUDSEntityDetails(lowerCase(selectedCase.entityCategory), selectedCase.entityId)
        );
      dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo));

      if (channel === 'frm' && selectedCase.entityId) {
        dispatch(
          onFetchEntityLogs(selectedCase.entityId, channel, {
            pageNo: 1,
            pageRecords: 10,
            filterCondition: []
          })
        );
      }
    }
  };
}

export { onFetchCaseDetails, onRequestForInformation, onUpdateCaseDetails };
