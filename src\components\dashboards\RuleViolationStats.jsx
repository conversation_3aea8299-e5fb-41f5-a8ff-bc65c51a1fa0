import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, CardTitle, CardSubtitle } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import { isCooperative } from 'constants/publicKey';

function RuleViolationStats({
  rule,
  period,
  ruleStats,
  fetchRuleStats,
  fetchOverallFeedbackAnalysis,
  ruleFeedbackAnalysis
}) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleStats({
        ruleId: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });

    !isCooperative && period.startDate &&
      period.endDate &&
      rule &&
      fetchOverallFeedbackAnalysis({
        ruleCode: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  return (
    <Row>
      <Col>
        <CardContainer>
          <CardTitle className="text-info">{ruleStats.data?.totalViolations ?? 0}</CardTitle>
          <CardSubtitle>Total Alerts</CardSubtitle>
        </CardContainer>
      </Col>
      <Col>
        <CardContainer>
          <CardTitle className="text-info">{ruleStats.data?.notFraudCases ?? 0}</CardTitle>
          <CardSubtitle>Not Fraud</CardSubtitle>
        </CardContainer>
      </Col>
      <Col>
        <CardContainer>
          <CardTitle className="text-info">{ruleStats.data?.fraudCases ?? 0}</CardTitle>
          <CardSubtitle>Fraud</CardSubtitle>
        </CardContainer>
      </Col>
      <Col>
        <CardContainer>
          <CardTitle className="text-info">{ruleStats.data?.manualCases ?? 0}</CardTitle>
          <CardSubtitle>Non Violation Fraud</CardSubtitle>
        </CardContainer>
      </Col>
      <Col>
        <CardContainer>
          <CardTitle className="text-info">{ruleStats.data?.totalFraudAmount ?? 0}</CardTitle>
          <CardSubtitle>Fraud Amount</CardSubtitle>
        </CardContainer>
      </Col>
      {!isCooperative && (<Col>
        <CardContainer>
          <CardTitle className="text-info">
            {ruleFeedbackAnalysis.data?.overallFeedback ?? 'NA'}
          </CardTitle>
          <CardSubtitle>Overall Feedback Analysis</CardSubtitle>
        </CardContainer>
      </Col>)}
    </Row>
  );
}

RuleViolationStats.propTypes = {
  rule: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  ruleStats: PropTypes.object.isRequired,
  fetchRuleStats: PropTypes.func.isRequired,
  fetchOverallFeedbackAnalysis: PropTypes.func.isRequired,
  ruleFeedbackAnalysis: PropTypes.object.isRequired
};

export default RuleViolationStats;
