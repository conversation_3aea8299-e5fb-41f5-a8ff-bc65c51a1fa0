import {
  ON_FETCH_TRANSACTION_DETAIL_LOADING,
  ON_FETCH_TRANSACTION_DETAIL_FAILURE,
  ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchTransactionDetails(txnid, channel) {
  return client({
    url: `uds/${channel}/alert-transaction/${txnid}`,
    badRequestMessage: 'Transaction not found.'
  });
}

function onFetchTransactionDetailsLoading() {
  return { type: ON_FETCH_TRANSACTION_DETAIL_LOADING };
}

function onSuccessfulFetchTransactionDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL,
    response
  };
}

function onTransactionDetailsFetchFailure(response) {
  return {
    type: ON_FETCH_TRANSACTION_DETAIL_FAILURE,
    response
  };
}

function onFetchTransactionDetails(txnid, channel) {
  return function (dispatch) {
    dispatch(onFetchTransactionDetailsLoading());
    return fetchTransactionDetails(txnid, channel).then(
      (success) => dispatch(onSuccessfulFetchTransactionDetails(success)),
      (error) => dispatch(onTransactionDetailsFetchFailure(error))
    );
  };
}

export { onFetchTransactionDetails };
