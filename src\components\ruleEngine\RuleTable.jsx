import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useMemo } from 'react';
import ReactTable from 'react-table';

import TableLoader from 'components/loader/TableLoader';
import SandboxHistoryTableContainer from 'containers/ruleEngine/SandboxHistoryTableContainer';
import useRuleTableHeaders from 'hooks/useRuleTableHeaders';
import { subHeaders } from 'utility/ruleTableHelpers';

function RuleTable({
  channel,
  moduleType,
  hasSandbox,
  data,
  ruleCreation,
  snoozelist,
  defaultSort = [],
  typeHeaders = [],
  actionHeaders = [],
  tableFilters = [],
  defaultSortMethod = undefined,
  setTableFilters = undefined,
  hasMakerChecker = false
}) {
  const headers = useRuleTableHeaders({
    actionHeaders,
    typeHeaders,
    tableFilters,
    channel,
    moduleType,
    hasMakerChecker,
    ruleCreation
  });

  // Find snooze entry by code
  const getSnoozeEntryByCode = (code) => {
    return _.find(snoozelist[channel], { code });
  };

  // Parse comma-separated attribute list into structured objects
  const parseAttributeList = (attributeList) => {
    if (!attributeList) return [];

    return attributeList
      .split(',')
      .map((item) => {
        const match = item.match(/(.*?)(<=|>=|!=|=|>|<)(.*)/);
        if (!match) return null;

        const [, rawAttr, rawOperator, rawValue] = match;
        return {
          attribute: _.trim(rawAttr),
          operator: _.trim(rawOperator),
          value: _.trim(rawValue)
        };
      })
      .filter(Boolean); // remove nulls
  };

  // Render parsed attributes as JSX
  const renderAttributes = (attributes) => {
    return attributes.map(({ attribute, operator, value }, index) => (
      <div key={index}>
        <b>Attribute:</b> {attribute}, <b>Operator:</b> {operator}, <b>Value:</b> {value};
        <br />
      </div>
    ));
  };

  // Main function combining all steps
  const modifiedSnoozeConditions = (code) => {
    const snoozeEntry = getSnoozeEntryByCode(code);
    if (!snoozeEntry) return '';

    const parsedAttributes = parseAttributeList(snoozeEntry.attributeList);
    return renderAttributes(parsedAttributes);
  };

  // Memoize table props to prevent unnecessary re-renders
  const tableProps = useMemo(
    () => ({
      ...(tableFilters && { filtered: tableFilters }),
      ...(setTableFilters && { onFilteredChange: setTableFilters }),
      ...(defaultSortMethod && { defaultSortMethod })
    }),
    [tableFilters, setTableFilters, defaultSortMethod]
  );

  if (data.loader) return <TableLoader />;

  if (data.error && _.isEmpty(data.list[channel]))
    return <div className="no-data-div">{data.errorMessage}</div>;

  return (
    <ReactTable
      filterable
      defaultFilterMethod={(filter, row) =>
        row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
      }
      columns={headers}
      data={data.list[channel]}
      SubComponent={(row) => (
        <div className="rule-sub-table">
          <ReactTable
            columns={subHeaders}
            data={[row.original]}
            defaultPageSize={1}
            showPagination={false}
          />
          {channel === 'frm' && hasSandbox === 1 && (
            <>
              <SandboxHistoryTableContainer ruleName={row.original.name} />
              <p className="ms-2">{modifiedSnoozeConditions(row.original.code)}</p>
            </>
          )}
        </div>
      )}
      pageSizeOptions={[5, 10, 20, 30, 40, 50]}
      defaultPageSize={10}
      minRows={6}
      showPaginationTop={true}
      showPaginationBottom={false}
      className="-highlight  -striped"
      defaultSorted={defaultSort}
      {...tableProps}
    />
  );
}

RuleTable.propTypes = {
  defaultSort: PropTypes.array,
  typeHeaders: PropTypes.array,
  tableFilters: PropTypes.array,
  actionHeaders: PropTypes.array,
  snoozelist: PropTypes.array,
  setTableFilters: PropTypes.func,
  defaultSortMethod: PropTypes.func,
  channel: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  ruleCreation: PropTypes.object.isRequired,
  data: PropTypes.object.isRequired,
  hasMakerChecker: PropTypes.bool.isRequired
};

// Custom comparison function for React.memo
const areEqual = (prevProps, nextProps) => {
  // Compare data arrays by length and key properties first (fast check)
  if (
    prevProps.data?.list?.[prevProps.channel]?.length !==
    nextProps.data?.list?.[nextProps.channel]?.length
  )
    return false;

  // Compare other primitive props
  if (
    prevProps.channel !== nextProps.channel ||
    prevProps.moduleType !== nextProps.moduleType ||
    prevProps.hasSandbox !== nextProps.hasSandbox ||
    prevProps.hasMakerChecker !== nextProps.hasMakerChecker
  )
    return false;

  // Compare arrays by reference (they should be memoized in parent)
  if (
    prevProps.defaultSort !== nextProps.defaultSort ||
    prevProps.typeHeaders !== nextProps.typeHeaders ||
    prevProps.actionHeaders !== nextProps.actionHeaders ||
    prevProps.tableFilters !== nextProps.tableFilters
  )
    return false;

  return true;
};

export default React.memo(RuleTable, areEqual);
