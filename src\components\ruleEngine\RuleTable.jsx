/* eslint-disable react/no-multi-comp */
import _ from 'lodash';
import React from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';

import TableLoader from 'components/loader/TableLoader';
import SandboxHistoryTableContainer from 'containers/ruleEngine/SandboxHistoryTableContainer';
import { isCooperative } from 'constants/publicKey';
import { getFilterValue } from 'utility/utils';

function RuleTable({
  channel,
  moduleType,
  hasSandbox,
  data,
  ruleCreation,
  defaultSort = [],
  typeHeaders = [],
  actionHeaders = [],
  tableFilters = [],
  defaultSortMethod = undefined,
  setTableFilters = undefined,
  hasMakerChecker = false
}) {
  const actionsOptions =
    ruleCreation.actionList &&
    _.map(ruleCreation?.actionList, (action) => (
      <option key={action.actionCode} value={action.actionName}>
        {action.actionName}
      </option>
    ));

  const tableProps = {
    ...(tableFilters && { filtered: tableFilters }),
    ...(setTableFilters && { onFilteredChange: setTableFilters }),
    ...(defaultSortMethod && { defaultSortMethod })
  };

  let header = [];

  actionHeaders.length > 0 && header.push(...actionHeaders);

  header.push(
    { Header: 'Name', accessor: 'name', minWidth: 200 },
    { Header: 'Logic', accessor: 'logic', minWidth: 400 }
  );

  typeHeaders.length > 0 && header.push(...typeHeaders);

  header.push(
    ...(channel === 'frm'
      ? moduleType !== 'issuer'
        ? [
            {
              Header: 'Violation Action',
              accessor: 'actionName',
              filterMethod: (filter, row) => row[filter.id] == filter.value,
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => {
                    onChange(event.target.value);
                  }}
                  value={getFilterValue('actionName', tableFilters)}
                >
                  <option value="">All</option>
                  {actionsOptions}
                </select>
              )
            },
            {
              Header: 'Low Risk Action',
              accessor: 'lowLevelOutcome',
              filterMethod: (filter, row) => row[filter.id] == filter.value,
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={getFilterValue('lowLevelOutcome', tableFilters)}
                >
                  <option value="">All</option>
                  {actionsOptions}
                </select>
              )
            },
            {
              Header: 'Medium Risk Action',
              accessor: 'medLevelOutcome',
              filterMethod: (filter, row) => row[filter.id] == filter.value,
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={getFilterValue('medLevelOutcome', tableFilters)}
                >
                  <option value="">All</option>
                  {actionsOptions}
                </select>
              )
            },
            {
              Header: 'High Risk Action',
              accessor: 'highLevelOutcome',
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={getFilterValue('highLevelOutcome', tableFilters)}
                >
                  <option value="">All</option>
                  {actionsOptions}
                </select>
              )
            }
          ]
        : [
            {
              Header: 'Violation Action',
              accessor: 'actionName',
              filterMethod: (filter, row) => row[filter.id] == filter.value,
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => {
                    onChange(event.target.value);
                  }}
                  value={getFilterValue('actionName', tableFilters)}
                >
                  <option value="">All</option>
                  {actionsOptions}
                </select>
              )
            }
          ]
      : []),
    ...(!isCooperative && hasMakerChecker && channel !== 'str'
      ? [
          {
            Header: 'Assign To',
            accessor: 'assignmentPriority',
            Cell: ({ value }) =>
              value ? (value == 0 ? 'Maker' : value == 1 ? 'Checker' : value) : null,
            filterMethod: (filter, row) => row[filter.id] == filter.value,
            Filter: ({ onChange }) => (
              <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('assignmentPriority', tableFilters)}>
                <option value="">All</option>
                <option value={0}>Maker</option>
                <option value={1}>Checker</option>
              </select>
            )
          }
        ]
      : []),
    {
      Header: 'Rule Order',
      accessor: 'order',
      Cell: ({ value }) => (!value ? '' : value),
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseInt(row[filter.id]) >= parseInt(filter.value),
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          max="100"
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('order', tableFilters)}
        />
      )
    },
    {
      Header: 'Category',
      accessor: 'alertCategoryId',
      Cell: ({ value }) => {
        const category =
          _.find(ruleCreation?.alertCategories, (category) => category.id == value) || {};
        return <span>{category?.categoryName || ''}</span>;
      },
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('alertCategoryId', tableFilters)}
        >
          <option value="">All</option>
          {_.chain(ruleCreation?.alertCategories)
            .filter((category) => category.categoryName !== 'NA')
            .map((category) => (
              <option key={category.id} value={category.id}>
                {category.categoryName}
              </option>
            ))
            .value()}
        </select>
      )
    },
    {
      Header: 'Fraud Category',
      accessor: 'fraudCategory',
      Cell: ({ value }) => {
        const category =
          _.find(ruleCreation?.fraudCategories, (category) => category.name == value) || {};
        return <span>{category?.name || ''}</span>;
      },
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('fraudCategory', tableFilters)}
        >
          <option value="">All</option>
          {_.chain(ruleCreation?.fraudCategories)
            .filter((category) => category.name !== 'NA')
            .map((category) => (
              <option key={category.id} value={category.name}>
                {category.name}
              </option>
            ))
            .value()}
        </select>
      )
    },
    {
      Header: 'Channels',
      accessor: 'channels',
      filterMethod: (filter, row) => _.includes(row[filter.id], filter.value),
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('channels', tableFilters)}>
          <option value="">All</option>
          {_.chain(ruleCreation?.ruleChannels)
            .filter((channel) => channel.name !== 'NA')
            .map((channel) => (
              <option key={channel.id} value={channel.name}>
                {channel.name}
              </option>
            ))
            .value()}
        </select>
      )
    },
    {
      Header: 'Label',
      accessor: 'label',
      Cell: ({ value }) => {
        const label = _.find(ruleCreation?.ruleLabels, (label) => label.name == value) || {};
        return <span>{label?.name || ''}</span>;
      },
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('label', tableFilters)}>
          <option value="">All</option>
          {_.chain(ruleCreation?.ruleLabels)
            .map((label) => (
              <option key={label.id} value={label.name}>
                {label.name}
              </option>
            ))
            .value()}
        </select>
      )
    }
  );

  channel !== 'str' &&
    header.push(
      {
        Header: 'Rule Type',
        accessor: 'ruleType',
        filterMethod: (filter, row) => row[filter.id] == filter.value,
        Filter: ({ onChange }) => (
          <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('ruleType', tableFilters)}>
            <option value="">All</option>
            <option>Default</option>
            <option>{channel === 'str' ? 'Str' : 'Dsl'}</option>
          </select>
        )
      },
      {
        Header: 'Transaction Type',
        accessor: 'methodType',
        Cell: ({ value }) => (value ? value + ' Auth' : ''),
        Filter: ({ onChange }) => (
          <select onChange={(event) => onChange(event.target.value)} value={getFilterValue('methodType', tableFilters )}>
            <option value="">All</option>
            <option value="Pre">Pre Auth</option>
            <option value="Post">Post Auth</option>
          </select>
        )
      }
    );

  let subHeader = [
    { Header: 'Description', accessor: 'description', minWidth: 300 },
    { Header: 'Created/Edited By', accessor: 'createdBy', minWidth: 50 },
    {
      Header: 'Created/Edited Date',
      accessor: 'createdAt',
      minWidth: 80,
      Cell: ({ value }) => value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null
    },
    { Header: 'Updated By', accessor: 'updatedBy', minWidth: 50 },
    {
      Header: 'Updated Date',
      accessor: 'updatedAt',
      minWidth: 80,
      Cell: ({ value }) => value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null
    }
  ];

  return data.loader ? (
    <TableLoader />
  ) : data.error && _.isEmpty(data.list[channel]) ? (
    <div className="no-data-div">{data.errorMessage}</div>
  ) : (
    <ReactTable
      filterable
      defaultFilterMethod={(filter, row) =>
        row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
      }
      columns={header}
      data={data.list[channel]}
      SubComponent={(row) => {
        return (
          <div className="rule-sub-table">
            <ReactTable
              columns={subHeader}
              data={[row.original]}
              defaultPageSize={1}
              showPagination={false}
            />
            {channel == 'frm' && hasSandbox === 1 && (
              <SandboxHistoryTableContainer ruleName={row.original.name} />
            )}
          </div>
        );
      }}
      pageSizeOptions={[5, 10, 20, 30, 40, 50]}
      defaultPageSize={10}
      minRows={6}
      showPaginationTop={true}
      showPaginationBottom={false}
      className={'-highlight  -striped'}
      defaultSorted={defaultSort}
      {...tableProps}
    />
  );
}

RuleTable.propTypes = {
  defaultSort: PropTypes.array,
  typeHeaders: PropTypes.array,
  tableFilters: PropTypes.array,
  actionHeaders: PropTypes.array,
  setTableFilters: PropTypes.func,
  defaultSortMethod: PropTypes.func,
  channel: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  ruleCreation: PropTypes.object.isRequired,
  data: PropTypes.object.isRequired,
  hasMakerChecker: PropTypes.bool.isRequired
};

export default RuleTable;
