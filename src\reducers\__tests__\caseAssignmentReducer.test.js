import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import caseAssignmentReducer from 'reducers/caseAssignmentReducer';
import initialState from 'reducers/initialState';

const selectedCase = {
  caseId: '123',
  caseRefNo: '123',
  channel: 'rpsl',
  customerId: '123',
  txnId: '123',
  txnAmount: 75500,
  txnTimestamp: '2020-06-26 03:58:12',
  ifrmVerdict: 'Stop',
  investigationVerdict: 'Others',
  lastUpdatedTimestamp: '2020-06-26 18:59:52',
  lastActionName: 'Reviewed&Closed',
  remarks: '23erf',
  fraudType: 'Auto closure as per banks requirement',
  weight: 65,
  currentStage: 'Investigation',
  currentStatus: 'READY_TO_OPEN',
  liability: 'NotApplicable',
  rrn: '*****************',
  isAcknowledged: 1,
  currentlyAssignedTo: 'user',
  isHold: 1,
  entityCategory: 'customer',
  entityId: '123'
};

describe('Case Assignment Reducer', () => {
  it('should return the intial state', () => {
    expect(caseAssignmentReducer(undefined, {})).toEqual(initialState.caseAssignment);
  });

  it('should handle ON_CLEAR_SELECTED_CASE', () => {
    expect(
      caseAssignmentReducer(
        {},
        {
          type: types.ON_CLEAR_SELECTED_CASE
        }
      )
    ).toEqual({
      selectedCase: {}
    });
  });

  it('should handle ON_FETCH_CASE_DETAIL_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {},
        {
          type: types.ON_FETCH_CASE_DETAIL_FAILURE,
          caseDetails: selectedCase
        }
      )
    ).toEqual({
      selectedCase
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CASE_DETAIL', () => {
    expect(
      caseAssignmentReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CASE_DETAIL,
          response: selectedCase
        }
      )
    ).toEqual({
      selectedCase
    });
  });

  it('should handle ON_LIABILITY_LIST_FETCH_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          liability: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_LIABILITY_LIST_FETCH_LOADING
        }
      )
    ).toEqual({
      liability: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_LIABILITY_LIST_FETCH', () => {
    expect(
      caseAssignmentReducer(
        {
          liability: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_LIABILITY_LIST_FETCH,
          response: [{ id: 1, liabilityType: 'NotApplicable' }]
        }
      )
    ).toEqual({
      liability: {
        list: [{ id: 1, liabilityType: 'NotApplicable' }],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_LIABILITY_LIST_FETCH_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          liability: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_LIABILITY_LIST_FETCH_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      liability: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FRAUD_TYPE_LIST_FETCH_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          fraudTypes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FRAUD_TYPE_LIST_FETCH_LOADING
        }
      )
    ).toEqual({
      fraudTypes: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH', () => {
    const response = [
      {
        id: 2,
        verdict: 'Confirmed Fraud',
        types: [
          {
            id: 8,
            fraudName: 'NRI NRC'
          },
          {
            id: 7,
            fraudName: 'Account Takeover'
          },
          {
            id: 6,
            fraudName: 'Internet MOTO'
          },
          {
            id: 5,
            fraudName: 'Application Fraud'
          },
          {
            id: 4,
            fraudName: 'Identity Theft'
          },
          {
            id: 3,
            fraudName: 'Lost Stolen'
          },
          {
            id: 2,
            fraudName: 'Skimming Counterfeit'
          },
          {
            id: 1,
            fraudName: 'Unknown'
          }
        ]
      },
      {
        id: 1,
        verdict: 'Undetermined',
        types: [
          {
            id: 9,
            fraudName: 'Not Applicable'
          }
        ]
      },
      {
        id: 3,
        verdict: 'Confirmed Genuine',
        types: [
          {
            id: 9,
            fraudName: 'Not Applicable'
          }
        ]
      },
      {
        id: 4,
        verdict: 'Assumed Genuine',
        types: [
          {
            id: 9,
            fraudName: 'Not Applicable'
          }
        ]
      }
    ];
    expect(
      caseAssignmentReducer(
        {
          fraudTypes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH,
          response
        }
      )
    ).toEqual({
      fraudTypes: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FRAUD_TYPE_LIST_FETCH_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          fraudTypes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FRAUD_TYPE_LIST_FETCH_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      fraudTypes: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_CLEAR_TRANSACTION_HISTORY_SEARCH', () => {
    expect(
      caseAssignmentReducer(
        {
          transactionHistory: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_CLEAR_TRANSACTION_HISTORY_SEARCH
        }
      )
    ).toEqual({
      transactionHistory: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          pastInvestigations: {
            frm: {
              entityId: '',
              isLastPage: true,
              count: 0,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING,
          channel: 'frm'
        }
      )
    ).toEqual({
      pastInvestigations: {
        frm: {
          entityId: '',
          isLastPage: true,
          count: 0,
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS', () => {
    const response = {
      investigatedTxns: [
        {
          caseId: 615,
          caseRefNo: 'hnr94A',
          txnId: 'hnr94A',
          txnAmount: 17000,
          txnTimestamp: '2021-01-26T17:32:07',
          ifrmVerdict: 'OTP',
          currentStatus: 'Closed',
          liability: 'NotApplicable',
          caseVerdict: 'Undetermined',
          isAcknowledged: 1,
          assignedTo: 22,
          bucket: 3,
          terminalId: '12345',
          deviceId: '78',
          customerId: '992233',
          beneficiaryId: '1113',
          payeeMcc: '5411, hotel',
          channel: 'rrr',
          txnType: '00, Purchase',
          responseCode: '00, Accepted',
          senderMaskedCard: '6666',
          senderHashedCard: '7777',
          reViolatedRules: ['15dd9b65-549d-44e1-b922-aa065d98766e']
        }
      ],
      isLastPage: true,
      count: 85
    };
    expect(
      caseAssignmentReducer(
        {
          pastInvestigations: {
            frm: {
              entityId: '',
              isLastPage: true,
              count: 0,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS,
          response,
          entityId: '123',
          channel: 'frm'
        }
      )
    ).toEqual({
      pastInvestigations: {
        frm: {
          entityId: '123',
          count: 85,
          isLastPage: true,
          list: response.investigatedTxns,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          pastInvestigations: {
            frm: {
              entityId: '123',
              isLastPage: true,
              count: 0,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE,
          response: { message: 'Insufficient rights to access data' },
          entityId: '123',
          channel: 'frm'
        }
      )
    ).toEqual({
      pastInvestigations: {
        frm: {
          count: undefined,
          entityId: '123',
          list: [],
          isLastPage: true,
          loader: false,
          error: true,
          errorMessage: 'Insufficient rights to access data'
        }
      }
    });
  });

  it('should handle ON_BUCKETS_FETCH_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          buckets: {
            frm: {
              stats: {},
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_BUCKETS_FETCH_LOADING,
          channel: 'frm'
        }
      )
    ).toEqual({
      buckets: {
        frm: { stats: {}, loader: true, error: false, errorMessage: '' }
      }
    });
  });

  it('should handle ON_SUCCESSFUL_BUCKETS_FETCH', () => {
    expect(
      caseAssignmentReducer(
        {
          buckets: {
            frm: { stats: {}, loader: false, error: false, errorMessage: '' }
          }
        },
        {
          type: types.ON_SUCCESSFUL_BUCKETS_FETCH,
          response: {
            openCases: 1,
            rejectedCases: 2,
            closedCases: 3,
            pendingCases: 4,
            masterQueue: 4
          },
          channel: 'frm'
        }
      )
    ).toEqual({
      buckets: {
        frm: {
          stats: {
            openCases: 1,
            rejectedCases: 2,
            closedCases: 3,
            pendingCases: 4,
            masterQueue: 4
          },
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_BUCKETS_FETCH_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          buckets: {
            frm: {
              stats: {},
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_BUCKETS_FETCH_FAILURE,
          response: { message: 'Unable to get buckets' },
          channel: 'frm'
        }
      )
    ).toEqual({
      buckets: {
        frm: { stats: {}, loader: false, error: true, errorMessage: 'Unable to get buckets' }
      }
    });
  });

  it('should handle ON_CASES_FETCH_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          cases: {
            frm: {
              list: [],
              conf: {},
              isLastPage: true,
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_CASES_FETCH_LOADING,
          channel: 'frm',
          conf: {}
        }
      )
    ).toEqual({
      cases: {
        frm: { list: [], conf: {}, isLastPage: true, loader: true, error: false, errorMessage: '' }
      }
    });
  });

  it('should handle ON_SUCCESSFUL_CASES_FETCH', () => {
    const response = {
      cases: [
        {
          caseId: 8,
          txnId: 'TXN8',
          caseRefNo: '67887',
          entityId: 'Entity3',
          entityCategory: 'Agent',
          txnTimestamp: '2020-05-14T14:18:01.522344',
          createdTimeStamp: '2020-05-14T14:18:01.522344',
          assignmentTimeStamp: '2020-05-14T14:18:01.522344',
          weight: 10,
          txnAmount: 300,
          investigationVerdict: 'Confirmed Fraud',
          ifrmVerdict: 'OTP',
          ifrmPostauthVerdictName: 'ACCEPTED',
          investigationStatus: 'Open',
          terminalId: 'Terminal123',
          deviceId: 'd123',
          customerId: 'cust123',
          beneficiaryId: 'b123',
          payeeMcc: '1121, shop ',
          channel: 'ch123',
          txnType: 'cASH',
          responseCode: '00,Accepted',
          senderMaskedCard: 'JGHER3545',
          senderHashedCard: 'SJHGS2653',
          reViolatedRules: 'SD-DD4-34,4545-4354FDG-DV',
          bucketId: 2
        }
      ],
      isLastPage: true
    };
    expect(
      caseAssignmentReducer(
        {
          cases: {
            frm: {
              list: [],
              conf: {},
              isLastPage: true,
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_SUCCESSFUL_CASES_FETCH,
          response,
          conf: {},
          channel: 'frm',
          refresh: false
        }
      )
    ).toEqual({
      cases: {
        frm: {
          list: response.cases,
          conf: {},
          isLastPage: true,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_CASES_FETCH_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          cases: {
            frm: {
              list: [],
              conf: {},
              isLastPage: true,
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_CASES_FETCH_FAILURE,
          response: { message: 'Unable to get buckets' },
          conf: {},
          channel: 'frm',
          refresh: false
        }
      )
    ).toEqual({
      cases: {
        frm: {
          list: [],
          conf: {},
          count: 0,
          isLastPage: true,
          loader: false,
          error: true,
          errorMessage: 'Unable to get buckets'
        }
      }
    });
  });

  it('should handle ON_FETCH_CLOSURE_CASES_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          closureCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSURE_CASES_LOADING
        }
      )
    ).toEqual({
      closureCases: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CLOSURE_CASES_SUCCESS', () => {
    const response = [
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '5674',
        caseRefNo: 'asdsffsgsgthyj',
        txnId: 'tx4567',
        terminalId: 't4567',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 2000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      },
      {
        caseId: '1234',
        caseRefNo: 'asdafdafdads',
        txnId: 'tx1234',
        terminalId: 't1234',
        txnTime: '2020-05-14 14:18:01',
        txnAmount: 1000,
        txnType: 'T-type',
        ifrmVerdict: 'ACCEPTED'
      }
    ];
    expect(
      caseAssignmentReducer(
        {
          closureCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSURE_CASES_SUCCESS,
          response
        }
      )
    ).toEqual({
      closureCases: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CLOSURE_CASES_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          closureCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSURE_CASES_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      closureCases: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_CLEAR_BULK_CLOSURE_SEARCH', () => {
    expect(
      caseAssignmentReducer(
        {
          closureCases: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_CLEAR_BULK_CLOSURE_SEARCH
        }
      )
    ).toEqual({
      closureCases: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CLOSE_CASE_BUCKETS_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          closeCaseBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSE_CASE_BUCKETS_LOADING
        }
      )
    ).toEqual({
      closeCaseBuckets: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS', () => {
    const response = [
      {
        id: 1,
        name: 'Fraud'
      },
      {
        id: 2,
        name: 'NonFraud'
      },
      {
        id: 3,
        name: 'Suspicious'
      }
    ];
    expect(
      caseAssignmentReducer(
        {
          closeCaseBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS,
          response
        }
      )
    ).toEqual({
      closeCaseBuckets: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          closeCaseBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE,
          response: { message: 'Error msg' }
        }
      )
    ).toEqual({
      closeCaseBuckets: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Error msg'
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          fraudTypesWithBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING
        }
      )
    ).toEqual({
      fraudTypesWithBuckets: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS', () => {
    const response = [
      {
        id: 3,
        verdict: 'Confirmed Genuine',
        buckets: [
          {
            id: 2,
            name: 'NonFraud'
          }
        ]
      },
      {
        id: 0,
        verdict: 'Others',
        buckets: [
          {
            id: 3,
            name: 'Suspicious'
          },
          {
            id: 2,
            name: 'NonFraud'
          },
          {
            id: 1,
            name: 'Fraud'
          }
        ]
      },
      {
        id: 1,
        verdict: 'Undetermined',
        buckets: [
          {
            id: 3,
            name: 'Suspicious'
          },
          {
            id: 2,
            name: 'NonFraud'
          },
          {
            id: 1,
            name: 'Fraud'
          }
        ]
      },
      {
        id: 4,
        verdict: 'Assumed Genuine',
        buckets: [
          {
            id: 3,
            name: 'Suspicious'
          },
          {
            id: 2,
            name: 'NonFraud'
          },
          {
            id: 1,
            name: 'Fraud'
          }
        ]
      },
      {
        id: 2,
        verdict: 'Confirmed Fraud',
        buckets: [
          {
            id: 1,
            name: 'Fraud'
          }
        ]
      }
    ];
    expect(
      caseAssignmentReducer(
        {
          fraudTypesWithBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS,
          response
        }
      )
    ).toEqual({
      fraudTypesWithBuckets: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          fraudTypesWithBuckets: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      fraudTypesWithBuckets: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_REMOVE_CASES_FROM_LIST', () => {
    expect(
      caseAssignmentReducer(
        {
          cases: {
            frm: {
              list: [],
              conf: {},
              isLastPage: true,
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_REMOVE_CASES_FROM_LIST,
          cases: ['123'],
          channel: 'frm'
        }
      )
    ).toEqual({
      cases: {
        frm: { list: [], conf: {}, isLastPage: true, loader: false, error: false, errorMessage: '' }
      }
    });
  });

  it('should handle ON_FETCH_XCHANNEL_LIST_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          xChannel: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_XCHANNEL_LIST_LOADING
        }
      )
    ).toEqual({
      xChannel: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_XCHANNEL_LIST_SUCCESS', () => {
    expect(
      caseAssignmentReducer(
        {
          xChannel: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_XCHANNEL_LIST_SUCCESS,
          response: responses.caseAssignment.xChannel
        }
      )
    ).toEqual({
      xChannel: {
        list: responses.caseAssignment.xChannel,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_XCHANNEL_LIST_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          xChannel: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_XCHANNEL_LIST_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      xChannel: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING', () => {
    expect(
      caseAssignmentReducer(
        {
          snoozeConditions: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING
        }
      )
    ).toEqual({
      snoozeConditions: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS', () => {
    expect(
      caseAssignmentReducer(
        {
          snoozeConditions: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS,
          response: responses.caseAssignment.snoozeConditions
        }
      )
    ).toEqual({
      snoozeConditions: {
        list: responses.caseAssignment.snoozeConditions,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE', () => {
    expect(
      caseAssignmentReducer(
        {
          snoozeConditions: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      snoozeConditions: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });
});
