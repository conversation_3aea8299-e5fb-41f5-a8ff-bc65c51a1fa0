import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { ButtonGroup, Button, DropdownItem } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faFolderOpen, faFlag } from '@fortawesome/free-solid-svg-icons';

import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import ParkCaseModalContainer from 'containers/common/ParkCaseModalContainer';
import HoldCaseModal from 'components/common/HoldCaseModal';
import DocumentRequestModal from 'components/common/DocumentRequestModal';
import VerdictModalSTRContainer from 'containers/common/VerdictModalSTRContainer';
import RequestForInformationButtonContainer from 'containers/common/RequestForInformationButtonContainer';
import STRCaseReopenModalContainer from 'containers/common/STRCaseReopenModalContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import DownloadSTRButton from 'containers/investigation/DownloadSTRButtonContainer';
import DropdownButton from 'components/common/DropdownButton';
import CreateCaseModalContainer from 'containers/common/CreateCaseModalContainer';
import { isCooperative } from 'constants/publicKey';
import CaseRuleTagModalContainer from 'containers/common/CaseRuleTagModalContainer';

const InvestigationActions = ({
  role,
  userId,
  toggle,
  history,
  userName,
  holdCase,
  txnDetails,
  selectedCase,
  toggleActions,
  documentStatus,
  requestDocument,
  clearSelectedCase,
  caseAssignmentActions,
  channel,
  moduleType,
  fetchIncident,
  createIncidents,
  hasHoldAndRelease,
  openSTRCase,
  mainPerson = ''
}) => {
  const handleHoldCaseSubmit = (comment) => {
    let formData = {
      caseRefNo: selectedCase.caseRefNo,
      comment,
      userId,
      txnId: selectedCase.txnId
    };

    holdCase(formData, selectedCase);
  };

  const handleRequestDocumentSubmit = (comment) => {
    let formData = {
      caseId: selectedCase.caseId,
      statusToBeUpdated: 1,
      desc: comment,
      caseRefNo: selectedCase.caseRefNo
    };

    requestDocument(formData, selectedCase);
  };

  const createCase = (selectedCase, externalCaseCreationData = {}) => {
    let formData = {
      assignedTo: userId,
      assignedBy: userId,
      channel: selectedCase.channel,
      txnId: selectedCase.txnId,
      partnerId: txnDetails?.details?.identifiers?.partnerId || 0,
      ...(selectedCase?.caseOrigin && { caseOrigin: selectedCase.caseOrigin }),
      ...(!_.isEmpty(externalCaseCreationData) && {
        agencyType: externalCaseCreationData.agencyType,
        enquiryDetails: externalCaseCreationData.enquiryDetails
      })
    };
    caseAssignmentActions.onCreateCaseAndAssign(formData, {}, selectedCase);
  };

  const submitIncidents = (data) => {
    const txnList = [data.txnId];
    createIncidents({ txnList });
  };

  const openIncident = (incidentId) => {
    fetchIncident(incidentId);
    toggleActions.onToggleCPIFRFormModal();
  };

  const handleOpenSTRCase = (selectedCase) => {
    let formData = {
      caseRefNos: [selectedCase?.caseRefNo]
    };

    openSTRCase(formData, selectedCase);
  };

  const caseAction = (caseInfo) => {
    const {
      currentStatus,
      caseRefNo,
      selectedCase,
      isHold,
      stage,
      caseOrigin,
      agencyType,
      isStrCaseCreated
    } = caseInfo;
    return (
      <>
        {(isCooperative ? stage !== 'checker' : true) && (
          <UserListDropdownContainer
            bucket={currentStatus}
            caseId={caseRefNo}
            isHold={isHold}
            channel={channel}
            showText
          />
        )}
        {channel === 'frm' ? (
          <>
            {(isCooperative ? stage !== 'checker' : true) && (
              <ParkCaseModalContainer
                stageId={stage == 'maker' ? 1 : 2}
                caseId={caseRefNo}
                selectedCase={selectedCase}
                channel={channel}
              />
            )}
            <Button
              size="sm"
              color="danger"
              className="ms-1"
              onClick={() => toggleActions.onToggleVerdictModal('frm')}>
              <FontAwesomeIcon icon={faTimes} /> {' Close Case'}
            </Button>

            {!isStrCaseCreated && (
              <Button
                outline
                size="sm"
                color="success"
                className="ms-1 hide"
                title="Open STR Case"
                onClick={() => handleOpenSTRCase(selectedCase)}>
                <FontAwesomeIcon icon={faFolderOpen} />
              </Button>
            )}
          </>
        ) : (
          <>
            {stage === 'maker' && (
              <RequestForInformationButtonContainer
                selectedCase={selectedCase}
                txnDetails={txnDetails}
              />
            )}
            <VerdictModalSTRContainer
              caseRefNo={selectedCase.caseRefNo}
              showText={true}
              childTxns={selectedCase?.childTxns}
              mainPerson={mainPerson}
              alertSourceType={
                caseOrigin === 'User' && !_.isEmpty(agencyType)
                  ? 'Agency'
                  : caseOrigin === 'User'
                  ? 'Employee'
                  : caseOrigin
              }
            />
          </>
        )}
      </>
    );
  };

  const getActions = () => {
    const {
      currentStatus,
      currentStage,
      currentlyAssignedTo,
      isAcknowledged,
      isHold,
      caseRefNo,
      entityId,
      caseOrigin,
      agencyType,
      transactionTimestamp,
      txnId,
      incidentId,
      investigationVerdict,
      reViolatedRules,
      taggedRule,
      isStrCaseCreated,
      makerAction,
      parentTxn
    } = selectedCase;
    const stage = _.lowerCase(currentStage);
    const caseInfo = {
      currentStatus,
      caseRefNo,
      selectedCase,
      isHold,
      stage,
      caseOrigin,
      agencyType,
      txnId,
      investigationVerdict,
      reViolatedRules,
      isStrCaseCreated
    };

    if (channel === 'str' && !_.isEmpty(parentTxn)) return false;

    if (currentStatus == 'READY_TO_OPEN' && channel == 'frm' && !isCooperative)
      return (
        <DropdownButton name="Open Case" color="success" title="Create Case">
          <DropdownItem onClick={() => createCase(selectedCase)}>Direct</DropdownItem>
          <DropdownItem
            onClick={() => createCase(_.assign({ caseOrigin: 'Customer' }, selectedCase))}>
            Customer request
          </DropdownItem>
        </DropdownButton>
      );

    if (currentStatus == 'READY_TO_OPEN' && channel == 'str' && role == 'maker')
      return (
        <>
          <DropdownButton name="Open Case" color="success" title="Create Case">
            <DropdownItem onClick={() => createCase(selectedCase)}>STR</DropdownItem>
            <DropdownItem onClick={() => toggleActions.onToggleCreateCaseModal()}>
              External STR
            </DropdownItem>
          </DropdownButton>
          <CreateCaseModalContainer
            handleExternalCreateCase={(externalCaseCreationData) =>
              createCase(selectedCase, externalCaseCreationData)
            }
          />
        </>
      );

    if (currentStatus === 'Closed' && channel === 'frm') {
      const closeActions = [];
      !isCooperative &&
        closeActions.push(
          <Button
            outline
            size="sm"
            className="ms-1"
            color="primary"
            onClick={() =>
              caseAssignmentActions.onReOpenCase(
                {
                  caseRefNo,
                  stageId: role === 'checker' ? 2 : 1
                },
                'frm',
                selectedCase
              )
            }>
            Re-open
          </Button>
        );

      if (
        isCooperative &&
        !_.isEmpty(investigationVerdict) &&
        _.isEmpty(selectedCase.investigatorVerdict) &&
        stage == 'reviewer'
      )
        closeActions.push(
          <Button
            size="sm"
            color="danger"
            className="ms-1"
            onClick={() => toggleActions.onToggleVerdictModal('frm')}>
            <FontAwesomeIcon icon={faTimes} /> {' Update Investigation'}
          </Button>
        );

      if (
        reViolatedRules?.length === 0 &&
        !taggedRule?.length > 0 &&
        (_.includes(investigationVerdict, 'Fraud') ||
          _.includes(selectedCase.investigatorVerdict, 'Fraud'))
      )
        closeActions.push(<CaseRuleTagModalContainer caseRefNo={caseRefNo} />);
      else if (
        (isCooperative && _.includes(selectedCase?.investigatorVerdict, 'Fraud')) ||
        (!isCooperative &&
          role === 'checker' &&
          moduleType === 'issuer' &&
          _.includes(investigationVerdict, 'Fraud'))
      ) {
        if (incidentId) {
          closeActions.push(
            <Button
              outline
              size="sm"
              color="warning"
              className="ms-1"
              onClick={() => openIncident(incidentId)}>
              <FontAwesomeIcon icon={faFlag} /> Update Incident
            </Button>
          );
        } else if (moment.duration(moment().diff(moment(transactionTimestamp))).days() <= 7) {
          closeActions.push(
            <Button
              outline
              size="sm"
              color="warning"
              className="ms-1"
              onClick={() => submitIncidents(caseInfo)}>
              <FontAwesomeIcon icon={faFlag} /> Report Incident
            </Button>
          );
        } else null;
      }

      return <>{closeActions}</>;
    }

    if (currentStatus == 'Closed' && channel === 'str' && _.isEmpty(parentTxn))
      return (
        <>
          <STRCaseReopenModalContainer caseRefNo={caseRefNo} selectedCase={selectedCase} showText />
          {makerAction === 'File STR' && (
            <DownloadSTRButton caseRefNo={caseRefNo} entityId={entityId} />
          )}
        </>
      );

    return currentlyAssignedTo == userName ? (
      role == 'maker' && stage == 'maker' ? (
        ['Open', 'Rejected'].includes(currentStatus) ? (
          isAcknowledged == 1 ? (
            <>
              {caseAction(caseInfo)}
              {hasHoldAndRelease === 1
                ? isHold == 1
                  ? documentStatus?.details?.status == 0 &&
                    channel === 'frm' && (
                      <DocumentRequestModal
                        toggle={toggle}
                        handleRequestDocumentSubmit={handleRequestDocumentSubmit}
                        toggleRequestDocumentModal={toggleActions.toggleRequestDocumentModal}
                      />
                    )
                  : !_.isEmpty(txnDetails.details) &&
                    _.includes(
                      ['accepted', 'rejected'],
                      _.toLower(txnDetails?.details?.ifrmPostauthVerdictName)
                    ) &&
                    txnDetails?.details?.responseCode == '00' &&
                    channel === 'frm' &&
                    moduleType !== 'issuer' && (
                      <HoldCaseModal
                        toggle={toggle}
                        toggleHoldCaseModal={toggleActions.onToggleHoldCaseModal}
                        handleHoldCaseSubmit={handleHoldCaseSubmit}
                      />
                    )
                : null}
            </>
          ) : (
            <Button
              outline
              size="sm"
              color="success"
              className="ms-1"
              onClick={() => caseAssignmentActions.onAcknowledgeCase(selectedCase, channel)}>
              Start Review
            </Button>
          )
        ) : currentStatus === 'Parked' ? (
          <>
            {(!isCooperative || channel !== 'frm') && (
              <UserListDropdownContainer
                bucket={currentStatus}
                caseId={caseRefNo}
                isHold={isHold}
                channel={channel}
                showText
              />
            )}
            <Button
              outline
              size="sm"
              color="warning"
              className="ms-1"
              onClick={() =>
                caseAssignmentActions.onUnparkCase(
                  { caseRefNo: [caseRefNo], stageId: 1, isManual: 1 },
                  selectedCase,
                  channel
                )
              }>
              <FontAwesomeIcon icon={faFolderOpen} /> {' Unpark Cases'}
            </Button>
          </>
        ) : null
      ) : _.includes(['checker', 'investigator'], role) &&
        _.includes(['checker', 'investigator'], stage) ? (
        currentStatus == 'Open' ? (
          isAcknowledged == 1 ? (
            caseAction(caseInfo)
          ) : (
            <Button
              size="sm"
              color="success"
              className="ms-1"
              onClick={() => caseAssignmentActions.onAcknowledgeCase(selectedCase, channel)}>
              Start Review
            </Button>
          )
        ) : currentStatus == 'Parked' && role !== 'investigator' ? (
          <>
            {(!isCooperative || channel !== 'frm') && (
              <UserListDropdownContainer
                bucket={currentStatus}
                caseId={caseRefNo}
                isHold={isHold}
                channel={channel}
                showText
              />
            )}
            <Button
              size="sm"
              color="warning"
              className="ms-1"
              onClick={() =>
                caseAssignmentActions.onUnparkCase(
                  { caseRefNo: [caseRefNo], stageId: 2, isManual: 1 },
                  selectedCase,
                  channel
                )
              }>
              <FontAwesomeIcon icon={faFolderOpen} /> {' Unpark Cases'}
            </Button>
          </>
        ) : currentStatus == 'Pending' && role !== 'investigator' ? (
          <ApprovalModalContainer
            caseId={caseRefNo || ''}
            status={currentStatus || ''}
            caseDetails={selectedCase}
            channel={channel}
            singleType=""
            makerAction={makerAction || ''}
          />
        ) : null
      ) : null
    ) : null;
  };

  return (
    <ButtonGroup>
      {getActions()}
      <Button
        outline
        size="sm"
        color="secondary"
        className="ms-1"
        onClick={() => {
          clearSelectedCase();
          history.goBack();
        }}>
        Back
      </Button>
    </ButtonGroup>
  );
};

InvestigationActions.propTypes = {
  mainPerson: PropTypes.string,
  hasHoldAndRelease: PropTypes.number,
  role: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  userId: PropTypes.number.isRequired,
  toggle: PropTypes.object.isRequired,
  history: PropTypes.object.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  documentStatus: PropTypes.object.isRequired,
  caseAssignmentActions: PropTypes.object.isRequired,
  holdCase: PropTypes.func.isRequired,
  requestDocument: PropTypes.func.isRequired,
  clearSelectedCase: PropTypes.func.isRequired,
  moduleType: PropTypes.string.isRequired,
  fetchIncident: PropTypes.func.isRequired,
  createIncidents: PropTypes.func.isRequired,
  openSTRCase: PropTypes.func.isRequired
};

export default InvestigationActions;
