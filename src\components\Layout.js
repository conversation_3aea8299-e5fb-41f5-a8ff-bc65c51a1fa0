import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import IdleTimer from 'react-idle-timer';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { IDEL_TIMER_VALUES } from 'constants/applicationConstants';
import Header from 'containers/common/HeaderContainer';
import RoutesContainer from 'containers/RoutesContainer';
import { useInterval } from 'utility/customHooks';

const Layout = ({ theme, userCreds, session, actions, sandboxStatus }) => {
  const history = useHistory();
  const { sessionTimeout, isIdle, logoutModal, isLoggedIn } = session;
  const idleTimer = useRef(null);
  const [timeRemaining, setTimeRemaining] = useState(IDEL_TIMER_VALUES.timeRemaining);
  const header = useMemo(() => <Header />, []);
  const routes = useMemo(() => <RoutesContainer />, []);

  useEffect(() => {
    if (!session.isLoggedIn) return history.push('/');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  useInterval(() => {
    if (sandboxStatus !== 'STARTED' && sandboxStatus !== 'PENDING')
      setTimeRemaining(sessionTimeout - new Date());
  }, IDEL_TIMER_VALUES.interval);

  const onAction = () => {
    if (isIdle) actions.onToggleSessionIdle(false);
    actions.onResetSessionTimeout();
  };

  const onIdle = () => {
    actions.onToggleSessionIdle(true);
    actions.onToggleLogoutModal(true);
  };

  const handleClose = () => {
    actions.onResetSessionTimeout();
    actions.onToggleSessionIdle(false);
    actions.onToggleLogoutModal(false);
    setTimeRemaining(IDEL_TIMER_VALUES.timeRemaining);
  };

  const refreshToken = () => {
    actions.onRefreshToken();
    handleClose();
  };

  const handleLogout = (permLogout = false) =>
    actions.onLogout({
      userName: userCreds.userName,
      effectiveRole: userCreds.roles,
      channel: userCreds.channels[0],
      permLogout
    });

  if (isLoggedIn && !isIdle && timeRemaining <= IDEL_TIMER_VALUES.showLogoutModal) onIdle();
  if (isLoggedIn && timeRemaining <= 0) handleLogout();

  return (
    <div className={`${theme} main-wrapper`}>
      {header}
      <div>
        <IdleTimer
          ref={idleTimer}
          onActive={onAction}
          onAction={onAction}
          startOnMount={false}
          element={document}
          timeout={IDEL_TIMER_VALUES.timeRemaining}
          debounce={IDEL_TIMER_VALUES.debounce}
        />
        {routes}
      </div>
      <ModalContainer
        theme={theme}
        isOpen={logoutModal}
        toggle={() => handleClose()}
        header="You have been idle!">
        <p>You have been inactive for a while. Do you wish to logout?</p>
        <br />
        <span className="d-flex justify-content-between">
          <Button size="sm" color="success" onClick={refreshToken}>
            Stay
          </Button>
          <span>
            <Button
              outline
              size="sm"
              color="primary"
              className="me-2"
              onClick={() => handleLogout(false)}>
              Logout
            </Button>
            <Button size="sm" color="danger" onClick={() => handleLogout(true)}>
              Logout <small>for today</small>
            </Button>
          </span>
        </span>
      </ModalContainer>
    </div>
  );
};

Layout.propTypes = {
  theme: PropTypes.string.isRequired,
  userCreds: PropTypes.object.isRequired,
  session: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  sandboxStatus: PropTypes.string.isRequired
};

export default Layout;
