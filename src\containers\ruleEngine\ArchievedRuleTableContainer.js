import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as ruleConfiguratorActions from 'actions/ruleConfiguratorActions';
import ArchievedRuleTable from 'components/ruleEngine/ArchievedRuleTable';

const mapStateToProps = (state) => ({
  ruleList: state.ruleConfigurator.archievedRules,
  role: state.auth.userCreds.roles,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  ruleConfiguratorActions: bindActionCreators(ruleConfiguratorActions, dispatch)
});

const ArchievedRuleTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ArchievedRuleTable);

export default ArchievedRuleTableContainer;
