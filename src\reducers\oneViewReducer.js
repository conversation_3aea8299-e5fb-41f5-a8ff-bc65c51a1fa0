import { xor } from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_REVIEWER_PARK_CASE_SUCCESS,
  ON_FETCH_REVIEWER_CASE_LOADING,
  ON_FETCH_REVIEWER_CASE_SUCCESS,
  ON_FETCH_REVIEWER_CASE_FAILURE,
  ON_REVIEWER_CASE_CLOSE_SUCCESS,
  ON_REVIEWER_ADD_TO_CASE_SUCCESS,
  ON_REVIEWER_CASE_NOTIFICATION_SUCCESS
} from 'constants/actionTypes';

import initialState from './initialState';

export default function oneViewReducer(state = initialState.oneView, action) {
  switch (action.type) {
    case ON_FETCH_REVIEWER_CASE_LOADING:
      return objectAssign({}, state, {
        case: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_REVIEWER_CASE_SUCCESS:
      return objectAssign({}, state, {
        case: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_REVIEWER_CASE_FAILURE:
      return objectAssign({}, state, {
        case: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_REVIEWER_CASE_CLOSE_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, state.case, {
          data: objectAssign({}, state.case.data, {
            investigationStatus: 'Closed',
            closeTimestamp: new Date().toString(),
            ...action.response
          })
        })
      });
    case ON_REVIEWER_ADD_TO_CASE_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, state.case, {
          data: objectAssign({}, state.case.data, {
            childTxns: xor(state.case.data?.childTxns || [], action.response)
          })
        })
      });
    case ON_REVIEWER_CASE_NOTIFICATION_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, state.case, {
          data: objectAssign({}, state.case.data, {
            [action?.response?.communicationHeader]: 1
          })
        })
      });
    case ON_REVIEWER_PARK_CASE_SUCCESS:
      return objectAssign({}, state, {
        case: objectAssign({}, state.case, {
          data: objectAssign({}, state.case.data, {
            investigationStatus: 'Parked'
          })
        })
      });
    default:
      return state;
  }
}
