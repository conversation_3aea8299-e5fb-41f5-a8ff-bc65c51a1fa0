import React, { useEffect } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { Input } from 'reactstrap';
import { getCombinedRuleNames } from 'constants/functions';

const CombinedViolationDropdown = ({
  ruleNames,
  channels,
  fetchRuleNamesList,
  name,
  onChange,
  defaultOption,
  value = undefined
}) => {
  useEffect(() => {
    channels.map((channel) => {
      if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
    });
  }, []);

  let combinedRuleNames = getCombinedRuleNames(channels, ruleNames);

  const inputProps = value ? { value } : {};

  return (
    <Input
      type="select"
      name={name || 'rule'}
      onChange={(e) => onChange(e.target.value)}
      {...inputProps}>
      <option value="">{defaultOption}</option>
      {ruleNames.loader || ruleNames.error || _.isEmpty(combinedRuleNames) ? (
        <option disabled>
          {ruleNames.loader
            ? 'Loading...'
            : ruleNames.error
            ? ruleNames.errorMessage
            : 'No rules found'}
        </option>
      ) : (
        _.map(combinedRuleNames, (d) => (
          <option key={d?.code} value={d?.code}>
            {d?.name}
          </option>
        ))
      )}
    </Input>
  );
};

CombinedViolationDropdown.propTypes = {
  ruleNames: PropTypes.object.isRequired,
  channels: PropTypes.array.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  name: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  defaultOption: PropTypes.string.isRequired
};

export default CombinedViolationDropdown;
