import _ from 'lodash';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';
import { Button, ButtonGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';

import Log from 'containers/common/LogContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';

const CaseDetails = ({ selectedCase, actions, txnDetails }) => {
  const history = useHistory();
  const caseInfo = history.location.state.caseInfo;
  useEffect(() => {
    if (!history.location.state) history.push('/release-funds');
    document.title = 'BANKiQ FRC | Release Fund - ' + caseInfo.txnId;

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  useEffect(() => {
    let currentCase = {
      customerId: caseInfo.merchantId,
      txnTimestamp: caseInfo.txnTimestamp,
      txnId: caseInfo.txnId,
      channel: selectedCase.channel
    };
    actions.onSelectCase(currentCase);
  }, [caseInfo, selectedCase]);

  const handleBackClick = () => {
    history.push('/release-funds');
    actions.onClearSelectedCase();
  };

  const cognitiveResponse = !_.isEmpty(txnDetails.details.cognitiveResponse)
    ? JSON.parse(txnDetails.details.cognitiveResponse)
    : {};
  const cognitiveViolations = cognitiveResponse?.unusualMethods || [];

  return (
    <div className={'content-wrapper'}>
      <ButtonGroup className="mb-3">
        <Button outline size="sm" color="secondary" onClick={() => handleBackClick()}>
          <FontAwesomeIcon icon={faChevronLeft} className="me-3" />
          Back
        </Button>
      </ButtonGroup>
      <CaseDetailCard action={null} caseDetails={selectedCase} channel="frm" />
      <TransactionDetailCardContainer channel={'frm'} />
      {!_.isEmpty(txnDetails.details) && (
        <ViolatedRulesCardContainer
          transactionId={selectedCase.txnId}
          txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
          reViolatedRules={txnDetails?.details?.reViolatedRules || []}
          cognitiveViolations={cognitiveViolations}
          channel={selectedCase.channel}
        />
      )}
      <Log module="Case" id={selectedCase.caseRefNo} />
      <ApprovalModalContainer
        caseId={selectedCase.caseRefNo || ''}
        status={selectedCase.currentStatus || ''}
        channel={'frm'}
      />
    </div>
  );
};

CaseDetails.propTypes = {
  actions: PropTypes.object.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired
};

export default CaseDetails;
