import _ from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_SELECTED_CASE_LOGS_LOADING,
  ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS,
  ON_FETCH_SELECTED_CASE_LOGS_FAILURE,
  ON_FETCH_SELECTED_ENTITY_LOGS_LOADING,
  ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS,
  ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function logsReducer(state = initialState.logs, action) {
  switch (action.type) {
    case ON_FETCH_SELECTED_CASE_LOGS_LOADING:
      return objectAssign({}, state, {
        caseLogs: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS:
      return objectAssign({}, state, {
        caseLogs: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SELECTED_CASE_LOGS_FAILURE:
      return objectAssign({}, state, {
        caseLogs: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_SELECTED_ENTITY_LOGS_LOADING:
      return objectAssign({}, state, {
        entityLogs: objectAssign({}, state.entityLogs, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS:
      return objectAssign({}, state, {
        entityLogs: {
          entityId: action.entityId,
          filterCondition: action.filterCondition,
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          list:
            action.entityId === state.entityLogs.entityId &&
            _.isEqual(action.filterCondition, state.entityLogs.filterCondition)
              ? _.unionWith(state.entityLogs.list, action.response.statusLogs, _.isEqual)
              : action.response.statusLogs,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE:
      return objectAssign({}, state, {
        entityLogs: objectAssign({}, state.entityLogs, {
          entityId: action.entityId,
          filterCondition: action.filterCondition,
          count: action.entityId === state.entityLogs.entityId ? state.entityLogs.count : 0,
          list:
            action.entityId === state.entityLogs.entityId &&
            _.isEqual(action.filterCondition, state.entityLogs.filterCondition)
              ? state.entityLogs.list
              : [],
          isLastPage:
            action.entityId === state.entityLogs.entityId ? state.entityLogs.isLastPage : true,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
