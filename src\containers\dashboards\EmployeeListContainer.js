import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchEmployeesSla } from 'actions/slaDashboardActions';
import EmployeeList from 'components/dashboards/EmployeeList';

const mapStateToProps = (state) => ({
  employeeSla: state.slaDashboard.employeeSla
});

const mapDispatchToProps = (dispatch) => ({
  fetchEmployeeSla: bindActionCreators(onFetchEmployeesSla, dispatch)
});

const EmployeeListContainer = connect(mapStateToProps, mapDispatchToProps)(EmployeeList);

export default EmployeeListContainer;
