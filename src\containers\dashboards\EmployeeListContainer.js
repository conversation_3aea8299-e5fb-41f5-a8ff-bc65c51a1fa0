import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchEmployeesSla } from 'actions/slaDashboardActions';
import EmployeeList from 'components/dashboards/EmployeeList';

const mapStateToProps = (state) => {
  return {
    employeeSla: state.slaDashboard.employeeSla
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchEmployeeSla: bindActionCreators(onFetchEmployeesSla, dispatch)
  };
};

const EmployeeListContainer = connect(mapStateToProps, mapDispatchToProps)(EmployeeList);

export default EmployeeListContainer;
