import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onClearSelectedCase, onSelectCase } from 'actions/caseReviewActions';
import AuditCaseDetails from 'components/audit/AuditCaseDetails';

const mapStateToProps = (state) => {
  return {
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase,
    documentStatus: state.releaseFunds.documentStatus
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    selectCase: bindActionCreators(onSelectCase, dispatch),
    clearSelectedCase: bindActionCreators(onClearSelectedCase, dispatch)
  };
};

const AuditCaseDetailsContainer = connect(mapStateToProps, mapDispatchToProps)(AuditCaseDetails);

export default AuditCaseDetailsContainer;
