/* eslint-disable react/prop-types */
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { includes, isEmpty, lowerCase } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import Datetime from 'react-datetime';
import ReactTable from 'react-table';
import { FormGroup, Label, Input, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';
import VerdictModalContainer from 'containers/common/VerdictModalContainer';

const BulkClosure = ({
  ruleConfigurator,
  closureCases,
  fetchRulesList,
  showFailureAlert,
  toggleVerdictModal,
  fetchCasesForClosure,
  clearBulkClosureSearch
}) => {
  const [rule, setRule] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectedCases, setSelectedCases] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [searchData, setSearchData] = useState({});

  useEffect(() => {
    clearBulkClosureSearch();
  }, []);

  useEffect(() => {
    if (isEmpty(ruleConfigurator.list['frm'])) fetchRulesList('frm');
  }, [fetchRulesList, ruleConfigurator]);

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCases([]);
      setSelectAll(false);
    } else {
      const caseRefNos = closureCases.list.map((d) => d.caseRefNo);
      setSelectedCases(caseRefNos);
      setSelectAll(true);
    }
  };

  const handleCheckboxChange = (caseRefNo) => {
    let checkedCases = [...selectedCases];
    const index = checkedCases.indexOf(caseRefNo);
    if (index > -1) {
      checkedCases.splice(index, 1);
      setSelectedCases([...checkedCases]);
    } else {
      checkedCases = [...checkedCases, caseRefNo];
      setSelectedCases([...checkedCases]);
    }

    checkedCases.length === closureCases.list.length ? setSelectAll(true) : setSelectAll(false);
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    const dateRegex = /[2][0-9]{3}-[0-1][0-9]-[0-3][0-9] [0-2][0-9]:[0-5][0-9]:[0-5][0-9]/;
    const validStartDate = !isEmpty(startDate) ? dateRegex.test(startDate) : true;
    const validEndDate = !isEmpty(endDate) ? dateRegex.test(endDate) : true;

    if (validStartDate && validEndDate) {
      const formData = {
        ruleId: rule,
        from_txn_timeStamp: moment(startDate).format('YYYY-MM-DD HH:mm:ss'),
        to_txn_timeStamp: moment(endDate).format('YYYY-MM-DD HH:mm:ss')
      };
      fetchCasesForClosure(formData);
      setSearchData(formData);
      setSelectedCases([]);
      setSelectAll(false);
    } else showFailureAlert({ message: 'Please provide valid search input' });
  };

  const violationOptions = ruleConfigurator.list['frm'].map((d) => (
    <option key={d.code} value={d.code}>
      {d.name}
    </option>
  ));

  const tableHeader = [
    {
      Header: <Input type="checkbox" onChange={() => handleSelectAll()} checked={selectAll} />,
      accessor: 'caseRefNo',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 20,
      Cell: ({ value }) => (
        <Input
          type="checkbox"
          value={value}
          name="tableSelect[]"
          checked={includes(selectedCases, value)}
          onChange={() => handleCheckboxChange(value)}
        />
      )
    },
    { Header: 'Transaction ID', accessor: 'txnId' },
    { Header: 'Transaction Type', accessor: 'txnTypeName' },
    { Header: 'Terminal ID', accessor: 'terminalId' },
    {
      Header: 'Transaction Timestamp',
      accessor: 'txnTimestamp',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      Cell: ({ value }) => `Rs.${value}`,
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Verdict',
      accessor: 'ifrmVerdict',
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
        </select>
      )
    }
  ];

  return (
    <CardContainer title="Search transactions for closure">
      <form
        id="searchform"
        className="d-flex flex-wrap justify-content-around align-items-end"
        onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Rule</Label>
          <Input
            type="select"
            name="rule"
            value={rule}
            onChange={(e) => setRule(e.target.value)}
            required>
            <option value=""> select rule </option>
            {violationOptions}
          </Input>
        </FormGroup>
        <FormGroup>
          <Label>Start Date</Label>
          <Datetime
            name="startDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            value={startDate}
            onChange={(dateObj) => setStartDate(dateObj._d)}
            inputProps={{ required: true }}
          />
        </FormGroup>
        <FormGroup>
          <Label>End Date</Label>
          <Datetime
            name="endDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            value={endDate}
            onChange={(dateObj) => setEndDate(dateObj._d)}
            inputProps={{ required: true }}
          />
        </FormGroup>
        <FormGroup>
          <Button size="sm" type="submit" color="primary">
            <FontAwesomeIcon icon={faSearch} />
          </Button>
        </FormGroup>
      </form>
      <div id="searchresult">
        {(() => {
          if (closureCases.loader) return <TableLoader />;

          if (closureCases.error)
            return (
              <div className="no-data-div no-data-card-padding">{closureCases.errorMessage}</div>
            );

          if (isEmpty(closureCases.list))
            return <div className="no-data-div no-data-card-padding">No transactions found</div>;

          return (
            <div>
              <br />
              <span className="d-flex justify-content-end bulk-closure-btn">
                <Button
                  color="success"
                  disabled={isEmpty(selectedCases)}
                  onClick={() => toggleVerdictModal()}>
                  Close Cases
                </Button>
              </span>
              <ReactTable
                defaultFilterMethod={(filter, row) =>
                  row[filter.id] && includes(lowerCase(row[filter.id]), lowerCase(filter.value))
                }
                columns={tableHeader}
                data={closureCases.list}
                noDataText="No transactions found"
                filterable
                showPaginationTop={true}
                showPaginationBottom={false}
                pageSizeOptions={[5, 10, 20, 30, 40, 50]}
                defaultPageSize={10}
                minRows={6}
                className="-highlight  -striped"
              />
            </div>
          );
        })()}
      </div>

      <VerdictModalContainer
        channel="frm"
        // eslint-disable-next-line jsx-a11y/aria-role
        role="supervisor"
        bulkCaseIds={selectedCases}
        searchData={searchData}
        singleType="/bulk"
      />
    </CardContainer>
  );
};

BulkClosure.propTypes = {
  ruleConfigurator: PropTypes.object.isRequired,
  closureCases: PropTypes.object.isRequired,
  fetchRulesList: PropTypes.func.isRequired,
  showFailureAlert: PropTypes.func.isRequired,
  toggleVerdictModal: PropTypes.func.isRequired,
  fetchCasesForClosure: PropTypes.func.isRequired,
  clearBulkClosureSearch: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired
};

export default BulkClosure;
