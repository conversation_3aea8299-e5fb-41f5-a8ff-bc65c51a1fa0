import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchNotifications } from 'actions/notificationActions';
import NotificationCounter from 'components/notifications/NotificationCounter';

const mapStateToProps = (state) => {
  return {
    notifications: state.notifications
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchNotifications: bindActionCreators(onFetchNotifications, dispatch)
  };
};

const NotificationCounterContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NotificationCounter);

export default NotificationCounterContainer;
