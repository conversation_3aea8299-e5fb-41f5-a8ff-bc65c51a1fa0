import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { lowerCase, find, isEmpty } from 'lodash';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import ModalContainer from 'components/common/ModalContainer';

const RequestForInformationButton = ({
  theme,
  txnDetails,
  entityInfo,
  selectedCase,
  requestForInformationModal,
  showFailureAlert,
  requestForInformation,
  toggleRequestForInformationModal
}) => {
  const [requestedInfo, setRequestedInfo] = useState('');

  function getAccountContactEmail(entityCategory, entityInfo) {
    if (entityCategory == 'customer') {
      const custAccNo = txnDetails?.details?.payerAccount?.payerAccountNumber?.value;
      const accountDetails = find(entityInfo?.account, (d) => d.accountNo.value === custAccNo);
      const email =
        (!isEmpty(accountDetails) && accountDetails?.rel_manager_email) ||
        accountDetails?.branch_manager_email;
      return email;
    }

    if (entityCategory === 'merchant') {
      return entityInfo?.account?.rel_manager_email || entityInfo?.account?.branch_manager_email;
    }

    return null;
  }

  const handleSubmit = (e) => {
    e.preventDefault();
    const entityCategory = lowerCase(txnDetails?.details?.entityCategory);
    const email = getAccountContactEmail(entityCategory, entityInfo[entityCategory]?.details);
    if (!isEmpty(email)) {
      let formData = {
        caseId: selectedCase.caseId,
        caseRefNo: selectedCase.caseRefNo,
        requestedInfo,
        email
      };

      requestForInformation(formData);
    } else {
      showFailureAlert({ message: 'No recepient found for request of information' });
    }
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="warning"
        className="ms-1"
        onClick={() => {
          toggleRequestForInformationModal();
          setRequestedInfo('');
        }}>
        Request For Information
      </Button>
      <ModalContainer
        size="md"
        theme={theme}
        header="Request for information"
        isOpen={requestForInformationModal}
        toggle={toggleRequestForInformationModal}>
        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>Details</Label>
            <Input
              type="textarea"
              name="requestedInfo"
              id="requestedInfo"
              placeholder="Add details of information required"
              onChange={(e) => setRequestedInfo(e.target.value)}
              value={requestedInfo}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button type="submit" size="sm" color="primary">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

RequestForInformationButton.propTypes = {
  theme: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  entityInfo: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  requestForInformationModal: PropTypes.bool.isRequired,
  showFailureAlert: PropTypes.func.isRequired,
  requestForInformation: PropTypes.func.isRequired,
  toggleRequestForInformationModal: PropTypes.func.isRequired
};

export default RequestForInformationButton;
