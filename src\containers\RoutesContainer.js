import { connect } from 'react-redux';

import Routes from 'components/Routes';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  channel: state.auth.userCreds.channels,
  moduleType: state.auth.moduleType,
  loginType: state.auth.loginType,
  hasHoldAndRelease: state.user.configurations.holdAndRelease,
  hasKnowageReports: state.user.configurations.knowageReport
});

const RoutesContainer = connect(mapStateToProps, null)(Routes);

export default RoutesContainer;
