'use strict';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import RuleListTable from 'containers/ruleEngine/RuleListTableContainer';
import DynamicCountersListTable from 'containers/ruleEngine/DynamicCountersListTableContainer';
import CognitiveStatusCard from 'containers/ruleEngine/CognitiveStatusCardContainer';
import { map, upperCase, includes } from 'lodash';

const RuleEngine = ({ channels, userRoles, hasCognitive, hasSandbox, fetchSandboxHistory }) => {
  useEffect(() => {
    document.title = 'BANKiQ FRC | Rule Engine';
    if (includes(channels, 'frm') && hasSandbox === 1) fetchSandboxHistory();

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const tabNames = map(channels, (tab) => upperCase(tab));

  let ruleList =
    channels.length == 1 ? (
      <>
        {userRoles == 'supervisor' && hasCognitive === 1 && (
          <CognitiveStatusCard channel={channels[0]} />
        )}
        <RuleListTable channel={channels[0]} />
        <DynamicCountersListTable channel={channels[0]} />
      </>
    ) : (
      <Tabs tabNames={tabNames} pills>
        {channels.map((channel, i) => {
          return (
            <TabPane tabId={i} key={channel}>
              {userRoles == 'supervisor' && hasCognitive === 1 && (
                <CognitiveStatusCard channel={channel} />
              )}
              <RuleListTable channel={channel} />
              <DynamicCountersListTable channel={channel} />
            </TabPane>
          );
        })}
      </Tabs>
    );

  return <div className={'content-wrapper '}>{ruleList}</div>;
};

RuleEngine.propTypes = {
  channels: PropTypes.array.isRequired,
  userRoles: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  hasCognitive: PropTypes.number.isRequired,
  fetchSandboxHistory: PropTypes.func.isRequired
};

export default RuleEngine;
