'use strict';
import { map, upperCase, includes } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useMemo } from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import CognitiveStatusCard from 'containers/ruleEngine/CognitiveStatusCardContainer';
import DynamicCountersListTable from 'containers/ruleEngine/DynamicCountersListTableContainer';
import RuleListTable from 'containers/ruleEngine/RuleListTableContainer';

const RuleEngine = ({ channels, userRoles, hasCognitive, hasSandbox, fetchSandboxHistory }) => {
  useEffect(() => {
    document.title = 'BANKiQ FRC | Rule Engine';
    if (includes(channels, 'frm') && hasSandbox === 1) fetchSandboxHistory();

    return () => {
      document.title = 'BANKiQ FRC';
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Memoize expensive computations
  const tabNames = useMemo(() => map(channels, (tab) => upperCase(tab)), [channels]);

  const showCognitive = useMemo(
    () => userRoles === 'supervisor' && hasCognitive === 1,
    [userRoles, hasCognitive]
  );

  // Memoize the rule list content to prevent unnecessary re-renders
  const ruleList = useMemo(() => {
    if (channels.length === 1)
      return (
        <>
          {showCognitive && <CognitiveStatusCard channel={channels[0]} />}
          <RuleListTable channel={channels[0]} />
          <DynamicCountersListTable channel={channels[0]} />
        </>
      );

    return (
      <Tabs tabNames={tabNames} pills>
        {channels.map((channel, i) => (
          <TabPane tabId={i} key={channel}>
            {showCognitive && <CognitiveStatusCard channel={channel} />}
            <RuleListTable channel={channel} />
            <DynamicCountersListTable channel={channel} />
          </TabPane>
        ))}
      </Tabs>
    );
  }, [channels, tabNames, showCognitive]);

  return <div className="content-wrapper ">{ruleList}</div>;
};

RuleEngine.propTypes = {
  channels: PropTypes.array.isRequired,
  userRoles: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  hasCognitive: PropTypes.number.isRequired,
  fetchSandboxHistory: PropTypes.func.isRequired
};

export default React.memo(RuleEngine);
