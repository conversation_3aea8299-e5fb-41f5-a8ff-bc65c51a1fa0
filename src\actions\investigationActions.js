import {
  ON_<PERSON><PERSON><PERSON>_SIMILAR_TXNS_LOADING,
  ON_<PERSON>ET<PERSON>_SIMILAR_TXNS_FAILURE,
  ON_SUCCESSFUL_FETCH_SIMILAR_TXNS,
  ON_CUSTOMER_TRENDS_FETCH_LOADING,
  ON_CUSTOMER_TRENDS_FETCH_FAILURE,
  ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS,
  ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS,
  ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleVerdictModal } from 'actions/toggleActions';
import client from 'utility/apiClient';

function fetchCustomerTrend(formData) {
  return client({
    method: 'POST',
    url: `uds/${formData.channel}/trends/customer/${formData.customerId}?timeTrendGraph=true&perPayeeTrendGraph=true`,
    data: formData?.filter,
    badRequestMessage: 'Currently unable to fetch trends'
  });
}

function onFetchCustomerTrendLoading() {
  return { type: ON_CUSTOMER_TRENDS_FETCH_LOADING };
}

function onSuccessFetchCustomerTrend(response) {
  return {
    type: ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH,
    response
  };
}

function onFetchCustomerTrendFailure(response) {
  return {
    type: ON_CUSTOMER_TRENDS_FETCH_FAILURE,
    response
  };
}

function onFetchCustomerTrend(formData) {
  return function (dispatch) {
    dispatch(onFetchCustomerTrendLoading());
    return fetchCustomerTrend(formData).then(
      (success) => dispatch(onSuccessFetchCustomerTrend(success)),
      (error) => dispatch(onFetchCustomerTrendFailure(error))
    );
  };
}

function fetchSimilarTransactions(formData, channel) {
  return client({
    method: 'POST',
    url: `casereview/similar/${channel}/txns`,
    data: formData,
    badRequestMessage: 'No similar transaction found.'
  });
}

function onFetchSimilarTransactionsLoading() {
  return { type: ON_FETCH_SIMILAR_TXNS_LOADING };
}

function onSuccessFetchSimilarTransactions(response, similarTxnCategory) {
  return {
    type: ON_SUCCESSFUL_FETCH_SIMILAR_TXNS,
    response,
    similarTxnCategory
  };
}

function onFetchSimilarTransactionsFailure(response, similarTxnCategory) {
  return {
    type: ON_FETCH_SIMILAR_TXNS_FAILURE,
    response,
    similarTxnCategory
  };
}

function onFetchSimilarTransactions(formData, channel) {
  return function (dispatch) {
    dispatch(onFetchSimilarTransactionsLoading());
    return fetchSimilarTransactions(formData, channel).then(
      (success) =>
        dispatch(onSuccessFetchSimilarTransactions(success, formData.similarTxnCategory)),
      (error) => dispatch(onFetchSimilarTransactionsFailure(error, formData.similarTxnCategory))
    );
  };
}

function escalateByMail(formData) {
  let params = {
    mobileNo: formData.mobileNo,
    npciTransactionId: formData.id,
    recipients: [formData.to],
    subject: formData.subject,
    body: formData.message,
    from: formData.from
  };

  return client({
    method: 'PUT',
    url: `investigation/case/user/escalate/mailer`,
    data: params,
    badRequestMessage: 'Unable to send mail'
  });
}

function onEscalateByMail(formData) {
  return function (dispatch) {
    return escalateByMail(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Mail sent successfully' }));
        dispatch(onToggleVerdictModal('frm'));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchSimilarTxnCategoryList() {
  return client({ url: `casereview/similar/txns/category` });
}

function onFetchSimilarTxnCategoryListLoading() {
  return { type: ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING };
}

function onFetchSimilarTxnCategoryListSuccess(response) {
  return {
    type: ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS,
    response
  };
}

function onFetchSimilarTxnCategoryListFailure(response) {
  return {
    type: ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE,
    response
  };
}

function onFetchSimilarTxnCategoryList() {
  return function (dispatch) {
    dispatch(onFetchSimilarTxnCategoryListLoading());
    return fetchSimilarTxnCategoryList().then(
      (success) => dispatch(onFetchSimilarTxnCategoryListSuccess(success)),
      (error) => dispatch(onFetchSimilarTxnCategoryListFailure(error))
    );
  };
}

function fetchChannelwiseCounterpartyId() {
  return client({ url: `casereview/channel/list/identifiers` });
}

function onFetchChannelwiseCounterpartyIdLoading() {
  return { type: ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING };
}

function onFetchChannelwiseCounterpartyIdSuccess(response) {
  return {
    type: ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS,
    response
  };
}

function onFetchChannelwiseCounterpartyIdFailure(response) {
  return {
    type: ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE,
    response
  };
}

function onFetchChannelwiseCounterpartyId() {
  return function (dispatch) {
    dispatch(onFetchChannelwiseCounterpartyIdLoading());
    return fetchChannelwiseCounterpartyId().then(
      (success) => dispatch(onFetchChannelwiseCounterpartyIdSuccess(success)),
      (error) => dispatch(onFetchChannelwiseCounterpartyIdFailure(error))
    );
  };
}

export {
  onFetchCustomerTrend,
  onFetchSimilarTransactions,
  onEscalateByMail,
  onFetchSimilarTxnCategoryList,
  onFetchChannelwiseCounterpartyId
};
