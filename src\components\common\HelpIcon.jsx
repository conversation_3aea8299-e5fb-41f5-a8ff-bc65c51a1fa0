import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Popover, PopoverBody } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleInfo } from '@fortawesome/free-solid-svg-icons';

function HelpIcon({ id, size, text }) {
  const [displayPopover, setDisplayPopover] = useState(false);

  const popoverDisplay = () => setDisplayPopover(true);
  const popOverHide = () => setDisplayPopover(false);

  return (
    <>
      <FontAwesomeIcon
        id={id}
        icon={faCircleInfo}
        size={size}
        className="text-info mb-1"
        onFocus={popoverDisplay}
        onMouseOver={popoverDisplay}
        onMouseOut={popOverHide}
        onBlur={popOverHide}
      />
      <Popover
        target={id}
        trigger="focus"
        isOpen={displayPopover}
        toggle={() => setDisplayPopover(!displayPopover)}>
        <PopoverBody tag="div">{text}</PopoverBody>
      </Popover>
    </>
  );
}

HelpIcon.propTypes = {
  id: PropTypes.string.isRequired,
  size: PropTypes.string.isRequired,
  text: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired
};

export default HelpIcon;
