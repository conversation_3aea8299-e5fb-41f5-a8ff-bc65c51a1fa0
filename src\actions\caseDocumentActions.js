import { onShowFailure<PERSON>lert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader } from 'actions/toggleActions';
import {
  ON_FETCH_CASE_DOCUMENTS_LOADING,
  ON_FETCH_CASE_DOCUMENTS_SUCCESS,
  ON_FETCH_CASE_DOCUMENTS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchCaseDocuments(caseRefNo) {
  return client({ url: `casereview/case/${caseRefNo}/files` });
}

function onFetchCaseDocumentsLoading() {
  return { type: ON_FETCH_CASE_DOCUMENTS_LOADING };
}

function onFetchCaseDocumentsSuccess(response) {
  return { type: ON_FETCH_CASE_DOCUMENTS_SUCCESS, response };
}

function onFetchCaseDocumentsFailure(response) {
  return { type: ON_FETCH_CASE_DOCUMENTS_FAILURE, response };
}

function onFetchCaseDocuments(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchCaseDocumentsLoading());
    return fetchCaseDocuments(caseRefNo).then(
      (success) => dispatch(onFetchCaseDocumentsSuccess(success)),
      (error) => dispatch(onFetchCaseDocumentsFailure(error))
    );
  };
}

function submitCaseDocument(caseRefNo, formData) {
  const fileUpload = new FormData();
  fileUpload.append('file', formData?.file);
  return client({
    method: 'POST',
    url: `casereview/case/${caseRefNo}/upload`,
    multipart: true,
    headers: {
      processData: false,
      contentType: false
    },
    data: fileUpload
  });
}

function onSubmitCaseDocument(caseRefNo, formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitCaseDocument(caseRefNo, formData)
      .then(
        () => {
          dispatch(onFetchCaseDocuments(caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'Document uploaded successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function downloadCaseDocument(url) {
  return client({ url });
}

function onDownloadCaseDocument(url) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return downloadCaseDocument(url)
      .then(
        () => dispatch(onShowSuccessAlert({ message: 'Document downloaded successfully' })),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export { onFetchCaseDocuments, onSubmitCaseDocument, onDownloadCaseDocument };
