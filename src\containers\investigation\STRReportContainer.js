import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchSTRReportMasters, onFetchSTRReportDetails } from 'actions/strReportActions';
import STRReport from 'components/investigation/STRReport';

const mapStateToProps = (state) => {
  return {
    strReportDetails: state.strReport.details,
    strReportMasters: state.strReport.masters
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getMasters: bindActionCreators(onFetchSTRReportMasters, dispatch),
    getSTRReport: bindActionCreators(onFetchSTRReportDetails, dispatch)
  };
};

const STRReportContainer = connect(mapStateToProps, mapDispatchToProps)(STRReport);

export default STRReportContainer;
