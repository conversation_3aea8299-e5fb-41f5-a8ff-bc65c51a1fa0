import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import statisticsDetailsReducer from 'reducers/statisticsDetailsReducer';

describe('Statistics Details reducer', () => {
  it('should return the intial state', () => {
    expect(statisticsDetailsReducer(undefined, {})).toEqual(initialState.statisticsDetails);
  });

  it('should handle ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING
        }
      )
    ).toEqual({
      transactionStatistics: {
        details: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS,
          response: responses.statisticsDetails.transactionStatistics
        }
      )
    ).toEqual({
      transactionStatistics: {
        details: responses.statisticsDetails.transactionStatistics.data,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE,
          response: { message: 'Service unavailable. Contact administrator' }
        }
      )
    ).toEqual({
      transactionStatistics: {
        details: [],
        loader: false,
        error: true,
        errorMessage: 'Service unavailable. Contact administrator'
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING
        }
      )
    ).toEqual({
      customerStatistics: {
        details: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS,
          response: responses.statisticsDetails.customerStatistics
        }
      )
    ).toEqual({
      customerStatistics: {
        details: responses.statisticsDetails.customerStatistics.data,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE', () => {
    expect(
      statisticsDetailsReducer(
        {},
        {
          type: types.ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE,
          response: { message: 'Service unavailable. Contact administrator' }
        }
      )
    ).toEqual({
      customerStatistics: {
        details: [],
        loader: false,
        error: true,
        errorMessage: 'Service unavailable. Contact administrator'
      }
    });
  });
});
