import client from 'utility/apiClient';
import {
  ON_FETCH_CUSTOMER_EVENTS_LOADING,
  ON_FETCH_CUSTOMER_EVENTS_FAILURE,
  ON_FETCH_CUSTOMER_EVENTS_SUCCESS
} from 'constants/actionTypes';

function fetchCustomerEvents(customerId) {
  return client({
    url: `uds/customer/event-details/${customerId}`,
    badRequestMessage: 'Unable to fetch customer events',
    notFoundMessage: 'No customer events available'
  });
}

function onFetchCustomerEventsLoading(customerId) {
  return { type: ON_FETCH_CUSTOMER_EVENTS_LOADING, customerId };
}

function onSuccessfulFetchLoginTypes(response) {
  return {
    type: ON_FETCH_CUSTOMER_EVENTS_SUCCESS,
    response
  };
}

function onFetchCustomerEventsFailure(response) {
  return {
    type: ON_FETCH_CUSTOMER_EVENTS_FAILURE,
    response
  };
}

function onFetchCustomerEvents(customerId) {
  return function (dispatch) {
    dispatch(onFetchCustomerEventsLoading(customerId));
    return fetchCustomerEvents(customerId).then(
      (success) => dispatch(onSuccessfulFetchLoginTypes(success)),
      (error) => dispatch(onFetchCustomerEventsFailure(error))
    );
  };
}

export { onFetchCustomerEvents };
