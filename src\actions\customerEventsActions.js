import {
  ON_FETCH_CUSTOMER_EVENTS_LOADING,
  ON_FETCH_CUSTOMER_EVENTS_FAILURE,
  ON_FETCH_CUSTOMER_EVENTS_SUCCESS
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchCustomerEvents(customerId, custAccountNumber) {
  return client({
    url: `uds/customer/event-details/${customerId}/${custAccountNumber}`,
    badRequestMessage: 'Unable to fetch customer events',
    notFoundMessage: 'No customer events available'
  });
}

function onFetchCustomerEventsLoading(customerId, custAccountNumber) {
  return { type: ON_FETCH_CUSTOMER_EVENTS_LOADING, customerId, custAccountNumber };
}

function onSuccessfulFetchLoginTypes(response) {
  return {
    type: ON_FETCH_CUSTOMER_EVENTS_SUCCESS,
    response
  };
}

function onFetchCustomerEventsFailure(response) {
  return {
    type: ON_FETCH_CUSTOMER_EVENTS_FAILURE,
    response
  };
}

function onFetchCustomerEvents(customerId, custAccountNumber) {
  return function (dispatch) {
    dispatch(onFetchCustomerEventsLoading(customerId, custAccountNumber));
    return fetchCustomerEvents(customerId, custAccountNumber).then(
      (success) => dispatch(onSuccessfulFetchLoginTypes(success)),
      (error) => dispatch(onFetchCustomerEventsFailure(error))
    );
  };
}

export { onFetchCustomerEvents };
