import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { CardTitle, CardSubtitle, Row, Col, Input, Button, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

import RFIReportsTable from './RFIReportsTable';

function HRCReport({ hrcTrxns, fetchHRCTrxnsCount, fetchHRCTrxnsData }) {
  const [cummValue, setCummValue] = useState(hrcTrxns.cummValue);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchHRCTrxnsData({ pageNo: page + 1, pageSize, cummValue });
    setPageNo(page);
  };

  const columnHeaders = [
    { Header: 'Customer ID', accessor: 'customerIdentifier' },
    {
      Header: 'Customer Credit',
      columns: [
        {
          Header: 'Amount',
          accessor: 'totalCreditTxnAmountByCustomer',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnAmountByCustomer'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'totalCreditTxnCountByCustomer',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'totalCreditTxnCountByCustomer'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  const onSubmit = (e) => {
    e.preventDefault();
    if (cummValue !== hrcTrxns.cummValue && !isNaN(cummValue)) fetchHRCTrxnsCount({ cummValue });
  };

  return (
    <Row>
      <Col md="12" className="mb-3">
        <form onSubmit={onSubmit}>
          <Row>
            <Col xs="6" md="3">
              <Label htmlFor="cummValue">Total credit amount</Label>
              <Input
                type="number"
                id="cummValue"
                name="cummValue"
                value={cummValue}
                placeholder="Enter value to generate report"
                onChange={(e) => setCummValue(+e.target.value)}
                min={0}
                max={99999999999}
                pattern="\d{1,11}"
                title="Please input valid amount"
                required
              />
            </Col>
            <Col className="d-flex align-items-end">
              <Button outline size="sm" color="primary">
                Search
              </Button>
            </Col>
          </Row>
        </form>
      </Col>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{hrcTrxns.count?.value ?? 0}</CardTitle>
          <CardSubtitle># High Risk Countries</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="High Risk Countries">
          {hrcTrxns.count?.value > 0 ? (
            <RFIReportsTable
              count={hrcTrxns.count?.value}
              additionalHeaders={columnHeaders}
              data={hrcTrxns.data}
              filter={{ cummValue }}
              fetchData={fetchHRCTrxnsData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div">No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

HRCReport.propTypes = {
  hrcTrxns: PropTypes.object.isRequired,
  fetchHRCTrxnsCount: PropTypes.func.isRequired,
  fetchHRCTrxnsData: PropTypes.func.isRequired
};

export default HRCReport;
