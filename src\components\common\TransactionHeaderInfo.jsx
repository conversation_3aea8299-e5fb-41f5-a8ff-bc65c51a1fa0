import PropTypes from 'prop-types';
import React from 'react';

import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getHeaderInfoList = (details) => {
  const headerInfoList = [];

  addItemToList(
    details?.deviceInfo?.xAppidToken,
    'ID Token',
    details?.deviceInfo?.xAppidToken,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xAppAccessToken,
    'Access Token',
    details?.deviceInfo?.xAppAccessToken,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceType,
    'Device Type',
    details?.deviceInfo?.xDeviceType,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceCapability,
    'Device Capability',
    details?.deviceInfo?.xDeviceCapability,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceIp,
    'Device IP',
    details?.deviceInfo?.xDeviceIp,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceLocation,
    'Device Location',
    details?.deviceInfo?.xDeviceLocation,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceMobile,
    'Device Mobile',
    details?.deviceInfo?.xDeviceMobile,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xSignature,
    'Signature',
    details?.deviceInfo?.xSignature,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xTraceId,
    'Trace ID',
    details?.deviceInfo?.xTraceId,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xUserIdToken,
    'User ID Token',
    details?.deviceInfo?.xUserIdToken,
    headerInfoList
  );

  addItemToList(
    details?.deviceInfo?.xUserAccessToken,
    'User Access Token',
    details?.deviceInfo?.xUserAccessToken,
    headerInfoList
  );

  return headerInfoList;
};
function TransactionHeaderInfo({ details }) {
  const headerInfoList = getHeaderInfoList(details);

  if (headerInfoList.length === 0) return null;

  return (
    <div className="transaction-item">
      <b>Header Details</b>
      {renderDataColumnList(headerInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionHeaderInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionHeaderInfo;
