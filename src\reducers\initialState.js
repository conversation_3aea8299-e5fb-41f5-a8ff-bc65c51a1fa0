export default {
  auth: {
    userCreds: {
      email: '',
      userId: -1,
      userName: '',
      roles: '',
      channelRoles: [],
      channels: [],
      isFirstLogin: false
    },
    session: {
      isLoggedIn: false,
      sessionTimeout: null,
      isIdle: false,
      logoutModal: false
    },
    availableRoles: [],
    allRoles: [],
    errorMessage: '',
    moduleType: '',
    loginTypes: [],
    loginType: ''
  },
  user: {
    roles: [],
    shifts: [],
    stages: [],
    channels: [],
    userslist: [],
    unapprovedUserslist: [],
    partnerIdList: [],
    adminlist: [],
    externalCheckers: {
      list: [],
      error: false,
      errorMessage: ''
    },
    attributesList: {
      list: [],
      error: false,
      errorMessage: ''
    },
    hasMakerChecker: false,
    configurations: {
      cognitive: 0,
      holdAndRelease: 0,
      provisionalFields: 0,
      knowageReport: 0,
      caseCriteria: 0,
      sandbox: 0,
      localAuthentication: 0,
      tFA: 0,
      acquirerPortals: 0,
      facctum: 0,
      peerAdmin: 0,
      dualRole: 0,
      fnrUserCreation: 0,
      allowDebitFreeze: 0
    },
    provisionalFields: []
  },
  partnerBanks: {
    list: [],
    loader: false,
    error: false,
    errorMessage: ''
  },
  alert: {
    type: '',
    message: '',
    show: false
  },
  logs: {
    caseLogs: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    entityLogs: {
      entityId: '',
      list: [],
      count: 1,
      filterCondition: [],
      isLastPage: true,
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  toggle: {
    theme: 'light',
    loader: false,
    shiftModal: false,
    verdictModal: {
      frm: false,
      str: false
    },
    downloadSTRModal: false,
    addUserModal: false,
    addBankModal: false,
    approvalModal: false,
    createCaseModal: false,
    statusLogModal: false,
    blacklistModal: false,
    watchlistModal: false,
    prefiltersListModal: {
      specializedList: false,
      limitList: false,
      createListModal: false
    },
    releaseFundsModal: false,
    holdCaseModal: false,
    requestDocumentModal: false,
    confirmAlertModal: {
      specializedList: false,
      limitList: false
    },
    escalationModal: false,
    ruleCreateModal: {
      frm: false,
      str: false
    },
    ruleEditModal: {
      frm: false,
      str: false
    },
    ruleDuplicateModal: {
      frm: false,
      str: false
    },
    dynamicCountersCreateModal: false,
    assignShiftModal: false,
    updateUserRolesModal: false,
    prefilterModal: {
      frm: false,
      str: false
    },
    addToListConfirmAlertModal: false,
    requestForInformationModal: false,
    cpifrFormModal: false,
    userCaseCriteriaModal: false,
    createLabelModal: false,
    uploadLoader: false,
    ruleFeedbackModal: false,
    resetPasswordModal: false
  },
  violatedRules: {
    list: [],
    loader: false,
    error: false,
    errorMessage: '',
    transactionLoader: false
  },
  monitor: {
    transactions: [],
    totalCount: {
      frm: 0,
      str: 0
    },
    flaggedCount: 0,
    fraudCount: 0
  },
  caseAssignment: {
    liability: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    fraudTypes: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    buckets: {
      frm: {
        stats: {},
        loader: false,
        error: false,
        errorMessage: ''
      },
      str: {
        stats: {},
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    cases: {
      frm: {
        list: [],
        conf: {
          bucket: '',
          role: '',
          pageNo: 1,
          pageRecords: 10,
          sortBy: 'weight',
          sortOrder: 'desc',
          filterCondition: []
        },
        count: 0,
        isLastPage: true,
        loader: false,
        error: false,
        errorMessage: ''
      },
      str: {
        list: [],
        conf: {
          bucket: '',
          role: '',
          pageNo: 1,
          pageRecords: 10,
          sortBy: 'weight',
          sortOrder: 'desc',
          filterCondition: []
        },
        count: 0,
        isLastPage: true,
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    pastInvestigations: {
      frm: {
        entityId: '',
        isLastPage: true,
        count: 0,
        list: [],
        loader: false,
        error: false,
        errorMessage: '',
        pastInvestigationConfig: null
      },
      str: {
        entityId: '',
        isLastPage: true,
        count: 0,
        list: [],
        loader: false,
        error: false,
        errorMessage: '',
        pastInvestigationConfig: null
      }
    },
    closureCases: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    selectedCase: {},
    closeCaseBuckets: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    fraudTypesWithBuckets: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    xChannel: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    txnType: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    snoozeConditions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  notations: {
    master: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    case: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  investigation: {
    trends: {
      timeTrend: [],
      payeeTrend: [],
      timeRange: {},
      averageAmount: 0,
      loader: false,
      error: false,
      errorMessage: ''
    },
    transactionHistorySearch: {
      frm: {
        entityId: '',
        selectedDates: [],
        selectedCriterion: [],
        count: 0,
        isLastPage: true,
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      str: {
        entityId: '',
        selectedDates: [],
        selectedCriterion: [],
        count: 0,
        isLastPage: true,
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    transactionHistoryByStatus: {
      frm: {
        entityId: '',
        selectedDates: [],
        count: 0,
        isLastPage: true,
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      str: {
        entityId: '',
        selectedDates: [],
        count: 0,
        isLastPage: true,
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    similar: {
      similarTxnCategory: [],
      list: [],
      count: 0,
      isLastPage: true,
      loader: false,
      error: false,
      errorMessage: ''
    },
    similarTxnCategoryList: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    channelCounterpartyId: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  prefiltersList: {
    specializedListTypes: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    specializedList: {
      conf: {
        listName: '',
        categoryName: '',
        identifier: '',
        pageNumber: 1,
        pageRecords: 10
      },
      data: {
        listInfo: [],
        isLastPage: true,
        count: 0
      },
      fileUpload: {
        fileId: '',
        listType: ''
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    category: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    limitList: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    limitListWithPagination: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    limitType: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    allLists: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    addToListCurrentItems: {
      currentListInfo: {},
      currentIndex: '',
      currentCategoryName: '',
      currentIdentifier: ''
    },
    blockedIdentifiers: {
      txnId: '',
      data: [],
      loader: false,
      error: false,
      errorMessage: []
    }
  },
  ruleCreation: {
    channel: '',
    helperList: {
      frm: {},
      str: {},
      nrt: {}
    },
    actionList: [],
    alertCategories: [],
    validation: {
      message: '',
      status: false,
      ruleFalsePositiveData: {},
      ruleSimilarityData: {}
    },
    loader: false,
    error: false,
    errorMessage: '',
    nonProductionRules: {
      list: {
        frm: [],
        str: []
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    checkListOptions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    checkList: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    ruleChannels: [],
    fraudCategories: [],
    ruleLabels: []
  },
  dynamicCounters: {
    counters: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    conditionalAttributes: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    allAttributes: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    subAttributes: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  ruleConfigurator: {
    ruleNames: {
      list: {
        frm: [],
        str: []
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    productionRules: {
      list: {
        frm: [],
        str: []
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    archievedRules: {
      list: {
        frm: [],
        str: []
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    rulesWithConditions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  snoozeRules: {
    attributes: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    list: {
      frm: [],
      str: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  cognitiveStatus: {
    list: {
      frm: false,
      str: false
    },
    loader: false,
    error: false,
    errorMessage: ''
  },
  transactionDetails: {
    details: {},
    loader: false,
    error: false,
    errorMessage: ''
  },
  prefilter: {
    filter: {
      list: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    tps: {
      list: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    category: {
      list: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  scp: {
    configurationsData: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    decisionMatrixActions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    propensityScores: ''
  },
  uds: {
    merchant: {
      riskScore: {
        details: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    agent: {
      riskScore: {
        details: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    customer: {
      vulnerability: {
        riskScore: '',
        details: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    facctumData: {
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  releaseFunds: {
    fundsToBeRelease: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    documentStatus: {
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  profiling: {
    profilingList: {
      list: [],
      loader: false,
      error: false,
      errorMessage: '',
      selected: {}
    },

    customerTransactionSummary: {
      list: [],
      loader: false,
      error: false,
      errorMessage: '',
      isLastPage: true,
      count: 0
    },
    searchConditions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  demographicDetails: {
    merchant: {
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    agent: {
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    customer: {
      details: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  statisticsDetails: {
    transactionStatistics: {
      details: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    customerStatistics: {
      details: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  advanceSearchTxns: {
    filterCondition: {},
    isLastPage: true,
    count: 0,
    list: [],
    loader: false,
    error: false,
    errorMessage: ''
  },
  sandboxing: {
    dateRange: {
      data: {
        startTimestamp: '',
        endTimestamp: ''
      },
      loader: false,
      error: false,
      errorMessage: ''
    },
    testing: {
      status: '',
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    violationDetails: {
      date: '',
      list: [],
      count: 0,
      isLastPage: true,
      loader: false,
      error: false,
      errorMessage: ''
    },
    testHistory: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  citations: {
    caseRefNo: '',
    data: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    loader: false,
    error: false,
    errorMessage: ''
  },
  strReport: {
    masters: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    details: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    history: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    allReports: {
      conf: {},
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  settings: {
    data: [],
    loader: false,
    error: false,
    errorMessage: ''
  },
  caseDocument: {
    data: [],
    loader: false,
    error: false,
    errorMessage: ''
  },
  rfiReports: {
    highValueClosedAccount: {
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    highValueNewAccount: {
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    fraudToSaleRatio: {
      cummValue: '',
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    topMerchant: {
      cummValue: '',
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    nbfcTrxns: {
      cummValue: '',
      period: 'Day',
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    hrcTrxns: {
      cummValue: '',
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    unusualDeclineTurnover: {
      cummValue: '',
      count: {
        value: 0,
        loader: false,
        error: false,
        errorMessage: ''
      },
      data: {
        records: [],
        loader: false,
        error: false,
        errorMessage: ''
      }
    }
  },
  incidents: {
    masters: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    closedList: {
      conf: {
        sortBy: 'weight',
        sortOrder: 'desc',
        filterCondition: []
      },
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    unverifiedList: {
      list: [],
      conf: {
        bucket: 'Unverified',
        role: 'investigator',
        pageNo: 1,
        pageRecords: 10,
        sortBy: 'weight',
        sortOrder: 'desc',
        filterCondition: []
      },
      count: 0,
      isLastPage: true,
      loader: false,
      error: false,
      errorMessage: ''
    },
    priorityList: {
      list: [],
      conf: {
        bucket: 'Priority',
        role: 'investigator',
        pageNo: 1,
        pageRecords: 10,
        sortBy: 'weight',
        sortOrder: 'desc',
        filterCondition: []
      },
      count: 0,
      isLastPage: true,
      loader: false,
      error: false,
      errorMessage: ''
    },
    allIncidents: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    incident: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    history: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  slaDashboard: {
    shiftDetails: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    slaKpis: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    employeeSla: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    slaBreachCases: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    firstContactRate: {
      data: 0,
      loader: false,
      error: false,
      errorMessage: ''
    },
    occupancyRate: {
      data: 0,
      loader: false,
      error: false,
      errorMessage: ''
    },
    analystTAT: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    partnersCaseStats: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  businessDashboard: {
    businessKpis: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    actionShare: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    highAlertCustomers: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    noViolationFraud: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    ruleCategoryTrend: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  ruleDashboard: {
    stats: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    efficiency: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    efficacy: {
      data: 0,
      loader: false,
      error: false,
      errorMessage: ''
    },
    effectiveness: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    behaviour: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    ruleFeedbacks: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    ruleFeedbackAnalysis: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    },
    ruleFeedbackAnalysisStats: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  complianceDashboard: {
    fmrReportedCases: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  oneView: {
    case: {
      data: {},
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  customerCommunication: {
    call: {
      caseRefNo: '',
      status: '',
      timeStamp: ''
    },
    dispositions: {
      list: [],
      loader: false,
      error: false,
      errorMessage: ''
    },
    logs: {
      data: [],
      loader: false,
      error: false,
      errorMessage: ''
    }
  },
  notifications: {
    list: [],
    loader: false,
    error: false,
    errorMessage: ''
  },
  customerEvents: {
    customerId: '',
    custAccountNumber: '',
    list: [],
    loader: false,
    error: false,
    errorMessage: ''
  }
};
