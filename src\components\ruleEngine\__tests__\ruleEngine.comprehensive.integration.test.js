import { render, screen, fireEvent, act } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { createStore, combineReducers, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';

import performanceMonitor from 'utility/performanceMonitor';

import RuleEngine from '../RuleEngine';
import RuleForm from '../RuleForm';
import RuleListTable from '../RuleListTable';

// Mock all child containers
jest.mock('containers/ruleEngine/CognitiveStatusCardContainer', () =>
  jest.fn(() => <div data-testid="cognitive-status">Cognitive Status</div>)
);

jest.mock('containers/ruleEngine/RuleListTableContainer', () =>
  jest.fn(({ channel }) => <div data-testid={`rule-list-${channel}`}>Rule List - {channel}</div>)
);

jest.mock('containers/ruleEngine/DynamicCountersListTableContainer', () =>
  jest.fn(({ channel }) => <div data-testid={`counters-${channel}`}>Counters - {channel}</div>)
);

jest.mock('containers/ruleEngine/ProductionRuleTableContainer', () =>
  jest.fn(() => <div data-testid="production-rules">Production Rules</div>)
);

jest.mock('containers/ruleEngine/NonProductionRuleTableContainer', () =>
  jest.fn(() => <div data-testid="non-production-rules">Non-Production Rules</div>)
);

jest.mock('containers/ruleEngine/ArchievedRuleTableContainer', () =>
  jest.fn(() => <div data-testid="archived-rules">Archived Rules</div>)
);

jest.mock('containers/ruleEngine/RuleFormContainer', () =>
  jest.fn(() => <div data-testid="rule-form">Rule Form</div>)
);

// Create comprehensive mock store
const createIntegrationStore = () => {
  const initialState = {
    ruleCreation: {
      loader: false,
      error: false,
      errorMessage: '',
      actionList: Array.from({ length: 20 }, (_, i) => ({
        actionCode: `${i + 1}`.padStart(2, '0'),
        actionName: `ACTION_${i + 1}`
      })),
      alertCategories: Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        categoryName: `Category ${i + 1}`
      })),
      ruleChannels: [{ name: 'FRM' }, { name: 'STR' }, { name: 'RPSL' }],
      fraudCategories: Array.from({ length: 15 }, (_, i) => ({
        id: i + 1,
        name: `Fraud Category ${i + 1}`
      })),
      ruleLabels: Array.from({ length: 25 }, (_, i) => ({
        id: i + 1,
        name: `Label ${i + 1}`
      })),
      validation: { status: true, message: '' },
      helperList: {
        frm: {
          prefix: Array.from({ length: 50 }, (_, i) => ({ name: `PREFIX_${i}` })),
          functions: Array.from({ length: 30 }, (_, i) => ({
            name: `FUNCTION_${i}`,
            description: `Function ${i} description`
          })),
          counters: Array.from({ length: 40 }, (_, i) => ({
            name: `COUNTER_${i}`,
            description: `Counter ${i} description`
          })),
          parameters: Array.from({ length: 35 }, (_, i) => ({
            name: `PARAM_${i}`,
            description: `Parameter ${i} description`
          })),
          operators: [
            { name: '>', description: 'Greater than' },
            { name: '<', description: 'Less than' },
            { name: '==', description: 'Equal to' },
            { name: '!=', description: 'Not equal to' }
          ]
        },
        str: {
          prefix: Array.from({ length: 30 }, (_, i) => ({ name: `STR_PREFIX_${i}` })),
          functions: Array.from({ length: 20 }, (_, i) => ({
            name: `STR_FUNCTION_${i}`,
            description: `STR Function ${i} description`
          }))
        }
      },
      nonProductionRules: {
        list: {
          frm: Array.from({ length: 25 }, (_, i) => ({
            code: `NPR_${i}`,
            name: `Non-Production Rule ${i}`,
            logic: `TXN.AMOUNT > ${1000 + i * 100}`
          })),
          str: Array.from({ length: 15 }, (_, i) => ({
            code: `STR_NPR_${i}`,
            name: `STR Non-Production Rule ${i}`,
            logic: `STR.FIELD > ${500 + i * 50}`
          }))
        }
      }
    },
    ruleConfigurator: {
      productionRules: {
        list: {
          frm: Array.from({ length: 100 }, (_, i) => ({
            code: `PROD_${i}`,
            name: `Production Rule ${i}`,
            logic: `TXN.AMOUNT > ${2000 + i * 100}`,
            order: i + 1,
            status: i % 3 === 0 ? 'ACTIVE' : 'INACTIVE'
          })),
          str: Array.from({ length: 75 }, (_, i) => ({
            code: `STR_PROD_${i}`,
            name: `STR Production Rule ${i}`,
            logic: `STR.FIELD > ${1500 + i * 75}`,
            order: i + 1,
            status: i % 2 === 0 ? 'ACTIVE' : 'INACTIVE'
          }))
        }
      },
      archievedRules: {
        list: {
          frm: Array.from({ length: 50 }, (_, i) => ({
            code: `ARCH_${i}`,
            name: `Archived Rule ${i}`,
            logic: `TXN.AMOUNT > ${3000 + i * 150}`
          }))
        }
      }
    },
    auth: {
      moduleType: 'acquirer',
      userCreds: {
        roles: 'supervisor',
        channels: ['frm', 'str', 'rpsl']
      }
    },
    user: {
      hasMakerChecker: true,
      configurations: {
        sandbox: 1,
        cognitive: 1,
        acquirerPortals: 1
      }
    },
    toggle: {
      theme: 'light',
      ruleCreateModal: { frm: false, str: false },
      ruleEditModal: { frm: false, str: false },
      ruleDuplicateModal: { frm: false, str: false }
    },
    prefiltersList: {
      allLists: {
        data: Array.from({ length: 20 }, (_, i) => ({
          categoryName: `Category ${i}`,
          listName: `List ${i}`
        }))
      }
    },
    snoozeRules: {
      list: {
        frm: Array.from({ length: 10 }, (_, i) => ({
          ruleCode: `SNOOZE_${i}`,
          reason: `Snooze reason ${i}`
        }))
      }
    },
    sandboxing: {
      testHistory: Array.from({ length: 30 }, (_, i) => ({
        testId: `test_${i}`,
        ruleName: `Test Rule ${i}`,
        status: ['COMPLETED', 'FAILED', 'PENDING'][i % 3]
      }))
    }
  };

  const rootReducer = combineReducers({
    ruleCreation: (state = initialState.ruleCreation) => state,
    ruleConfigurator: (state = initialState.ruleConfigurator) => state,
    auth: (state = initialState.auth) => state,
    user: (state = initialState.user) => state,
    toggle: (state = initialState.toggle) => state,
    prefiltersList: (state = initialState.prefiltersList) => state,
    snoozeRules: (state = initialState.snoozeRules) => state,
    sandboxing: (state = initialState.sandboxing) => state
  });

  return createStore(rootReducer, applyMiddleware(thunk));
};

describe('RuleEngine Comprehensive Integration Tests', () => {
  let store;

  beforeEach(() => {
    store = createIntegrationStore();
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const renderWithProviders = (component) =>
    render(
      <Provider store={store}>
        <BrowserRouter>{component}</BrowserRouter>
      </Provider>
    );

  describe('Multi-Channel Performance', () => {
    it('should handle multiple channels efficiently', () => {
      const multiChannelProps = {
        channels: ['frm', 'str', 'rpsl'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      performanceMonitor.startTiming('multi_channel_integration');

      renderWithProviders(<RuleEngine {...multiChannelProps} />);

      const duration = performanceMonitor.endTiming('multi_channel_integration');
      expect(duration).toBeLessThan(300);

      // Verify all channels are rendered
      expect(screen.getByTestId('rule-list-frm')).toBeInTheDocument();
      expect(screen.getByTestId('rule-list-str')).toBeInTheDocument();
      expect(screen.getByTestId('rule-list-rpsl')).toBeInTheDocument();
    });

    it('should handle channel switching efficiently', () => {
      const multiChannelProps = {
        channels: ['frm', 'str', 'rpsl'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      renderWithProviders(<RuleEngine {...multiChannelProps} />);

      performanceMonitor.startTiming('channel_switching');

      // Simulate tab switching
      const tabs = ['FRM', 'STR', 'RPSL'];
      tabs.forEach((tab) => {
        const tabElement = screen.getByText(tab);
        fireEvent.click(tabElement);
      });

      const duration = performanceMonitor.endTiming('channel_switching');
      expect(duration).toBeLessThan(150);
    });
  });

  describe('Large Dataset Integration', () => {
    it('should handle large rule datasets across components', () => {
      const ruleListProps = {
        channel: 'frm',
        role: 'supervisor',
        toggle: store.getState().toggle,
        ruleCreation: store.getState().ruleCreation,
        ruleConfiguratorActions: {
          onFetchProductionRulesList: jest.fn(),
          onFetchNonProductionRulesList: jest.fn(),
          onFetchArchievedRulesList: jest.fn()
        },
        ruleCreationActions: {
          onFetchActionList: jest.fn(),
          onFetchAlertCategories: jest.fn(),
          onFetchRuleChannelsList: jest.fn(),
          onFetchRuleFraudCategoriesList: jest.fn(),
          onFetchRuleLabels: jest.fn(),
          onClearValidation: jest.fn(),
          onVerifyDSL: jest.fn()
        },
        toggleActions: {
          onToggleRuleCreateModal: jest.fn(),
          onToggleRuleEditModal: jest.fn(),
          onToggleRuleDuplicateModal: jest.fn()
        },
        onFetchSnoozeRulesList: jest.fn(),
        onShowFailureAlert: jest.fn(),
        fetchSnoozeRulesList: jest.fn()
      };

      performanceMonitor.startTiming('large_dataset_integration');

      renderWithProviders(<RuleListTable {...ruleListProps} />);

      const duration = performanceMonitor.endTiming('large_dataset_integration');
      expect(duration).toBeLessThan(400);
    });
  });

  describe('Memory Management Integration', () => {
    it('should not leak memory during complex component interactions', () => {
      const { rerender } = renderWithProviders(
        <RuleEngine
          channels={['frm']}
          userRoles="supervisor"
          hasCognitive={1}
          hasSandbox={1}
          fetchSandboxHistory={jest.fn()}
        />
      );

      performanceMonitor.takeMemorySnapshot('before_complex_interactions');

      // Simulate complex interactions
      for (let i = 0; i < 20; i++) {
        const newProps = {
          channels: i % 2 === 0 ? ['frm', 'str'] : ['frm'],
          userRoles: i % 3 === 0 ? 'supervisor' : 'checker',
          hasCognitive: i % 4 === 0 ? 1 : 0,
          hasSandbox: 1,
          fetchSandboxHistory: jest.fn()
        };

        rerender(
          <Provider store={store}>
            <BrowserRouter>
              <RuleEngine {...newProps} />
            </BrowserRouter>
          </Provider>
        );
      }

      performanceMonitor.takeMemorySnapshot('after_complex_interactions');
      performanceMonitor.checkMemoryLeaks();

      expect(true).toBe(true);
    });
  });

  describe('State Management Integration', () => {
    it('should handle Redux state updates efficiently', async () => {
      const ruleFormProps = {
        channel: 'frm',
        formName: 'create',
        formData: {
          name: '',
          description: '',
          logic: '',
          actionCode: '',
          methodType: 'GET'
        },
        ruleList: {
          ...store.getState().ruleConfigurator.productionRules,
          frm: store.getState().ruleConfigurator.productionRules.list.frm || []
        },
        ruleCreation: {
          ...store.getState().ruleCreation,
          nonProductionRules: {
            ...store.getState().ruleCreation.nonProductionRules,
            list: {
              ...store.getState().ruleCreation.nonProductionRules.list,
              frm: store.getState().ruleCreation.nonProductionRules.list.frm || []
            }
          }
        },
        fetchPrefilterLists: jest.fn(),
        ruleCreationActions: {
          onFetchActionList: jest.fn(),
          onFetchAlertCategories: jest.fn(),
          onFetchRuleChannelsList: jest.fn(),
          onFetchRuleFraudCategoriesList: jest.fn(),
          onFetchRuleLabels: jest.fn(),
          onClearValidation: jest.fn(),
          onVerifyDSL: jest.fn()
        },
        submit: jest.fn(),
        hasSandbox: 1,
        moduleType: 'acquirer',
        prefilterLists: store.getState().prefiltersList,
        combinedRuleList: [
          ...store.getState().ruleConfigurator.productionRules.list.frm,
          ...store.getState().ruleCreation.nonProductionRules.list.frm
        ],
        updateRuleData: jest.fn(),
        invalidateLogic: jest.fn(),
        resetValidation: jest.fn()
      };

      performanceMonitor.startTiming('redux_state_integration');

      renderWithProviders(<RuleForm {...ruleFormProps} />);

      const duration = performanceMonitor.endTiming('redux_state_integration');
      expect(duration).toBeLessThan(200);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle error states efficiently', () => {
      // Create store with error state
      const errorState = {
        ...store.getState(),
        ruleCreation: {
          ...store.getState().ruleCreation,
          error: true,
          errorMessage: 'Test error message',
          loader: false
        }
      };

      const errorStore = createStore(() => errorState);

      performanceMonitor.startTiming('error_state_handling');

      render(
        <Provider store={errorStore}>
          <BrowserRouter>
            <RuleEngine
              channels={['frm']}
              userRoles="checker"
              hasCognitive={0}
              hasSandbox={1}
              fetchSandboxHistory={jest.fn()}
            />
          </BrowserRouter>
        </Provider>
      );

      const duration = performanceMonitor.endTiming('error_state_handling');
      expect(duration).toBeLessThan(100);
    });
  });

  describe('Concurrent Operations Integration', () => {
    it('should handle multiple simultaneous operations', async () => {
      const multiOpProps = {
        channels: ['frm', 'str'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      renderWithProviders(<RuleEngine {...multiOpProps} />);

      performanceMonitor.startTiming('concurrent_operations');

      // Simulate concurrent operations
      await act(async () => {
        const operations = [
          () => fireEvent.click(screen.getByText('FRM')),
          () => fireEvent.click(screen.getByText('STR')),
          () => fireEvent.click(screen.getByText('FRM'))
        ];

        await Promise.all(operations.map((op) => op()));
      });

      const duration = performanceMonitor.endTiming('concurrent_operations');
      expect(duration).toBeLessThan(200);
    });
  });
});
