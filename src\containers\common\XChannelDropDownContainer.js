import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchXChannelList } from 'actions/caseReviewActions';
import XChannelDropDown from 'components/common/XChannelDropDown';

const mapStateToProps = (state) => ({
  xChannelData: state.caseAssignment.xChannel
});

const mapDispatchToProps = (dispatch) => ({
  getXChannelList: bindActionCreators(onFetchXChannelList, dispatch)
});

const XChannelDropDownContainer = connect(mapStateToProps, mapDispatchToProps)(XChannelDropDown);

export default XChannelDropDownContainer;
