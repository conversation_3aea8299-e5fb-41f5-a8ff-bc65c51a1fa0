import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import TransactionTable from 'components/common/TransactionTable';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  loginType: state.auth.loginType,
  ruleNames: state.ruleConfigurator.ruleNames,
  closeCaseBuckets: state.caseAssignment.closeCaseBuckets,
  channels: state.auth.userCreds.channels,
  hasProvisionalFields: state.user.configurations.provisionalFields
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch),
  fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
});

const TransactionTableContainer = connect(mapStateToProps, mapDispatchToProps)(TransactionTable);

export default TransactionTableContainer;
