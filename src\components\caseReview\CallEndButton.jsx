import React, { useState, useEffect } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhoneSlash } from '@fortawesome/free-solid-svg-icons';
import ModalContainer from 'components/common/ModalContainer';

function CallEndButton({
  theme,
  channel,
  callDetails,
  dispositions,
  endCall,
  fetchDispositionsList
}) {
  const [display, setDisplay] = useState(false);
  const [selectedDisposition, setSelectedDisposition] = useState(
    dispositions.list.length > 0 ? dispositions.list[0].id : ''
  );

  useEffect(() => {
    if (!dispositions.loader && !dispositions.error && dispositions.list.length === 0)
      fetchDispositionsList();
  }, []);

  const dispositionOptions = dispositions.loader ? (
    <option disabled value="">
      Loading...
    </option>
  ) : dispositions.error ? (
    <option disabled value="">
      {dispositions.errorMessage}
    </option>
  ) : (
    dispositions.list.map((d) => (
      <option key={d.id} value={d.id}>
        {d.name}
      </option>
    ))
  );

  const submit = (e) => {
    e.preventDefault();
    endCall(channel, callDetails.caseRefNo, selectedDisposition);
    setDisplay(false);
  };

  return (
    <div>
      <div id="CallStatus" className="call-container">
        {callDetails.status === 'PLACED' && (
          <span className="last-call-text">
            Call placed at {moment(callDetails.timeStamp).format('HH:mm')}
          </span>
        )}
        <Button
          data-testid="display-btn"
          color="danger"
          className="btn-circle"
          onClick={() => setDisplay(!display)}
          disabled={callDetails.status !== 'PLACED'}>
          <FontAwesomeIcon icon={faPhoneSlash} />
        </Button>
      </div>
      <ModalContainer
        header="Set disposition for call"
        isOpen={display}
        theme={theme}
        size="sm"
        toggle={() => setDisplay(!display)}>
        <form data-testid="form" onSubmit={submit}>
          <FormGroup>
            <Label htmlFor="disposition">Disposition</Label>
            <Input
              type="select"
              id="disposition"
              name="disposition"
              value={selectedDisposition}
              onChange={(e) => setSelectedDisposition(e.target.value)}
              required>
              {dispositionOptions}
            </Input>
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button size="sm" color="danger">
              End Call
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </div>
  );
}

CallEndButton.propTypes = {
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  callDetails: PropTypes.object.isRequired,
  dispositions: PropTypes.object.isRequired,
  endCall: PropTypes.func.isRequired,
  fetchDispositionsList: PropTypes.func.isRequired
};

export default CallEndButton;
