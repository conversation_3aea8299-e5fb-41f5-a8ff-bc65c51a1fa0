import { map } from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import GraphContainer from 'components/common/GraphContainer';

function RuleFeedbackAnalysisStats({
  theme,
  period,
  ruleFeedbackAnalysisStats,
  fetchFeedBackTimeRangeAnalysis,
  rule
}) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchFeedBackTimeRangeAnalysis({
        ruleCode: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const datediff = Moment.duration(Moment(period.endDate).diff(Moment(period.startDate))).days();

  const processedData = map(ruleFeedbackAnalysisStats.data, (item) => {
    const total = item.likes + item.dislikes;
    return {
      time: item.time,
      likes: (item.likes / total) * 100,
      dislikes: (item.dislikes / total) * 100
    };
  });

  const times = map(processedData, (item) =>
    Moment(item.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm')
  );
  const likes = map(processedData, (item) => item.likes);
  const dislikes = map(processedData, (item) => item.dislikes);

  const config = {
    tooltip: {
      trigger: 'axis',
      formatter(params) {
        return `
            Time: ${params[0]?.axisValue || 'N/A'}<br/>
  ${params.map((param) => `${param?.seriesName}: ${param?.value.toFixed(0)}%`).join('<br/>')}
        `;
      }
    },
    legend: {
      data: ['Positive', 'Negative']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: times
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value} %'
      }
    },
    series: [
      {
        name: 'Positive',
        type: 'line',
        data: likes
      },
      {
        name: 'Negative',
        type: 'line',
        data: dislikes
      }
    ],
    color: ['#3ba272', '#c1232b']
  };

  return (
    <GraphContainer
      theme={theme}
      config={config}
      className="card-height-500"
      title="Rule Feedback Time Range Analysis"
      noData={ruleFeedbackAnalysisStats.data?.length === 0}
      loader={ruleFeedbackAnalysisStats.loader}
      error={{
        flag: ruleFeedbackAnalysisStats.error,
        errorMessage: ruleFeedbackAnalysisStats.errorMessage
      }}
    />
  );
}

RuleFeedbackAnalysisStats.propTypes = {
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  rule: PropTypes.string.isRequired,
  ruleFeedbackAnalysisStats: PropTypes.object.isRequired,
  fetchFeedBackTimeRangeAnalysis: PropTypes.func.isRequired
};

export default RuleFeedbackAnalysisStats;
