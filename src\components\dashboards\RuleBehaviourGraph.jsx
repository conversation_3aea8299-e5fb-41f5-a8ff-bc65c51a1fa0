import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

import GraphContainer from 'components/common/GraphContainer';
import HelpIcon from 'components/common/HelpIcon';

function RuleBehaviourGraph({ theme, rule, period, ruleBehaviour, fetchRuleBehaviour }) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleBehaviour({
        ruleId: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const chartFraudData = ruleBehaviour?.data
    ?.filter((d) => d.caseVerdict == 'Fraud')
    .map((d) => [
      d.txnTypeName,
      d.entityCategory,
      d.payeeMccCodeName,
      new Date(
        '1990',
        '00',
        '01',
        new Date(d.txnTimestamp).getHours(),
        new Date(d.txnTimestamp).getMinutes(),
        '00'
      ),
      d.device<PERSON>,
      d.txnAmount,
      d.responseCodeName,
      d.ifrmVerdict,
      d.caseVerdict == 'NonFraud' ? 'Not Fraud' : d.caseVerdict
    ]);

  const chartNonFraudData = ruleBehaviour?.data
    ?.filter((d) => d.caseVerdict == 'NonFraud')
    .map((d) => [
      d.txnTypeName,
      d.entityCategory,
      d.payeeMccCodeName,
      new Date(
        '1990',
        '00',
        '01',
        new Date(d.txnTimestamp).getHours(),
        new Date(d.txnTimestamp).getMinutes(),
        '00'
      ),
      d.deviceOs,
      d.txnAmount,
      d.responseCodeName,
      d.ifrmVerdict,
      d.caseVerdict == 'NonFraud' ? 'Not Fraud' : d.caseVerdict
    ]);

  const config = {
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    parallelAxis: [
      { dim: 0, name: 'Transaction Type', type: 'category' },
      { dim: 1, name: 'Payee Type', type: 'category' },
      { dim: 2, name: 'Payee MCC', type: 'category' },
      {
        dim: 3,
        name: 'Time',
        type: 'time',
        formatter: '{HH}:{mm}',
        max: function (value) {
          return Math.min(value.max + 3600 * 1000, new Date('01-01-1990 23:59:59').getTime());
        },
        min: function (value) {
          return Math.max(value.min - 3600 * 1000, new Date('01-01-1990 00:00:00').getTime());
        }
      },
      { dim: 4, name: 'OS', type: 'category' },
      { dim: 5, name: 'Amount' },
      { dim: 6, name: 'Response Code', type: 'category' },
      { dim: 7, name: 'Verdict', type: 'category' },
      {
        dim: 8,
        name: 'Case Verdict',
        type: 'category',
        data: ['Fraud', 'Not Fraud']
      }
    ],
    parallel: {
      areaSelectStyle: {
        opacity: 0.3
      }
    },
    series: [
      {
        name: 'Fraud',
        type: 'parallel',
        lineStyle: {
          width: 4
        },
        emphasis: {
          disabled: true
        },
        data: chartFraudData
      },
      {
        name: 'Not Fraud',
        type: 'parallel',
        lineStyle: {
          width: 4
        },
        emphasis: {
          disabled: true
        },
        data: chartNonFraudData
      }
    ]
  };

  const subtitle = (
    <HelpIcon
      size="lg"
      key="behaviourHelp"
      id="behaviourHelp"
      text={
        <small>
          Analyse the contribution of various transaction parameter in the eventual verdict of case.{' '}
          <br />
          <b>Green paths</b> denote journey to Not Fraud verdict. <br />
          <b>Blue paths</b> denote journey to Fraud verdict. <br />
        </small>
      }
    />
  );

  return (
    <GraphContainer
      theme={theme}
      config={config}
      className={'card-height-500'}
      title="Rule Behaviour"
      subtitle={subtitle}
      noData={ruleBehaviour.data?.length === 0}
      loader={ruleBehaviour.loader}
      error={{ flag: ruleBehaviour.error, errorMessage: ruleBehaviour.errorMessage }}
    />
  );
}

RuleBehaviourGraph.propTypes = {
  rule: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  ruleBehaviour: PropTypes.object.isRequired,
  fetchRuleBehaviour: PropTypes.func.isRequired
};

export default RuleBehaviourGraph;
