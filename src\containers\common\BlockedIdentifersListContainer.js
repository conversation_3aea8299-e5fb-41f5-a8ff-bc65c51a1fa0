import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchChannelwiseCounterpartyId } from 'actions/investigationActions';
import { onFetchBlockedListIdentifier } from 'actions/prefiltersListAction';
import BlockedIdentifiersList from 'components/common/BlockedIdentifiersList';

const mapStateToProps = (state) => ({
  txnDetails: state.transactionDetails.details,
  blockedIdentifiersList: state.prefiltersList.blockedIdentifiers,
  channelCounterpartyId: state.investigation.channelCounterpartyId
});

const mapDispatchToProps = (dispatch) => ({
  fetchBlockedIdentifersList: bindActionCreators(onFetchBlockedListIdentifier, dispatch),
  fetchChannelCounterpartyId: bindActionCreators(onFetchChannelwiseCounterpartyId, dispatch)
});

const BlockedIdentifiersListContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(BlockedIdentifiersList);

export default BlockedIdentifiersListContainer;
