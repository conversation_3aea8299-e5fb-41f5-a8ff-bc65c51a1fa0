import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchBlockedListIdentifier } from 'actions/prefiltersListAction';
import { onFetchChannelwiseCounterpartyId } from 'actions/investigationActions';
import BlockedIdentifiersList from 'components/common/BlockedIdentifiersList';

const mapStateToProps = (state) => {
  return {
    txnDetails: state.transactionDetails.details,
    blockedIdentifiersList: state.prefiltersList.blockedIdentifiers,
    channelCounterpartyId: state.investigation.channelCounterpartyId
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchBlockedIdentifersList: bindActionCreators(onFetchBlockedListIdentifier, dispatch),
    fetchChannelCounterpartyId: bindActionCreators(onFetchChannelwiseCounterpartyId, dispatch)
  };
};

const BlockedIdentifiersListContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(BlockedIdentifiersList);

export default BlockedIdentifiersListContainer;
