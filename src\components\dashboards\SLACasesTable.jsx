import React, { useEffect } from 'react';
import { lowerCase, includes } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import ReactTable from 'react-table';
import { useHistory, withRouter } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';

import CardContainer from 'components/common/CardContainer';
import { getScreen } from 'constants/functions';

function SLACasesTable({ role, formData, slaBreachCases, fetchSlaBreachCases }) {
  const history = useHistory();
  useEffect(() => {
    if (formData.startDate && formData.endDate) fetchSlaBreachCases(formData);
  }, [formData]);

  const getPage = (role, caseItem) => `${getScreen(role)}/frm/${caseItem.transactionId}`;

  const columns = [
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      width: 40,
      fixed: true,
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <Button
          outline
          title="view"
          size="sm"
          color="primary"
          className="ms-auto"
          onClick={() => history.push(getPage(role, row.original))}
          onContextMenu={() => window.open(getPage(role, row.original))}>
          <FontAwesomeIcon icon={faChevronRight} />
        </Button>
      )
    },
    { Header: 'SLA', accessor: 'sla', Cell: ({ value }) => value + ' mins' },
    { Header: 'Analyst', accessor: 'analyst' },
    {
      Header: 'Transaction Timestamp',
      accessor: 'transactionTimestamp',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Transaction ID', accessor: 'transactionId' },
    {
      Header: 'Amount',
      accessor: 'amount',
      filterMethod: (filter, row) =>
        row[filter.id] && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/no-multi-comp, react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    { Header: 'Customer ID', accessor: 'customerId' },
    { Header: 'Verdict', accessor: 'closeVerdictBucket' }
  ];

  const subHeader = [
    { Header: 'Analyst', accessor: 'analyst' },
    { Header: 'Assigned On', accessor: 'assignmentTime' },
    { Header: 'Time on Case', accessor: 'totalTimeTaken' },
    { Header: 'Action Taken', accessor: 'caseActivity' }
  ];

  return (
    <CardContainer title="SLA Breaches">
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && includes(lowerCase(row[filter.id]), lowerCase(filter.value))
        }
        columns={columns}
        data={slaBreachCases.data}
        SubComponent={(row) => {
          return (
            <div className="rule-sub-table">
              <ReactTable
                columns={subHeader}
                data={row.original?.users || []}
                defaultPageSize={3}
                minRows={2}
                pageSizeOptions={[3, 5, 10, 15]}
                showPaginationTop={true}
                showPaginationBottom={false}
              />
            </div>
          );
        }}
        noDataText="No transaction found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        defaultPageSize={5}
        minRows={5}
        showPageJump={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        className={'-highlight -striped'}
      />
    </CardContainer>
  );
}

SLACasesTable.propTypes = {
  role: PropTypes.string.isRequired,
  formData: PropTypes.object.isRequired,
  slaBreachCases: PropTypes.object.isRequired,
  fetchSlaBreachCases: PropTypes.func.isRequired
};

export default withRouter(SLACasesTable);
