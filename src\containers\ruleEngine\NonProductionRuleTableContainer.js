import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as ruleCreationActions from 'actions/ruleCreationActions';
import NonProductionRuleTable from 'components/ruleEngine/NonProductionRuleTable';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  role: state.auth.userCreds.roles,
  nonProductionRules: state.ruleCreation.nonProductionRules,
  moduleType: state.auth.moduleType,
  hasSandbox: state.user.configurations.sandbox
});

const mapDispatchToProps = (dispatch) => ({
  ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch)
});

const NonProductionRuleTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NonProductionRuleTable);

export default NonProductionRuleTableContainer;
