import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect, useMemo } from 'react';
import { useLocation, withRouter } from 'react-router-dom';

import CardContainer from 'components/common/CardContainer';
import { removeEmptyItemsFromObject } from 'constants/functions';
import AdvanceSearchFormContainer from 'containers/advanceSearch/AdvanceSearchFormContainer';
import AdvanceSearchTableContainer from 'containers/advanceSearch/AdvanceSearchTableContainer';

const AdvanceSearchPage = ({ ruleNames, advanceSearchTxns, channels, advanceSearchTxn }) => {
  const initialState = useMemo(
    () => ({
      payeeId: '',
      payerId: '',
      payeeMmid: '',
      payerMmid: '',
      payerMcc: '',
      payeeMcc: '',
      payeeAccountNumber: '',
      payerAccountNumber: '',
      payeeCardNumberMask: '',
      payerCardNumberMask: '',
      payerCardNumberHash: '',
      payeeCardNumberHash: '',
      txnId: '',
      violatedRules: [],
      responseCode: '',
      startTimestamp: undefined,
      endTimestamp: undefined,
      txnAmount: '',
      txnType: '',
      identifiers: {
        channel: channels[0],
        xchannelId: '',
        sourceInstitutionId: '',
        entityId: '',
        agentId: '',
        terminalId: '',
        attribute2: ''
      }
    }),
    [channels]
  );

  const { state } = useLocation();
  const { filterCondition } = advanceSearchTxns;

  const filteredRuleCodeArray = useMemo(
    () =>
      _.has(filterCondition, 'violatedRules') ? _.split(filterCondition.violatedRules, ',') : [],
    [filterCondition]
  );

  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [searchData, setSearchData] = useState(initialState);

  useEffect(() => {
    if (
      Object.getOwnPropertyNames(formDataAdvanceSearch)?.length > 1 ||
      Object.getOwnPropertyNames(formDataAdvanceSearch.identifiers)?.length > 1
    ) {
      const formData = {
        filters: formDataAdvanceSearch,
        pageNo: pageNo + 1,
        pageSize: pageRecords
      };
      advanceSearchTxn(formData);
    }
  }, [advanceSearchTxn, formDataAdvanceSearch, pageNo, pageRecords]);

  const getViolatedRulesCode = () => _.map(searchData?.violatedRules, (d) => d.value).toString();

  const formDataFormatted = {
    ...searchData,
    ...(!_.isEmpty(searchData.violatedRules) && { violatedRules: getViolatedRulesCode() }),
    ...(searchData.startTimestamp !== undefined &&
      searchData.endTimestamp !== undefined &&
      moment(searchData.startTimestamp).isValid() &&
      moment(searchData.endTimestamp).isValid() && {
        startTimestamp: moment(searchData.startTimestamp).format('YYYY-MM-DDTHH:mm:ss'),
        endTimestamp: moment(searchData.endTimestamp).format('YYYY-MM-DDTHH:mm:ss')
      })
  };

  const formDataAdvanceSearch = removeEmptyItemsFromObject(formDataFormatted);

  useEffect(() => {
    if (!_.isEmpty(state)) setSearchData((prevData) => ({ ...prevData, ...state }));
    else if (!_.isEmpty(filterCondition)) {
      const getViolatedRulesForFilter = () => {
        if (_.isEmpty(filterCondition?.violatedRules)) return {};
        if (_.isEmpty(filteredRuleCodeArray)) return { violatedRules: [] };
        if (_.isEmpty(ruleNames?.list[channels[0]])) return { violatedRules: [] };
        const filteredRules = _.filter(ruleNames.list[channels[0]], (rule) =>
          _.includes(filteredRuleCodeArray, rule.code)
        ).map((d) => ({ label: d.name, value: d.code }));
        return { violatedRules: filteredRules };
      };

      setSearchData((prevData) => ({
        ...prevData,
        ...filterCondition,
        ...getViolatedRulesForFilter(),
        identifiers: { ...prevData.identifiers, ...filterCondition.identifiers }
      }));
    } else setSearchData(initialState);
  }, [state, filterCondition, channels, filteredRuleCodeArray, initialState, ruleNames]);

  return (
    <div className="content-wrapper ">
      <CardContainer title="Search Transaction">
        <AdvanceSearchFormContainer
          pageRecords={pageRecords}
          searchData={searchData}
          formDataAdvanceSearch={formDataAdvanceSearch}
          setPageNo={setPageNo}
          setSearchData={setSearchData}
        />
        <AdvanceSearchTableContainer
          pageNo={pageNo}
          pageRecords={pageRecords}
          formDataAdvanceSearch={formDataAdvanceSearch}
          setPageNo={setPageNo}
          setPageRecords={setPageRecords}
        />
      </CardContainer>
    </div>
  );
};

AdvanceSearchPage.propTypes = {
  channels: PropTypes.array.isRequired,
  ruleNames: PropTypes.object.isRequired,
  advanceSearchTxns: PropTypes.object.isRequired,
  advanceSearchTxn: PropTypes.func.isRequired
};

export default withRouter(AdvanceSearchPage);
