import React, { useState, useEffect } from 'react';
import { useLocation, withRouter } from 'react-router-dom';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';

import CardContainer from 'components/common/CardContainer';
import { removeEmptyItemsFromObject } from 'constants/functions';
import AdvanceSearchFormContainer from 'containers/advanceSearch/AdvanceSearchFormContainer';
import AdvanceSearchTableContainer from 'containers/advanceSearch/AdvanceSearchTableContainer';

const AdvanceSearchPage = ({ ruleNames, advanceSearchTxns, channels, advanceSearchTxn }) => {
  const initialState = {
    payeeId: '',
    payerId: '',
    payeeMmid: '',
    payerMmid: '',
    payerMcc: '',
    payeeMcc: '',
    payeeAccountNumber: '',
    payerAccountNumber: '',
    payeeCardNumberMask: '',
    payerCardNumberMask: '',
    payerCardNumberHash: '',
    payeeCardNumberHash: '',
    txnId: '',
    violatedRules: [],
    responseCode: '',
    startTimestamp: undefined,
    endTimestamp: undefined,
    txnAmount: '',
    txnType: '',
    identifiers: {
      channel: channels[0],
      xchannelId: '',
      sourceInstitutionId: '',
      entityId: '',
      agentId: '',
      terminalId: '',
      attribute2: ''
    }
  };
  const { state } = useLocation();
  const { filterCondition } = advanceSearchTxns;
  const filteredRuleCodeArray = _.has(filterCondition, 'violatedRules')
    ? _.split(filterCondition.violatedRules, ',')
    : [];

  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [searchData, setSearchData] = useState(initialState);

  useEffect(() => {
    if (
      Object.getOwnPropertyNames(formDataAdvanceSearch)?.length > 1 ||
      Object.getOwnPropertyNames(formDataAdvanceSearch.identifiers)?.length > 1
    ) {
      let formData = {
        filters: formDataAdvanceSearch,
        pageNo: pageNo + 1,
        pageSize: pageRecords
      };
      advanceSearchTxn(formData);
    }
  }, [pageNo, pageRecords]);

  const getViolatedRulesCode = () => _.map(searchData?.violatedRules, (d) => d.value).toString();

  const formDataFormatted = {
    ...searchData,
    ...(!_.isEmpty(searchData.violatedRules) && { violatedRules: getViolatedRulesCode() }),
    ...(searchData.startTimestamp != undefined &&
      searchData.endTimestamp != undefined &&
      moment(searchData.startTimestamp).isValid() &&
      moment(searchData.endTimestamp).isValid() && {
        startTimestamp: moment(searchData.startTimestamp).format('YYYY-MM-DDTHH:mm:ss'),
        endTimestamp: moment(searchData.endTimestamp).format('YYYY-MM-DDTHH:mm:ss')
      })
  };

  const formDataAdvanceSearch = removeEmptyItemsFromObject(formDataFormatted);

  useEffect(() => {
    !_.isEmpty(state)
      ? setSearchData((prevData) => {
          return { ...prevData, ...state };
        })
      : !_.isEmpty(filterCondition)
      ? setSearchData((prevData) => {
          return {
            ...prevData,
            ...filterCondition,
            ...(!_.isEmpty(filterCondition?.violatedRules) && {
              violatedRules: !_.isEmpty(filteredRuleCodeArray)
                ? !_.isEmpty(ruleNames?.list[channels[0]])
                  ? _.filter(ruleNames.list[channels[0]], (rule) =>
                      _.includes(filteredRuleCodeArray, rule.code)
                    ).map((d) => {
                      return { label: d.name, value: d.code };
                    })
                  : []
                : []
            }),
            identifiers: { ...prevData.identifiers, ...filterCondition.identifiers }
          };
        })
      : initialState;
  }, [state, filterCondition]);

  return (
    <div className={'content-wrapper '}>
      <CardContainer title="Search Transaction">
        <AdvanceSearchFormContainer
          pageRecords={pageRecords}
          searchData={searchData}
          formDataAdvanceSearch={formDataAdvanceSearch}
          setPageNo={setPageNo}
          setSearchData={setSearchData}
        />
        <AdvanceSearchTableContainer
          pageNo={pageNo}
          pageRecords={pageRecords}
          formDataAdvanceSearch={formDataAdvanceSearch}
          setPageNo={setPageNo}
          setPageRecords={setPageRecords}
        />
      </CardContainer>
    </div>
  );
};

AdvanceSearchPage.propTypes = {
  channels: PropTypes.array.isRequired,
  ruleNames: PropTypes.object.isRequired,
  advanceSearchTxns: PropTypes.object.isRequired,
  advanceSearchTxn: PropTypes.func.isRequired
};

export default withRouter(AdvanceSearchPage);
