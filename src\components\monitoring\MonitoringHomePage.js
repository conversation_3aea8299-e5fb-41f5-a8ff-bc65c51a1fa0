/* eslint-disable react/prop-types */

'use strict';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { useHistory } from 'react-router-dom';
import { DropdownItem, Button, Badge } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBan,
  faInfoCircle,
  faCheckCircle,
  faChevronRight,
  faExclamationCircle
} from '@fortawesome/free-solid-svg-icons';

import { getScreen, countFormatter } from 'constants/functions';
import CardContainer from 'components/common/CardContainer';
import DropdownButton from 'components/common/DropdownButton';
import { VERDICT_ARRAY } from 'constants/applicationConstants';

const MonitoringHomePage = ({
  userslist,
  authDetails,
  monitoringData,
  fetchRulesList,
  ruleConfigurator,
  showFailureAlert,
  monitoringActions,
  createCaseAndAssign
}) => {
  const history = useHistory();
  const { userId, channelRoles, roles } = authDetails;
  const [selected, setselected] = useState({ transaction: {}, channel: '' });
  const [showEscalation, setshowEscalation] = useState(false);

  useEffect(() => {
    if (roles != 'monitoring') history.goBack();
    document.title = 'BANKiQ FRC | Monitoring';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const channels = () =>
    channelRoles
      .filter((channelRole) => _.includes(channelRole, 'monitoring'))
      .map((channelRole) => {
        let arr = _.split(channelRole, ':');
        return arr[0];
      });

  useEffect(() => {
    channels.map((channel) => {
      if (_.isEmpty(ruleConfigurator.list[channel])) return fetchRulesList(channel);
    });
    fetchTransactions();
  }, []);

  const fetchTransactions = () => {
    let { onFetchMonitoringData, onFetchTransactionCount } = monitoringActions;
    _.map(channels, (channel) => onFetchTransactionCount(channel));
    onFetchMonitoringData();
  };

  const toggleSelection = (transaction) => {
    transaction.transactionId == selected.transaction?.transactionId
      ? (setselected({
          transaction: {},
          channel: ''
        }),
        setshowEscalation(false))
      : (setselected({
          transaction: transaction,
          channel: transaction.channel
        }),
        setshowEscalation(true));
  };

  const createCase = (userId) => {
    if (!_.isEmpty(selected.transaction)) {
      let formData = {
        assignedTo: userId,
        assignedBy: authDetails.userId,
        channel: selected.channel,
        txnId: selected.transaction.transactionId,
        partnerId: selected.transaction?.identifiers?.partnerId || 0
      };
      createCaseAndAssign(formData);
      setselected({
        transaction: {},
        channel: ''
      });
      setshowEscalation(false);
    } else showFailureAlert({ message: 'Unable to assign case.' });
  };

  let channelOptions = _.map(channels, (channel) => <option key={channel}>{channel}</option>);

  let totalCount = _.reduce(
    monitoringData.totalCount,
    function (sum, count) {
      return sum + count;
    },
    0
  );

  let reviewers = userslist
    .filter((user) => _.includes(user.channelRoles, selected.channel + ':maker'))
    .map((user) => {
      return user.id == userId ? (
        <DropdownItem key={user.id} onClick={() => createCase(user.id)}>
          Self
        </DropdownItem>
      ) : (
        <DropdownItem key={user.id} onClick={() => createCase(user.id)}>
          {user.userName}
        </DropdownItem>
      );
    });

  let assignmentAction = (
    <DropdownButton
      name="Assign to"
      className="assignment-action"
      disabled={_.isEmpty(selected.transaction)}>
      {reviewers}
    </DropdownButton>
  );

  let action = (
    <span>
      {showEscalation ? assignmentAction : null}
      <Button color="primary" onClick={() => fetchTransactions()}>
        Refresh
      </Button>
    </span>
  );

  let violationOptions = _.map(ruleConfigurator.list, (channel) =>
    channel.map((d) => (
      <option key={d.code} value={d.code}>
        {d.name}
      </option>
    ))
  );

  const getPage = (role, caseItem) =>
    `${getScreen(role)}/${caseItem.caseType}/${caseItem.transactionId}`;

  const monitoring_header = [
    {
      Header: '',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 20,
      Cell: (row) => {
        return row.original.escalationType == 'SimpleManualAssignment' ? (
          <input
            type="radio"
            value={row.value}
            name="tableSelect[]"
            onClick={() => toggleSelection(row.original)}
          />
        ) : (
          <FontAwesomeIcon icon={faCheckCircle} className="color-success" />
        );
      }
    },
    {
      Header: 'Priority',
      accessor: 'priority',
      minWidth: 30,
      Cell: ({ value }) =>
        value == 3 ? (
          <FontAwesomeIcon icon={faBan} className="color-danger" />
        ) : value == 2 ? (
          <FontAwesomeIcon icon={faExclamationCircle} className="color-warning" />
        ) : value == 1 ? (
          <FontAwesomeIcon icon={faInfoCircle} className="color-info" />
        ) : (
          <FontAwesomeIcon icon={faCheckCircle} className="color-success" />
        ),
      filterMethod: (filter, row) =>
        row[filter.id] && filter.value && row[filter.id] == filter.value,
      Filter: ({ onChange }) => (
        <select placeholder="priority" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option value="1">Level 1</option>
          <option value="2">Level 2</option>
          <option value="3">Level 3</option>
        </select>
      )
    },
    {
      Header: 'Channel',
      accessor: 'channel',
      Cell: (row) => _.upperCase(row.original.channel),
      Filter: ({ onChange }) => (
        <select placeholder="Channels" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          {channelOptions}
        </select>
      )
    },
    { Header: 'Transaction ID', accessor: 'transactionId' },
    {
      Header: 'Time',
      accessor: 'transactionDate',
      Cell: ({ value }) => Moment(value).format('HH:mm:ss')
    },
    {
      Header: 'Amount',
      accessor: 'transactionAmount',
      style: { textAlign: 'right' },
      headerStyle: { textAlign: 'right' },
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Violations',
      accessor: 'violatedRules',
      minWidth: 130,
      Cell: (row) => {
        let violatedRules = row.original.violatedRules
          .map((rule) => ruleConfigurator.list[row.original.channel].find((d) => rule === d.code))
          .sort((a, b) => (a.weight < b.weight ? 1 : a.weight > b.weight ? -1 : 0));
        return (
          <ul>
            {violatedRules.map((rule) => {
              return (
                <li key={rule.code}>
                  <span
                    className={
                      rule.priority == 3
                        ? 'color-danger '
                        : rule.priority == 2
                          ? 'color-warning '
                          : 'color-primary '
                    }>
                    {rule.name} ({rule.weight})
                  </span>
                </li>
              );
            })}
          </ul>
        );
      },
      filterMethod: (filter, row) => {
        if (!row[filter.id]) return false;
        return row[filter.id].find((d) => filter.value === d) ? true : false;
      },
      Filter: ({ onChange }) => (
        <select placeholder="Violations" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          {violationOptions}
        </select>
      )
    },
    {
      Header: 'Rule Engine Result',
      accessor: 'reResult',
      Cell: ({ value }) => _.lowerCase(value),
      filterMethod: (filter, row) => filter.value == '' || filter.value == row.reResult,
      Filter: ({ onChange }) => (
        <select placeholder="rule engine result" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option value="Normal">normal</option>
          <option value="Suspicious">suspicious</option>
          <option value="HighlySuspicious">highly suspicious</option>
        </select>
      )
    },
    {
      Header: 'Cognitive Result',
      accessor: 'cognitiveIsSuspicious',
      Cell: (row) => (
        <span>
          {row.original.cognitiveIsSuspicious}
          <br />
          <small>
            <span
              className={
                row.original.cognitiveCredibilityLevel == 'High' ? 'color-primary' : 'color-danger'
              }>
              <b>{row.original.cognitiveCredibilityLevel}</b>
            </span>
          </small>
        </span>
      ),
      filterMethod: (filter, row) => {
        if (filter.value == '') return true;
        let filterValues = filter.value.split(' ');
        let isCognitiveResult =
          row._original.cognitiveIsSuspicious == filterValues[0] ? true : false;
        let isCredibility =
          row._original.cognitiveCredibilityLevel == filterValues[1] ? true : false;
        return isCognitiveResult && isCredibility;
      },
      Filter: ({ onChange }) => (
        <select placeholder="cognitive result" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option value="Usual High">Usual High</option>
          <option value="Usual Low">Usual Low</option>
          <option value="Unusual High">Unusual High</option>
          <option value="Unusual Low">Unusual Low</option>
        </select>
      )
    },
    {
      Header: 'Verdict',
      accessor: 'ifrmVerdict',
      Cell: (row) => (
        <span>
          {VERDICT_ARRAY.filter((d) => row.value === d.label).map((d, i) => {
            return (
              <span key={i} className={d.color}>
                {d.label}
              </span>
            );
          })}
        </span>
      ),
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
        </select>
      )
    },
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      minWidth: 20,
      fixed: true,
      Cell: (row) => (
        <span>
          <Button
            size="sm"
            title="view"
            color="primary"
            className="me-1"
            onClick={() => history.push(getPage('monitor', row.original))}
            onContextMenu={() => window.open(getPage('monitor', row.original))}>
            <FontAwesomeIcon icon={faChevronRight} />
          </Button>
        </span>
      )
    }
  ];

  let monitoringKpi = (
    <div className="d-flex card-kpi">
      <span>
        Total Transactions:
        <Badge pill color="primary" className={'p-1 '}>
          {countFormatter(totalCount)}
        </Badge>
      </span>
      <span>
        RE Alerts:
        <Badge pill color="warning" className={'p-1 '}>
          {countFormatter(monitoringData.flaggedCount)}
        </Badge>
      </span>
      <span>
        Identified Frauds:
        <Badge pill color="danger" className={'p-1 '}>
          {countFormatter(monitoringData.fraudCount)}
        </Badge>
      </span>
    </div>
  );

  return (
    <div className={'content-wrapper '}>
      <CardContainer title={'Monitoring'} kpi={monitoringKpi} action={action}>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
          }
          columns={monitoring_header}
          data={monitoringData.transactions}
          noDataText="No data found"
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={20}
          minRows={6}
          filterable
          defaultSorted={[
            {
              id: 'priority',
              desc: true
            },
            {
              id: 'transactionDateTime',
              desc: true
            }
          ]}
          className={'-highlight -striped'}
        />
      </CardContainer>
    </div>
  );
};

MonitoringHomePage.propTypes = {
  userslist: PropTypes.array.isRequired,
  authDetails: PropTypes.object.isRequired,
  monitoringData: PropTypes.object.isRequired,
  ruleConfigurator: PropTypes.object.isRequired,
  showFailureAlert: PropTypes.func.isRequired,
  fetchRulesList: PropTypes.func.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  monitoringActions: PropTypes.object.isRequired,
  createCaseAndAssign: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired
};

export default MonitoringHomePage;
