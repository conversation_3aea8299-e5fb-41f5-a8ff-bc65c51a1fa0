import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Sandbox from 'components/ruleEngine/Sandbox';
import * as sandboxingActions from 'actions/sandboxingActions';

const mapStateToProps = (state) => {
  return {
    sandboxing: state.sandboxing
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    sandboxingActions: bindActionCreators(sandboxingActions, dispatch)
  };
};
const SandboxContainer = connect(mapStateToProps, mapDispatchToProps)(Sandbox);

export default SandboxContainer;
