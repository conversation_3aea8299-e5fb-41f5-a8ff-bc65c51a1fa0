import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as sandboxingActions from 'actions/sandboxingActions';
import Sandbox from 'components/ruleEngine/Sandbox';

const mapStateToProps = (state) => ({
  sandboxing: state.sandboxing
});

const mapDispatchToProps = (dispatch) => ({
  sandboxingActions: bindActionCreators(sandboxingActions, dispatch)
});
const SandboxContainer = connect(mapStateToProps, mapDispatchToProps)(Sandbox);

export default SandboxContainer;
