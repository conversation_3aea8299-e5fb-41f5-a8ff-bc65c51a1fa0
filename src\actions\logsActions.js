import {
  ON_FETCH_SELECTED_CASE_LOGS_LOADING,
  ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS,
  ON_FETCH_SELECTED_CASE_LOGS_FAILURE,
  ON_FETCH_SELECTED_ENTITY_LOGS_LOADING,
  ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS,
  ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchCaseLogs(module, id) {
  return client({
    url: `audit/${module}/${id}/log`,
    badRequestMessage: 'Unable to fetch audit logs for selected case.'
  });
}

function onFetchCaseLogsLoading() {
  return { type: ON_FETCH_SELECTED_CASE_LOGS_LOADING };
}

function onFetchCaseLogsSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS,
    response
  };
}

function onFetchCaseLogsFailure(response) {
  return {
    type: ON_FETCH_SELECTED_CASE_LOGS_FAILURE,
    response
  };
}

function onFetchCaseLogs(module, id) {
  return function (dispatch) {
    dispatch(onFetchCaseLogsLoading());
    return fetchCaseLogs(module, id).then(
      (success) => dispatch(onFetchCaseLogsSuccess(success)),
      (error) => dispatch(onFetchCaseLogsFailure(error))
    );
  };
}

function fetchEntityLogs(entityId, channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/statuslog/${entityId}/${channel}`,
    data: formData,
    badRequestMessage: 'Unable to fetch status logs for selected entity.'
  });
}

function onFetchEntityLogsLoading() {
  return { type: ON_FETCH_SELECTED_ENTITY_LOGS_LOADING };
}

function onFetchEntityLogsSuccess(response, entityId, filterCondition) {
  return {
    type: ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS,
    response,
    entityId,
    filterCondition
  };
}

function onFetchEntityLogsFailure(response, entityId, filterCondition) {
  return {
    type: ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE,
    response,
    entityId,
    filterCondition
  };
}

function onFetchEntityLogs(entityId, channel, formData) {
  return function (dispatch) {
    dispatch(onFetchEntityLogsLoading());
    return fetchEntityLogs(entityId, channel, formData).then(
      (success) => dispatch(onFetchEntityLogsSuccess(success, entityId, formData?.filterCondition)),
      (error) => dispatch(onFetchEntityLogsFailure(error, entityId, formData?.filterCondition))
    );
  };
}

export { onFetchCaseLogs, onFetchEntityLogs };
