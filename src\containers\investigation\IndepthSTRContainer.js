import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onSelectCase, onAddChildTxnToCase } from 'actions/caseReviewActions';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import IndepthSTR from 'components/investigation/IndepthSTR';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  investigationData: state.investigation,
  txnDetails: state.transactionDetails,
  selectedCase: state.caseAssignment.selectedCase,
  userName: state.auth.userCreds.userName
});

const mapDispatchToProps = (dispatch) => ({
  transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
  addChildTxn: bindActionCreators(onAddChildTxnToCase, dispatch),
  selectCase: bindActionCreators(onSelectCase, dispatch)
});

const IndepthSTRContainer = connect(mapStateToProps, mapDispatchToProps)(IndepthSTR);

export default IndepthSTRContainer;
