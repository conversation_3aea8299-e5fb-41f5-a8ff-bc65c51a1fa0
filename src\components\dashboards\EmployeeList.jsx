import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { ListGroup, ListGroupItem, ListGroupItemText, Row, Col, Badge } from 'reactstrap';

import ListLoader from 'components/loader/ListLoader';
import CardContainer from 'components/common/CardContainer';

const getInitials = (name) => {
  const nameArray = name.split(' ');
  return `${nameArray[0].substring(0, 1)}${nameArray[1]?.substring(0, 1) ?? ''}`;
};

function EmployeeList({ formData, employeeSla, fetchEmployeeSla, action }) {
  useEffect(() => {
    if (formData.startDate && formData.endDate) fetchEmployeeSla(formData);
  }, [formData]);

  const title = (
    <>
      <span>Operator SLA</span>
      <Badge color="danger" pill>
        {employeeSla.data?.average ?? 0} mins
      </Badge>
    </>
  );

  const listProps = (d) =>
    action !== undefined ? { onClick: () => action(d.userId, d.name) } : {};

  return (
    <CardContainer title={title}>
      {employeeSla.loader ? (
        <ListLoader />
      ) : employeeSla.error ? (
        <div className="no-data-div">{employeeSla.errorMessage}</div>
      ) : (
        <ListGroup>
          {employeeSla.data?.analysts?.map((d) => (
            <ListGroupItem key={d.key} {...listProps(d)}>
              <Row>
                <Col md="2" className="d-flex align-items-center p-0">
                  <Badge pill className="p-2">
                    {getInitials(d?.name)}
                  </Badge>
                </Col>
                <Col md="7">
                  <ListGroupItemText>
                    <b>{d.name}</b>
                  </ListGroupItemText>
                  <div>
                    <ListGroupItemText>Today&apos;s cases: {d.todayCasesCount}</ListGroupItemText>
                    <hr className="m-0" />
                    <ListGroupItemText>Total cases: {d.totalCasesCount}</ListGroupItemText>
                  </div>
                </Col>
                <Col md="3" className="d-flex align-items-center p-0">
                  <Badge color="warning" pill className="p-2">
                    {d.timeTakenInMinutes}
                    <br /> mins
                  </Badge>
                </Col>
              </Row>
            </ListGroupItem>
          ))}
        </ListGroup>
      )}
    </CardContainer>
  );
}

EmployeeList.propTypes = {
  action: PropTypes.func,
  formData: PropTypes.object.isRequired,
  employeeSla: PropTypes.object.isRequired,
  fetchEmployeeSla: PropTypes.func.isRequired
};

export default EmployeeList;
