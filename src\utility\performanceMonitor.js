import React from 'react';

/* eslint-disable no-console */
/**
 * Performance monitoring utilities for the ruleEngine module
 * Helps track component re-renders, expensive operations, and memory usage
 */

class PerformanceMonitor {
  constructor() {
    this.renderCounts = new Map();
    this.timings = new Map();
    this.memorySnapshots = [];
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  /**
   * Track component re-renders
   * @param {string} componentName - Name of the component
   * @param {Object} props - Component props for debugging
   */
  trackRender(componentName, props = {}) {
    if (!this.isEnabled) return;

    const count = this.renderCounts.get(componentName) || 0;
    this.renderCounts.set(componentName, count + 1);

    if (count > 10) {
      console.warn(`⚠️ ${componentName} has re-rendered ${count} times. Consider optimization.`);
      console.log('Props:', props);
    }
  }

  /**
   * Start timing an operation
   * @param {string} operationName - Name of the operation
   */
  startTiming(operationName) {
    if (!this.isEnabled) return;
    this.timings.set(operationName, performance.now());
  }

  /**
   * End timing an operation and log if it's slow
   * @param {string} operationName - Name of the operation
   * @param {number} threshold - Warning threshold in milliseconds (default: 16ms for 60fps)
   */
  endTiming(operationName, threshold = 16) {
    if (!this.isEnabled) return;

    const startTime = this.timings.get(operationName);
    if (!startTime) return;

    const duration = performance.now() - startTime;
    this.timings.delete(operationName);

    if (duration > threshold)
      console.warn(`⚠️ Slow operation: ${operationName} took ${duration.toFixed(2)}ms`);

    return duration;
  }

  /**
   * Take a memory snapshot
   * @param {string} label - Label for the snapshot
   */
  takeMemorySnapshot(label) {
    if (!this.isEnabled || !performance.memory) return;

    const snapshot = {
      label,
      timestamp: Date.now(),
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
    };

    this.memorySnapshots.push(snapshot);

    // Keep only last 50 snapshots
    if (this.memorySnapshots.length > 50) this.memorySnapshots.shift();

    return snapshot;
  }

  /**
   * Check for memory leaks by comparing snapshots
   */
  checkMemoryLeaks() {
    if (!this.isEnabled || this.memorySnapshots.length < 2) return;

    const recent = this.memorySnapshots.slice(-5);
    const growth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize;
    const growthMB = growth / (1024 * 1024);

    if (growthMB > 10)
      console.warn(`⚠️ Potential memory leak detected: ${growthMB.toFixed(2)}MB growth`);
  }

  /**
   * Get performance report
   */
  getReport() {
    if (!this.isEnabled) return null;

    return {
      renderCounts: Object.fromEntries(this.renderCounts),
      memorySnapshots: this.memorySnapshots.slice(-10),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Reset all tracking data
   */
  reset() {
    this.renderCounts.clear();
    this.timings.clear();
    this.memorySnapshots = [];
  }

  /**
   * Enable/disable monitoring
   * @param {boolean} enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for tracking component renders
 * @param {string} componentName
 * @param {Object} props
 */
export const useRenderTracker = (componentName, props) => {
  React.useEffect(() => {
    performanceMonitor.trackRender(componentName, props);
  });
};

/**
 * Higher-order component for performance tracking
 * @param {React.Component} WrappedComponent
 * @param {string} componentName
 */
export const withPerformanceTracking = (WrappedComponent, componentName) =>
  React.memo((props) => {
    performanceMonitor.trackRender(componentName, props);
    return React.createElement(WrappedComponent, props);
  });

/**
 * Decorator for timing expensive operations
 * @param {string} operationName
 */
export const timeOperation = (operationName) => (target, propertyKey, descriptor) => {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args) {
    performanceMonitor.startTiming(operationName);
    const result = originalMethod.apply(this, args);

    if (result && typeof result.then === 'function')
      // Handle async operations
      return result.finally(() => {
        performanceMonitor.endTiming(operationName);
      });
    else {
      performanceMonitor.endTiming(operationName);
      return result;
    }
  };

  return descriptor;
};

/**
 * Utility to measure component render time
 * @param {Function} renderFunction
 * @param {string} componentName
 */
export const measureRender = (renderFunction, componentName) => {
  performanceMonitor.startTiming(`${componentName}_render`);
  const result = renderFunction();
  performanceMonitor.endTiming(`${componentName}_render`);
  return result;
};

/**
 * Check if a component should re-render based on props
 * @param {Object} prevProps
 * @param {Object} nextProps
 * @param {string} componentName
 */
export const shouldComponentUpdate = (prevProps, nextProps, componentName) => {
  const propsChanged = JSON.stringify(prevProps) !== JSON.stringify(nextProps);

  if (!propsChanged) console.log(`✅ ${componentName} skipped unnecessary re-render`);

  return propsChanged;
};

export default performanceMonitor;
