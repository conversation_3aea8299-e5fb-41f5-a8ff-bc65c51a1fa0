import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Table, FormGroup, Input } from 'reactstrap';
import HelpIcon from 'components/common/HelpIcon';
import { isEmpty } from 'lodash';
import { decisionMatrixActionChangeHandler } from 'components/scp/scpFunctions';

const DecisionMatrixTable = ({
  transactionRiskScore,
  setTransactionRiskScore,
  isDisabled,
  highlightText,
  decisionMatrixActions,
  fetchDecisionMatrixActions,
  shouldInterceptIsOff,
  configurationsData
}) => {
  useEffect(() => {
    if (isEmpty(decisionMatrixActions.list)) fetchDecisionMatrixActions();
  }, []);

  return (
    <>
      <div className="pt-3 my-1 row default-head fw-bold">
        <span className="searchable">
          {highlightText('Decision Matrix Settings:')}
          {!isEmpty(transactionRiskScore?.decisionMatrix?.desc) && <HelpIcon
            size="lg"
            id='decisionMatrixSettings'
            text={transactionRiskScore?.decisionMatrix?.desc}
          />}
        </span>
      </div>
      <Table bordered striped responsive className="decisionmatrix-table">
        <thead>
          <tr>
            <th className='col-id'>ID</th>
            <th>TRS Outcome</th>
            <th>Confidence Level</th>
            <th>RE Outcome</th>
            <th className='col-action'>Action</th>
            <th>Implied Verdict</th>
          </tr>
        </thead>
        <tbody>
          {transactionRiskScore?.decisionMatrix?.value.map((row) => (
            <tr key={row.id}>
              <td className='col-id'>{row.id}</td>
              <td>{row.trsOutcome}</td>
              <td>{row.confidenceLevel}</td>
              <td>{row.reOutcome}</td>
              <td className='col-action'>
                <FormGroup row>
                  <Input
                    type="select"
                    name="action"
                    id="action"
                    onChange={(event) => {
                      decisionMatrixActionChangeHandler(
                        event,
                        transactionRiskScore,
                        setTransactionRiskScore,
                        row.id,
                        configurationsData
                      );
                    }}
                    value={row.action}
                    disabled={isDisabled || shouldInterceptIsOff}>
                    <option value=""> -- select -- </option>
                    {decisionMatrixActions.list.map((action) => (
                      <option key={action.action} value={action.action}>
                        {action.label}
                      </option>
                    ))}
                  </Input>
                </FormGroup>
              </td>
              <td>{!shouldInterceptIsOff &&
                ['ALERT_SUPPRESS_REVERSE', 'ALERT_ESCALATE_REVERSE'].includes(row.action)
                  ? row.reOutcome === 'ACCEPTED'
                    ? 'REJECTED'
                    : 'ACCEPTED'
                  : row.impliedVerdict}
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    </>
  );
};

DecisionMatrixTable.propTypes = {
  transactionRiskScore: PropTypes.object.isRequired,
  setTransactionRiskScore: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  highlightText: PropTypes.func.isRequired,
  decisionMatrixActions: PropTypes.object.isRequired,
  fetchDecisionMatrixActions: PropTypes.func.isRequired,
  shouldInterceptIsOff: PropTypes.bool.isRequired,
  configurationsData: PropTypes.object.isRequired
};

export default DecisionMatrixTable;
