import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import settingsReducer from 'reducers/settingsReducer';

describe('settings Reducer', () => {
  it('should return the intial state', () => {
    expect(settingsReducer(undefined, {})).toEqual(initialState.settings);
  });

  it('should handle ON_FETCH_SETTINGS_LOADING', () => {
    expect(
      settingsReducer(
        {},
        {
          type: types.ON_FETCH_SETTINGS_LOADING
        }
      )
    ).toEqual({
      data: [],
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_SETTINGS_SUCCESS', () => {
    expect(
      settingsReducer(
        {},
        {
          type: types.ON_FETCH_SETTINGS_SUCCESS,
          response: responses.settings
        }
      )
    ).toEqual({
      data: responses.settings,
      loader: false
    });
  });

  it('should handle ON_FETCH_SETTINGS_FAILURE', () => {
    expect(
      settingsReducer(
        {},
        {
          type: types.ON_FETCH_SETTINGS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      loader: false,
      error: true,
      errorMessage: 'error message'
    });
  });
});
