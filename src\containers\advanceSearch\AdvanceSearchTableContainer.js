import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onCreateCaseAndAssign, onReOpenCase } from 'actions/caseAssignmentActions';
import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import { onToggleCreateCaseModal } from 'actions/toggleActions';
import { onFetchStages } from 'actions/userManagementActions';
import AdvanceSearchTable from 'components/advanceSearch/AdvanceSearchTable';

const mapStateToProps = (state) => ({
  stages: state.user.stages,
  role: state.auth.userCreds.roles,
  userId: state.auth.userCreds.userId,
  advanceSearchTxns: state.advanceSearchTxns,
  closeCaseBuckets: state.caseAssignment.closeCaseBuckets
});

const mapDispatchToProps = (dispatch) => ({
  reOpenCase: bindActionCreators(onReOpenCase, dispatch),
  fetchStages: bindActionCreators(onFetchStages, dispatch),
  createCaseAndAssign: bindActionCreators(onCreateCaseAndAssign, dispatch),
  toggleCreateCaseModal: bindActionCreators(onToggleCreateCaseModal, dispatch),
  fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
});

const AdvanceSearchTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(AdvanceSearchTable);

export default AdvanceSearchTableContainer;
