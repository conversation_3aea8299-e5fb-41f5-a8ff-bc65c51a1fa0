import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchStages } from 'actions/userManagementActions';
import { onToggleCreateCaseModal } from 'actions/toggleActions';
import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import { onCreateCaseAndAssign, onReOpenCase } from 'actions/caseAssignmentActions';
import AdvanceSearchTable from 'components/advanceSearch/AdvanceSearchTable';

const mapStateToProps = (state) => {
  return {
    stages: state.user.stages,
    role: state.auth.userCreds.roles,
    userId: state.auth.userCreds.userId,
    advanceSearchTxns: state.advanceSearchTxns,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    reOpenCase: bindActionCreators(onReOpenCase, dispatch),
    fetchStages: bindActionCreators(onFetchStages, dispatch),
    createCaseAndAssign: bindActionCreators(onCreateCaseAndAssign, dispatch),
    toggleCreateCaseModal: bindActionCreators(onToggleCreateCaseModal, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
  };
};

const AdvanceSearchTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(AdvanceSearchTable);

export default AdvanceSearchTableContainer;
