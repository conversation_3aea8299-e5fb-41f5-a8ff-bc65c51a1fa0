import responses from 'mocks/responses';

import * as actions from 'actions/rfiReportActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const mockedStore = {
  oneView: {}
};

describe('rfi Report actions', () => {
  it('should Fetch High Value Closed Account Count', () => {
    const expectedActions = [
      { type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING },
      {
        type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS,
        response: responses.rfiReports.highValueClosedAccount.count
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHighValueClosedAccountCount()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch High Value Closed Account Data', () => {
    const formData = { pageNo: 1, pageSize: 10 };

    const expectedActions = [
      { type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING },
      {
        type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS,
        response: responses.rfiReports.highValueClosedAccount.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHighValueClosedAccountData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Unusal Decline Turnover Count', () => {
    const formData = { cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING },
      {
        type: types.ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS,
        response: responses.rfiReports.unusualDeclineTurnover.count,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchUnusalDeclineTurnoverCount(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Unusal Decline Turnover Data', () => {
    const formData = { pageNo: 1, pageSize: 10, cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING },
      {
        type: types.ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS,
        response: responses.rfiReports.unusualDeclineTurnover.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchUnusalDeclineTurnoverData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch High Value New Account Count', () => {
    const expectedActions = [
      { type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING },
      {
        type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS,
        response: responses.rfiReports.highValueNewAccount.count
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHighValueNewAccountCount()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch High Value New Account Data', () => {
    const formData = { pageNo: 1, pageSize: 10 };

    const expectedActions = [
      { type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING },
      {
        type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS,
        response: responses.rfiReports.highValueNewAccount.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHighValueNewAccountData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Fraud To Sale Ratio Count', () => {
    const formData = { cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING },
      {
        type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS,
        response: responses.rfiReports.fraudToSaleRatio.count,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchFraudToSaleRatioCount(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Fraud To Sale Ratio Data', () => {
    const formData = { pageNo: 1, pageSize: 10, cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING },
      {
        type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS,
        response: responses.rfiReports.fraudToSaleRatio.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchFraudToSaleRatioData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Top Merchant Count', () => {
    const formData = { cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_TOP_MERCHANT_COUNT_LOADING },
      {
        type: types.ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS,
        response: responses.rfiReports.topMerchant.count,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchTopMerchantCount(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Top Merchant Data', () => {
    const formData = { pageNo: 1, pageSize: 10, cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_TOP_MERCHANT_DATA_LOADING },
      {
        type: types.ON_FETCH_TOP_MERCHANT_DATA_SUCCESS,
        response: responses.rfiReports.topMerchant.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchTopMerchantData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch NBFC Trxns Count', () => {
    const formData = { cummValue: 40, period: 'Day' };

    const expectedActions = [
      { type: types.ON_FETCH_NBFC_TRXNS_COUNT_LOADING },
      {
        type: types.ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS,
        response: responses.rfiReports.nbfcTrxns.count,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchNBFCTrxnsCount(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch NBFC Trxns Data', () => {
    const formData = { pageNo: 1, pageSize: 10, cummValue: 40, period: 'Day' };

    const expectedActions = [
      { type: types.ON_FETCH_NBFC_TRXNS_DATA_LOADING },
      {
        type: types.ON_FETCH_NBFC_TRXNS_DATA_SUCCESS,
        response: responses.rfiReports.nbfcTrxns.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchNBFCTrxnsData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch HRC Trxns Count', () => {
    const formData = { cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_HRC_TRXNS_COUNT_LOADING },
      {
        type: types.ON_FETCH_HRC_TRXNS_COUNT_SUCCESS,
        response: responses.rfiReports.hrcTrxns.count,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHRCTrxnsCount(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch HRC Trxns Data', () => {
    const formData = { pageNo: 1, pageSize: 10, cummValue: 40 };

    const expectedActions = [
      { type: types.ON_FETCH_HRC_TRXNS_DATA_LOADING },
      {
        type: types.ON_FETCH_HRC_TRXNS_DATA_SUCCESS,
        response: responses.rfiReports.hrcTrxns.data,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHRCTrxnsData(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
