import { faClock } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isEmpty } from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import CardContainer from 'components/common/CardContainer';
import ListLoader from 'components/loader/ListLoader';

const getEventsList = (list = []) =>
  list.map((event) => (
    <li key={event.eventTimestamp} className="comment">
      <div className="d-flex justify-content-between flex-wrap comment-header">
        <p>{event.eventType}</p>
        <p className="comment-timestamp">
          <FontAwesomeIcon icon={faClock} />{' '}
          {Moment(event.eventTimestamp).format('YYYY-MM-DD hh:mm A')}
        </p>
      </div>
    </li>
  ));

function CustomerEventsTable({
  customerId,
  custAccountNumber,
  customerEvents,
  fetchCustomerEvents
}) {
  useEffect(() => {
    if (
      (customerId && customerId !== customerEvents.customerId) ||
      (custAccountNumber && custAccountNumber !== customerEvents.custAccountNumber)
    )
      fetchCustomerEvents(customerId, custAccountNumber);
  }, [
    customerId,
    custAccountNumber,
    fetchCustomerEvents,
    customerEvents.customerId,
    customerEvents.custAccountNumber
  ]);

  const renderContent = () => {
    if (customerEvents.loader) return <ListLoader />;

    if (customerEvents.error)
      return (
        <div className="no-data-div">
          {customerEvents.errorMessage || 'Unable to fetch events.'}
        </div>
      );

    return (
      <ul className="comment-list">
        {isEmpty(customerEvents.list) ? (
          <p className="no-comment-text">No non-financial events found for this customer.</p>
        ) : (
          getEventsList(customerEvents.list)
        )}
      </ul>
    );
  };

  return <CardContainer title="Non-financial Events">{renderContent()}</CardContainer>;
}

CustomerEventsTable.propTypes = {
  customerId: PropTypes.string.isRequired,
  custAccountNumber: PropTypes.string.isRequired,
  customerEvents: PropTypes.shape({
    customerId: PropTypes.string.isRequired,
    custAccountNumber: PropTypes.string.isRequired,
    list: PropTypes.array.isRequired,
    loader: PropTypes.bool.isRequired,
    error: PropTypes.bool,
    errorMessage: PropTypes.string
  }).isRequired,
  fetchCustomerEvents: PropTypes.func.isRequired
};

export default CustomerEventsTable;
