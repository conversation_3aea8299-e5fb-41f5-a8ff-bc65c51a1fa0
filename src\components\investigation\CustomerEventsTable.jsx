import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Moment from 'moment';
import { isEmpty } from 'lodash';
import { faClock } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import ListLoader from 'components/loader/ListLoader';
import CardContainer from 'components/common/CardContainer';

const getEventsList = (list = []) =>
  list.map((event) => (
    <li key={event.eventTimestamp} className="comment">
      <div className="d-flex justify-content-between flex-wrap comment-header">
        <p>{event.eventType}</p>
        <p className="comment-timestamp">
          <FontAwesomeIcon icon={faClock} />{' '}
          {Moment(event.eventTimestamp).format('YYYY-MM-DD hh:mm A')}
        </p>
      </div>
    </li>
  ));

function CustomerEventsTable({ customerId, customerEvents, fetchCustomerEvents }) {
  useEffect(() => {
    if (customerId && customerId !== customerEvents.customerId) {
      fetchCustomerEvents(customerId);
    }
  }, [customerId, fetchCustomerEvents, customerEvents.customerId]);

  return (
    <CardContainer title="Non-financial Events">
      {customerEvents.loader ? (
        <ListLoader />
      ) : customerEvents.error ? (
        <div className="no-data-div">
          {customerEvents.errorMessage || 'Unable to fetch events.'}
        </div>
      ) : (
        <ul className="comment-list">
          {isEmpty(customerEvents.list) ? (
            <p className="no-comment-text">No non-financial events found for this customer.</p>
          ) : (
            getEventsList(customerEvents.list)
          )}
        </ul>
      )}
    </CardContainer>
  );
}

CustomerEventsTable.propTypes = {
  customerId: PropTypes.string.isRequired,
  customerEvents: PropTypes.shape({
    customerId: PropTypes.string.isRequired,
    list: PropTypes.array.isRequired,
    loader: PropTypes.bool.isRequired,
    error: PropTypes.bool,
    errorMessage: PropTypes.string
  }).isRequired,
  fetchCustomerEvents: PropTypes.func.isRequired
};

export default CustomerEventsTable;
