import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import { MultiSelect } from 'react-multi-select-component';
import { Row, Col, FormGroup, Label, Input, Button, InputGroup } from 'reactstrap';

import SandboxLoader from 'components/loader/SandboxLoader';
import SandboxResult from 'containers/ruleEngine/SandboxResultContainer';
import { useInterval } from 'utility/customHooks';

const Sandbox = ({ selectedRule, ruleList, sandboxing, sandboxingActions }) => {
  const { dateRange, testing } = sandboxing;
  const [comparisonRules, setComparisonRules] = useState([]);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [txnType, setTxnType] = useState('');
  const [txnAmount, setTxnAmount] = useState('');
  const [payeeMcc, setPayeeMcc] = useState('');
  const [payerMcc, setPayerMcc] = useState('');
  const [responseCode, setResponseCode] = useState('');
  const [sourceInstitutionId, setSourceInstitutionId] = useState('');
  const [txnAmountComparator, setTxnAmountComparator] = useState('>');
  const MILLISECONDS_IN_15SECONDS = 15000;

  const resultHeaders = [selectedRule.name, ..._.map(comparisonRules, (rule) => rule.label)];

  useEffect(() => {
    if (_.isEmpty(dateRange.data.startTimestamp)) sandboxingActions.onFetchSandboxDateRange();
  }, []);

  useEffect(() => {
    if (startDate == null && dateRange.data?.startTimestamp != '')
      setStartDate(new Date(dateRange.data?.startTimestamp));

    if (endDate == null && dateRange.data?.endTimestamp != '')
      setEndDate(new Date(dateRange.data?.endTimestamp));
  }, [dateRange]);

  useInterval(() => {
    if (testing.status === 'STARTED' || testing.status === 'PENDING') {
      sandboxingActions.onFetchSandboxTestStatus(
        +testing?.testId,
        testing?.ruleName,
        testing?.ruleCode
      );
    }
  }, MILLISECONDS_IN_15SECONDS);

  const valid = (current) =>
    current.isSameOrAfter(Moment(dateRange.data.startTimestamp)) &&
    current.isSameOrBefore(dateRange.data.endTimestamp);

  const filteredRuleList = _.filter(ruleList, (d) => d.code !== selectedRule?.code);

  const violationOptions =
    !_.isEmpty(filteredRuleList) &&
    _.map(filteredRuleList, (rule) => {
      return {
        label: rule.name,
        value: rule.code
      };
    });

  const submit = (e) => {
    e.preventDefault();
    const comparison = _.filter(filteredRuleList, (rule) => {
      for (let selected in comparisonRules) {
        if (comparisonRules[selected].value === rule.code) return true;
      }
      return false;
    });

    const formData = {
      rules: [selectedRule, ...comparison],
      ...(startDate &&
        endDate && { startDate: Moment(startDate).local().format('YYYY-MM-DDTHH:mm:ss') }),
      ...(startDate &&
        endDate && { endDate: Moment(endDate).local().format('YYYY-MM-DDTHH:mm:ss') }),
      ...(txnType && { txnType }),
      ...(txnAmount && { txnAmount }),
      ...(payeeMcc && { payeeMcc }),
      ...(payerMcc && { payerMcc }),
      ...(responseCode && { responseCode }),
      ...(sourceInstitutionId && { sourceInstitutionId }),
      ...(txnAmountComparator && { operator: txnAmountComparator })
    };

    sandboxingActions.onTestSandboxRules(formData, selectedRule.name, selectedRule?.code);
  };

  return (
    <>
      <Row className="row-cols-auto g-3 align-items-center">
        <Col xs="12">
          <FormGroup>
            <Label>Compare with rules</Label>
            <MultiSelect
              options={violationOptions}
              labelledBy="select rule"
              name="comparisonRules"
              value={comparisonRules}
              onChange={(selected) => setComparisonRules(selected)}
            />
          </FormGroup>
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Start Date</Label>
          <Datetime
            name="startDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            isValidDate={valid}
            value={startDate}
            onChange={(dateObj) => setStartDate(dateObj._d)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>End Date</Label>
          <Datetime
            name="endDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            isValidDate={valid}
            value={endDate}
            onChange={(dateObj) => setEndDate(dateObj._d)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Transaction Type</Label>
          <Input
            type="text"
            name="txnType"
            value={txnType}
            onChange={(e) => setTxnType(e.target.value)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Transaction Amount</Label>
          <InputGroup>
            <Input
              type="select"
              name="txnAmountComparator"
              value={txnAmountComparator}
              onChange={(e) => setTxnAmountComparator(e.target.value)}>
              <option value=">">&gt;</option>
              <option value="<">&lt;</option>
              <option value="=">=</option>
            </Input>
            <Input
              type="text"
              name="txnAmount"
              value={txnAmount}
              onChange={(e) => setTxnAmount(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Payee MCC</Label>
          <Input
            type="text"
            name="payeeMcc"
            value={payeeMcc}
            onChange={(e) => setPayeeMcc(e.target.value)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Payer MCC</Label>
          <Input
            type="text"
            name="payerMcc"
            value={payerMcc}
            onChange={(e) => setPayerMcc(e.target.value)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Response Code</Label>
          <Input
            type="text"
            name="responseCode"
            value={responseCode}
            onChange={(e) => setResponseCode(e.target.value)}
          />
        </Col>
        <Col xl="2" lg="3" md="4" sm="6">
          <Label>Source Institution ID</Label>
          <Input
            type="text"
            name="sourceInstitutionId"
            value={sourceInstitutionId}
            onChange={(e) => setSourceInstitutionId(e.target.value)}
          />
        </Col>
        <Col xs="12" className="d-flex justify-content-center">
          <Button
            color="success"
            type="button"
            size="sm"
            className="me-2"
            onClick={submit}
            disabled={testing.status == 'STARTED' || testing.status == 'PENDING'}>
            Start Test
          </Button>
        </Col>
      </Row>
      {testing.ruleName == selectedRule?.name &&
        testing.status !== '' &&
        testing.status !== 'COMPLETE' && <SandboxLoader status={testing.status} />}
      {testing.ruleName == selectedRule?.name && testing.status === 'COMPLETE' && (
        <SandboxResult rules={resultHeaders} result={sandboxing?.testing?.data} />
      )}
    </>
  );
};

Sandbox.propTypes = {
  ruleList: PropTypes.array.isRequired,
  sandboxing: PropTypes.object.isRequired,
  selectedRule: PropTypes.object.isRequired,
  sandboxingActions: PropTypes.object.isRequired
};

export default Sandbox;
