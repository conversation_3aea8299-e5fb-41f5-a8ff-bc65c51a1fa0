'use strict';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { FormGroup, Label, Input, Button, Form } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

function RuleLabel({
  channel,
  ruleData,
  updateRuleData,
  ruleLabels,
  toggle,
  onToggleCreateLabelModal,
  ruleCreationActions
}) {
  const [labelName, setLabelName] = useState('');

  const ruleLabelOptions =
    ruleLabels &&
    ruleLabels.map((label) => (
      <option key={label.id} value={label.name}>
        {label.name}
      </option>
    ));

  const handleCreateLabel = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const formData = {
      label: labelName
    };

    ruleCreationActions.onCreateRuleLabel(channel, formData);

    updateRuleData('label', labelName);
  };

  const handleToggleCreateLabelModal = () => {
    onToggleCreateLabelModal();
    setLabelName('');
  };

  return (
    <>
      <FormGroup>
        <Label>Labels</Label>
        <Input
          type="select"
          name="label"
          value={ruleData.label}
          onChange={(event) => {
            event.target.value === 'Custom'
              ? handleToggleCreateLabelModal()
              : updateRuleData('label', event.target.value);
          }}>
          <option value="">-- Select --</option>
          {ruleLabelOptions}
          <option value="Custom" className="custom">
            Custom
          </option>
        </Input>
      </FormGroup>
      <ModalContainer
        theme={toggle.theme}
        isOpen={toggle.createLabelModal}
        header="Create new label"
        size="md"
        toggle={handleToggleCreateLabelModal}>
        <Form onSubmit={handleCreateLabel}>
          <FormGroup>
            <Label for="labelName">Name</Label>
            <Input
              type="text"
              name="labelName"
              id="labelName"
              onChange={(event) => setLabelName(event.target.value)}
              value={labelName}
              required
            />
          </FormGroup>
          <FormGroup>
            <Button size="sm" color="primary" className="d-flex ms-auto">
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </>
  );
}

RuleLabel.propTypes = {
  channel: PropTypes.string.isRequired,
  ruleData: PropTypes.object.isRequired,
  updateRuleData: PropTypes.func.isRequired,
  ruleLabels: PropTypes.array.isRequired,
  toggle: PropTypes.object.isRequired,
  onToggleCreateLabelModal: PropTypes.func.isRequired,
  ruleCreationActions: PropTypes.object.isRequired
};

export default RuleLabel;
