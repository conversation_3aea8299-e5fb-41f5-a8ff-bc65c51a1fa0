import React from 'react';
import { render } from 'react-dom';
import store, { persistor } from './store/configureStore';
import Root from 'components/Root';
import 'react-datetime/css/react-datetime.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-table/react-table.css';
import 'styles/index.sass';

if (process.env.NODE_ENV == 'production') {
  if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ === 'object') {
    for (let [key, value] of Object.entries(window.__REACT_DEVTOOLS_GLOBAL_HOOK__)) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__[key] = typeof value == 'function' ? () => {} : null;
    }
  }
}

render(<Root store={store} persistor={persistor} />, document.getElementById('app'));
