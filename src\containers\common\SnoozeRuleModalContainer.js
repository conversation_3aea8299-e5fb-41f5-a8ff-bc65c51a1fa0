import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onSnoozeRule, onFetchSnoozeAttributesList } from 'actions/ruleSnoozeActions';
import RuleSnoozeModal from 'components/common/RuleSnoozeModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    channels: state.auth.userCreds.channels,
    snoozeAttributes: state.snoozeRules.attributes,
    txnDetails: state.transactionDetails
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    snoozeRule: bindActionCreators(onSnoozeRule, dispatch),
    fetchSnoozeAttributes: bindActionCreators(onFetchSnoozeAttributesList, dispatch)
  };
};

const RuleSnoozeModalContainer = connect(mapStateToProps, mapDispatchToProps)(RuleSnoozeModal);

export default RuleSnoozeModalContainer;
