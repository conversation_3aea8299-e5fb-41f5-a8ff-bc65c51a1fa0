import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import React from 'react';
import { useParams, Router } from 'react-router-dom';

import AuditCaseDetails from '../audit/AuditCaseDetails';

// Mock necessary components
jest.mock('containers/common/LogContainer', () =>
  jest.fn(() => <div data-testid="log">Log Component</div>)
);
jest.mock('components/loader/Loader', () =>
  jest.fn(() => <div data-testid="loader">Loader Component</div>)
);
jest.mock('containers/common/CaseDetailCardContainer', () =>
  jest.fn(() => <div data-testid="case-detail-card">CaseDetailCard Component</div>)
);
jest.mock('containers/investigation/STRReportLogContainer', () =>
  jest.fn(() => <div data-testid="str-report-log">STRReportLogContainer Component</div>)
);
jest.mock('containers/common/ViolatedRulesCardContainer', () =>
  jest.fn(() => <div data-testid="violated-rules-card">ViolatedRulesCardContainer Component</div>)
);
jest.mock('containers/common/TransactionDetailCardContainer', () =>
  jest.fn(() => (
    <div data-testid="transaction-detail-card">TransactionDetailCardContainer Component</div>
  ))
);

// Mock useParams
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn()
}));

describe('AuditCaseDetails', () => {
  let history;
  const mockSelectCase = jest.fn();
  const mockClearSelectedCase = jest.fn();
  const txnDetails = {
    details: {
      cognitiveResponse: '{"unusualMethods": ["method1", "method2"]}',
      transactionInfo: { txnTimestamp: '2023-01-01T00:00:00Z' },
      reViolatedRules: ['rule1', 'rule2']
    }
  };
  const txnDetailsWithoutReViolatedRules = {
    details: {
      cognitiveResponse: '{"unusualMethods": ["method1", "method2"]}',
      transactionInfo: { txnTimestamp: '2023-01-01T00:00:00Z' }
    }
  };
  const selectedCase = { txnId: '123', caseRefNo: 'ABC123' };
  const documentStatus = {};

  beforeEach(() => {
    history = createMemoryHistory();
    history.goBack = jest.fn();

    useParams.mockReturnValue({ txnId: '123', channel: 'str' });
  });

  test('selects case if txnId is present', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(mockSelectCase).toHaveBeenCalledWith({ txnId: '123', channel: 'str' });
  });

  test('does not select case if txnId is not present', () => {
    useParams.mockReturnValue({ txnId: '', channel: 'str' });
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(mockSelectCase).not.toHaveBeenCalled();
  });

  test('sets the document title correctly', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(document.title).toBe('BANKiQ FRC | Audit Case - ABC123');
  });

  test('cleans up document title on unmount', () => {
    const { unmount } = render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    unmount();

    expect(document.title).toBe('BANKiQ FRC');
  });

  test('renders Loader component if selectedCase is not available', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={{}}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  test('renders CaseDetailCard, TransactionDetailCardContainer, ViolatedRulesCardContainer, STRReportLogContainer, and Log components', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(screen.getByTestId('case-detail-card')).toBeInTheDocument();
    expect(screen.getByTestId('transaction-detail-card')).toBeInTheDocument();
    expect(screen.getByTestId('violated-rules-card')).toBeInTheDocument();
    expect(screen.getByTestId('str-report-log')).toBeInTheDocument();
    expect(screen.getByTestId('log')).toBeInTheDocument();
  });

  test('calls clearSelectedCase and history.goBack when Back button is clicked', async () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    fireEvent.click(screen.getByText('Back'));

    await waitFor(() => {
      expect(mockClearSelectedCase).toHaveBeenCalled();
      expect(history.goBack).toHaveBeenCalled();
    });
  });

  test('does not render ViolatedRulesCardContainer when txnDetails.details is empty', () => {
    const emptyTxnDetails = {
      details: {}
    };

    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={emptyTxnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    expect(screen.queryByTestId('violated-rules-card')).not.toBeInTheDocument();
  });

  test('renders ViolatedRulesCardContainer with reViolatedRules if available', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetails}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    const violatedRulesCard = screen.getByTestId('violated-rules-card');
    expect(violatedRulesCard).toBeInTheDocument();
    expect(violatedRulesCard).toHaveTextContent('ViolatedRulesCardContainer Component');
  });

  test('renders ViolatedRulesCardContainer with empty reViolatedRules if not available', () => {
    render(
      <Router history={history}>
        <AuditCaseDetails
          txnDetails={txnDetailsWithoutReViolatedRules}
          selectCase={mockSelectCase}
          selectedCase={selectedCase}
          documentStatus={documentStatus}
          clearSelectedCase={mockClearSelectedCase}
        />
      </Router>
    );

    const violatedRulesCard = screen.getByTestId('violated-rules-card');
    expect(violatedRulesCard).toBeInTheDocument();
    expect(violatedRulesCard).not.toHaveTextContent('rule1');
    expect(violatedRulesCard).not.toHaveTextContent('rule2');
  });
});
