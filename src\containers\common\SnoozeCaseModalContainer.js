import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import {
  onFetchLiabilityList,
  onFetchFraudTypesList,
  onFetchCloseCaseBuckets,
  onFetchSnoozeConditionsList
} from 'actions/caseReviewActions';
import SnoozeCaseModal from 'components/common/SnoozedCaseModal';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  liability: state.caseAssignment.liability,
  fraudTypes: state.caseAssignment.fraudTypes,
  caseSnoozeList: state.caseAssignment.snoozeConditions,
  closeCaseBuckets: state.caseAssignment.closeCaseBuckets
});

const mapDispatchToProps = (dispatch) => ({
  fetchSnoozeConditionsList: bindActionCreators(onFetchSnoozeConditionsList, dispatch),
  fetchLiabilityList: bindActionCreators(onFetchLiabilityList, dispatch),
  fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch),
  fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
});

const SnoozeCaseModalContainer = connect(mapStateToProps, mapDispatchToProps)(SnoozeCaseModal);

export default SnoozeCaseModalContainer;
