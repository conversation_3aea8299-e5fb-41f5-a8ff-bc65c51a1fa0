import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import customerCommunicationReducer from 'reducers/CustomerCommunicationReducer';
import initialState from 'reducers/initialState';

describe('customer Communication Reducer', () => {
  it('should return the intial state', () => {
    expect(customerCommunicationReducer(undefined, {})).toEqual(initialState.customerCommunication);
  });

  it('should handle ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING', () => {
    expect(
      customerCommunicationReducer(
        {
          logs: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING
        }
      )
    ).toEqual({
      logs: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS', () => {
    expect(
      customerCommunicationReducer(
        {
          logs: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS,
          response: responses.customerCommunication
        }
      )
    ).toEqual({
      logs: {
        data: responses.customerCommunication,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE', () => {
    expect(
      customerCommunicationReducer(
        {
          logs: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      logs: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
