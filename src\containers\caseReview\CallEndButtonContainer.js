import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onEndCustomerCall, onFetchCallDispositionList } from 'actions/communicationActions';
import CallEndButton from 'components/caseReview/CallEndButton';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  callDetails: state.customerCommunication.call,
  dispositions: state.customerCommunication.dispositions
});

const mapDispatchToProps = (dispatch) => ({
  endCall: bindActionCreators(onEndCustomerCall, dispatch),
  fetchDispositionsList: bindActionCreators(onFetchCallDispositionList, dispatch)
});

const CallEndButtonContainer = connect(mapStateToProps, mapDispatchToProps)(CallEndButton);

export default CallEndButtonContainer;
