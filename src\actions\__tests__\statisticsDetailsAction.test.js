import responses from 'mocks/responses';

import * as actions from 'actions/statisticsDetailsAction';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('statistics details actions', () => {
  it('should fetch transaction statistics details', () => {
    const selected = {
      id: 123,
      name: 'neha last-name',
      entityCategory: 'merchant',
      mobileNumber: '**********',
      accountNumber: '*************'
    };

    const expectedActions = [
      { type: types.ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS,
        response: responses.statisticsDetails.transactionStatistics
      }
    ];
    const store = mockStore({ statisticsDetails: {} });

    return store.dispatch(actions.onFetchTransactionStatisticsDetails(selected, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch customer statistics details', () => {
    const selected = {
      id: 123,
      name: 'neha last-name',
      entityCategory: 'merchant',
      mobileNumber: '**********',
      accountNumber: '*************'
    };

    const expectedActions = [
      { type: types.ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS,
        response: responses.statisticsDetails.customerStatistics
      }
    ];
    const store = mockStore({ statisticsDetails: {} });

    return store.dispatch(actions.onFetchCustomerStatisticsDetails(selected, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
