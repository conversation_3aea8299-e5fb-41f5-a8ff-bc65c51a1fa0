import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import dynamicCountersReducer from 'reducers/dynamicCountersReducer';
import initialState from 'reducers/initialState';

describe('dynamic Counters Reducer', () => {
  it('should return the intial state', () => {
    expect(dynamicCountersReducer(undefined, {})).toEqual(initialState.dynamicCounters);
  });

  it('should handle ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING', () => {
    expect(
      dynamicCountersReducer(
        {
          counters: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING
        }
      )
    ).toEqual({
      counters: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS', () => {
    expect(
      dynamicCountersReducer(
        {
          counters: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS,
          response: responses.dynamicCounters.counters
        }
      )
    ).toEqual({
      counters: {
        list: responses.dynamicCounters.counters,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE', () => {
    expect(
      dynamicCountersReducer(
        {
          counters: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      counters: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING', () => {
    expect(
      dynamicCountersReducer(
        {
          conditionalAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING
        }
      )
    ).toEqual({
      conditionalAttributes: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS', () => {
    expect(
      dynamicCountersReducer(
        {
          conditionalAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS,
          response: responses.dynamicCounters.conditionalAttributes
        }
      )
    ).toEqual({
      conditionalAttributes: {
        list: responses.dynamicCounters.conditionalAttributes,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE', () => {
    expect(
      dynamicCountersReducer(
        {
          conditionalAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      conditionalAttributes: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_ALL_ATTRIBUTES_LOADING', () => {
    expect(
      dynamicCountersReducer(
        {
          allAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_ATTRIBUTES_LOADING
        }
      )
    ).toEqual({
      allAttributes: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_ATTRIBUTES_SUCCESS', () => {
    expect(
      dynamicCountersReducer(
        {
          allAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_ATTRIBUTES_SUCCESS,
          response: responses.dynamicCounters.allAttributes
        }
      )
    ).toEqual({
      allAttributes: {
        list: responses.dynamicCounters.allAttributes,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_ATTRIBUTES_FAILURE', () => {
    expect(
      dynamicCountersReducer(
        {
          allAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_ATTRIBUTES_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      allAttributes: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_SUB_ATTRIBUTES_LOADING', () => {
    expect(
      dynamicCountersReducer(
        {
          subAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SUB_ATTRIBUTES_LOADING
        }
      )
    ).toEqual({
      subAttributes: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SUB_ATTRIBUTES_SUCCESS', () => {
    expect(
      dynamicCountersReducer(
        {
          subAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SUB_ATTRIBUTES_SUCCESS,
          response: responses.dynamicCounters.subAttributes
        }
      )
    ).toEqual({
      subAttributes: {
        list: responses.dynamicCounters.subAttributes,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SUB_ATTRIBUTES_FAILURE', () => {
    expect(
      dynamicCountersReducer(
        {
          subAttributes: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SUB_ATTRIBUTES_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      subAttributes: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });
});
