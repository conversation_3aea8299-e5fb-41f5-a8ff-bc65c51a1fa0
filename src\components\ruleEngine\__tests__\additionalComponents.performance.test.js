/* eslint-disable react/prop-types */
import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';

import performanceMonitor from 'utility/performanceMonitor';

import ArchievedRuleTable from '../ArchievedRuleTable';
import Checklist from '../Checklist';
import RuleDetailsForm from '../RuleDetailsForm';
import RuleEngineConfigurator from '../RuleEngineConfigurator';

// Mock dependencies
jest.mock('react-select', () => ({ options, value, onChange, isMulti }) => (
  <select
    data-testid="react-select"
    multiple={isMulti}
    value={isMulti ? value?.map((v) => v.value) : value?.value}
    onChange={(e) => {
      if (isMulti) {
        const selectedValues = Array.from(e.target.selectedOptions, (option) =>
          options.find((opt) => opt.value === option.value)
        );
        onChange(selectedValues);
      } else onChange(options.find((opt) => opt.value === e.target.value));
    }}>
    {options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ))}
  </select>
));

jest.mock('containers/ruleEngine/ActionOrderListContainer', () =>
  jest.fn(() => <div data-testid="action-order-list">Action Order List</div>)
);

jest.mock('containers/ruleEngine/LabelOrderListContainer', () =>
  jest.fn(() => <div data-testid="label-order-list">Label Order List</div>)
);

describe('Additional Components Performance Tests', () => {
  beforeEach(() => {
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  describe('RuleDetailsForm Performance', () => {
    const createLargeOptions = (count, prefix) =>
      Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        name: `${prefix} ${i + 1}`,
        value: `${prefix.toLowerCase()}_${i + 1}`
      }));

    const defaultRuleDetailsProps = {
      ruleData: {
        name: 'Test Rule',
        description: 'Test Description',
        actionCode: '01 - ACCEPTED',
        methodType: 'GET',
        alertCategoryId: 1,
        fraudCategory: 'Card Fraud',
        channels: 'FRM / STR',
        order: 1,
        assignmentPriority: 1,
        lowLevelOutcome: 'LOW',
        medLevelOutcome: 'MED',
        highLevelOutcome: 'HIGH'
      },
      toggle: { createLabelModal: false, theme: 'light' },
      moduleType: 'acquirer',
      actionList: createLargeOptions(50, 'Action'),
      ruleChannels: createLargeOptions(10, 'Channel'),
      alertCategories: createLargeOptions(25, 'Alert Category'),
      fraudCategories: createLargeOptions(30, 'Fraud Category'),
      ruleLabels: createLargeOptions(100, 'Label'),
      hasMakerChecker: true,
      hasAcquirerPortals: true,
      updateRuleData: jest.fn(),
      updateNextDisable: jest.fn(),
      ruleCreationActions: {
        onCreateRuleLabel: jest.fn()
      },
      onToggleCreateLabelModal: jest.fn()
    };

    it('should render with large option lists efficiently', () => {
      performanceMonitor.startTiming('rule_details_large_options');

      render(<RuleDetailsForm {...defaultRuleDetailsProps} />);

      const duration = performanceMonitor.endTiming('rule_details_large_options');
      expect(duration).toBeLessThan(200);
    });

    it('should handle memoized computations efficiently', () => {
      const { rerender } = render(<RuleDetailsForm {...defaultRuleDetailsProps} />);

      performanceMonitor.startTiming('rule_details_memoization');

      // Re-render with same props
      rerender(<RuleDetailsForm {...defaultRuleDetailsProps} />);

      const duration = performanceMonitor.endTiming('rule_details_memoization');
      expect(duration).toBeLessThan(30);
    });

    it('should handle form field changes efficiently', () => {
      render(<RuleDetailsForm {...defaultRuleDetailsProps} />);

      const nameInput = screen.getByDisplayValue('Test Rule');

      performanceMonitor.startTiming('rule_details_field_change');

      fireEvent.change(nameInput, { target: { value: 'Updated Rule Name' } });

      const duration = performanceMonitor.endTiming('rule_details_field_change');
      expect(duration).toBeLessThan(40);
    });
  });

  describe('Checklist Performance', () => {
    const defaultChecklistProps = {
      channel: 'frm',
      fetchCheckListOptions: jest.fn(),
      fetchCheckList: jest.fn(),
      code: 'TEST_RULE',
      checkListOptions: {
        list: Array.from({ length: 100 }, (_, i) => ({
          id: i + 1,
          citationName: `Citation ${i + 1}`,
          description: `Description for citation ${i + 1}`
        }))
      },
      checkList: {
        list: [
          { id: 1, citationName: 'Citation 1' },
          { id: 2, citationName: 'Citation 2' }
        ]
      },
      onChecklistChange: jest.fn()
    };

    it('should render large checklist options efficiently', () => {
      performanceMonitor.startTiming('checklist_large_options');

      render(<Checklist {...defaultChecklistProps} />);

      const duration = performanceMonitor.endTiming('checklist_large_options');
      expect(duration).toBeLessThan(150);
    });

    it('should handle checklist item addition efficiently', () => {
      render(<Checklist {...defaultChecklistProps} />);

      const addButton = screen.getByRole('button', { name: /plus/i });

      performanceMonitor.startTiming('checklist_add_item');

      fireEvent.click(addButton);

      const duration = performanceMonitor.endTiming('checklist_add_item');
      expect(duration).toBeLessThan(50);
    });

    it('should handle checklist item removal efficiently', () => {
      const propsWithMultipleItems = {
        ...defaultChecklistProps,
        checkList: {
          list: Array.from({ length: 5 }, (_, i) => ({
            id: i + 1,
            citationName: `Citation ${i + 1}`
          }))
        }
      };

      render(<Checklist {...propsWithMultipleItems} />);

      const removeButton = screen.getAllByRole('button', { name: /trash/i })[0];

      performanceMonitor.startTiming('checklist_remove_item');

      fireEvent.click(removeButton);

      const duration = performanceMonitor.endTiming('checklist_remove_item');
      expect(duration).toBeLessThan(40);
    });
  });

  describe('RuleEngineConfigurator Performance', () => {
    const defaultConfiguratorProps = {
      theme: 'light',
      channel: 'frm'
    };

    it('should render configurator efficiently', () => {
      performanceMonitor.startTiming('configurator_render');

      render(<RuleEngineConfigurator {...defaultConfiguratorProps} />);

      const duration = performanceMonitor.endTiming('configurator_render');
      expect(duration).toBeLessThan(60);
    });

    it('should handle modal toggles efficiently', () => {
      render(<RuleEngineConfigurator {...defaultConfiguratorProps} />);

      const configButton = screen.getByRole('button');
      fireEvent.click(configButton);

      const actionOrderItem = screen.getByText('Action Order');

      performanceMonitor.startTiming('configurator_modal_toggle');

      fireEvent.click(actionOrderItem);

      const duration = performanceMonitor.endTiming('configurator_modal_toggle');
      expect(duration).toBeLessThan(30);
    });
  });

  describe('ArchievedRuleTable Performance', () => {
    const createArchivedRules = (count) =>
      Array.from({ length: count }, (_, i) => ({
        code: `ARCH_${i}`,
        name: `Archived Rule ${i}`,
        logic: `TXN.AMOUNT > ${1000 + i}`,
        archivedDate: new Date(Date.now() - i * 86400000).toISOString(),
        archivedBy: `user${i % 5}`
      }));

    const defaultArchivedProps = {
      ruleList: {
        list: { frm: createArchivedRules(100) },
        loader: false,
        error: false
      },
      role: 'supervisor',
      theme: 'light',
      channel: 'frm',
      ruleConfiguratorActions: {
        onFetchArchievedRulesList: jest.fn(),
        onRestoreRule: jest.fn(),
        onPermanentDeleteRule: jest.fn()
      }
    };

    it('should render large archived rule lists efficiently', () => {
      performanceMonitor.startTiming('archived_rules_large_list');

      render(<ArchievedRuleTable {...defaultArchivedProps} />);

      const duration = performanceMonitor.endTiming('archived_rules_large_list');
      expect(duration).toBeLessThan(250);
    });

    it('should handle rule restoration efficiently', () => {
      render(<ArchievedRuleTable {...defaultArchivedProps} />);

      // Assuming there's a restore button for each rule
      const restoreButtons = screen.getAllByText(/restore/i);
      if (restoreButtons.length > 0) {
        performanceMonitor.startTiming('archived_rule_restore');

        fireEvent.click(restoreButtons[0]);

        const duration = performanceMonitor.endTiming('archived_rule_restore');
        expect(duration).toBeLessThan(50);
      }
    });
  });

  describe('Memory Management Across Components', () => {
    it('should not leak memory during component switching', () => {
      performanceMonitor.takeMemorySnapshot('before_component_switching');

      const components = [
        <RuleDetailsForm
          key="details"
          {...{
            ruleData: { name: 'Test' },
            toggle: { theme: 'light' },
            moduleType: 'acquirer',
            actionList: [],
            ruleChannels: [],
            alertCategories: [],
            fraudCategories: [],
            ruleLabels: [],
            updateRuleData: jest.fn(),
            updateNextDisable: jest.fn(),
            ruleCreationActions: {},
            onToggleCreateLabelModal: jest.fn()
          }}
        />,
        <Checklist
          key="checklist"
          {...{
            channel: 'frm',
            fetchCheckListOptions: jest.fn(),
            fetchCheckList: jest.fn(),
            checkListOptions: { list: [] },
            checkList: { list: [] },
            onChecklistChange: jest.fn()
          }}
        />,
        <RuleEngineConfigurator key="configurator" theme="light" channel="frm" />
      ];

      const renders = components.map((component) => render(component));

      performanceMonitor.takeMemorySnapshot('after_component_render');

      // Unmount all components
      renders.forEach(({ unmount }) => unmount());

      performanceMonitor.takeMemorySnapshot('after_component_unmount');
      performanceMonitor.checkMemoryLeaks();

      expect(true).toBe(true);
    });
  });
});
