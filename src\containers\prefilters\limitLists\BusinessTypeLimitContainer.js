import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import BusinessTypeLimit from 'components/prefilters/limitLists/BusinessTypeLimit';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  prefiltersList: state.prefiltersList
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const BusinessTypeLimitContainer = connect(mapStateToProps, mapDispatchToProps)(BusinessTypeLimit);

export default BusinessTypeLimitContainer;
