import { render, screen } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import React from 'react';
import { Router } from 'react-router-dom';

import AuditHomePage from '../audit/AuditHomePage';

jest.mock('components/common/Tabs', () =>
  jest.fn(() => <div data-testid="tabs">Tabs Component</div>)
);
jest.mock('containers/common/BucketCasesCardContainer', () =>
  jest.fn(() => (
    <div data-testid="bucket-cases-card-container">BucketCasesCardContainer Component</div>
  ))
);

describe('AuditHomePage', () => {
  let history;

  beforeEach(() => {
    history = createMemoryHistory();
    history.goBack = jest.fn();
  });

  test('redirects if userRoles is not auditor', () => {
    render(
      <Router history={history}>
        <AuditHomePage userRoles="not-auditor" channels={['channel1']} />
      </Router>
    );

    expect(history.goBack).toHaveBeenCalled();
  });

  test('sets the document title correctly', () => {
    render(
      <Router history={history}>
        <AuditHomePage userRoles="auditor" channels={['channel1']} />
      </Router>
    );

    expect(document.title).toBe('BANKiQ FRC | Supervise Cases');
  });

  test('renders Tabs component when there are multiple channels', () => {
    render(
      <Router history={history}>
        <AuditHomePage userRoles="auditor" channels={['channel1', 'channel2']} />
      </Router>
    );

    expect(screen.getByTestId('tabs')).toBeInTheDocument();
  });

  test('renders BucketCasesCardContainer component when there is only one channel', () => {
    render(
      <Router history={history}>
        <AuditHomePage userRoles="auditor" channels={['channel1']} />
      </Router>
    );

    expect(screen.getByTestId('bucket-cases-card-container')).toBeInTheDocument();
  });

  test('cleans up document title on unmount', () => {
    const { unmount } = render(
      <Router history={history}>
        <AuditHomePage userRoles="auditor" channels={['channel1']} />
      </Router>
    );

    unmount();

    expect(document.title).toBe('BANKiQ FRC');
  });
});
