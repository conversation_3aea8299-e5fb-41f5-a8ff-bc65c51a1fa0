import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchMerchantDemographicDetails } from 'actions/demographicDetailsAction';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import { onFetchUDSEntityDetails, onFetchFacctumDetails } from 'actions/udsActions';
import MerchantInfoCard from 'components/common/MerchantInfoCard';

const mapStateToProps = (state) => ({
  data: state.uds.merchant,
  facctumData: state.uds.facctumData,
  demographicDetails: state.demographicDetails,
  isFacctum: state.user.configurations.facctum
});

const mapDispatchToProps = (dispatch) => ({
  fetchDetails: bindActionCreators(onFetchUDSEntityDetails, dispatch),
  toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
  fetchMerchantDemographicDetails: bindActionCreators(onFetchMerchantDemographicDetails, dispatch),
  fetchFacctumDetails: bindActionCreators(onFetchFacctumDetails, dispatch)
});

const MerchantInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(MerchantInfoCard);

export default MerchantInfoCardContainer;
