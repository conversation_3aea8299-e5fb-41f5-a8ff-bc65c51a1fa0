import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchUDSEntityDetails, onFetchFacctumDetails } from 'actions/udsActions';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import { onFetchMerchantDemographicDetails } from 'actions/demographicDetailsAction';
import MerchantInfoCard from 'components/common/MerchantInfoCard';

const mapStateToProps = (state) => {
  return {
    data: state.uds.merchant,
    facctumData: state.uds.facctumData,
    demographicDetails: state.demographicDetails,
    isFacctum: state.user.configurations.facctum
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchDetails: bindActionCreators(onFetchUDSEntityDetails, dispatch),
    toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
    fetchMerchantDemographicDetails: bindActionCreators(
      onFetchMerchantDemographicDetails,
      dispatch
    ),
    fetchFacctumDetails: bindActionCreators(onFetchFacctumDetails, dispatch)
  };
};

const MerchantInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(MerchantInfoCard);

export default MerchantInfoCardContainer;
