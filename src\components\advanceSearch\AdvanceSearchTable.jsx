import React, { useState, useEffect, useMemo, useCallback } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { DropdownItem, Button } from 'reactstrap';

import DropdownButton from 'components/common/DropdownButton';
import CreateCaseModalContainer from 'containers/common/CreateCaseModalContainer';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';
import STRCaseReopenModalContainer from 'containers/common/STRCaseReopenModalContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import { formatMobile } from 'constants/functions';
import { isCooperative } from 'constants/publicKey';

function AdvanceSearchTable({
  role,
  userId,
  pageNo,
  pageRecords,
  stages,
  advanceSearchTxns,
  formDataAdvanceSearch,
  setPageNo,
  reOpenCase,
  fetchStages,
  setPageRecords,
  closeCaseBuckets,
  createCaseAndAssign,
  toggleCreateCaseModal,
  fetchCloseCaseBuckets
}) {
  const [tableFilters, setTableFilters] = useState([]);
  const [selectedCase, setSelectedCase] = useState({});

  useEffect(() => {
    if (_.isEmpty(stages)) fetchStages();
    if (_.isEmpty(closeCaseBuckets.list) && !closeCaseBuckets.loader) fetchCloseCaseBuckets();
  }, []);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const tablePageCountProp = useMemo(() => {
    return _.isEmpty(tableFilters)
      ? { pages: Math.max(1, Math.ceil(advanceSearchTxns.count / pageRecords)) }
      : {};
  }, [tableFilters, advanceSearchTxns, pageRecords]);

  const componentHeaders = useMemo(
    () => [
      {
        Header: 'Phone No.',
        accessor: 'payeeMobileNumber',
        Cell: ({ value }) => value ? formatMobile(value) : null
      },
      { Header: 'Agent ID', accessor: 'agentId' }
    ],
    []
  );

  const createCase = useCallback(
    (transaction, externalCaseCreationData = {}) => {
      const formData = {
        assignedTo: userId,
        assignedBy: userId,
        channel: transaction.caseType,
        txnId: transaction.txnId,
        partnerId: transaction?.partnerId || 0,
        ...(transaction?.caseOrigin && { caseOrigin: transaction.caseOrigin }),
        ...(!_.isEmpty(externalCaseCreationData) && {
          agencyType: externalCaseCreationData.agencyType,
          enquiryDetails: externalCaseCreationData.enquiryDetails
        })
      };

      const advanceSearchFormData = {
        filters: formDataAdvanceSearch,
        pageNo: pageNo + 1,
        pageSize: pageRecords
      };

      createCaseAndAssign(formData, advanceSearchFormData);
    },
    [userId, formDataAdvanceSearch, pageNo, pageRecords, createCaseAndAssign]
  );

  const renderTableActions = useCallback(
    (row) => {
      const { currentStatus, caseType, caseRefNo } = row.original;
      const roleLowerCase = role.toLowerCase();

      const createDropdown = (title, items) => (
        <DropdownButton name="Open Case" color="success" title={title}>
          {items.map((item, index) => (
            <DropdownItem key={index} onClick={item.onClick}>
              {item.label}
            </DropdownItem>
          ))}
        </DropdownButton>
      );

      if (
        currentStatus === '' &&
        caseType === 'frm' &&
        roleLowerCase !== 'supervisor' &&
        (!isCooperative || ['reviewer', 'investigator'].includes(roleLowerCase))
      ) {
        return createDropdown('Create Case', [
          { label: 'Direct', onClick: () => createCase(row.original) },
          {
            label: 'Customer Request',
            onClick: () => createCase({ ...row.original, caseOrigin: 'Customer' })
          }
        ]);
      }

      if (currentStatus === '' && caseType === 'str' && roleLowerCase === 'maker') {
        return createDropdown('Create Case', [
          { label: 'STR', onClick: () => createCase(row.original) },
          {
            label: 'External STR',
            onClick: () => {
              toggleCreateCaseModal();
              setSelectedCase(row.original);
            }
          }
        ]);
      }

      if (currentStatus === 'Closed' && caseType === 'str' && roleLowerCase !== 'supervisor') {
        return (
          <STRCaseReopenModalContainer
            caseRefNo={caseRefNo}
            advanceSearchData={{
              filters: formDataAdvanceSearch,
              pageNo: pageNo + 1,
              pageSize: pageRecords
            }}
          />
        );
      }

      if (
        currentStatus === 'Closed' &&
        caseType === 'frm' &&
        roleLowerCase !== 'supervisor' &&
        !isCooperative
      ) {
        return (
          <Button
            outline
            color="primary"
            size="sm"
            onClick={() =>
              reOpenCase(
                {
                  caseRefNo,
                  stageId:
                    _.find(stages, (stage) => stage.stageName.toLowerCase() === roleLowerCase)
                      ?.id || 0
                },
                'frm',
                null,
                { filters: formDataAdvanceSearch, pageNo: pageNo + 1, pageSize: pageRecords }
              )
            }>
            Re-open
          </Button>
        );
      }

      if (
        ['New', 'Open', 'Pending', 'Parked', 'MasterQueue'].includes(currentStatus) &&
        roleLowerCase === 'supervisor' &&
        !((currentStatus === 'Pending' || currentStatus === 'Parked') && caseType === 'str')
      ) {
        return (
          <UserListDropdownContainer
            className="ms-1"
            bucket={currentStatus}
            caseId={caseRefNo}
            channel={caseType}
          />
        );
      }

      return null;
    },
    [
      role,
      isCooperative,
      stages,
      formDataAdvanceSearch,
      pageNo,
      pageRecords,
      createCase,
      reOpenCase,
      toggleCreateCaseModal,
      setSelectedCase
    ]
  );

  const tableActions = useMemo(
    () => ({
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      minWidth: 130,
      style: { overflow: 'visible' },
      fixed: true,
      Cell: renderTableActions
    }),
    [renderTableActions]
  );

  const handlePageChange = useCallback((page) => setPageNo(page), [setPageNo]);

  const handlePageSizeChange = useCallback(
    (pageSize, page) => {
      setPageNo(page);
      setPageRecords(pageSize);
    },
    [setPageNo, setPageRecords]
  );

  const handleFilteredChange = useCallback((filtered) => setTableFilters(filtered), [
    setTableFilters
  ]);

  const data = useMemo(() => advanceSearchTxns, [advanceSearchTxns]);

  return (
    <>
      <TransactionTableContainer
        minRows={6}
        page={pageNo}
        pageSize={pageRecords}
        filtered={tableFilters}
        tableActions={tableActions}
        componentHeaders={componentHeaders}
        data={data}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onFilteredChange={handleFilteredChange}
        {...tablePageCountProp}
      />
      {role.toLowerCase() === 'maker' &&
        role.toLowerCase() === 'checker' &&
        role.toLowerCase() === 'investigator' && (
          <CreateCaseModalContainer
            handleExternalCreateCase={(data) => createCase(selectedCase, data)}
          />
        )}
    </>
  );
}

AdvanceSearchTable.propTypes = {
  role: PropTypes.string.isRequired,
  userId: PropTypes.string.isRequired,
  pageNo: PropTypes.number.isRequired,
  pageRecords: PropTypes.number.isRequired,
  stages: PropTypes.array.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  advanceSearchTxns: PropTypes.object.isRequired,
  formDataAdvanceSearch: PropTypes.object.isRequired,
  setPageNo: PropTypes.func.isRequired,
  reOpenCase: PropTypes.func.isRequired,
  fetchStages: PropTypes.func.isRequired,
  setPageRecords: PropTypes.func.isRequired,
  createCaseAndAssign: PropTypes.func.isRequired,
  toggleCreateCaseModal: PropTypes.func.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired
};

export default AdvanceSearchTable;
