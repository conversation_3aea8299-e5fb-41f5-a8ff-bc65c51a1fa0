import React from 'react';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getPayeeInfo = (details) => {
  let payeeInfoList = [];

  addItemToList(
    details?.payeeAccount?.payeeId?.value,
    'ID',
    details?.payeeAccount?.payeeId?.value,
    payeeInfoList,
    details?.payeeAccount?.payeeId
  );

  addItemToList(
    details?.payeeAccount?.payeeType,
    'Type',
    details?.payeeAccount?.payeeType,
    payeeInfoList
  );

  addItemToList(
    details?.masterFields?.payeeMccCodeName && details?.payeeAccount?.payeeMcc,
    'MCC',
    details?.masterFields?.payeeMccCodeName,
    payeeInfoList,
    details?.payeeAccount?.payeeMcc,
    true
  );

  addItemToList(
    details?.payeeAccount?.payeeCardNumberMask,
    'Masked Card Number',
    details?.payeeAccount?.payeeCardNumberMask,
    payeeInfoList
  );

  addItemToList(
    details?.payeeAccount?.payeeCardNumberHash?.value,
    'Hashed Card Number',
    details?.payeeAccount?.payeeCardNumberHash?.value,
    payeeInfoList,
    details?.payeeAccount?.payeeCardNumberHash
  );

  addItemToList(
    details?.masterFields?.payeeAccountTypeName,
    'Account Type',
    details?.masterFields?.payeeAccountTypeName,
    payeeInfoList,
    null,
    true
  );

  addItemToList(
    details?.payeeAccount?.payeeAccountNumber?.value,
    'Account No',
    details?.payeeAccount?.payeeAccountNumber?.value,
    payeeInfoList,
    details?.payeeAccount?.payeeAccountNumber
  );

  addItemToList(
    details?.payeeAccount?.payeeBankIfsc?.value,
    'Bank IFSC',
    details?.payeeAccount?.payeeBankIfsc?.value,
    payeeInfoList,
    details?.payeeAccount?.payeeBankIfsc
  );

  addItemToList(
    details?.payeeAccount?.payeeVpa?.value,
    'VPA',
    details?.payeeAccount?.payeeVpa?.value,
    payeeInfoList,
    details?.payeeAccount?.payeeVpa
  );

  addItemToList(
    details?.payeeAccount?.payeeMmidMobileNumber,
    'MMID Mobile',
    details?.payeeAccount?.payeeMmidMobileNumber,
    payeeInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.beneficiaryEmailID,
    'Email ID',
    details?.masterFields?.txnAdditionalFields?.beneficiaryEmailID,
    payeeInfoList
  );

  addItemToList(details?.payeeDeviceOf, 'Device', details?.payeeDeviceOf, payeeInfoList);

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.payeeAccountIsStaff,
    'Is Staff',
    details?.masterFields?.txnAdditionalFields?.payeeAccountIsStaff,
    payeeInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.payeeAccountIsMarked,
    'Is Marked',
    details?.masterFields?.txnAdditionalFields?.payeeAccountIsMarked,
    payeeInfoList
  );

  return payeeInfoList;
};
function TransactionPayeeInfo({ details }) {
  const payeeInfoList = getPayeeInfo(details);

  if (payeeInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Payee Details</b>
      {renderDataColumnList(payeeInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionPayeeInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionPayeeInfo;
