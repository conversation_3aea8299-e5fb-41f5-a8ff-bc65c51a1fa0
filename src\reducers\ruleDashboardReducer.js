import objectAssign from 'object-assign';

import {
  ON_FETCH_RULE_STATS_LOADING,
  ON_FETCH_RULE_STATS_SUCCESS,
  ON_FETCH_RULE_STATS_FAILURE,
  ON_FETCH_RULE_EFFICACY_LOADING,
  ON_<PERSON>ETCH_RULE_EFFICACY_SUCCESS,
  ON_FETCH_RULE_EFFICACY_FAILURE,
  ON_FETCH_RULE_BEHAVIOUR_LOADING,
  ON_FETCH_RULE_BEHAVIOUR_SUCCESS,
  ON_FETCH_RULE_BEHAVIOUR_FAILURE,
  ON_FETCH_RULE_EFFICIENCY_LOADING,
  ON_FETCH_RULE_EFFICIENCY_SUCCESS,
  ON_FETCH_RULE_EFFICIENCY_FAILURE,
  ON_FETCH_RULE_EFFECTIVENESS_LOADING,
  ON_FETCH_RULE_EFFECTIVENESS_SUCCESS,
  ON_FETCH_RULE_EFFECTIVENESS_FAILURE,
  ON_FETCH_RULE_FEEDBACK_LOADING,
  ON_FETCH_RULE_FEEDBACK_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_FAILURE,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function ruleDashboardReducer(state = initialState.ruleDashboard, action) {
  switch (action.type) {
    case ON_FETCH_RULE_STATS_LOADING:
      return objectAssign({}, state, {
        stats: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_STATS_SUCCESS:
      return objectAssign({}, state, {
        stats: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_STATS_FAILURE:
      return objectAssign({}, state, {
        stats: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_EFFICACY_LOADING:
      return objectAssign({}, state, {
        efficacy: {
          data: 0,
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFICACY_SUCCESS:
      return objectAssign({}, state, {
        efficacy: {
          data: action.response.ruleEfficacy,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFICACY_FAILURE:
      return objectAssign({}, state, {
        efficacy: {
          data: 0,
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_EFFICIENCY_LOADING:
      return objectAssign({}, state, {
        efficiency: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFICIENCY_SUCCESS:
      return objectAssign({}, state, {
        efficiency: {
          data: action.response.ruleEfficiency,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFICIENCY_FAILURE:
      return objectAssign({}, state, {
        efficiency: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_EFFECTIVENESS_LOADING:
      return objectAssign({}, state, {
        effectiveness: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFECTIVENESS_SUCCESS:
      return objectAssign({}, state, {
        effectiveness: {
          data: action.response.alertRates,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_EFFECTIVENESS_FAILURE:
      return objectAssign({}, state, {
        effectiveness: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_BEHAVIOUR_LOADING:
      return objectAssign({}, state, {
        behaviour: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_BEHAVIOUR_SUCCESS:
      return objectAssign({}, state, {
        behaviour: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_BEHAVIOUR_FAILURE:
      return objectAssign({}, state, {
        behaviour: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_FEEDBACK_LOADING:
      return objectAssign({}, state, {
        ruleFeedbacks: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_SUCCESS:
      return objectAssign({}, state, {
        ruleFeedbacks: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_FAILURE:
      return objectAssign({}, state, {
        ruleFeedbacks: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING:
      return objectAssign({}, state, {
        ruleFeedbackAnalysis: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS:
      return objectAssign({}, state, {
        ruleFeedbackAnalysis: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE:
      return objectAssign({}, state, {
        ruleFeedbackAnalysis: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING:
      return objectAssign({}, state, {
        ruleFeedbackAnalysisStats: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS:
      return objectAssign({}, state, {
        ruleFeedbackAnalysisStats: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE:
      return objectAssign({}, state, {
        ruleFeedbackAnalysisStats: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.message
        }
      });
    default:
      return state;
  }
}
