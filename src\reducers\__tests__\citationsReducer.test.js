import citationsReducer from 'reducers/citationsReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import responses from 'mocks/responses';

describe('citations Reducer', () => {
  it('should return the intial state', () => {
    expect(citationsReducer(undefined, {})).toEqual(initialState.citations);
  });

  it('should handle ON_FETCH_CASE_CITATION_LOADING', () => {
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_CITATION_LOADING
        }
      )
    ).toEqual({
      caseRefNo: '',
      data: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_CASE_CITATION_SUCCESS', () => {
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_CITATION_SUCCESS,
          response: responses.citations
        }
      )
    ).toEqual({
      caseRefNo: responses.citations.caseRefNo,
      data: {
        list: responses.citations.ruleCitationResponses,
        loader: false,
        error: false,
        errorMessage: ''
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_CASE_CITATION_FAILURE', () => {
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_CITATION_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      caseRefNo: '',
      data: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      loader: false,
      error: true,
      errorMessage: 'Insufficient rights to access data'
    });
  });

  it('should handle ON_ADD_CITATION_COMMENT_LOADING', () => {
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_ADD_CITATION_COMMENT_LOADING
        }
      )
    ).toEqual({
      caseRefNo: '',
      data: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_ADD_CITATION_COMMENT_SUCCESS', () => {
    let formData = {
      caseRefNo: '123',
      citationId: 45,
      response: 'test',
      txnId: 'iucaaTxn20230421063',
      userId: '1',
      userName: 'admin',
      userRole: 'principal-officer'
    };
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_ADD_CITATION_COMMENT_SUCCESS,
          response: formData
        }
      )
    ).toEqual({
      caseRefNo: '',
      data: {
        list: [],
        loader: false,
        error: false,
        errorMessage: ''
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_ADD_CITATION_COMMENT_FAILURE', () => {
    expect(
      citationsReducer(
        {
          caseRefNo: '',
          data: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          },
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_ADD_CITATION_COMMENT_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      caseRefNo: '',
      data: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      },
      loader: false,
      error: false,
      errorMessage: ''
    });
  });
});
