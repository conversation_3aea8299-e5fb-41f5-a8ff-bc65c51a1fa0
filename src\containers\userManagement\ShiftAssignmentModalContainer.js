import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onToggleAssignShiftModal } from 'actions/toggleActions';
import { onUserAssignShifts } from 'actions/userManagementActions';
import ShiftAssignmentModal from 'components/userManagement/ShiftAssignmentModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    shiftslist: state.user.shifts,
    showAssignShiftModal: state.toggle.assignShiftModal
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    userAssignShifts: bindActionCreators(onUserAssignShifts, dispatch),
    toggleAssignShiftModal: bindActionCreators(onToggleAssignShiftModal, dispatch)
  };
};

const ShiftAssignmentModalContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ShiftAssignmentModal);

export default ShiftAssignmentModalContainer;
