import responses from 'mocks/responses';

import * as actions from 'actions/complianceDashboardActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  auth: { userCreds },
  complianceDashboard: {}
};

describe('compliance Dashboard actions', () => {
  it('should Fetch FMR Reported Cases', () => {
    const expectedActions = [
      { type: types.ON_FETCH_FMR_REPORTED_CASE_LOADING },
      {
        type: types.ON_FETCH_FMR_REPORTED_CASE_SUCCESS,
        response: responses.complianceDashboard
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchFMRReportedCases()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
