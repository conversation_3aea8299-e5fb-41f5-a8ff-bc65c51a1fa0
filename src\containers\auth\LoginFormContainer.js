import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchAllRoles, onFetchLoginTypes } from 'actions/authActions';
import { onFetchChannels } from 'actions/userManagementActions';
import LoginForm from 'components/auth/LoginForm';

const mapStateToProps = (state) => ({
  roles: state.auth.allRoles,
  channels: state.user.channels,
  loginTypes: state.auth.loginTypes
});

const mapDispatchToProps = (dispatch) => ({
  fetchRoles: bindActionCreators(onFetchAllRoles, dispatch),
  fetchChannels: bindActionCreators(onFetchChannels, dispatch),
  fetchLoginTypes: bindActionCreators(onFetchLoginTypes, dispatch)
});

const LoginFormContainer = connect(mapStateToProps, mapDispatchToProps)(LoginForm);

export default LoginFormContainer;
