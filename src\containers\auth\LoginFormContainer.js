import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchChannels } from 'actions/userManagementActions';
import { onFetchAllRoles, onFetchLoginTypes } from 'actions/authActions';
import LoginForm from 'components/auth/LoginForm';

const mapStateToProps = (state) => {
  return {
    roles: state.auth.allRoles,
    channels: state.user.channels,
    loginTypes: state.auth.loginTypes
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRoles: bindActionCreators(onFetchAllRoles, dispatch),
    fetchChannels: bindActionCreators(onFetchChannels, dispatch),
    fetchLoginTypes: bindActionCreators(onFetchLoginTypes, dispatch)
  };
};

const LoginFormContainer = connect(mapStateToProps, mapDispatchToProps)(LoginForm);

export default LoginFormContainer;
