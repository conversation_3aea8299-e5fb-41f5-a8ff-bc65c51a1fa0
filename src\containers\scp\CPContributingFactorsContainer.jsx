import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onRecalculatePropensityScores } from 'actions/scpActions';
import CPContributingFactors from 'components/scp/CPContributingFactors';

const mapStateToProps = (state) => ({
  propensityScores: state.scp.propensityScores
});

const mapDispatchToProps = (dispatch) => ({
  recalculatePropensityScores: bindActionCreators(onRecalculatePropensityScores, dispatch)
});

const CPContributingFactorsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CPContributingFactors);

export default CPContributingFactorsContainer;
