import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onLogin, onValidateUser2FA, onSubmitOTP2FA } from 'actions/authActions';
import { onFetchConfigurations } from 'actions/userManagementActions';
import LoginPage from 'components/auth/LoginPage';

const mapStateToProps = (state) => ({
  authDetails: state.auth,
  theme: state.toggle.theme,
  has2FA: state.user.configurations.tFA
});

const mapDispatchToProps = (dispatch) => ({
  login: bindActionCreators(onLogin, dispatch),
  submitOTP: bindActionCreators(onSubmitOTP2FA, dispatch),
  validateUser2FA: bindActionCreators(onValidateUser2FA, dispatch),
  fetchConfigurations: bindActionCreators(onFetchConfigurations, dispatch)
});

const LoginContainer = connect(mapStateToProps, mapDispatchToProps)(LoginPage);

export default LoginContainer;
