import React from 'react';
import { connect } from 'react-redux';
import AnalystDashboard from 'components/dashboards/AnalystDashboard';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    channels: state.auth.userCreds.channels,
    hasKnowageReport: state.user.configurations.knowageReport,
    moduleType: state.auth.moduleType
  };
};

const AnalystDashboardContainer = connect(
  mapStateToProps,
  null
)((props) => (
  <DateRangeProvider contextKey="analystDashboard">
    <AnalystDashboard {...props} contextKey="analystDashboard" />
  </DateRangeProvider>
));

export default AnalystDashboardContainer;
