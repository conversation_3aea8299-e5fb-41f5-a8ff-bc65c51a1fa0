import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import CaseDetailCard from 'components/common/CaseDetailCard';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
  };
};

const CaseDetailCardContainer = connect(mapStateToProps, mapDispatchToProps)(CaseDetailCard);

export default CaseDetailCardContainer;
