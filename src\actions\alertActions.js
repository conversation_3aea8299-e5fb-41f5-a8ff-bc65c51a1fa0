import {
  ON_CLEAR_ALERT,
  ON_FAILURE_ALERT,
  ON_SUCCESS_ALERT,
  ON_WARNING_ALERT
} from 'constants/actionTypes';

function onClearAlert() {
  return { type: ON_CLEAR_ALERT };
}

{
  /*TODO: Clear alert on LOCATION_CHANGE*/
}
function onShowSuccessAlert(response) {
  return function (dispatch) {
    dispatch({ type: ON_SUCCESS_ALERT, response });
    setTimeout(() => dispatch(onClearAlert()), 8000);
  };
}

function onShowWarningAlert(response) {
  return function (dispatch) {
    dispatch({ type: ON_WARNING_ALERT, response });
    setTimeout(() => dispatch(onClearAlert()), 8000);
  };
}

function onShowFailureAlert(response) {
  return function (dispatch) {
    dispatch({ type: ON_FAILURE_ALERT, response });
    setTimeout(() => dispatch(onClearAlert()), 8000);
  };
}

export { onClear<PERSON>lert, onShowSuc<PERSON><PERSON>lert, onShowFailureAlert, onShowWarning<PERSON>lert };
