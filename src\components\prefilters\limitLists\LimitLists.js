'use strict';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input } from 'reactstrap';
import BusinessTypeLimit from 'containers/prefilters/limitLists/BusinessTypeLimitContainer';
import MccLimit from 'containers/prefilters/limitLists/MccLimitContainer';
import MerchantLimit from 'containers/prefilters/limitLists/MerchantLimitContainer';
import MerchantDailyLimit from 'containers/prefilters/limitLists/MerchantDailyLimitContainer';
import SettlementTypeLimit from 'containers/prefilters/limitLists/SettlementTypeLimitContainer';
import OnboardingTypeLimit from 'containers/prefilters/limitLists/OnboardingTypeLimitContainer';

const LimitLists = ({ theme, role, actions }) => {
  const [prefilterId, setPrefilterId] = useState('');
  const [currentPrefilterList, setCurrentPrefilterList] = useState('');

  const handleSelectPrefilterList = (event, prefilterLists) => {
    const target = event.target;
    let currentFilterObject = _.filter(prefilterLists, (o) => {
      return o.prefilterId == target.value;
    });

    setPrefilterId(target.value);
    setCurrentPrefilterList(currentFilterObject[0]);
    (currentFilterObject[0].prefilterValue == 'merchant' ||
      currentFilterObject[0].prefilterValue == 'merchantdaily') &&
      actions.onResetLimitListWithPagination();
  };

  useEffect(() => {
    document.title = 'BANKiQ FRC | Prefilters - ' + currentPrefilterList.prefilterValue;

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [currentPrefilterList]);

  const prefilterLists = [
    {
      prefilterName: 'Business Type Limit',
      prefilterId: 0,
      prefilterValue: 'business'
    },
    { prefilterName: 'Mcc Limit', prefilterId: 1, prefilterValue: 'mcc' },
    { prefilterName: 'Merchant Limit', prefilterId: 2, prefilterValue: 'merchant' },
    { prefilterName: 'Merchant Daily Limit', prefilterId: 3, prefilterValue: 'merchantdaily' },
    {
      prefilterName: 'Settlement Type Limit',
      prefilterId: 4,
      prefilterValue: 'settlement'
    },
    {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    }
  ];

  let prefilterOptions = prefilterLists.map((prefilter) => (
    <option key={prefilter.prefilterId} value={prefilter.prefilterId}>
      {prefilter.prefilterName}
    </option>
  ));

  let indepthComponent = {
    business: BusinessTypeLimit,
    mcc: MccLimit,
    merchant: MerchantLimit,
    merchantdaily: MerchantDailyLimit,
    settlement: SettlementTypeLimit,
    onboarding: OnboardingTypeLimit
  };

  let Indepth = indepthComponent[currentPrefilterList.prefilterValue];

  return (
    <div className={`${theme} prefilter-container`}>
      <div>
        <FormGroup className="select-pefilter">
          <Label for="prefilterId">Select Limit List</Label>
          <Input
            type="select"
            id="prefilterId"
            name="prefilterId"
            className="prefilter-list"
            value={prefilterId}
            onChange={(e) => handleSelectPrefilterList(e, prefilterLists)}
            required>
            {currentPrefilterList == '' && <option value="">-- select --</option>}
            {prefilterOptions}
          </Input>
        </FormGroup>

        {currentPrefilterList !== '' && (
          <Indepth currentPrefilterList={currentPrefilterList} listType={'limitList'} role={role} />
        )}
      </div>
    </div>
  );
};

LimitLists.propTypes = {
  theme: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired,
  actions: PropTypes.object.isRequired
};

export default LimitLists;
