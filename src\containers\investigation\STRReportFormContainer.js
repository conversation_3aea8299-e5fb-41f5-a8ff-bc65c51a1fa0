import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSTRReportMasters } from 'actions/strReportActions';
import STRReportForm from 'components/investigation/STRReportForm';

const mapStateToProps = (state) => ({
  strReportMasters: state.strReport.masters,
  violations: state.violatedRules,
  citations: state.citations,
  caseDocument: state.caseDocument
});

const mapDispatchToProps = (dispatch) => ({
  getMasters: bindActionCreators(onFetchSTRReportMasters, dispatch)
});

const STRReportFormContainer = connect(mapStateToProps, mapDispatchToProps)(STRReportForm);

export default STRReportFormContainer;
