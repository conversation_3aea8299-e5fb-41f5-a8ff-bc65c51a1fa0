import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, DropdownItem } from 'reactstrap';
import DropdownButton from 'components/common/DropdownButton';
import ConfirmAlert from 'components/common/ConfirmAlert';
import NotificationButtonsContainer from 'containers/caseReview/NotificationButtonsContainer';
import Moment from 'moment';
import { useHistory } from 'react-router-dom';

function OneViewActions({
  theme,
  userId,
  userName,
  stages,
  parkCase,
  fetchCase,
  addVerdict,
  fetchStages,
  assignNewCase,
  fetchChannelCounterpartyId,
  txnDetails,
  oneViewData,
  prefiltersList,
  prefilterActions,
  channelCounterpartyId,
  txnId = undefined
}) {
  const [toggle, setToggle] = useState(false);
  const [selectedBlock, setSelectedBlock] = useState({});
  const [blockHours, setBlockHours] = useState('');
  const history = useHistory();

  const handleIsNight = () => {
    const now = new Date();
    const hour = now.getHours();

    const sunriseHour = 8; // Example: 8 AM
    const sunsetHour = 20; // Example: 8 PM
    return hour < sunriseHour || hour >= sunsetHour;
  };

  const isNight = handleIsNight() || false;

  useEffect(() => {
    _.isEmpty(prefiltersList.specializedListTypes.data) &&
      prefilterActions.onFetchSpecializedListTypes();
    _.isEmpty(channelCounterpartyId.list) &&
      !channelCounterpartyId.loader &&
      !channelCounterpartyId.error &&
      fetchChannelCounterpartyId();
    if (_.isEmpty(stages)) fetchStages();
  }, []);

  const blockedList = prefiltersList.specializedListTypes.data?.find(
    (d) => d.listName === 'Blocked'
  );

  const fetchCaseOrRedirect = () => {
    if (txnId) history.push('/review');
    else fetchCase();
  };

  const toggleBlock = (isOpen, data, blockHours = '') => {
    if (!isOpen && data) {
      setSelectedBlock(data);
      setToggle(true);
      setBlockHours(blockHours);
    } else {
      setSelectedBlock({});
      setToggle(false);
      setBlockHours('');
    }
  };

  const handleSelectItem = () => {
    const currentListInfo = {
      prefilterName: `${blockedList?.listName ?? 'Blocked'} List`,
      prefilterValue: blockedList?.listName ?? 'Blocked',
      prefilterListType: blockedList?.groupName ?? 'specializedList'
    };
    let formData = {
      categoryName: selectedBlock?.categoryName,
      identifier: selectedBlock?.value,
      partnerId: oneViewData?.partnerId,
      caseRefNo: oneViewData?.caseRefNo,
      txnId: oneViewData?.txnId,
      entityId: oneViewData?.entityId,
      isActive: 1,
      isHashed: true,
      ...(!_.isEmpty(blockHours) && {
        startDate: Moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      }),
      ...(!_.isEmpty(blockHours) && {
        endDate: Moment(new Date()).add(blockHours, 'hours').format('YYYY-MM-DD HH:mm:ss')
      })
    };

    if (_.includes(selectedBlock?.listTypeData, 'Blocked')) {
      prefilterActions.onDeleteSpecializedListItem(
        formData,
        currentListInfo,
        'addToListAction',
        'specializedList'
      );
    } else {
      prefilterActions.onAddSingleItemToSpecializedList(
        formData,
        currentListInfo,
        'specializedList',
        'addToListAction'
      );
    }

    setToggle(false);
  };

  const counterpartyId = channelCounterpartyId.list?.find((d) => {
    const channel = _.split(txnDetails?.masterFields?.channelName, ', ');
    return _.includes(channel, d.channel);
  });

  const isPayeeIdBlocked =
    _.includes(
      txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId]?.listTypeData,
      'Blocked'
    ) ||
    _.includes(
      txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId]?.listTypeData,
      'Blocked'
    );

  const isPayerIdBlocked = _.includes(
    txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId]?.listTypeData,
    'Blocked'
  );

  const stageId = _.find(stages, (stage) => _.lowerCase(stage.stageName) === 'reviewer')?.id;

  const assignToSelf = () => {
    const formData = {
      stageId,
      caseRefNo: oneViewData?.caseRefNo,
      assignedTo: userId,
      assignedToName: userName
    };
    assignNewCase(formData, false, oneViewData?.channel);
  };

  return (
    <>
      <div className="d-flex justify-content-between mb-2">
        <span>
          {!_.isEmpty(blockedList) &&
            !_.isEmpty(counterpartyId?.payeeInfo) &&
            (!_.isEmpty(txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId]) ||
              !_.isEmpty(txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId])) &&
            (isPayeeIdBlocked ? (
              <Button
                size="sm"
                outline
                color="warning"
                onClick={() =>
                  toggleBlock(
                    toggle,
                    txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId] ??
                      txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId]
                  )
                }
                disabled={oneViewData?.assignedTo !== userName}>
                {`Unblock ${counterpartyId?.payeeInfo.payeeAttribute}`}
              </Button>
            ) : (
              <DropdownButton
                name={`Block ${counterpartyId?.payeeInfo.payeeAttribute}`}
                color="warning"
                data-testid="permanent-block-dropdown-payeeId"
                disabled={oneViewData?.assignedTo !== userName}>
                <DropdownItem
                  data-testid="permanent-block-dropdown-item-payeeId"
                  onClick={() =>
                    toggleBlock(
                      toggle,
                      txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId] ??
                        txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId]
                    )
                  }>
                  Permanent Block
                </DropdownItem>
                <DropdownItem
                  data-testid="block-for-dropdown-payeeId"
                  onClick={() =>
                    toggleBlock(
                      toggle,
                      txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId] ??
                        txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId],
                      isNight ? '8' : '4'
                    )
                  }>
                  Block for {isNight ? '8' : '4'} hours
                </DropdownItem>
              </DropdownButton>
            ))}

          {!_.isEmpty(blockedList) &&
            !_.isEmpty(counterpartyId?.payerInfo) &&
            !_.isEmpty(txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId]) &&
            (isPayerIdBlocked ? (
              <Button
                size="sm"
                color="warning"
                className="ms-3"
                outline
                onClick={() =>
                  toggleBlock(toggle, txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId])
                }
                disabled={oneViewData?.assignedTo !== userName}>
                {`Unblock ${counterpartyId?.payerInfo.payerAttribute}`}
              </Button>
            ) : (
              <DropdownButton
                name={`Block ${counterpartyId?.payerInfo.payerAttribute}`}
                color="warning"
                data-testid="permanent-block-dropdown-payerId"
                disabled={oneViewData?.assignedTo !== userName}>
                <DropdownItem
                  data-testid="permanent-block-dropdown-item-payerId"
                  onClick={() =>
                    toggleBlock(
                      toggle,
                      txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId]
                    )
                  }>
                  Permanent Block
                </DropdownItem>
                <DropdownItem
                  data-testid="block-for-dropdown-payerId"
                  onClick={() =>
                    toggleBlock(
                      toggle,
                      txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId],
                      isNight ? '8' : '4'
                    )
                  }>
                  Block for {isNight ? '8' : '4'} hours
                </DropdownItem>
              </DropdownButton>
            ))}
          {_.includes(['Open', 'Parked'], oneViewData?.investigationStatus) && (
            <span>
              <Button
                size="sm"
                data-testid="park-case-btn"
                outline
                color="primary"
                className="ms-3"
                disabled={
                  oneViewData?.investigationStatus === 'Parked' ||
                  oneViewData?.assignedTo !== userName
                }
                onClick={() =>
                  oneViewData?.assignedTo == userName &&
                  parkCase(
                    {
                      stageId,
                      caseRefNo: oneViewData?.caseRefNo,
                      reason: 'Called the customer - Customer not available'
                    },
                    {},
                    'frm'
                  )
                }>
                Park Case
              </Button>
              <NotificationButtonsContainer
                channel={oneViewData?.channel}
                entityId={oneViewData?.entityId}
                caseRefNo={oneViewData?.caseRefNo}
                disabled={oneViewData?.investigationStatus !== 'Parked'}
                caseDetails={oneViewData}
              />
              <Button
                size="sm"
                data-testid="fraud-btn"
                outline
                color="danger"
                className="ms-3"
                disabled={
                  oneViewData?.investigationStatus === 'Parked' ||
                  oneViewData?.assignedTo !== userName
                }
                onClick={() => oneViewData?.assignedTo === userName && addVerdict('Fraud')}>
                Fraud
              </Button>
              <Button
                size="sm"
                data-testid="not-fraud-btn"
                color="success"
                className="ms-3"
                disabled={
                  oneViewData?.investigationStatus === 'Parked' ||
                  oneViewData?.assignedTo !== userName
                }
                onClick={() => oneViewData?.assignedTo === userName && addVerdict('Not Fraud')}>
                Not Fraud
              </Button>
            </span>
          )}
        </span>
        <span>
          {oneViewData?.assignedTo !== userName && oneViewData?.assignedTo !== 'frmsys' && (
            <Button outline size="sm" color="warning" className="ms-1" onClick={assignToSelf}>
              Self Assign
            </Button>
          )}
          <Button
            outline
            size="sm"
            data-testid="next-btn"
            color="secondary"
            className="ms-1 ps-4 pe-4"
            onClick={() => fetchCaseOrRedirect()}
            disabled={
              oneViewData?.investigationStatus === 'Open' && oneViewData?.assignedTo == userName
            }>
            Next
          </Button>
        </span>
      </div>
      <ConfirmAlert
        theme={theme}
        confirmAlertModal={toggle}
        toggleConfirmAlertModal={() => toggleBlock(toggle)}
        confirmationAction={handleSelectItem}
        confirmAlertTitle={
          _.includes(selectedBlock?.listTypeData, 'Blocked')
            ? `Are you sure you want to remove ${selectedBlock?.categoryName} from Blocked list ?`
            : `Are you sure you want to add ${selectedBlock?.categoryName} to Blocked list ?`
        }
      />
    </>
  );
}

OneViewActions.propTypes = {
  txnId: PropTypes.string,
  theme: PropTypes.string.isRequired,
  userId: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  stages: PropTypes.array.isRequired,
  parkCase: PropTypes.func.isRequired,
  fetchCase: PropTypes.func.isRequired,
  addVerdict: PropTypes.func.isRequired,
  fetchStages: PropTypes.func.isRequired,
  assignNewCase: PropTypes.func.isRequired,
  fetchChannelCounterpartyId: PropTypes.func.isRequired,
  txnDetails: PropTypes.object.isRequired,
  oneViewData: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  prefilterActions: PropTypes.object.isRequired,
  channelCounterpartyId: PropTypes.object.isRequired
};

export default OneViewActions;
