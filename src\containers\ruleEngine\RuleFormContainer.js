import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import { onFetchAllLists } from 'actions/prefiltersListAction';
import RuleForm from 'components/ruleEngine/RuleForm';

const mapStateToProps = (state) => {
  return {
    moduleType: state.auth.moduleType,
    ruleCreation: state.ruleCreation,
    ruleList: state.ruleConfigurator.productionRules.list,
    hasMakerChecker: state.user.hasMakerChecker,
    hasSandbox: state.user.configurations.sandbox,
    hasAcquirerPortals: state.user.configurations.acquirerPortals
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
    fetchPrefilterLists: bindActionCreators(onFetchAllLists, dispatch)
  };
};

const RuleFormContainer = connect(mapStateToProps, mapDispatchToProps)(RuleForm);

export default RuleFormContainer;
