import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchAllLists } from 'actions/prefiltersListAction';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import RuleForm from 'components/ruleEngine/RuleForm';
import {
  getModuleType,
  getRuleCreationState,
  getProductionRules,
  getHasMakerChecker,
  getHasSandbox,
  getHasAcquirerPortals
} from 'selectors/ruleEngineSelectors';

// Optimized mapStateToProps using memoized selectors
const mapStateToProps = (state) => ({
  moduleType: getModuleType(state),
  ruleCreation: getRuleCreationState(state),
  ruleList: getProductionRules(state).list,
  hasMakerChecker: getHasMakerChecker(state),
  hasSandbox: getHasSandbox(state),
  hasAcquirerPortals: getHasAcquirerPortals(state)
});

const mapDispatchToProps = (dispatch) => ({
  ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
  fetchPrefilterLists: bindActionCreators(onFetchAllLists, dispatch)
});

const RuleFormContainer = connect(mapStateToProps, mapDispatchToProps)(RuleForm);

export default RuleFormContainer;
