import authReducer from 'reducers/authReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import objectAssign from 'object-assign';

describe('Auth reducer', () => {
  //jest.spyOn(Date, 'now').mockReturnValue(() => 1705651511);
  beforeAll(() => {
    function dateMock() {
      this.getMinutes = jest.fn(() => {
        return 1705651511;
      });
      this.setMinutes = jest.fn(() => {
        return 1705665112254;
      });
    }
    global.Date = dateMock;
  });

  it('should return the intial state', () => {
    expect(authReducer(undefined, {})).toEqual(initialState.auth);
  });

  it('should handle ON_SUCCESSFUL_LOGIN', () => {
    expect(
      authReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_LOGIN,
          response: {
            id: 1,
            userName: 'sdchecker',
            email: 'sdchecker',
            channelRoles: ['rpsl:checker'],
            moduleType: 'acquirer'
          }
        }
      )
    ).toEqual({
      userCreds: {
        userId: 1,
        email: 'sdchecker',
        userName: 'sdchecker',
        channelRoles: ['rpsl:checker'],
        roles: 'checker',
        channels: ['rpsl']
      },
      session: {
        isLoggedIn: true,
        sessionTimeout: 1705665112254,
        isIdle: false,
        logoutModal: false
      },
      moduleType: 'acquirer',
      errorMessage: 'Successfully logged in'
    });
  });

  it('should handle ON_LOGOUT_FAILURE', () => {
    expect(
      authReducer(
        {
          userCreds: {
            email: '<EMAIL>',
            userName: 'admin',
            role: 'admin',
            roleId: '1'
          },
          isLoggedIn: true,
          errorMessage: 'Successfully logged in'
        },
        {
          type: types.ON_LOGOUT_FAILURE,
          response: { message: 'Something went wrong. Please try again' }
        }
      )
    ).toEqual({
      userCreds: {
        email: '<EMAIL>',
        userName: 'admin',
        role: 'admin',
        roleId: '1'
      },
      isLoggedIn: true,
      errorMessage: 'Something went wrong. Please try again'
    });
  });

  it('should handle ON_SUCCESSFUL_CHECK_ROLE', () => {
    expect(
      authReducer(
        {
          userCreds: {
            userName: ''
          },
          availableRoles: []
        },
        {
          type: types.ON_SUCCESSFUL_CHECK_ROLE,
          userName: 'sdchecker',
          response: ['super-admin', 'admin', 'supervisor', 'maker', 'checker']
        }
      )
    ).toEqual({
      userCreds: objectAssign({}, initialState.userCreds, { userName: 'sdchecker' }),
      availableRoles: ['super-admin', 'admin', 'supervisor', 'maker', 'checker']
    });
  });

  it('should handle ON_RESET_LOGIN_FORM', () => {
    expect(
      authReducer(
        {
          userCreds: {
            email: '',
            userId: -1,
            userName: '',
            roles: '',
            channelRoles: [],
            channels: []
          },
          moduleType: '',
          session: {
            isLoggedIn: false,
            sessionTimeout: null,
            isIdle: false,
            logoutModal: false
          },
          availableRoles: [],
          allRoles: [],
          errorMessage: ''
        },
        {
          type: types.ON_RESET_LOGIN_FORM
        }
      )
    ).toEqual({
      userCreds: {
        email: '',
        userId: -1,
        userName: '',
        roles: '',
        channelRoles: [],
        channels: []
      },
      session: {
        isLoggedIn: false,
        sessionTimeout: null,
        isIdle: false,
        logoutModal: false
      },
      moduleType: '',
      availableRoles: [],
      allRoles: [],
      errorMessage: ''
    });
  });

  it('should handle ON_TOGGLE_LOGOUT_MODAL', () => {
    expect(
      authReducer(
        {
          session: {
            isLoggedIn: false,
            sessionTimeout: null,
            isIdle: false,
            logoutModal: false
          }
        },
        {
          type: types.ON_TOGGLE_LOGOUT_MODAL,
          newState: true
        }
      )
    ).toEqual({
      session: {
        isLoggedIn: false,
        sessionTimeout: null,
        isIdle: false,
        logoutModal: true
      }
    });
  });

  it('should handle ON_TOGGLE_SESSION_IDLE', () => {
    expect(
      authReducer(
        {
          session: {
            isLoggedIn: false,
            sessionTimeout: null,
            isIdle: false,
            logoutModal: false
          }
        },
        {
          type: types.ON_TOGGLE_SESSION_IDLE,
          newState: true
        }
      )
    ).toEqual({
      session: {
        isLoggedIn: false,
        sessionTimeout: null,
        isIdle: true,
        logoutModal: false
      }
    });
  });

  it('should handle ON_RESET_SESSION_TIMEOUT', () => {
    expect(
      authReducer(
        {
          session: {
            isLoggedIn: false,
            sessionTimeout: null,
            isIdle: true,
            logoutModal: false
          }
        },
        {
          type: types.ON_RESET_SESSION_TIMEOUT
        }
      )
    ).toEqual({
      session: {
        isLoggedIn: false,
        sessionTimeout: 1705665112254,
        isIdle: true,
        logoutModal: false
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_ALL_ROLES', () => {
    const response = [
      { id: '1', name: 'checker' },
      { id: '2', name: 'supervisor' },
      { id: '7', name: 'maker' }
    ];
    expect(
      authReducer(
        {
          allRoles: []
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_ALL_ROLES,
          response
        }
      )
    ).toEqual({
      allRoles: response
    });
  });
});
