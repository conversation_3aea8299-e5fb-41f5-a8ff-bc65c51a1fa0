import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as actions from 'actions/partnerBanksActions';
import { onToggleAddBankModal } from 'actions/toggleActions';
import PartnerBanksPage from 'components/auth/PartnerBanksPage';

const mapStateToProps = (state) => ({
  partnerBanks: state.partnerBanks,
  userRoles: state.auth.userCreds.roles,
  theme: state.toggle.theme,
  addBankModal: state.toggle.addBankModal
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(actions, dispatch),
  toggleAddBankModal: bindActionCreators(onToggleAddBankModal, dispatch)
});

const PartnerBanksPageContainer = connect(mapStateToProps, mapDispatchToProps)(PartnerBanksPage);

export default PartnerBanksPageContainer;
