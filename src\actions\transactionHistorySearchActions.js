import { toLower, find } from 'lodash';

import {
  ON_<PERSON><PERSON><PERSON>_TRANSACTION_HISTORY_SEARCH,
  ON_<PERSON>ETCH_TRANSACTION_HISTORY_LOADING,
  ON_FETCH_TRANSACTION_HISTORY_FAILURE,
  ON_<PERSON>ETCH_SUCCESSFUL_TRANSACTION_HISTORY,
  ON_ADVANCE_SEARCH_TRANSACTION_LOADING,
  ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS,
  ON_ADVANCE_SEARCH_TRANSACTION_FAILURE,
  ON_REMOVE_SEARCH_TRANSACTION,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS,
  ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE,
  ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function searchTxn(entityCategory, formData, channel) {
  return client({
    method: 'POST',
    url: `uds/${channel}/transactions/history/category/${toLower(entityCategory)}`,
    data: formData,
    badRequestMessage: 'Unable to search transactions. Please check input data.'
  });
}

function onTransactionHistorySearchLoading(channel) {
  return { type: ON_FETCH_TRANSACTION_HISTORY_LOADING, channel };
}

function onSuccessfulTransactionHistorySearch(response, selectedDates, selectedCriteria, channel) {
  return {
    type: ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY,
    response,
    channel,
    selectedDates: selectedDates ?? [],
    selectedCriteria: selectedCriteria ?? []
  };
}

function onTransactionHistorySearchFailure(response, selectedDates, selectedCriteria, channel) {
  return {
    type: ON_FETCH_TRANSACTION_HISTORY_FAILURE,
    response,
    channel,
    selectedDates: selectedDates ?? [],
    selectedCriteria: selectedCriteria ?? []
  };
}

function onSearchTxn(entityCategory, formData, channel) {
  const selectedDates = find(formData.criterion, (criterion) => criterion.key === 'txn_timestamp');

  const selectedCriteria = formData.criterion.reduce((acc, criterion) => {
    if (['txn_timestamp'].indexOf(criterion.key) === -1) acc[criterion.key] = criterion.values[0];

    return acc;
  }, {});

  return function (dispatch) {
    dispatch(onTransactionHistorySearchLoading(channel));
    return searchTxn(entityCategory, formData, channel).then(
      (success) =>
        dispatch(
          onSuccessfulTransactionHistorySearch(
            success,
            selectedDates?.values,
            selectedCriteria,
            channel
          )
        ),
      (error) =>
        dispatch(
          onTransactionHistorySearchFailure(error, selectedDates?.values, selectedCriteria, channel)
        )
    );
  };
}

function onClearSearch() {
  return { type: ON_CLEAR_TRANSACTION_HISTORY_SEARCH };
}

function advanceSearchTxn(formData) {
  return client({
    method: 'POST',
    url: `uds/${formData.filters.identifiers.channel}/advance-search`,
    data: formData,
    badRequestMessage: 'Unable to search transactions. Please check input data.'
  });
}

function onTransactionHistoryAdvanceSearchLoading() {
  return { type: ON_ADVANCE_SEARCH_TRANSACTION_LOADING };
}

function onSuccessfulTransactionHistoryAdvanceSearch(response, filterCondition) {
  return {
    type: ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS,
    response,
    channel: filterCondition.identifiers.channel,
    filterCondition
  };
}

function onTransactionAdvanceHistorySearchFailure(response, filterCondition) {
  return {
    type: ON_ADVANCE_SEARCH_TRANSACTION_FAILURE,
    response,
    filterCondition
  };
}

function onAdvanceSearchTxn(formData) {
  return function (dispatch) {
    dispatch(onTransactionHistoryAdvanceSearchLoading());
    return advanceSearchTxn(formData).then(
      (success) => dispatch(onSuccessfulTransactionHistoryAdvanceSearch(success, formData.filters)),
      (error) => dispatch(onTransactionAdvanceHistorySearchFailure(error, formData.filters))
    );
  };
}

function onRemoveSearchTxn(items, channel) {
  return { type: ON_REMOVE_SEARCH_TRANSACTION, items, channel };
}

function onFetchTransactionHistoryByStatusLoading(channel) {
  return { type: ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_LOADING, channel };
}

function onFetchTransactionHistoryByStatusSuccess(response, selectedDates, channel, entityId) {
  return {
    type: ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_SUCCESS,
    response,
    channel,
    entityId,
    selectedDates: selectedDates ?? []
  };
}

function onFetchTransactionHistoryByStatusailure(response, selectedDates, channel, entityId) {
  return {
    type: ON_FETCH_TRANSACTION_HISTORY_BY_STATUS_FAILURE,
    response,
    channel,
    entityId,
    selectedDates: selectedDates ?? []
  };
}

function onFetchTransactionHistoryByStatus(entityCategory, formData, channel) {
  const selectedDates = find(formData.criterion, (criterion) => criterion.key === 'txn_timestamp');
  const entity_id = find(formData.criterion, (criterion) => criterion.key === 'entity_id');
  return function (dispatch) {
    dispatch(onFetchTransactionHistoryByStatusLoading(channel));
    return searchTxn(entityCategory, formData, channel).then(
      (success) =>
        dispatch(
          onFetchTransactionHistoryByStatusSuccess(
            success,
            selectedDates?.values,
            channel,
            entity_id?.values[0]
          )
        ),
      (error) =>
        dispatch(
          onFetchTransactionHistoryByStatusailure(
            error,
            selectedDates?.values,
            channel,
            entity_id?.values[0]
          )
        )
    );
  };
}

function onClearTransactionHistoryByStatus() {
  return { type: ON_CLEAR_TRANSACTION_HISTORY_BY_STATUS };
}

export {
  onSearchTxn,
  onClearSearch,
  onAdvanceSearchTxn,
  onRemoveSearchTxn,
  onFetchTransactionHistoryByStatus,
  onClearTransactionHistoryByStatus
};
