import PropTypes from 'prop-types';
import React, { useState } from 'react';
import Datetime from 'react-datetime';
import { Button, Row, Col } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

function DateRangePopOver({ theme, onSubmit, title = 'Custom', active = false }) {
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [display, setDisplay] = useState(false);

  function onSubmitRange(e) {
    e.preventDefault();
    onSubmit(fromDate, toDate);
    setFromDate('');
    setToDate('');
    setDisplay(false);
  }

  return (
    <>
      <ModalContainer
        size="md"
        theme={theme}
        header="Select Date Range"
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form onSubmit={(e) => onSubmitRange(e)}>
          <Row>
            <Col sm="5" className="p-0">
              <Datetime
                name="fromDate"
                dateFormat="YYYY-MM-DD"
                timeFormat={false}
                value={fromDate}
                onChange={(dateObj) => setFromDate(dateObj._d)}
                inputProps={{ required: true, placeholder: 'YYYY-MM-DD' }}
                closeOnSelect={true}
              />
            </Col>
            <Col sm="2" className="d-flex justify-content-center">
              to
            </Col>
            <Col sm="5" className="p-0">
              <Datetime
                name="toDate"
                dateFormat="YYYY-MM-DD"
                timeFormat={false}
                value={toDate}
                onChange={(dateObj) => setToDate(dateObj._d)}
                inputProps={{ required: true, placeholder: 'YYYY-MM-DD' }}
                closeOnSelect={true}
              />
            </Col>
          </Row>
          <div className="mt-3 d-flex justify-content-end">
            <Button size="sm" color="primary">
              Save
            </Button>
          </div>
        </form>
      </ModalContainer>
      <Button outline size="sm" onClick={() => setDisplay(!display)} active={active}>
        {title}
      </Button>
    </>
  );
}

DateRangePopOver.propTypes = {
  active: PropTypes.bool,
  title: PropTypes.string,
  theme: PropTypes.string.isRequired,
  onSubmit: PropTypes.func.isRequired
};

export default DateRangePopOver;
