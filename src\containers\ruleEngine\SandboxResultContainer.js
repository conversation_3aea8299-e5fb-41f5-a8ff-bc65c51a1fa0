import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SandboxResult from 'components/ruleEngine/SandboxResult';
import { onFetchSandboxViolationDetails } from 'actions/sandboxingActions';

const mapStateToProps = (state) => {
  return {
    violationDetails: state.sandboxing.violationDetails,
    theme: state.toggle.theme
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchViolationDetails: bindActionCreators(onFetchSandboxViolationDetails, dispatch)
  };
};
const SandboxResultContainer = connect(mapStateToProps, mapDispatchToProps)(SandboxResult);

export default SandboxResultContainer;
