import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSandboxViolationDetails } from 'actions/sandboxingActions';
import SandboxResult from 'components/ruleEngine/SandboxResult';

const mapStateToProps = (state) => ({
  violationDetails: state.sandboxing.violationDetails,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  fetchViolationDetails: bindActionCreators(onFetchSandboxViolationDetails, dispatch)
});
const SandboxResultContainer = connect(mapStateToProps, mapDispatchToProps)(SandboxResult);

export default SandboxResultContainer;
