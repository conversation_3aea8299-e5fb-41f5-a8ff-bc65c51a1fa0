import { chain, sortBy, filter } from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_RULE_LIST_LOADING,
  ON_FETCH_RULE_LIST_FAILURE,
  ON_SUCCESSFUL_FETCH_RULE_LIST,
  ON_FETCH_RULE_NAMES_LIST_LOADING,
  ON_FETCH_RULE_NAMES_LIST_SUCCESS,
  ON_FETCH_RULE_NAMES_LIST_FAILURE,
  ON_SUCCESSFUL_TOGGLE_RULE_STATUS,
  ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT,
  ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION,
  ON_DELETE_RULE_SUCCESS,
  ON_FETCH_RULES_WITH_CONDITIONS_LOADING,
  ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS,
  ON_<PERSON>ETCH_RULES_WITH_CONDITIONS_FAILURE,
  ON_RESTORE_RULE_SUCCESS
} from 'constants/actionTypes';

import initialState from './initialState';

// Optimized helper function for updating rule state
const updateRuleState = (list, ruleCode, accessor, newState) => {
  const index = list.findIndex((item) => item.code === ruleCode);
  if (index === -1) return list;

  const newList = [...list];
  newList[index] = { ...newList[index], [accessor]: newState };
  return newList;
};

export default function ruleConfiguratorReducer(state = initialState.ruleConfigurator, action) {
  switch (action.type) {
    case ON_FETCH_RULE_LIST_LOADING:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          loader: true,
          error: false,
          errorMessage: ''
        }),
        archievedRules: objectAssign({}, state.archievedRules, {
          loader: true,
          error: false,
          errorMessage: ''
        })
      });
    case ON_SUCCESSFUL_FETCH_RULE_LIST:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          list: objectAssign({}, state.productionRules.list, {
            [action.channel]: chain(action.response)
              .filter(['isDelete', 0])
              .sortBy(['order'])
              .value()
          }),
          loader: false
        }),
        archievedRules: objectAssign({}, state.archievedRules, {
          list: objectAssign({}, state.archievedRules.list, {
            [action.channel]: chain(action.response)
              .filter(['isDelete', 1])
              .sortBy(['order'])
              .value()
          }),
          loader: false
        })
      });
    case ON_FETCH_RULE_LIST_FAILURE:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }),
        archievedRules: objectAssign({}, state.archievedRules, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_RULE_NAMES_LIST_LOADING:
      return objectAssign({}, state, {
        ruleNames: objectAssign({}, state.ruleNames, { loader: true })
      });
    case ON_FETCH_RULE_NAMES_LIST_SUCCESS:
      return objectAssign({}, state, {
        ruleNames: objectAssign({}, state.ruleNames, {
          list: objectAssign({}, state.ruleNames.list, {
            [action.channel]: sortBy(action.response, ['name'])
          }),
          loader: false
        })
      });
    case ON_FETCH_RULE_NAMES_LIST_FAILURE:
      return objectAssign({}, state, {
        ruleNames: objectAssign({}, state.ruleNames, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_SUCCESSFUL_TOGGLE_RULE_STATUS:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          list: objectAssign({}, state.productionRules.list, {
            [action.channel]: updateRuleState(
              state.productionRules.list[action.channel],
              action.ruleCode,
              'status',
              action.status
            )
          })
        })
      });
    case ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          list: objectAssign({}, state.productionRules.list, {
            [action.channel]: updateRuleState(
              state.productionRules.list[action.channel],
              action.ruleCode,
              'active',
              action.active
            )
          })
        })
      });
    case ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          list: objectAssign({}, state.productionRules.list, {
            [action.channel]: updateRuleState(
              state.productionRules.list[action.channel],
              action.ruleCode,
              'explicit',
              action.explicit
            )
          })
        })
      });
    case ON_DELETE_RULE_SUCCESS:
      return objectAssign({}, state, {
        productionRules: objectAssign({}, state.productionRules, {
          list: objectAssign({}, state.productionRules.list, {
            [action.channel]: filter(
              state.productionRules.list[action.channel],
              (rule) => rule.code === action.rule.code
            )
          })
        }),
        archievedRules: objectAssign({}, state.archievedRules, {
          list: objectAssign({}, state.archievedRules.list, {
            [action.channel]: [
              ...state.archievedRules.list[action.channel],
              { ...action.rule, isDelete: 1 }
            ]
          })
        })
      });
    case ON_FETCH_RULES_WITH_CONDITIONS_LOADING:
      return objectAssign({}, state, {
        rulesWithConditions: objectAssign({}, state.rulesWithConditions, { loader: true })
      });
    case ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS:
      return objectAssign({}, state, {
        rulesWithConditions: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULES_WITH_CONDITIONS_FAILURE:
      return objectAssign({}, state, {
        rulesWithConditions: objectAssign({}, state.rulesWithConditions, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_RESTORE_RULE_SUCCESS:
      return objectAssign({}, state, {
        archievedRules: objectAssign({}, state.archievedRules, {
          list: objectAssign({}, state.archievedRules.list, {
            [action.channel]: filter(
              state.archievedRules.list[action.channel],
              (rule) => rule.code === action.rule.code
            )
          })
        })
      });
    default:
      return state;
  }
}
