import {
  ON_FETCH_FMR_REPORTED_CASE_LOADING,
  ON_FETCH_FMR_REPORTED_CASE_SUCCESS,
  ON_FETCH_FMR_REPORTED_CASE_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function complianceDashboardReducer(
  state = initialState.complianceDashboard,
  action
) {
  switch (action.type) {
    case ON_FETCH_FMR_REPORTED_CASE_LOADING:
      return objectAssign({}, state, {
        fmrReportedCases: {
          loader: true,
          error: false,
          errorMessage: '',
          list: state.fmrReportedCases.list
        }
      });
    case ON_FETCH_FMR_REPORTED_CASE_SUCCESS:
      return objectAssign({}, state, {
        fmrReportedCases: {
          loader: false,
          error: false,
          errorMessage: '',
          list: action.response
        }
      });
    case ON_FETCH_FMR_REPORTED_CASE_FAILURE:
      return objectAssign({}, state, {
        fmrReportedCases: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          list: state.fmrReportedCases.list
        }
      });
    default:
      return state;
  }
}
