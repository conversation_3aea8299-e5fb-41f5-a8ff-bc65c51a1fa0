import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import { onToggleCreateLabelModal } from 'actions/toggleActions';
import RuleDetailsForm from 'components/ruleEngine/RuleDetailsForm';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    moduleType: state.auth.moduleType,
    actionList: state.ruleCreation.actionList,
    ruleChannels: state.ruleCreation.ruleChannels,
    alertCategories: state.ruleCreation.alertCategories,
    fraudCategories: state.ruleCreation.fraudCategories,
    hasMakerChecker: state.user.hasMakerChecker,
    hasAcquirerPortals: state.user.configurations.acquirerPortals,
    ruleLabels: state.ruleCreation.ruleLabels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
    onToggleCreateLabelModal: bindActionCreators(onToggleCreateLabelModal, dispatch)
  };
};

const RuleDetailsFormContainer = connect(mapStateToProps, mapDispatchToProps)(RuleDetailsForm);

export default RuleDetailsFormContainer;
