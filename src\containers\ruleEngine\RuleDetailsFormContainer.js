import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as ruleCreationActions from 'actions/ruleCreationActions';
import { onToggleCreateLabelModal } from 'actions/toggleActions';
import RuleDetailsForm from 'components/ruleEngine/RuleDetailsForm';
import {
  getToggleState,
  getModuleType,
  getActionList,
  getRuleChannels,
  getAlertCategories,
  getFraudCategories,
  getHasMakerChecker,
  getHasAcquirerPortals,
  getRuleLabels
} from 'selectors/ruleEngineSelectors';

// Optimized mapStateToProps using memoized selectors
const mapStateToProps = (state) => ({
  toggle: getToggleState(state),
  moduleType: getModuleType(state),
  actionList: getActionList(state),
  ruleChannels: getRuleChannels(state),
  alertCategories: getAlertCategories(state),
  fraudCategories: getFraudCategories(state),
  hasMakerChecker: getHasMaker<PERSON>he<PERSON>(state),
  hasAcquirerPortals: getHasAcquirerPortals(state),
  ruleLabels: getRuleLabels(state)
});

const mapDispatchToProps = (dispatch) => ({
  ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
  onToggleCreateLabelModal: bindActionCreators(onToggleCreateLabelModal, dispatch)
});

const RuleDetailsFormContainer = connect(mapStateToProps, mapDispatchToProps)(RuleDetailsForm);

export default RuleDetailsFormContainer;
