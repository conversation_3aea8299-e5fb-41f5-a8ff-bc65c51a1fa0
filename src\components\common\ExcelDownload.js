import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import fileDownload from 'js-file-download';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { Button } from 'reactstrap';
import { utils, write } from 'xlsx';

import Loader from 'components/loader/Loader';

const ExcelDownload = ({ data, sheetName, headers, action = () => false, disabled = false }) => {
  const [showLoader, setShowLoader] = useState(false);

  const handleDownload = () => {
    if (!data || !data.length) return;

    setShowLoader(true);

    // Generate the worksheet data array from headers and data
    const rows = [
      headers.map((header) => header.label), // header row
      ...data.map((row) =>
        headers.map((header) =>
          typeof header.value === 'function' ? header.value(row) : row[header.value]
        )
      )
    ];

    const worksheet = utils.aoa_to_sheet(rows);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, sheetName);

    // Generate binary string
    const xlsxData = write(workbook, { bookType: 'xlsx', type: 'array' });

    // Download the file
    fileDownload(new Blob([xlsxData], { type: 'application/octet-stream' }), `${sheetName}.xlsx`);

    setShowLoader(false);
  };

  const onClick = () => {
    handleDownload();
    action();
  };

  return (
    <>
      <Loader show={showLoader} />
      <Button
        outline
        size="sm"
        color="primary"
        title={`Download ${sheetName}`}
        onClick={onClick}
        disabled={disabled}>
        <FontAwesomeIcon icon={faDownload} className="me-1" /> Download {sheetName}
      </Button>
    </>
  );
};

ExcelDownload.propTypes = {
  data: PropTypes.array.isRequired,
  headers: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.func]).isRequired
    })
  ).isRequired,
  sheetName: PropTypes.string.isRequired,
  action: PropTypes.func,
  disabled: PropTypes.bool
};

export default ExcelDownload;
