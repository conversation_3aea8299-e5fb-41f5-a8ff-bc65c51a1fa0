import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Row, Col, Input, Button, FormFeedback } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import ReactTable from 'react-table';

import ModalContainer from 'components/common/ModalContainer';
import CategoryBasedRulesDropdownContainer from 'containers/common/CategoryBasedRulesDropdownContainer';
import { handleAttributeValueValidationProps } from 'constants/functions';
import { ATTRIBUTE_LIST_OPERATORS } from 'constants/applicationConstants';

function CaseAssignmentModal({
  theme,
  userActions,
  selectedUser,
  attributeList,
  showCaseCriteriaModal,
  toggleCaseCriteriaModal,
  ruleList,
  categoryList,
  fetchRules,
  fetchCategory
}) {
  const [attribute, setAttribute] = useState({});
  const [operator, setOperator] = useState('');
  const [value, setValue] = useState('');
  const [conditionsList, setConditionsList] = useState([]);
  const [invalidValue, setInvalidValue] = useState(false);
  const [invalidValueMessage, setInvalidValueMessage] = useState('');
  const [duplicateCondition, setDuplicateCondition] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState([]);
  const [selectedRule, setSelectedRule] = useState([]);

  useEffect(() => {
    if (attributeList.list.length === 0 && !attributeList.error && !attributeList.loader)
      userActions.onFetchCaseCriteriaAttributesList();

    if (categoryList.length === 0) fetchCategory('frm');
    if (ruleList.length === 0) fetchRules('frm');
  }, []);

  const handleOnLoadTxnCategory = (categoryNames) => {
    return _.filter(categoryList, (category) =>
      _.includes(categoryNames, category.categoryName)
    ).map((d) => ({
      value: d?.id,
      label: d?.categoryName,
      description: d?.desc
    }));
  };

  const handleOnLoadSelectedRule = (ruleIds) => {
    return _.filter(ruleList, (rule) => _.includes(ruleIds, rule.code)).map((d) => ({
      value: d?.code,
      label: d?.name,
      description: d?.description,
      alertCategoryId: d?.alertCategoryId,
      methodType: d?.methodType
    }));
  };

  useEffect(() => {
    setConditionsList(selectedUser?.caseCriteriaInfo || []);
    setSelectedCategory(
      _.has(selectedUser, 'ruleCriteria')
        ? handleOnLoadTxnCategory(selectedUser.ruleCriteria.categoryNames)
        : []
    );
    setSelectedRule(
      _.has(selectedUser, 'ruleCriteria')
        ? handleOnLoadSelectedRule(selectedUser.ruleCriteria.ruleIds)
        : []
    );
  }, [selectedUser, categoryList, ruleList]);

  const validateValue = (dataType, value) => {
    const applicableRegex = handleAttributeValueValidationProps(dataType);
    setInvalidValue(!applicableRegex.pattern.test(value));
    setInvalidValueMessage(applicableRegex.title);
  };

  const validateDuplicateCondition = (condition) => {
    if (_.find(conditionsList, (d) => _.isEqual(d, condition)) !== undefined) {
      setDuplicateCondition(true);
      setInvalidValueMessage('Condition already exists!');
    } else {
      setDuplicateCondition(false);
    }
  };

  let operatorOptions = () =>
    ATTRIBUTE_LIST_OPERATORS.filter((operator) =>
      _.includes(operator.dataType, _.toLower(attribute?.dataType))
    ).map((operator) => (
      <option key={operator.value} value={operator.key}>
        {operator.value}
      </option>
    ));

  const handleFieldChange = (e) => {
    const selectedField = attributeList.list.find((d) => d.value == e.target.value);
    setAttribute(selectedField);
    setOperator(ATTRIBUTE_LIST_OPERATORS[0].key);
    setValue('');
    validateDuplicateCondition({
      attribute: selectedField.value,
      operator: ATTRIBUTE_LIST_OPERATORS[0].key,
      value: ''
    });
  };

  const handleAddConditions = () => {
    let condition = {
      attribute: attribute?.value,
      operator: operator,
      value: value
    };
    setConditionsList((prev) => {
      return [...prev, condition];
    });
    setAttribute({});
    setOperator('');
    setValue('');
  };

  const handleRemoveConditions = (selectedCondition) => {
    if (confirm('Are you sure you wish to remove this condition ?')) {
      const newConditionsList =
        conditionsList &&
        conditionsList?.filter((condition) => !_.isEqual(condition, selectedCondition));
      setConditionsList(newConditionsList);
    }
  };

  const handleDeleteCaseCriteriaForUser = () => {
    if (confirm('Are you sure you wish to remove case criteria for this user ?')) {
      const formData = {
        userId: selectedUser.id
      };
      userActions.onDeleteCaseCriteriaForUser(formData);
    }
  };

  const onCategoryChange = (selectedOptions) => {
    let prevSelectedCategories;
    let isCategoryAdded;
    setSelectedCategory((prevState) => {
      prevSelectedCategories = prevState;
      return selectedOptions;
    });

    isCategoryAdded = prevSelectedCategories.length < selectedOptions.length;

    const newElement = isCategoryAdded
      ? _.differenceWith(selectedOptions, prevSelectedCategories, _.isEqual)
      : _.differenceWith(prevSelectedCategories, selectedOptions, _.isEqual);

    const filteredRules = isCategoryAdded
      ? _.filter(ruleList, (rule) => newElement[0].value == rule.alertCategoryId).map((d) => ({
          ...d,
          value: d?.code,
          label: d?.name
        }))
      : _.filter(selectedRule, (rule) => newElement[0].value !== rule.alertCategoryId);

    setSelectedRule((prevState) =>
      _.isEmpty(selectedOptions)
        ? []
        : isCategoryAdded
        ? [...prevState, ...filteredRules]
        : filteredRules
    );
  };

  const onRuleChange = (selectedOptions) => {
    setSelectedRule(selectedOptions);
    const selectedRuleCategoryIds = selectedOptions?.map((d) => d.alertCategoryId);

    const filteredRuleCategories = _.filter(selectedCategory, (category) =>
      _.includes(selectedRuleCategoryIds, category.value)
    );

    setSelectedCategory(filteredRuleCategories);
  };

  const submitCaseCriteraUpdate = (e) => {
    e.preventDefault();
    const formData = {
      userId: selectedUser.id,
      caseCriteriaInfo: conditionsList,
      ruleCriteria: {
        categoryNames: selectedCategory.map((d) => d.label),
        ruleIds: selectedRule.map((d) => d.value)
      }
    };
    userActions.onUserAssignCaseCriteria(formData);
    setConditionsList([]);
    setInvalidValue(false);
    setInvalidValueMessage('');
    setDuplicateCondition(false);
    setSelectedCategory([]);
    setSelectedRule([]);
  };

  let getConditionsTableheader = () => [
    {
      Header: 'Attribute',
      accessor: 'attribute',
      // eslint-disable-next-line react/no-multi-comp, react/prop-types
      Cell: ({ value }) => {
        return <span>{value}</span>;
      }
    },
    { Header: 'Operator', accessor: 'operator' },
    { Header: 'Values', accessor: 'value' },
    {
      Header: 'Actions',
      filterable: false,
      sortable: false,
      maxWidth: 80,
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <Button
          size="sm"
          color="danger"
          title="Delete"
          onClick={() => {
            handleRemoveConditions(row.original);
          }}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    }
  ];

  const conditionalAttributesOptions =
    attributeList?.list &&
    _.map(attributeList?.list, (attribute) => (
      <option key={attribute.key} value={attribute.value}>
        {attribute.key}
      </option>
    ));

  const createRoleCaseCriteriaForm = () => (
    <>
      {(!_.isEmpty(conditionsList) || !_.isEmpty(selectedCategory) || !_.isEmpty(selectedRule)) && (
        <FormGroup className="d-flex justify-content-end">
          <Button
            size="sm"
            color="danger"
            title="Delete All"
            onClick={() => handleDeleteCaseCriteriaForUser()}>
            Delete All
          </Button>
        </FormGroup>
      )}
      <CategoryBasedRulesDropdownContainer
        categoryState={{ value: selectedCategory, onChange: onCategoryChange }}
        ruleState={{ value: selectedRule, onChange: onRuleChange }}
      />
      <FormGroup>
        <FormGroup className="d-flex justify-content-between">
          <Label>Conditions</Label>
        </FormGroup>
        <Row>
          <Col md="4" sm="4" xs="12">
            <FormGroup>
              <Label>Select Attribute </Label>
              <Input
                type="select"
                name={'attribute'}
                value={attribute?.value}
                onChange={(e) => handleFieldChange(e)}>
                <option value="">-- Select --</option>
                {conditionalAttributesOptions}
              </Input>
            </FormGroup>
          </Col>
          <Col md="4" sm="4" xs="12">
            <FormGroup>
              <Label>Select Operator </Label>
              <Input
                type="select"
                name={'operator'}
                value={operator}
                onChange={(e) => {
                  setOperator(e.target.value);
                }}
                disabled={_.isEmpty(attribute)}>
                <option value="">-- Select --</option>
                {operatorOptions()}
              </Input>
            </FormGroup>
          </Col>
          <Col md="3" sm="3" xs="12">
            <FormGroup>
              <Label>Value </Label>
              <Input
                placeholder="value"
                type="text"
                name={'value'}
                value={value}
                onChange={(e) => {
                  validateValue(attribute?.dataType, e.target.value);
                  validateDuplicateCondition({
                    attribute: attribute.value,
                    operator,
                    value: e.target.value
                  });
                  setValue(e.target.value);
                }}
                disabled={_.isEmpty(attribute) || _.isEmpty(operator)}
                spellCheck={false}
                invalid={!_.isEmpty(value) && (invalidValue || duplicateCondition)}
              />
              <FormFeedback>{invalidValueMessage}</FormFeedback>
            </FormGroup>
          </Col>
          <Col md="1" sm="1" xs="12">
            <FormGroup className="d-flex justify-content-end">
              <Button
                color="primary"
                size="sm"
                className="add-condition-btn"
                disabled={
                  invalidValue ||
                  duplicateCondition ||
                  _.isEmpty(attribute) ||
                  value === '' ||
                  _.isEmpty(operator)
                }
                onClick={() => handleAddConditions()}>
                <FontAwesomeIcon icon={faPlus} />
              </Button>
            </FormGroup>
          </Col>
        </Row>
      </FormGroup>

      {!_.isEmpty(conditionsList) && (
        <ReactTable
          filterable={false}
          columns={getConditionsTableheader()}
          data={conditionsList}
          defaultPageSize={20}
          minRows={2}
          showPaginationTop={false}
          showPaginationBottom={false}
          className={'-highlight  -striped mb-3'}
        />
      )}
    </>
  );

  return (
    <ModalContainer
      theme={theme}
      isOpen={showCaseCriteriaModal}
      header={'Update case criteria for user - ' + selectedUser.userName}
      toggle={() => toggleCaseCriteriaModal()}>
      <form onSubmit={submitCaseCriteraUpdate}>
        {!_.isEmpty(selectedUser) ? (
          <>{createRoleCaseCriteriaForm()}</>
        ) : (
          <span>{'No user selected.'}</span>
        )}
        <FormGroup className="d-flex justify-content-end">
          <Button
            outline
            size="sm"
            color="primary"
            disabled={
              _.isEmpty(conditionsList) && _.isEmpty(selectedCategory) && _.isEmpty(selectedRule)
            }>
            Save
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
}

CaseAssignmentModal.propTypes = {
  theme: PropTypes.string.isRequired,
  userActions: PropTypes.object.isRequired,
  selectedUser: PropTypes.object.isRequired,
  attributeList: PropTypes.object.isRequired,
  showCaseCriteriaModal: PropTypes.bool.isRequired,
  toggleCaseCriteriaModal: PropTypes.func.isRequired,
  ruleList: PropTypes.array.isRequired,
  categoryList: PropTypes.array.isRequired,
  fetchCategory: PropTypes.func.isRequired,
  fetchRules: PropTypes.func.isRequired
};

export default CaseAssignmentModal;
