import _ from 'lodash';

import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onFetchCaseDetails } from 'actions/caseDetailsActions';
import { onFetchCaseLogs, onFetchEntityLogs } from 'actions/logsActions';
import { onFetchCaseNotationList } from 'actions/notationActions';
import {
  onToggleReleaseFundsModal,
  onToggleHoldCaseModal,
  onToggleRequestDocumentModal,
  onToggleLoader
} from 'actions/toggleActions';
import {
  ON_FETCH_RELEASE_FUNDS_LIST_LOADING,
  ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST,
  ON_FETCH_RELEASE_FUNDS_LIST_FAILURE,
  ON_FETCH_DOCUMENT_STATUS_LOADING,
  ON_FETCH_DOCUMENT_STATUS_SUCCESS,
  ON_FETCH_DOCUMENT_STATUS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchReleaseFundsList(userCreds) {
  return client({ url: `alertmanager/heldfunds/${userCreds.userId}/torelease` });
}

function onFetchReleaseFundsListLoading() {
  return { type: ON_FETCH_RELEASE_FUNDS_LIST_LOADING };
}

function onSuccessfulFetchReleaseFundsList(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST,
    response
  };
}

function onFailureReleaseFundsListFetch(response) {
  return {
    type: ON_FETCH_RELEASE_FUNDS_LIST_FAILURE,
    response
  };
}

function onFetchReleaseFundsList(userCreds) {
  return function (dispatch) {
    dispatch(onFetchReleaseFundsListLoading());
    return fetchReleaseFundsList(userCreds).then(
      (success) => dispatch(onSuccessfulFetchReleaseFundsList(success)),
      (error) => dispatch(onFailureReleaseFundsListFetch(error))
    );
  };
}

function releaseFundsInBulk(formData, userCreds) {
  return client({
    method: 'PUT',
    url: `casereview/${userCreds.userId}/bulk/funds/release`,
    data: formData.releaseObj,
    badRequestMessage: 'Unable to release funds'
  });
}

function onReleaseFundsInBulk(formData, userCreds) {
  return function (dispatch) {
    return releaseFundsInBulk(formData, userCreds).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Funds released successfully' }));
        dispatch(onFetchReleaseFundsList(userCreds));
        dispatch(onToggleReleaseFundsModal());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function releaseSingleFund(formData) {
  return client({
    method: 'PUT',
    url: 'casereview/user/funds/release',
    data: formData,
    badRequestMessage: 'Unable to release fund'
  });
}

function onReleaseSingleFund(formData, userCreds) {
  return function (dispatch) {
    return releaseSingleFund(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Fund released successfully' }));
        dispatch(onFetchReleaseFundsList(userCreds));
        dispatch(onToggleReleaseFundsModal());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchDocumentStatus(txnId) {
  return client({ url: `alertmanager/holdrelease/${txnId}/docstatus` });
}

function onFetchDocumentStatusLoading() {
  return { type: ON_FETCH_DOCUMENT_STATUS_LOADING };
}

function onFetchDocumentStatusSuccess(response) {
  return {
    type: ON_FETCH_DOCUMENT_STATUS_SUCCESS,
    response
  };
}

function onFetchDocumentStatusFailure(response) {
  return {
    type: ON_FETCH_DOCUMENT_STATUS_FAILURE,
    response
  };
}

function onFetchDocumentStatus(selectedCase) {
  return function (dispatch) {
    dispatch(onFetchDocumentStatusLoading());
    return fetchDocumentStatus(selectedCase.txnId).then(
      (success) => dispatch(onFetchDocumentStatusSuccess(success)),
      (error) => dispatch(onFetchDocumentStatusFailure(error))
    );
  };
}

function holdCase(formData) {
  return client({
    method: 'PUT',
    url: 'casereview/funds/hold',
    data: formData,
    badRequestMessage: 'Unable to put transaction settlement on hold'
  });
}

function onCaseHold(formData, selectedCase) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return holdCase(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Case kept on hold successfully' }));
          dispatch(onToggleHoldCaseModal());
          !_.isEmpty(selectedCase) &&
            (dispatch(onFetchCaseDetails(selectedCase, 'frm')),
            dispatch(onFetchDocumentStatus(selectedCase)),
            dispatch(onFetchCaseNotationList(selectedCase.caseId)),
            dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo)),
            dispatch(
              onFetchEntityLogs(selectedCase.entityId, selectedCase.channel, {
                pageNo: 1,
                pageRecords: 10,
                filterCondition: []
              })
            ));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function requestDocument(formData, txnId) {
  return client({
    method: 'PUT',
    url: `alertmanager/holdrelease/${txnId}/update/docstatus`,
    data: formData,
    badRequestMessage: 'Request for documents failed'
  });
}

function onRequestDocument(formData, selectedCase) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return requestDocument(formData, selectedCase.txnId)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({ message: 'Verification documents requested successfully' })
          );
          dispatch(onToggleRequestDocumentModal());
          !_.isEmpty(selectedCase) &&
            (dispatch(onFetchDocumentStatus(selectedCase)),
            dispatch(onFetchCaseNotationList(selectedCase.caseId)),
            dispatch(onFetchCaseLogs('Case', selectedCase.caseRefNo)),
            dispatch(
              onFetchEntityLogs(selectedCase.entityId, selectedCase.channel, {
                pageNo: 1,
                pageRecords: 10,
                filterCondition: []
              })
            ));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchReleaseFundsList,
  onReleaseFundsInBulk,
  onReleaseSingleFund,
  onFetchDocumentStatus,
  onCaseHold,
  onRequestDocument
};
