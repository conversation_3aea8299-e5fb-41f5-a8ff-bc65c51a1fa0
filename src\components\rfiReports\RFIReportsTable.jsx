import _ from 'lodash';
import moment from 'moment';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';

import TableLoader from 'components/loader/TableLoader';

function RFIReportsTable({
  count,
  data,
  fetchData,
  page,
  pageSize,
  filtered,
  onPageChange,
  onPageSizeChange,
  onFilteredChange,
  filter = {},
  additionalHeaders = []
}) {
  useEffect(() => {
    fetchData({ pageNo: page + 1, pageSize, ...filter });
  }, [pageSize]);

  let columns = [
    { Header: 'ID', accessor: 'merchantID' },
    { Header: 'Name', accessor: 'merchantName' },
    {
      Header: 'Account Opened on',
      accessor: 'accountOpeningDate',
      Cell: ({ value }) => value ? moment(value).format('YYYY-MM-DD hh:mm A') : null,
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    }
  ];
  columns.push(...additionalHeaders);
  columns.push(
    { Header: 'Contact', accessor: 'merchantContactNumber' },
    { Header: 'Email', accessor: 'merchantEmail' }
  );

  const tablePageCountProp = _.isEmpty(filtered)
    ? {
        pages: count / pageSize > 1 ? Math.ceil(count / pageSize) : 1
      }
    : {};

  return data.loader ? (
    <TableLoader />
  ) : data.error ? (
    <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>
  ) : _.isEmpty(data.records) ? (
    <div className="no-data-div no-data-card-padding">No transactions found</div>
  ) : (
    <ReactTable
      filterable
      columns={columns}
      data={data.records}
      showPaginationTop={true}
      showPaginationBottom={false}
      showPageJump={false}
      minRows={5}
      page={page}
      pageSize={pageSize}
      filtered={filtered}
      pageSizeOptions={[5, 10, 20, 30, 40, 50]}
      noDataText="No data found"
      className={'-highlight -striped'}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange}
      onFilteredChange={onFilteredChange}
      defaultFilterMethod={(filter, row) =>
        row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
      }
      {...tablePageCountProp}
    />
  );
}

RFIReportsTable.propTypes = {
  filter: PropTypes.object,
  additionalHeaders: PropTypes.array,
  page: PropTypes.number.isRequired,
  count: PropTypes.number.isRequired,
  pageSize: PropTypes.number.isRequired,
  data: PropTypes.object.isRequired,
  filtered: PropTypes.array.isRequired,
  fetchData: PropTypes.func.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onPageSizeChange: PropTypes.func.isRequired,
  onFilteredChange: PropTypes.func.isRequired
};

export default RFIReportsTable;
