import { faPlusCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { filter, includes, isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useMemo } from 'react';
import { TabPane, ListGroupItem } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import { useMultipleDebouncedSearches } from 'hooks/useDebouncedSearch';
import useListBuilder from 'hooks/useListBuilder';

import DSLHelperList from './DSLHelperList';

const DSLHelperTabs = ({
  channel,
  moduleType,
  helperList,
  prefilterLists,
  appendDSL,
  fetchDSLHelpers
}) => {
  useEffect(() => {
    if (isEmpty(helperList[channel])) fetchDSLHelpers(channel);
  }, [channel, fetchDSLHelpers, helperList]);

  // Optimized debounced search for all tabs
  const searchKeys = [
    'prefix',
    'parameter',
    'event',
    'operator',
    'function',
    'counter',
    'specializedList'
  ];
  const { debouncedSearches, handleSearchChange } = useMultipleDebouncedSearches(searchKeys, 300);

  // Extract individual search values for easier access
  const {
    prefix: prefixSearch,
    parameter: parameterSearch,
    event: eventSearch,
    operator: operatorSearch,
    function: functionSearch,
    counter: counterSearch,
    specializedList: specializedListSearch
  } = debouncedSearches;

  const tabNames = useMemo(() => {
    const baseTabs = [
      'Prefix',
      'Parameters',
      'Operators',
      'Functions',
      'Counters',
      'Specialized Lists'
    ];
    if (channel === 'frm' && moduleType === 'issuer') baseTabs.push('Events');
    return baseTabs;
  }, [channel, moduleType]);

  // Memoize expensive list computations
  const prefixList = useListBuilder(helperList[channel]?.prefix || [], prefixSearch, appendDSL);

  const functionsList = useListBuilder(
    helperList[channel]?.functions || [],
    functionSearch,
    appendDSL
  );

  const countersList = useListBuilder(
    helperList[channel]?.counters || [],
    counterSearch,
    appendDSL
  );

  const parametersList = useListBuilder(
    helperList[channel]?.parameters || [],
    parameterSearch,
    appendDSL
  );

  const eventList = useListBuilder(helperList[channel]?.events || [], eventSearch, appendDSL);

  const operatorsList = useListBuilder(
    helperList[channel]?.operators || [],
    operatorSearch,
    appendDSL
  );

  const specializedLists = useMemo(
    () =>
      filter(prefilterLists.data, (d) =>
        includes(`${d.categoryName} On "${d.listName}"`, specializedListSearch)
      ).map((item, i) => (
        <ListGroupItem
          key={i}
          onClick={() => appendDSL(`${item.categoryName} On "${item.listName}"`)}>
          <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
          {`${item.categoryName} On "${item.listName}"`}
        </ListGroupItem>
      )),
    [appendDSL, prefilterLists.data, specializedListSearch]
  );

  return (
    <Tabs pills vertical={true} tabNames={tabNames}>
      <TabPane key={0} tabId={0}>
        <DSLHelperList
          label="Prefix"
          searchValue={prefixSearch}
          onSearchChange={handleSearchChange('prefix')}
          list={prefixList}
        />
      </TabPane>
      <TabPane key={1} tabId={1}>
        <DSLHelperList
          label="Parameters"
          searchValue={parameterSearch}
          onSearchChange={handleSearchChange('parameter')}
          list={parametersList}
        />
      </TabPane>
      <TabPane key={2} tabId={2}>
        <DSLHelperList
          label="Operators"
          searchValue={operatorSearch}
          onSearchChange={handleSearchChange('operator')}
          list={operatorsList}
        />
      </TabPane>
      <TabPane key={3} tabId={3}>
        <DSLHelperList
          label="Functions"
          searchValue={functionSearch}
          onSearchChange={handleSearchChange('function')}
          list={functionsList}
        />
      </TabPane>
      <TabPane key={4} tabId={4}>
        <DSLHelperList
          label="Counters"
          searchValue={counterSearch}
          onSearchChange={handleSearchChange('counter')}
          list={countersList}
        />
      </TabPane>
      <TabPane key={5} tabId={5}>
        <DSLHelperList
          label="Specialized Lists"
          searchValue={specializedListSearch}
          onSearchChange={handleSearchChange('specializedList')}
          list={specializedLists}
        />
      </TabPane>
      {channel === 'frm' && moduleType === 'issuer' && (
        <TabPane key={6} tabId={6}>
          <DSLHelperList
            label="Events"
            searchValue={eventSearch}
            onSearchChange={handleSearchChange('event')}
            list={eventList}
          />
        </TabPane>
      )}
    </Tabs>
  );
};

DSLHelperTabs.propTypes = {
  channel: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  helperList: PropTypes.object.isRequired,
  prefilterLists: PropTypes.object.isRequired,
  appendDSL: PropTypes.func.isRequired,
  fetchDSLHelpers: PropTypes.func.isRequired
};

export default DSLHelperTabs;
