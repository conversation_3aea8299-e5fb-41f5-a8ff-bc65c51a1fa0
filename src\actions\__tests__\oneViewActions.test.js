import responses from 'mocks/responses';

import * as actions from 'actions/oneViewActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  auth: { userCreds },
  oneView: {}
};

describe('one view actions', () => {
  it('should Fetch Reviewer Case', () => {
    const formData = { userId: 1, userName: 'admin', channel: 'frm' };

    const expectedActions = [
      { type: types.ON_FETCH_REVIEWER_CASE_LOADING },
      {
        type: types.ON_FETCH_REVIEWER_CASE_SUCCESS,
        response: responses.oneView.case
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchReviewerCase(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Reviewer Case Close', () => {
    const formData = {
      caseRefNo: 'test1',
      bucketName: 'Confirmed Genuine',
      remark: 'remark'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_REVIEWER_CASE_CLOSE_SUCCESS,
        response: formData
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Case(s) closed successfully!' }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onReviewerCaseClose(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Reviewer Add To Case', () => {
    const formData = { txnId: 'txn23', parentTxnId: 'txnId' };
    const expectedAction = {
      type: types.ON_REVIEWER_ADD_TO_CASE_SUCCESS,
      response: formData.txnId
    };
    expect(actions.onReviewerAddToCase(formData)).toEqual(expectedAction);
  });

  it('should Reviewer Park Case', () => {
    const expectedAction = {
      type: types.ON_REVIEWER_PARK_CASE_SUCCESS
    };
    expect(actions.onReviewerParkCase()).toEqual(expectedAction);
  });
});
