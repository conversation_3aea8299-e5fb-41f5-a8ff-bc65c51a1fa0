import React from 'react';
import PropTypes from 'prop-types';

/**
 * A reusable section container component that provides consistent styling for sections
 * with a heading and left-bordered content area.
 *
 * @example
 * <SectionContainer
 *   title="Total Outstanding"
 *   variant="dark"
 *   headerSize="small"
 *   spacing="mb-4"
 * >
 *   <div>Content goes here</div>
 * </SectionContainer>
 */
const SectionContainer = ({
  title,
  children,
  variant = 'muted',
  headerSize = 'small',
  spacing = 'mb-3',
  headerClassName = '',
  contentClassName = ''
}) => {
  const headerSizeClass = headerSize === 'small' ? 'small' : '';

  return (
    <div className={spacing}>
      {title && (
        <h6 className={`text-${variant} ${headerSizeClass} mb-3 ${headerClassName}`}>{title}</h6>
      )}
      <div className={`border-start border-3 ps-3 ${contentClassName}`}>{children}</div>
    </div>
  );
};

SectionContainer.propTypes = {
  /** The title text to display in the header */
  title: PropTypes.string,
  /** The content to render inside the bordered container */
  children: PropTypes.node.isRequired,
  /** Color variant for the header text (muted, dark, primary, etc) */
  variant: PropTypes.string,
  /** Size of the header text (small or normal) */
  headerSize: PropTypes.oneOf(['small', 'normal']),
  /** Margin/spacing classes to apply to container */
  spacing: PropTypes.string,
  /** Additional classes for the header */
  headerClassName: PropTypes.string,
  /** Additional classes for the content container */
  contentClassName: PropTypes.string
};

export default SectionContainer;
