import {
  ON_<PERSON>ET<PERSON>_RULE_STATS_LOADING,
  ON_FETCH_RULE_STATS_SUCCESS,
  ON_FETCH_RULE_STATS_FAILURE,
  ON_FETCH_RULE_EFFICACY_LOADING,
  ON_FETCH_RULE_EFFICACY_SUCCESS,
  ON_FETCH_RULE_EFFICACY_FAILURE,
  ON_FETCH_RULE_BEHAVIOUR_LOADING,
  ON_FETCH_RULE_BEHAVIOUR_SUCCESS,
  ON_FETCH_RULE_BEHAVIOUR_FAILURE,
  ON_FETCH_RULE_EFFICIENCY_LOADING,
  ON_FETCH_RULE_EFFICIENCY_SUCCESS,
  ON_FETCH_RULE_EFFICIENCY_FAILURE,
  ON_FETCH_RULE_EFFECTIVENESS_LOADING,
  ON_FETCH_RULE_EFFECTIVENESS_SUCCESS,
  ON_FETCH_RULE_EFFECTIVENESS_FAILURE,
  ON_FETCH_RULE_FEEDBACK_LOADING,
  ON_FETCH_RULE_FEEDBACK_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_FAILURE,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS,
  ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchRuleStats(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/rule/${channel}/count`,
    data: formData
  });
}

function onFetchRuleStatsLoading() {
  return { type: ON_FETCH_RULE_STATS_LOADING };
}

function onFetchRuleStatsSuccess(response) {
  return { type: ON_FETCH_RULE_STATS_SUCCESS, response };
}

function onFetchRuleStatsFailure(response) {
  return { type: ON_FETCH_RULE_STATS_FAILURE, response };
}

function onFetchRuleStats(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleStatsLoading());
    return fetchRuleStats(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleStatsSuccess(success)),
      (error) => dispatch(onFetchRuleStatsFailure(error))
    );
  };
}

function fetchRuleFeedbackAnalysis(channel, formData) {
  return client({
    method: 'POST',
    url: `${channel}/case/feedback/getOverallFeedbackAnalysis`,
    data: formData
  });
}

function onFetchRuleFeedbackAnalysisLoading() {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_LOADING };
}

function onFetchRuleFeedbackAnalysisSuccess(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_SUCCESS, response };
}

function onFetchRuleFeedbackAnalysisFailure(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_FAILURE, response };
}

function onFetchOverallFeedbackAnalysis(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleFeedbackAnalysisLoading());
    return fetchRuleFeedbackAnalysis(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleFeedbackAnalysisSuccess(success)),
      (error) => dispatch(onFetchRuleFeedbackAnalysisFailure(error))
    );
  };
}

function fetchRuleEfficacy(channel, formData) {
  return client({
    method: 'POST',
    headers: {
      env: 'sandbox'
    },
    url: `uds/${channel}/rule/efficacy`,
    data: formData
  });
}

function onFetchRuleEfficacyLoading() {
  return { type: ON_FETCH_RULE_EFFICACY_LOADING };
}

function onFetchRuleEfficacySuccess(response) {
  return { type: ON_FETCH_RULE_EFFICACY_SUCCESS, response };
}

function onFetchRuleEfficacyFailure(response) {
  return { type: ON_FETCH_RULE_EFFICACY_FAILURE, response };
}

function onFetchRuleEfficacy(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleEfficacyLoading());
    return fetchRuleEfficacy(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleEfficacySuccess(success)),
      (error) => dispatch(onFetchRuleEfficacyFailure(error))
    );
  };
}

function fetchRuleEfficiency(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/rule/${channel}/efficiency`,
    data: formData
  });
}

function onFetchRuleEfficiencyLoading() {
  return { type: ON_FETCH_RULE_EFFICIENCY_LOADING };
}

function onFetchRuleEfficiencySuccess(response) {
  return { type: ON_FETCH_RULE_EFFICIENCY_SUCCESS, response };
}

function onFetchRuleEfficiencyFailure(response) {
  return { type: ON_FETCH_RULE_EFFICIENCY_FAILURE, response };
}

function onFetchRuleEfficiency(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleEfficiencyLoading());
    return fetchRuleEfficiency(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleEfficiencySuccess(success)),
      (error) => dispatch(onFetchRuleEfficiencyFailure(error))
    );
  };
}

function fetchRuleEffectiveness(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/rule/${channel}/effectiveness`,
    data: formData
  });
}

function onFetchRuleEffectivenessLoading() {
  return { type: ON_FETCH_RULE_EFFECTIVENESS_LOADING };
}

function onFetchRuleEffectivenessSuccess(response) {
  return { type: ON_FETCH_RULE_EFFECTIVENESS_SUCCESS, response };
}

function onFetchRuleEffectivenessFailure(response) {
  return { type: ON_FETCH_RULE_EFFECTIVENESS_FAILURE, response };
}

function onFetchRuleEffectiveness(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleEffectivenessLoading());
    return fetchRuleEffectiveness(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleEffectivenessSuccess(success)),
      (error) => dispatch(onFetchRuleEffectivenessFailure(error))
    );
  };
}

function fetchRuleBehaviour(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/rule/${channel}/behaviour`,
    data: formData
  });
}

function onFetchRuleBehaviourLoading() {
  return { type: ON_FETCH_RULE_BEHAVIOUR_LOADING };
}

function onFetchRuleBehaviourSuccess(response) {
  return { type: ON_FETCH_RULE_BEHAVIOUR_SUCCESS, response };
}

function onFetchRuleBehaviourFailure(response) {
  return { type: ON_FETCH_RULE_BEHAVIOUR_FAILURE, response };
}

function onFetchRuleBehaviour(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleBehaviourLoading());
    return fetchRuleBehaviour(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleBehaviourSuccess(success)),
      (error) => dispatch(onFetchRuleBehaviourFailure(error))
    );
  };
}

function fetchRuleFeedback(channel, formData) {
  return client({
    method: 'POST',
    url: `${channel}/case/feedback/getFeedBack`,
    data: formData
  });
}

function onFetchRuleFeedbackLoading() {
  return { type: ON_FETCH_RULE_FEEDBACK_LOADING };
}

function onFetchRuleFeedbackSuccess(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_SUCCESS, response };
}

function onFetchRuleFeedbackFailure(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_FAILURE, response };
}

function onFetchRuleFeedback(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleFeedbackLoading());
    return fetchRuleFeedback(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleFeedbackSuccess(success)),
      (error) => dispatch(onFetchRuleFeedbackFailure(error))
    );
  };
}

function fetchRuleFeedbackAnalysisStats(channel, formData) {
  return client({
    method: 'POST',
    url: `${channel}/case/feedback/getFeedBackTimeRangeAnalysis`,
    data: formData
  });
}

function onFetchRuleFeedbackAnalysisStatsLoading() {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_LOADING };
}

function onFetchRuleFeedbackAnalysisStatsSuccess(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_SUCCESS, response };
}

function onFetchRuleFeedbackAnalysisStatsFailure(response) {
  return { type: ON_FETCH_RULE_FEEDBACK_ANALYSIS_STATS_FAILURE, response };
}

function onFetchFeedBackTimeRangeAnalysis(formData) {
  return function (dispatch, getState) {
    const { auth } = getState();
    dispatch(onFetchRuleFeedbackAnalysisStatsLoading());
    return fetchRuleFeedbackAnalysisStats(auth.userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchRuleFeedbackAnalysisStatsSuccess(success)),
      (error) => dispatch(onFetchRuleFeedbackAnalysisStatsFailure(error))
    );
  };
}

export {
  onFetchRuleStats,
  onFetchRuleEfficacy,
  onFetchRuleEfficiency,
  onFetchRuleEffectiveness,
  onFetchRuleBehaviour,
  onFetchRuleFeedback,
  onFetchOverallFeedbackAnalysis,
  onFetchFeedBackTimeRangeAnalysis
};
