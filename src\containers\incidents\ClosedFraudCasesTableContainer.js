import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/incidentActions';
import ClosedFraudCasesTable from 'components/incidents/ClosedFraudCasesTable';

const mapStateToProps = (state) => {
  return {
    channels: state.auth.userCreds.channels,
    data: state.incidents.closedList,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch)
  };
};

const ClosedFraudCasesTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ClosedFraudCasesTable);

export default ClosedFraudCasesTableContainer;
