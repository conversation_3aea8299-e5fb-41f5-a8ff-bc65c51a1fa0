import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSlaKpis } from 'actions/slaDashboardActions';
import CaseStatusStats from 'components/dashboards/CaseStatusStats';

const mapStateToProps = (state) => ({
  slaKpis: state.slaDashboard.slaKpis
});

const mapDispatchToProps = (dispatch) => ({
  fetchSlaKpis: bindActionCreators(onFetchSlaKpis, dispatch)
});

const CaseStatusStatsContainer = connect(mapStateToProps, mapDispatchToProps)(CaseStatusStats);

export default CaseStatusStatsContainer;
