/* eslint-disable react/prop-types */
import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';

import performanceMonitor from 'utility/performanceMonitor';

import RuleTable from '../RuleTable';

// Mock ReactTable for performance testing
jest.mock(
  'react-table',
  () =>
    function MockReactTable({ data, columns, filtered, onFilteredChange }) {
      return (
        <div data-testid="react-table">
          <div>Columns: {columns.length}</div>
          <div>Data: {data.length} rows</div>
          <div>Filtered: {filtered?.length || 0} filters</div>
          {onFilteredChange && (
            <button onClick={() => onFilteredChange([{ id: 'name', value: 'test' }])}>
              Apply Filter
            </button>
          )}
          {data.map((row, i) => (
            <div key={i} data-testid={`table-row-${i}`}>
              {row.name} - {row.logic}
            </div>
          ))}
        </div>
      );
    }
);

describe('RuleTable Performance Tests', () => {
  beforeEach(() => {
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const createMockRuleData = (count) =>
    Array.from({ length: count }, (_, i) => ({
      code: `RULE_${i}`,
      name: `Rule ${i}`,
      logic: `TXN.AMOUNT > ${1000 + i}`,
      status: i % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
      order: i + 1
    }));

  const defaultProps = {
    data: {
      list: { frm: createMockRuleData(50) },
      loader: false,
      error: false
    },
    channel: 'frm',
    moduleType: 'acquirer',
    hasSandbox: 1,
    hasMakerChecker: true,
    ruleCreation: {
      actionList: [
        { actionCode: '01', actionName: 'ACCEPTED' },
        { actionCode: '02', actionName: 'DECLINED' }
      ]
    },
    defaultSort: [{ id: 'order', desc: false }],
    typeHeaders: [
      { Header: 'Status', accessor: 'status' },
      { Header: 'Order', accessor: 'order' }
    ],
    actionHeaders: [{ Header: 'Actions', accessor: 'actions' }]
  };

  it('should render large datasets efficiently', () => {
    const largeDataProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        list: { frm: createMockRuleData(1000) }
      }
    };

    performanceMonitor.startTiming('large_dataset_render');

    render(<RuleTable {...largeDataProps} />);

    const duration = performanceMonitor.endTiming('large_dataset_render');
    expect(duration).toBeLessThan(500);
    expect(screen.getByText('Data: 1000 rows')).toBeInTheDocument();
  });

  it('should memoize action options efficiently', () => {
    const { rerender } = render(<RuleTable {...defaultProps} />);

    performanceMonitor.startTiming('action_options_memoization');

    // Re-render with same ruleCreation.actionList
    rerender(<RuleTable {...defaultProps} />);

    const duration = performanceMonitor.endTiming('action_options_memoization');
    expect(duration).toBeLessThan(20);
  });

  it('should handle table props memoization correctly', () => {
    const tableFilters = [{ id: 'name', value: 'test' }];
    const setTableFilters = jest.fn();

    const propsWithFilters = {
      ...defaultProps,
      tableFilters,
      setTableFilters
    };

    const { rerender } = render(<RuleTable {...propsWithFilters} />);

    performanceMonitor.startTiming('table_props_memoization');

    // Re-render with same filters
    rerender(<RuleTable {...propsWithFilters} />);

    const duration = performanceMonitor.endTiming('table_props_memoization');
    expect(duration).toBeLessThan(15);
  });

  it('should construct headers efficiently', () => {
    const complexHeaders = {
      ...defaultProps,
      actionHeaders: Array.from({ length: 10 }, (_, i) => ({
        Header: `Action ${i}`,
        accessor: `action${i}`
      })),
      typeHeaders: Array.from({ length: 15 }, (_, i) => ({
        Header: `Type ${i}`,
        accessor: `type${i}`
      }))
    };

    performanceMonitor.startTiming('header_construction');

    render(<RuleTable {...complexHeaders} />);

    const duration = performanceMonitor.endTiming('header_construction');
    expect(duration).toBeLessThan(50);
    expect(screen.getByText('Columns: 27')).toBeInTheDocument(); // 2 base + 10 action + 15 type
  });

  it('should handle filter changes efficiently', () => {
    const setTableFilters = jest.fn();
    const propsWithFilterHandler = {
      ...defaultProps,
      setTableFilters
    };

    render(<RuleTable {...propsWithFilterHandler} />);

    performanceMonitor.startTiming('filter_change_handling');

    const filterButton = screen.getByText('Apply Filter');
    fireEvent.click(filterButton);

    const duration = performanceMonitor.endTiming('filter_change_handling');
    expect(duration).toBeLessThan(30);
    expect(setTableFilters).toHaveBeenCalledWith([{ id: 'name', value: 'test' }]);
  });

  it('should handle empty data gracefully', () => {
    const emptyDataProps = {
      ...defaultProps,
      data: {
        list: { frm: [] },
        loader: false,
        error: false
      }
    };

    performanceMonitor.startTiming('empty_data_render');

    render(<RuleTable {...emptyDataProps} />);

    const duration = performanceMonitor.endTiming('empty_data_render');
    expect(duration).toBeLessThan(30);
    expect(screen.getByText('Data: 0 rows')).toBeInTheDocument();
  });

  it('should handle loading state efficiently', () => {
    const loadingProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        loader: true
      }
    };

    performanceMonitor.startTiming('loading_state_render');

    render(<RuleTable {...loadingProps} />);

    const duration = performanceMonitor.endTiming('loading_state_render');
    expect(duration).toBeLessThan(50);
  });

  it('should maintain performance with complex data structures', () => {
    const complexData = Array.from({ length: 200 }, (_, i) => ({
      code: `COMPLEX_${i}`,
      name: `Complex Rule ${i}`,
      logic: `(TXN.AMOUNT > ${1000 + i} AND CARD.TYPE == "CREDIT") OR (MERCHANT.CATEGORY == "HIGH_RISK")`,
      status: ['ACTIVE', 'INACTIVE', 'PENDING'][i % 3],
      order: i + 1,
      metadata: {
        tags: [`tag${i % 5}`, `category${i % 3}`],
        complexity: i % 4,
        lastModified: new Date().toISOString()
      },
      nestedData: {
        conditions: Array.from({ length: (i % 5) + 1 }, (_, j) => ({
          field: `field${j}`,
          operator: ['>', '<', '==', '!='][j % 4],
          value: `value${j}`
        }))
      }
    }));

    const complexProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        list: { frm: complexData }
      }
    };

    performanceMonitor.startTiming('complex_data_render');

    render(<RuleTable {...complexProps} />);

    const duration = performanceMonitor.endTiming('complex_data_render');
    expect(duration).toBeLessThan(300);
  });

  it('should not cause memory leaks during prop changes', () => {
    const { rerender } = render(<RuleTable {...defaultProps} />);

    performanceMonitor.takeMemorySnapshot('before_prop_changes');

    // Simulate multiple prop changes
    for (let i = 0; i < 20; i++) {
      const newProps = {
        ...defaultProps,
        data: {
          ...defaultProps.data,
          list: { frm: createMockRuleData(50 + i) }
        }
      };
      rerender(<RuleTable {...newProps} />);
    }

    performanceMonitor.takeMemorySnapshot('after_prop_changes');
    performanceMonitor.checkMemoryLeaks();

    expect(true).toBe(true);
  });

  it('should handle React.memo optimization correctly', () => {
    const { rerender } = render(<RuleTable {...defaultProps} />);

    performanceMonitor.startTiming('memo_optimization_test');

    // Re-render with identical props (should be optimized by React.memo)
    rerender(<RuleTable {...defaultProps} />);

    const duration = performanceMonitor.endTiming('memo_optimization_test');
    expect(duration).toBeLessThan(10); // Should be very fast due to memoization
  });
});
