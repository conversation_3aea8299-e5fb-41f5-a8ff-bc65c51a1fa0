import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSandboxHistory } from 'actions/sandboxingActions';
import RuleEngine from 'components/ruleEngine/RuleEngine';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  userRoles: state.auth.userCreds.roles,
  channels: state.auth.userCreds.channels,
  hasSandbox: state.user.configurations.sandbox,
  hasCognitive: state.user.configurations.cognitive
});

const mapDispatchToProps = (dispatch) => ({
  fetchSandboxHistory: bindActionCreators(onFetchSandboxHistory, dispatch)
});

const RuleEngineContainer = connect(mapStateToProps, mapDispatchToProps)(RuleEngine);

export default RuleEngineContainer;
