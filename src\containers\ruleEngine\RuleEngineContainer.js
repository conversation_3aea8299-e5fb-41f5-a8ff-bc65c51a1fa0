import { connect } from 'react-redux';
import RuleEngine from 'components/ruleEngine/RuleEngine';
import { onFetchSandboxHistory } from 'actions/sandboxingActions';
import { bindActionCreators } from 'redux';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    userRoles: state.auth.userCreds.roles,
    channels: state.auth.userCreds.channels,
    hasSandbox: state.user.configurations.sandbox,
    hasCognitive: state.user.configurations.cognitive
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchSandboxHistory: bindActionCreators(onFetchSandboxHistory, dispatch)
  };
};

const RuleEngineContainer = connect(mapStateToProps, mapDispatchToProps)(RuleEngine);

export default RuleEngineContainer;
