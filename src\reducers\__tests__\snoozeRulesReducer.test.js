import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import snoozeRulesReducer from 'reducers/snoozeRulesReducer';

describe('snoozeRules Reducer', () => {
  it('should return the intial state', () => {
    expect(snoozeRulesReducer(undefined, {})).toEqual(initialState.snoozeRules);
  });

  it('should handle ON_FETCH_SNOOZE_RULE_LIST_LOADING', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_RULE_LIST_LOADING
        }
      )
    ).toEqual({
      list: {
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_RULE_LIST_SUCCESS', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_RULE_LIST_SUCCESS,
          response: responses.snoozeRules.list,
          channel: 'frm'
        }
      )
    ).toEqual({
      list: {
        frm: responses.snoozeRules.list,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_RULE_LIST_FAILURE', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_RULE_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      list: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_ATTRIBUTES_LOADING', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_ATTRIBUTES_LOADING
        }
      )
    ).toEqual({
      attributes: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS,
          response: responses.snoozeRules.attributes
        }
      )
    ).toEqual({
      attributes: {
        list: responses.snoozeRules.attributes,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE', () => {
    expect(
      snoozeRulesReducer(
        {},
        {
          type: types.ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      attributes: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
