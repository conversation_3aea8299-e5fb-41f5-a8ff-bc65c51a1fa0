import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import {
  onToggleAssignShiftModal,
  onToggleUpdateUserRolesModal,
  onToggleUserCaseCriteriaModal
} from 'actions/toggleActions';
import {
  onUserToggleAutoCase,
  onFetchUsersList,
  onDeleteUser
} from 'actions/userManagementActions';
import UsersTable from 'components/userManagement/UsersTable';

const mapStateToProps = (state) => ({
  roleslist: state.user.roles,
  shiftslist: state.user.shifts,
  userslist: state.user.userslist,
  channelslist: state.user.channels,
  userChannels: state.auth.userCreds.channels,
  hasCaseCriteria: state.user.configurations.caseCriteria,
  theme: state.toggle.theme,
  loginType: state.auth.loginType,
  ruleList: state.ruleConfigurator.productionRules.list.frm
});

const mapDispatchToProps = (dispatch) => ({
  fetchUsersList: bindActionCreators(onFetchUsersList, dispatch),
  userAutoCaseUpdate: bindActionCreators(onUserToggleAutoCase, dispatch),
  toggleAssignShiftModal: bindActionCreators(onToggleAssignShiftModal, dispatch),
  toggleUpdateUserRolesModal: bindActionCreators(onToggleUpdateUserRolesModal, dispatch),
  toggleUserCaseCriteriaModal: bindActionCreators(onToggleUserCaseCriteriaModal, dispatch),
  deleteUser: bindActionCreators(onDeleteUser, dispatch),
  fetchRules: bindActionCreators(onFetchRulesList, dispatch)
});

const UsersTableContainer = connect(mapStateToProps, mapDispatchToProps)(UsersTable);

export default UsersTableContainer;
