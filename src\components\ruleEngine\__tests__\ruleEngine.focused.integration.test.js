import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { createStore, combineReducers } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import RuleEngine from '../RuleEngine';

// Mock child containers to avoid complex dependencies
jest.mock('containers/ruleEngine/CognitiveStatusCardContainer', () =>
  jest.fn(() => <div data-testid="cognitive-status">Cognitive Status</div>)
);

jest.mock('containers/ruleEngine/RuleListTableContainer', () =>
  jest.fn(({ channel }) => <div data-testid={`rule-list-${channel}`}>Rule List - {channel}</div>)
);

jest.mock('containers/ruleEngine/DynamicCountersListTableContainer', () =>
  jest.fn(({ channel }) => <div data-testid={`counters-${channel}`}>Counters - {channel}</div>)
);

// Create minimal mock store
const createMockStore = () => {
  const initialState = {
    auth: {
      moduleType: 'acquirer',
      userCreds: {
        roles: 'supervisor',
        channels: ['frm', 'str']
      }
    },
    user: {
      configurations: {
        sandbox: 1,
        cognitive: 1
      }
    },
    toggle: {
      theme: 'light'
    }
  };

  const rootReducer = combineReducers({
    auth: (state = initialState.auth) => state,
    user: (state = initialState.user) => state,
    toggle: (state = initialState.toggle) => state
  });

  return createStore(rootReducer);
};

describe('RuleEngine Focused Integration Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const renderWithProviders = (component) =>
    render(
      <Provider store={store}>
        <BrowserRouter>{component}</BrowserRouter>
      </Provider>
    );

  describe('Basic Integration Tests', () => {
    it('should render single channel efficiently', () => {
      const singleChannelProps = {
        channels: ['frm'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      performanceMonitor.startTiming('single_channel_integration');

      renderWithProviders(<RuleEngine {...singleChannelProps} />);

      const duration = performanceMonitor.endTiming('single_channel_integration');
      expect(duration).toBeLessThan(150);

      expect(screen.getByTestId('cognitive-status')).toBeInTheDocument();
      expect(screen.getByTestId('rule-list-frm')).toBeInTheDocument();
      expect(screen.getByTestId('counters-frm')).toBeInTheDocument();
    });

    it('should render multiple channels with tabs efficiently', () => {
      const multiChannelProps = {
        channels: ['frm', 'str'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      performanceMonitor.startTiming('multi_channel_integration');

      renderWithProviders(<RuleEngine {...multiChannelProps} />);

      const duration = performanceMonitor.endTiming('multi_channel_integration');
      expect(duration).toBeLessThan(200);

      // Should render tabs
      expect(screen.getByText('FRM')).toBeInTheDocument();
      expect(screen.getByText('STR')).toBeInTheDocument();
    });

    it('should handle channel switching efficiently', () => {
      const multiChannelProps = {
        channels: ['frm', 'str'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      renderWithProviders(<RuleEngine {...multiChannelProps} />);

      performanceMonitor.startTiming('channel_switching');

      // Click on STR tab
      const strTab = screen.getByText('STR');
      fireEvent.click(strTab);

      // Click back to FRM tab
      const frmTab = screen.getByText('FRM');
      fireEvent.click(frmTab);

      const duration = performanceMonitor.endTiming('channel_switching');
      expect(duration).toBeLessThan(100);
    });

    it('should handle cognitive status visibility correctly', () => {
      const { rerender } = renderWithProviders(
        <RuleEngine
          channels={['frm']}
          userRoles="supervisor"
          hasCognitive={1}
          hasSandbox={1}
          fetchSandboxHistory={jest.fn()}
        />
      );

      expect(screen.getByTestId('cognitive-status')).toBeInTheDocument();

      // Re-render without cognitive access
      rerender(
        <Provider store={store}>
          <BrowserRouter>
            <RuleEngine
              channels={['frm']}
              userRoles="checker"
              hasCognitive={1}
              hasSandbox={1}
              fetchSandboxHistory={jest.fn()}
            />
          </BrowserRouter>
        </Provider>
      );

      expect(screen.queryByTestId('cognitive-status')).not.toBeInTheDocument();
    });

    it('should handle role-based rendering efficiently', () => {
      const checkerProps = {
        channels: ['frm'],
        userRoles: 'checker',
        hasCognitive: 0,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      performanceMonitor.startTiming('role_based_rendering');

      renderWithProviders(<RuleEngine {...checkerProps} />);

      const duration = performanceMonitor.endTiming('role_based_rendering');
      expect(duration).toBeLessThan(100);

      expect(screen.queryByTestId('cognitive-status')).not.toBeInTheDocument();
      expect(screen.getByTestId('rule-list-frm')).toBeInTheDocument();
    });
  });

  describe('Performance Integration Tests', () => {
    it('should handle rapid prop changes efficiently', () => {
      const { rerender } = renderWithProviders(
        <RuleEngine
          channels={['frm']}
          userRoles="supervisor"
          hasCognitive={1}
          hasSandbox={1}
          fetchSandboxHistory={jest.fn()}
        />
      );

      performanceMonitor.startTiming('rapid_prop_changes');

      // Simulate rapid prop changes
      for (let i = 0; i < 10; i++) {
        const newProps = {
          channels: i % 2 === 0 ? ['frm'] : ['frm', 'str'],
          userRoles: i % 3 === 0 ? 'supervisor' : 'checker',
          hasCognitive: i % 4 === 0 ? 1 : 0,
          hasSandbox: 1,
          fetchSandboxHistory: jest.fn()
        };

        rerender(
          <Provider store={store}>
            <BrowserRouter>
              <RuleEngine {...newProps} />
            </BrowserRouter>
          </Provider>
        );
      }

      const duration = performanceMonitor.endTiming('rapid_prop_changes');
      expect(duration).toBeLessThan(300);
    });

    it('should not cause memory leaks during component lifecycle', () => {
      performanceMonitor.takeMemorySnapshot('before_lifecycle');

      const renders = [];

      // Create multiple instances
      for (let i = 0; i < 5; i++) {
        const props = {
          channels: ['frm'],
          userRoles: 'supervisor',
          hasCognitive: 1,
          hasSandbox: 1,
          fetchSandboxHistory: jest.fn()
        };

        renders.push(renderWithProviders(<RuleEngine {...props} />));
      }

      performanceMonitor.takeMemorySnapshot('after_creation');

      // Unmount all instances
      renders.forEach(({ unmount }) => unmount());

      performanceMonitor.takeMemorySnapshot('after_cleanup');
      performanceMonitor.checkMemoryLeaks();

      expect(true).toBe(true);
    });

    it('should maintain performance with complex channel configurations', () => {
      const complexProps = {
        channels: ['frm', 'str', 'rpsl'],
        userRoles: 'supervisor',
        hasCognitive: 1,
        hasSandbox: 1,
        fetchSandboxHistory: jest.fn()
      };

      performanceMonitor.startTiming('complex_channel_config');

      renderWithProviders(<RuleEngine {...complexProps} />);

      const duration = performanceMonitor.endTiming('complex_channel_config');
      expect(duration).toBeLessThan(250);

      // Verify all channels are rendered
      expect(screen.getByText('FRM')).toBeInTheDocument();
      expect(screen.getByText('STR')).toBeInTheDocument();
      expect(screen.getByText('RPSL')).toBeInTheDocument();
    });
  });

  describe('Memoization Integration Tests', () => {
    it('should use memoized tab names efficiently', () => {
      const { rerender } = renderWithProviders(
        <RuleEngine
          channels={['frm', 'str']}
          userRoles="supervisor"
          hasCognitive={1}
          hasSandbox={1}
          fetchSandboxHistory={jest.fn()}
        />
      );

      performanceMonitor.startTiming('tab_names_memoization');

      // Re-render with same channels (should use memoized tab names)
      rerender(
        <Provider store={store}>
          <BrowserRouter>
            <RuleEngine
              channels={['frm', 'str']}
              userRoles="supervisor"
              hasCognitive={1}
              hasSandbox={1}
              fetchSandboxHistory={jest.fn()}
            />
          </BrowserRouter>
        </Provider>
      );

      const duration = performanceMonitor.endTiming('tab_names_memoization');
      expect(duration).toBeLessThan(50);
    });

    it('should use memoized rule list content efficiently', () => {
      const { rerender } = renderWithProviders(
        <RuleEngine
          channels={['frm']}
          userRoles="supervisor"
          hasCognitive={1}
          hasSandbox={1}
          fetchSandboxHistory={jest.fn()}
        />
      );

      performanceMonitor.startTiming('rule_list_memoization');

      // Re-render with same props (should use memoized rule list)
      rerender(
        <Provider store={store}>
          <BrowserRouter>
            <RuleEngine
              channels={['frm']}
              userRoles="supervisor"
              hasCognitive={1}
              hasSandbox={1}
              fetchSandboxHistory={jest.fn()}
            />
          </BrowserRouter>
        </Provider>
      );

      const duration = performanceMonitor.endTiming('rule_list_memoization');
      expect(duration).toBeLessThan(30);
    });
  });
});
