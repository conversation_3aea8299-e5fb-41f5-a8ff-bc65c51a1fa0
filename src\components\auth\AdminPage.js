import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import {
  Button,
  Form,
  FormGroup,
  Label,
  Input,
  FormFeedback,
  Row,
  Col,
  Alert,
  InputGroup,
  InputGroupText,
  TabPane
} from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import Tabs from 'components/common/Tabs';
import { EMAIL_APPEND_TEXT, USERNAME_APPEND_TEXT } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';
import ActiveUsersTableContainer from 'containers/auth/ActiveUsersTableContainer';
import UnapprovedUsersTableContainer from 'containers/auth/UnapprovedUsersTableContainer';

const AdminPage = ({
  userRoles,
  channelslist,
  userActions,
  userslist,
  toggleActions,
  theme,
  toggleAddUserModal,
  toggleUpdateSupervisor,
  toggleUpdateUserModal,
  partnerIdList,
  allRoles,
  authDetails,
  adminlist,
  peerAdmin,
  fnrUserCreation
}) => {
  const history = useHistory();
  const [email, setEmail] = useState('');
  const [userId, setUserId] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [userName, setUserName] = useState('');
  const [type, setType] = useState('user');
  const [channelRoles, setChannelRoles] = useState([]);
  const [alert, setAlert] = useState({
    visible: false,
    message: '',
    type: ''
  });
  const [invalidEmail, setInvalidEmail] = useState(false);
  const [invalidUserName, setInvalidUserName] = useState(false);
  const [partnerId, setPartnerId] = useState('');
  const [userChannel, setUserChannel] = useState('');
  const [loginType, setLoginType] = useState('');
  const [approver, setApprover] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [selectedUser, setSelectedUser] = useState({});

  useEffect(() => {
    document.title = 'BANKiQ FRC | User Management';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  useEffect(() => {
    if (userRoles !== 'super-admin' && userRoles !== 'admin') history.goBack();
  }, [history, userRoles]);

  useEffect(() => {
    if (_.isEmpty(channelslist)) userActions.onFetchChannels();
  }, [channelslist, userActions]);

  useEffect(() => {
    if (_.isEmpty(partnerIdList) && _.includes(['super-admin', 'admin'], userRoles))
      userActions.onFetchPartnerIdList();
    if (_.isEmpty(adminlist) && userRoles === 'admin') userActions.onFetchAdminList();

    if (_.isEmpty(userslist)) userActions.onFetchUsersList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const newId = email + EMAIL_APPEND_TEXT;
    const existingUser = userslist.filter((user) => user.email === newId);
    setInvalidEmail(!_.isEmpty(existingUser));
  }, [email, userslist]);

  useEffect(() => {
    const newUserName = userName + USERNAME_APPEND_TEXT;
    const existingUser = userslist.filter((user) => user.userName === newUserName);
    setInvalidUserName(!_.isEmpty(existingUser));
  }, [userName, userslist]);

  const toggleAlert = (message = '', type = '') =>
    setAlert({
      type: !_.isEmpty(message) ? type : '',
      visible: !_.isEmpty(message),
      message
    });

  const validateChannelRoles = () => {
    if (
      type === 'supervisor' &&
      authDetails.loginType.toLowerCase() !== 'qrt' &&
      _.isEmpty(channelRoles)
    ) {
      toggleAlert('Assign atleast 1 channel to supervisor', 'danger');
      return false;
    } else return true;
  };

  const toggleAddSupervisorModal = () => {
    setType('supervisor');
    toggleActions();
  };

  const showAddUserModal = (type = 'user') => {
    setType(type);
    if (userRoles !== 'super-admin') {
      setUserName('');
      setChannelRoles([]);
    }
    setSelectedUser({});
    setEditMode(false);
    toggleActions();
  };

  const editUser = (userData) => {
    setType(userData.userType === 'user' ? 'user' : 'supervisor');
    setSelectedUser(userData);

    setEmail(userData.email);
    setFirstName(userData.firstName);
    setLastName(userData.lastName);
    setUserName(userData.userName);
    setApprover(userData.approver);
    setEditMode(true);

    userData.userType === 'user'
      ? setUserChannel(userData.channel)
      : setChannelRoles(userData.channelRoles);

    toggleActions();
  };

  const toggleUpdateSupervisorModal = (userData) => {
    if (userData) {
      setType('supervisor');
      setUserId(userData.id);
      setUserName(userData.userName);
      setChannelRoles(userData.channelRoles);
    } else {
      setType('user');
      setUserName('');
      setChannelRoles([]);
    }
    toggleUpdateSupervisor();
  };

  const addUserSubmit = (e) => {
    e.preventDefault();
    let currentChannelRoles = channelRoles;

    if (invalidEmail) {
      toggleAlert('Invalid user email', 'danger');
      return false;
    }
    if (invalidUserName) {
      toggleAlert('Invalid user name', 'danger');
      return false;
    }

    if (type === 'supervisor' && channelslist.length > 1) {
      const channelValidation = validateChannelRoles();
      if (!channelValidation) return channelValidation;
    } else if (type === 'supervisor') currentChannelRoles = [`${channelslist[0].name}:supervisor`];
    else if (type === 'admin') currentChannelRoles = ['admin'];
    else if (type === 'user') currentChannelRoles = [];

    const currentEmail = email + EMAIL_APPEND_TEXT;
    const currentUserName = userName + USERNAME_APPEND_TEXT;

    const userData = {
      email: currentEmail,
      userName: currentUserName,
      firstName,
      lastName,
      channel: userChannel,
      channelRoles: currentChannelRoles,
      ...(!_.isEmpty(adminlist) && peerAdmin === 1 && { approver }),
      ...(partnerId && { partnerId: parseInt(partnerId) }),
      ...(isCooperative &&
        ['user', 'admin'].includes(type) &&
        authDetails.loginType.toLowerCase() !== 'qrt' && { loginType }),
      ...(editMode && { id: selectedUser.id })
    };

    const formData = { type, userData };
    editMode ? userActions.onUpdateStageUser(formData) : userActions.onAddUser(formData);

    // Reset form fields
    setEmail('');
    setFirstName('');
    setLastName('');
    setUserChannel('');
    setUserId('');
    setUserName('');
    setChannelRoles([]);
    setAlert({
      visible: false,
      message: '',
      type: ''
    });
    setInvalidEmail(false);
    setInvalidUserName(false);
    setPartnerId('');
    setLoginType('');
    setApprover('');
    setEditMode(false);
    setSelectedUser({});
  };

  const addSupervisorChannelSubmit = (e) => {
    e.preventDefault();
    const userData = { userName, userId, channelRoles };
    const formData = { userData };
    userActions.onUpdateUserRoles(formData);
  };

  const toggleChannelCheckbox = (selectedChannelRole) => {
    let updatedChannelRoles = [];
    if (_.includes(channelRoles, selectedChannelRole))
      updatedChannelRoles = _.filter(channelRoles, (role) => role !== selectedChannelRole);
    else updatedChannelRoles = [...channelRoles, selectedChannelRole];

    setChannelRoles(updatedChannelRoles);
  };

  const channelSelector =
    type === 'supervisor' &&
    channelslist.length > 1 &&
    authDetails.loginType.toLowerCase() !== 'qrt' ? (
      <FormGroup>
        <Label>Channel</Label>
        <Row>
          {channelslist.map((channel) => (
            <Col key={channel.id}>
              <FormGroup check>
                <Label>
                  <Input
                    type="checkbox"
                    id={`checkbox${channel.id}`}
                    name="rolesChannel[]"
                    value={`${channel.name}:supervisor`}
                    onChange={() => toggleChannelCheckbox(`${channel.name}:supervisor`)}
                    checked={_.includes(channelRoles, `${channel.name}:supervisor`)}
                  />
                  {_.upperCase(channel.name)}
                </Label>
              </FormGroup>
            </Col>
          ))}
        </Row>
      </FormGroup>
    ) : null;

  const action = (
    <span>
      {userRoles === 'admin' && (
        <Button size="sm" color="warning" onClick={() => toggleAddSupervisorModal()}>
          Add supervisor
        </Button>
      )}
      {userRoles === 'super-admin' && fnrUserCreation === 1 && (
        <Button size="sm" color="warning" className="ms-2" onClick={() => showAddUserModal('fnr')}>
          Add FNR User
        </Button>
      )}
      <Button
        size="sm"
        color="primary"
        className="ms-2"
        onClick={() => showAddUserModal(userRoles === 'super-admin' ? 'admin' : 'user')}>
        {userRoles === 'super-admin' ? 'Add admin' : 'Add user'}
      </Button>
    </span>
  );

  const partnerIdOptions = partnerIdList.map((partner) => (
    <option key={partner.id} value={partner.id}>
      {partner.partnerName}
    </option>
  ));

  const channelOption = _.map(channelslist, (channel) => (
    <option key={channel.name}>{channel.name}</option>
  ));

  const loginTypeData =
    type === 'user'
      ? _.filter(authDetails.loginTypes, (type) => type.loginType.toLowerCase() !== 'qrt').map(
          (type) => type.loginType
        )
      : _.find(allRoles, (role) => role.name === 'admin')?.loginType;

  const loginTypeOptions = _.map(loginTypeData, (type) => (
    <option key={type} value={type}>
      {type}
    </option>
  ));

  const approverOption = _.map(adminlist, (admin) => (
    <option key={admin.id} value={admin.userName}>
      {admin.userName}
    </option>
  ));

  const tabNames = ['Active', 'Non-Active'];

  return (
    <div className="content-wrapper ">
      <CardContainer title="User List" action={action}>
        {peerAdmin === 1 && userRoles === 'admin' ? (
          <Tabs tabNames={tabNames}>
            <TabPane tabId={0}>
              <ActiveUsersTableContainer peerAdmin={peerAdmin} />
            </TabPane>
            <TabPane tabId={1}>
              <UnapprovedUsersTableContainer editUser={editUser} />
            </TabPane>
          </Tabs>
        ) : (
          <ActiveUsersTableContainer peerAdmin={peerAdmin} />
        )}
      </CardContainer>

      <ModalContainer
        theme={theme}
        isOpen={toggleAddUserModal}
        header={`${editMode ? 'Edit ' : 'Add '}${type}`}
        size="md"
        toggle={() => showAddUserModal()}>
        <Form onSubmit={addUserSubmit} autoComplete="off" autoSave="off">
          <Alert color={alert.type} isOpen={alert.visible} toggle={() => toggleAlert()}>
            {alert.message}
          </Alert>
          <FormGroup>
            <Label for="email">Email</Label>
            <InputGroup>
              <Input
                type="text"
                name="email"
                id="email"
                pattern="[A-Za-z0-9._%+\-@]{3,70}"
                title="Min 4 characters. Max 70 characters"
                onChange={(event) => setEmail(event.target.value)}
                value={email}
                required
                invalid={invalidEmail || undefined}
              />
              {!_.isEmpty(EMAIL_APPEND_TEXT) && (
                <InputGroupText>{EMAIL_APPEND_TEXT}</InputGroupText>
              )}
            </InputGroup>
            <FormFeedback
              invalid={invalidEmail || undefined}
              className={invalidEmail ? 'block' : 'none'}>
              Email id already registered.
            </FormFeedback>
          </FormGroup>
          <FormGroup>
            <Label for="firstName">First Name</Label>
            <Input
              type="text"
              name="firstName"
              id="firstName"
              pattern="[A-Za-z]{3,20}"
              title="First name should be alphabetical. Min 3 characters. Max 20 characters"
              onChange={(event) => setFirstName(event.target.value)}
              value={firstName}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label for="lastName">Last Name</Label>
            <Input
              type="text"
              name="lastName"
              id="lastName"
              pattern="[A-Za-z]{1,20}"
              title="Last name should be alphabetical. Min 1 characters. Max 20 characters"
              onChange={(event) => setLastName(event.target.value)}
              value={lastName}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label for="userName">UserName</Label>
            <InputGroup>
              <Input
                type="text"
                id="userName"
                name="userName"
                pattern="[A-Za-z0-9._%+\-@]{3,70}"
                title="Min 4 characters. Max 70 characters"
                onChange={(event) => setUserName(event.target.value)}
                value={userName}
                required
                invalid={invalidUserName || undefined}
              />
              {!_.isEmpty(USERNAME_APPEND_TEXT) && (
                <InputGroupText>{USERNAME_APPEND_TEXT}</InputGroupText>
              )}
            </InputGroup>
            <FormFeedback
              invalid={invalidUserName || undefined}
              className={invalidUserName ? 'block' : 'none'}>
              Username already in use.
            </FormFeedback>
          </FormGroup>
          {!_.isEmpty(adminlist) &&
            (type === 'user' || type === 'supervisor') &&
            peerAdmin === 1 &&
            !editMode && (
              <FormGroup>
                <Label>Select approver</Label>
                <Input
                  type="select"
                  name="approver"
                  id="approver"
                  onChange={(e) => setApprover(e.target.value)}
                  value={approver}
                  required>
                  <option value=""> -- SELECT --</option>
                  {approverOption}
                </Input>
              </FormGroup>
            )}
          {type === 'user' && authDetails.loginType.toLowerCase() !== 'qrt' && (
            <FormGroup>
              <Label>Channel</Label>
              <Input
                type="select"
                name="userChannel"
                id="userChannel"
                onChange={(e) => setUserChannel(e.target.value)}
                value={userChannel}
                required>
                <option value=""> -- SELECT --</option>
                {channelOption}
              </Input>
            </FormGroup>
          )}
          {['user', 'admin'].includes(type) &&
            authDetails.loginType.toLowerCase() !== 'qrt' &&
            isCooperative && (
              <FormGroup>
                <Label>Select Login Type</Label>
                <Input
                  type="select"
                  name="loginType"
                  value={loginType}
                  onChange={(e) => setLoginType(e.target.value)}
                  required>
                  <option value="">-- Select --</option>
                  {loginTypeOptions}
                </Input>
              </FormGroup>
            )}
          {((userRoles === 'super-admin' && !isCooperative) ||
            (type === 'user' && isCooperative && loginType.toLowerCase() === 'bank')) && (
            <FormGroup>
              <Label>Select Partner ID</Label>
              <Input
                type="select"
                name="partnerId"
                value={partnerId}
                onChange={(e) => setPartnerId(e.target.value)}
                required>
                <option value="">-- Select --</option>
                {partnerIdOptions}
              </Input>
            </FormGroup>
          )}
          {channelSelector}
          <FormGroup>
            <Button
              size="sm"
              color="primary"
              className="d-flex ms-auto"
              disabled={invalidEmail || invalidUserName}>
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>

      <ModalContainer
        theme={theme}
        isOpen={toggleUpdateUserModal}
        header={`Update channels for supervisor role - ${userName}`}
        size="lg"
        toggle={() => toggleUpdateSupervisorModal()}>
        <Form onSubmit={addSupervisorChannelSubmit}>
          {channelSelector}
          <FormGroup>
            <Button size="sm" color="primary" className="d-flex ms-auto">
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </div>
  );
};

AdminPage.propTypes = {
  userslist: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  userRoles: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  toggleAddUserModal: PropTypes.bool.isRequired,
  toggleUpdateUserModal: PropTypes.bool.isRequired,
  userActions: PropTypes.object.isRequired,
  toggleActions: PropTypes.func.isRequired,
  toggleUpdateSupervisor: PropTypes.func.isRequired,
  partnerIdList: PropTypes.array.isRequired,
  allRoles: PropTypes.array.isRequired,
  authDetails: PropTypes.object.isRequired,
  adminlist: PropTypes.array.isRequired,
  peerAdmin: PropTypes.number.isRequired,
  fnrUserCreation: PropTypes.number.isRequired
};

export default AdminPage;
