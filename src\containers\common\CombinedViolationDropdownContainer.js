import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import CombinedViolationDropdown from 'components/common/CombinedViolationDropdown';

const mapStateToProps = (state) => ({
  ruleNames: state.ruleConfigurator.ruleNames,
  channels: state.auth.userCreds.channels
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch)
});

const CombinedViolationDropdownContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CombinedViolationDropdown);

export default CombinedViolationDropdownContainer;
