import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { CardTitle, CardSubtitle, Row, Col, Input, Button, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

import RFIReportsTable from './RFIReportsTable';

function UnusalDeclineTurnoverReport({
  unusualDeclineTurnover,
  fetchUnusualDeclineTurnoverCount,
  fetchUnusualDeclineTurnoverData
}) {
  const [cummValue, setCummValue] = useState(unusualDeclineTurnover.cummValue);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const handlePageChange = (page) => {
    fetchUnusualDeclineTurnoverData({ pageNo: page + 1, pageSize, cummValue });
    setPageNo(page);
  };

  const columnHeaders = [
    {
      Header: 'Previous Month',
      columns: [
        {
          Header: 'Amount',
          accessor: 'txnAmountInPrevMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'txnAmountInPrevMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'txnCountInPrevMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'txnCountInPrevMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    },
    {
      Header: 'Current Month',
      columns: [
        {
          Header: 'Amount',
          accessor: 'txnAmountInCurrentMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              step="0.01"
              placeholder="Amount greater than"
              value={_.find(tableFilters, ['id', 'txnAmountInCurrentMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        },
        {
          Header: 'Count',
          accessor: 'txnCountInCurrentMonth',
          filterMethod: (filter, row) =>
            !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
          // eslint-disable-next-line react/prop-types
          Filter: ({ onChange }) => (
            <input
              type="number"
              min="0"
              step="0.01"
              placeholder="Count greater than"
              value={_.find(tableFilters, ['id', 'txnCountInCurrentMonth'])?.value}
              onChange={(event) => onChange(event.target.value)}
            />
          )
        }
      ]
    }
  ];

  const onSubmit = (e) => {
    e.preventDefault();
    if (cummValue !== unusualDeclineTurnover.cummValue && !isNaN(cummValue))
      fetchUnusualDeclineTurnoverCount({ cummValue });
  };

  return (
    <Row>
      <Col md="12" className="mb-3">
        <form onSubmit={onSubmit}>
          <Row>
            <Col xs="8" md="3">
              <Label htmlFor="cummValue">Transaction Amount (current month)</Label>
              <Input
                type="number"
                id="cummValue"
                name="cummValue"
                value={cummValue}
                placeholder="Enter value to generate report"
                onChange={(e) => setCummValue(+e.target.value)}
                min={0}
                max={99999999999}
                pattern="\d{1,11}"
                title="Please input valid count"
                required
              />
            </Col>
            <Col className="d-flex align-items-end">
              <Button outline size="sm" color="primary">
                Search
              </Button>
            </Col>
          </Row>
        </form>
      </Col>
      <Col md="3">
        <CardContainer>
          <CardTitle className="text-info">{unusualDeclineTurnover.count?.value ?? 0}</CardTitle>
          <CardSubtitle># Unusual Decline Turnover</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="12">
        <CardContainer title="Unusual Decline Turnover">
          {unusualDeclineTurnover.count?.value > 0 ? (
            <RFIReportsTable
              count={unusualDeclineTurnover.count?.value}
              additionalHeaders={columnHeaders}
              data={unusualDeclineTurnover.data}
              filter={{ cummValue }}
              fetchData={fetchUnusualDeclineTurnoverData}
              page={pageNo}
              pageSize={pageSize}
              filtered={tableFilters}
              onPageChange={(page) => handlePageChange(page)}
              onPageSizeChange={(pageSize) => {
                setPageNo(0);
                setPageSize(pageSize);
              }}
              onFilteredChange={(filtered) => setTableFilters(filtered)}
            />
          ) : (
            <div className="no-data-div">No data available</div>
          )}
        </CardContainer>
      </Col>
    </Row>
  );
}

UnusalDeclineTurnoverReport.propTypes = {
  unusualDeclineTurnover: PropTypes.object.isRequired,
  fetchUnusualDeclineTurnoverCount: PropTypes.func.isRequired,
  fetchUnusualDeclineTurnoverData: PropTypes.func.isRequired
};

export default UnusalDeclineTurnoverReport;
