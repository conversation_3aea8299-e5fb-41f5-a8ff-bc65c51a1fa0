import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import {
  onFetchSimilarTransactions,
  onFetchSimilarTxnCategoryList
} from 'actions/investigationActions';
import SimilarTxnsTable from 'components/investigation/SimilarTxnsTable';

const mapStateToProps = (state) => ({
  similarData: state.investigation.similar,
  similarTxnCategoryList: state.investigation.similarTxnCategoryList,
  moduleType: state.auth.moduleType
});

const mapDispatchToProps = (dispatch) => ({
  fetchSimilarTransactions: bindActionCreators(onFetchSimilarTransactions, dispatch),
  fetchSimilarTxnCategoryList: bindActionCreators(onFetchSimilarTxnCategoryList, dispatch)
});

const SimilarTxnsTableContainer = connect(mapStateToProps, mapDispatchToProps)(SimilarTxnsTable);

export default SimilarTxnsTableContainer;
