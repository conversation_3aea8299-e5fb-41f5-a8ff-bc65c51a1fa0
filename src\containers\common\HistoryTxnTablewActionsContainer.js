import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onCreateCaseAndAssign } from 'actions/caseAssignmentActions';
import {
  onFetchTransactionHistoryByStatus,
  onClearTransactionHistoryByStatus
} from 'actions/transactionHistorySearchActions';
import HistoryTxnTablewActions from 'components/common/HistoryTxnTablewActions';

const mapStateToProps = (state) => {
  return {
    userId: state.auth.userCreds.userId,
    historyData: state.investigation.transactionHistoryByStatus
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    createCaseAndAssign: bindActionCreators(onCreateCaseAndAssign, dispatch),
    clearHistory: bindActionCreators(onClearTransactionHistoryByStatus, dispatch),
    fetchHistoryTransactions: bindActionCreators(onFetchTransactionHistoryByStatus, dispatch)
  };
};

const HistoryTxnTablewActionsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(HistoryTxnTablewActions);

export default HistoryTxnTablewActionsContainer;
