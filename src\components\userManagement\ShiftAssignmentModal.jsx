import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Row, Col, FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

function ShiftAssignmentModal({
  theme,
  shiftslist,
  selectedUser,
  showAssignShiftModal,
  userAssignShifts,
  toggleAssignShiftModal
}) {
  const [selectedShifts, setSelectedShifts] = useState([]);
  const [selectedShiftsName, setSelectedShiftsName] = useState([]);

  useEffect(() => {
    setSelectedShifts(
      shiftslist
        .filter((shift) => selectedUser.shiftNames?.includes(shift.shiftName))
        .map((shift) => shift.id)
    );

    setSelectedShiftsName(
      shiftslist
        .filter((shift) => selectedUser.shiftNames?.includes(shift.shiftName))
        .map((shift) => shift.shiftName)
    );
  }, [selectedUser, shiftslist]);

  function toggleShiftCheckbox(shift) {
    let newShifts = [];
    let newShiftsName = [];
    if (selectedShifts.includes(shift.id)) {
      newShifts = selectedShifts?.filter((selectedShift) => selectedShift !== shift.id);
      newShiftsName = selectedShiftsName.filter(
        (selectedShift) => selectedShift !== shift.shiftName
      );
    } else {
      newShifts = [...selectedShifts, shift.id];
      newShiftsName = [...selectedShiftsName, shift.shiftName];
    }
    setSelectedShifts(newShifts);
    setSelectedShiftsName(newShiftsName);
  }

  function assignShiftSubmit(e) {
    e.preventDefault();
    const formData = {
      userId: selectedUser.id,
      shiftIds: selectedShifts,
      shifts: selectedShiftsName
    };
    userAssignShifts(formData);
    setSelectedShifts([]);
    setSelectedShiftsName([]);
  }

  const shiftCheckboxes =
    shiftslist.length > 0
      ? shiftslist.map((shift) => (
          <Col sm="4" xs="6" key={shift.shiftName}>
            <FormGroup check>
              <Label>
                <Input
                  type="checkbox"
                  id={`checkbox${shift.shiftName}`}
                  name="shifts[]"
                  value={shift.id}
                  onChange={() => toggleShiftCheckbox(shift)}
                  checked={selectedShifts.includes(shift.id)}
                />
                {shift.shiftName}
              </Label>
            </FormGroup>
          </Col>
        ))
      : null;

  return (
    <ModalContainer
      theme={theme}
      header={`Update user shift - ${selectedUser.userName}`}
      isOpen={showAssignShiftModal}
      size="md"
      toggle={() => toggleAssignShiftModal()}>
      <form onSubmit={(event) => assignShiftSubmit(event)}>
        <FormGroup>
          <Label>Shift</Label>
          <Row>{shiftCheckboxes}</Row>
        </FormGroup>
        <Button size="sm" color="primary" className="d-flex ms-auto">
          Submit
        </Button>
      </form>
    </ModalContainer>
  );
}

ShiftAssignmentModal.propTypes = {
  theme: PropTypes.string.isRequired,
  shiftslist: PropTypes.array.isRequired,
  selectedUser: PropTypes.object.isRequired,
  showAssignShiftModal: PropTypes.bool.isRequired,
  userAssignShifts: PropTypes.func.isRequired,
  toggleAssignShiftModal: PropTypes.func.isRequired
};

export default ShiftAssignmentModal;
