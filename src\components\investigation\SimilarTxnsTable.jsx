import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';

import CardContainer from 'components/common/CardContainer';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';
import TxnSearchFilterForm from 'components/common/TxnSearchFilterForm';
import { findValueInTxnDetail } from '../../utility/utils';

const SimilarTxnsTable = ({
  similarData,
  fetchSimilarTransactions,
  txnDetails,
  similarTxnCategoryList,
  fetchSimilarTxnCategoryList,
  channel,
  moduleType
}) => {
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);
  const [pageChangeOnSubmit, setPageChangeOnSubmit] = useState(false);

  const [txnCategory, setTxnCategory] = useState(
    !_.isEmpty(txnDetails?.deviceInfo?.terminalId?.value || '')
      ? [
          {
            label: 'terminalId',
            value: 'terminalId'
          }
        ]
      : moduleType == 'issuer'
      ? [
          {
            label: 'payerId',
            value: 'payerId'
          }
        ]
      : [
          {
            label: 'payeeId',
            value: 'payeeId'
          }
        ]
  );

  useEffect(() => {
    setPageNo(0);
    !_.isEmpty(getSimilarTxnCategory()) &&
      fetchSimilarTransactions(
        {
          similarTxnCategory: getSimilarTxnCategory(),
          pageNo: 1,
          pageSize: pageRecords
        },
        channel
      );

    _.isEmpty(similarTxnCategoryList?.list) &&
      !similarTxnCategoryList.loader &&
      fetchSimilarTxnCategoryList();
  }, []);

  useEffect(() => {
    !_.isEmpty(similarData.similarTxnCategory) &&
      (!pageChangeOnSubmit || _.isEqual(similarData.similarTxnCategory, getSimilarTxnCategory())) &&
      fetchSimilarTransactions(
        {
          similarTxnCategory: similarData.similarTxnCategory,
          pageNo: pageNo + 1,
          pageSize: pageRecords
        },
        channel
      );

    setPageChangeOnSubmit(false);
  }, [pageNo, pageRecords]);

  const handleSubmit = () => {
    setPageChangeOnSubmit(true);
    setPageNo(0);
    !_.isEmpty(getSimilarTxnCategory()) &&
      fetchSimilarTransactions(
        {
          similarTxnCategory: getSimilarTxnCategory(),
          pageNo: 1,
          pageSize: pageRecords
        },
        channel
      );
  };

  // Define special handling keys and mappings
  const specialHandlingKeys = new Map([
    ['txnType', { split: true }],
    ['channelId', { mapTo: 'channelName', split: true }]
  ]);

  // Get similar transaction categories
  const getSimilarTxnCategory = () => {
    const txnDetail = txnDetails || {};

    const mappings = _.reduce(
      txnCategory,
      (acc, { value: key }) => {
        let value = findValueInTxnDetail(key, txnDetail);

        // Check for special handling cases
        const specialHandling = specialHandlingKeys.get(key);

        if (specialHandling) {
          if (specialHandling.mapTo) {
            // Handle case where one key maps to another key
            value = findValueInTxnDetail(specialHandling.mapTo, txnDetail);
          }
          if (specialHandling.split) {
            // Handle case where value needs to be split
            value = _.isString(value) ? value.split(',')[0].trim() : value;
          }
        }

        acc[key] = value;
        return acc;
      },
      {}
    );

    // Check if payerAccountNumber or payeeAccountNumber are selected
    const isPayerSelected = _.some(txnCategory, { value: 'payerAccountNumber' });
    const isPayeeSelected = _.some(txnCategory, { value: 'payeeAccountNumber' });

    if (isPayerSelected || isPayeeSelected) {
      // Add txnId to the mappings if payerAccountNumber or payeeAccountNumber are selected
      mappings.txnId = findValueInTxnDetail('txnId', txnDetail);
    }

    return mappings;
  };

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const tablePageCountProp = _.isEmpty(tableFilters)
    ? {
        pages: similarData.count / pageRecords > 1 ? Math.ceil(similarData.count / pageRecords) : 1
      }
    : {};

  const similarHeader = [{ Header: 'Similarity', accessor: 'similarity' }];

  return (
    <CardContainer title="Similar Transactions">
      <TxnSearchFilterForm
        id="similarTxnFilterForm"
        txnCategory={txnCategory}
        setTxnCategory={setTxnCategory}
        categoryList={similarTxnCategoryList?.list}
        onClickSearchBtn={handleSubmit}
        isSearchBtnDisabled={
          _.isEmpty(txnCategory) ||
          _.isEqual(similarData.similarTxnCategory, getSimilarTxnCategory())
        }
        txnDetails={txnDetails}
      />
      <TransactionTableContainer
        minRows={6}
        page={pageNo}
        pageSize={pageRecords}
        filtered={tableFilters}
        componentHeaders={similarHeader}
        data={similarData}
        onPageChange={(page) => setPageNo(page)}
        onPageSizeChange={(pageSize, page) => {
          setPageNo(page);
          setPageRecords(pageSize);
        }}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        {...tablePageCountProp}
      />
    </CardContainer>
  );
};

SimilarTxnsTable.propTypes = {
  similarData: PropTypes.object.isRequired,
  fetchSimilarTransactions: PropTypes.func.isRequired,
  txnDetails: PropTypes.object.isRequired,
  fetchSimilarTxnCategoryList: PropTypes.func.isRequired,
  similarTxnCategoryList: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired
};

export default SimilarTxnsTable;
