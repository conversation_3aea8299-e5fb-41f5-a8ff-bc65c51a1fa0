import logsReducer from 'reducers/logsReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';

describe('Logs reducer', () => {
  it('should return the intial state', () => {
    expect(logsReducer(undefined, {})).toEqual(initialState.logs);
  });

  it('should handle ON_FETCH_SELECTED_CASE_LOGS_LOADING', () => {
    expect(
      logsReducer(
        {
          caseLogs: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING
        }
      )
    ).toEqual({
      caseLogs: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS', () => {
    const response = [
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated',
        entityId: "{{ urlParam 'entityId' }}",
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated1',
        entityId: "{{ urlParam 'entityId' }}",
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated2',
        entityId: "{{ urlParam 'entityId' }}",
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      },
      {
        userName: 'supervisor',
        logTimestamp: '2020-08-05 10:10:10',
        module: 'Rule',
        activityType: 'RuleCreated3',
        entityId: "{{ urlParam 'entityId' }}",
        description:
          'Name: rule001, Desc: Count Rule, Logic: (Transactions within 15 minutes should have count greater than 2), Channel: ATM, Priority: 2, Wt: 10, Type: Dsl'
      }
    ];
    expect(
      logsReducer(
        {
          caseLogs: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_SELECTED_CASE_LOGS,
          response
        }
      )
    ).toEqual({
      caseLogs: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SELECTED_CASE_LOGS_FAILURE', () => {
    expect(
      logsReducer(
        {
          caseLogs: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SELECTED_CASE_LOGS_FAILURE,
          response: { message: 'Unable to fetch audit logs for selected case.' }
        }
      )
    ).toEqual({
      caseLogs: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'Unable to fetch audit logs for selected case.'
      }
    });
  });

  it('should handle ON_FETCH_SELECTED_ENTITY_LOGS_LOADING', () => {
    expect(
      logsReducer(
        {
          entityLogs: {
            entityId: '',
            list: [],
            count: 1,
            filterCondition: [],
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING
        }
      )
    ).toEqual({
      entityLogs: {
        entityId: '',
        list: [],
        count: 1,
        filterCondition: [],
        isLastPage: true,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS', () => {
    const response = {
      statusLogs: [
        {
          dateAndTime: '2021-01-26T17:32:07',
          txnType: 'txn123',
          userName: 'user1',
          action: 'closed',
          description: 'rule violated with id 123',
          txnId: 'hnr93A',
          txnAmount: 17000,
          responseCode: '00,Accepted',
          violatedRules: '12353-43545,dfd-4343',
          caseId: '614',
          txnTypeName: 'Bill Payment'
        },
        {
          dateAndTime: '2021-01-27T17:32:07',
          txnType: 'txn987',
          userName: 'user2',
          action: 'closed',
          description: 'rule violated closed case',
          txnId: 'hnr94A',
          txnAmount: 17000,
          responseCode: '00',
          violatedRules: '12353-43545,dfd-4343',
          caseId: '614',
          txnTypeName: 'Bill Payment'
        }
      ],
      isLastPage: false,
      count: 100
    };

    let filterCondition = [
      { key: 'rules', condition: 'EQUAL', values: ['15dd9b65-549d-44e1-b922-aa065d98766e'] },
      { key: 'user', condition: 'EQUAL', values: ['<EMAIL>'] },
      {
        key: 'activityDateTime',
        condition: 'BETWEEN',
        values: ['2021-10-14 00:00:00', '2021-10-04 00:00:00']
      },
      { key: 'autoCaseAssignment', condition: 'EQUAL', values: ['true'] }
    ];
    expect(
      logsReducer(
        {
          entityLogs: {
            entityId: '',
            list: [],
            count: 1,
            filterCondition: [],
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SELECTED_ENTITY_LOGS_SUCCESS,
          response,
          entityId: '123',
          filterCondition
        }
      )
    ).toEqual({
      entityLogs: {
        entityId: '123',
        list: response.statusLogs,
        count: response.count,
        filterCondition,
        isLastPage: response.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE', () => {
    let filterCondition = [
      { key: 'rules', condition: 'EQUAL', values: ['15dd9b65-549d-44e1-b922-aa065d98766e'] },
      { key: 'user', condition: 'EQUAL', values: ['<EMAIL>'] },
      {
        key: 'activityDateTime',
        condition: 'BETWEEN',
        values: ['2021-10-14 00:00:00', '2021-10-04 00:00:00']
      },
      { key: 'autoCaseAssignment', condition: 'EQUAL', values: ['true'] }
    ];

    expect(
      logsReducer(
        {
          entityLogs: {
            entityId: '',
            list: [],
            count: 1,
            filterCondition: [],
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SELECTED_ENTITY_LOGS_FAILURE,
          response: { message: 'Unable to fetch status logs for selected entity.' },
          entityId: '123',
          filterCondition
        }
      )
    ).toEqual({
      entityLogs: {
        entityId: '123',
        list: [],
        count: 0,
        filterCondition,
        isLastPage: true,
        loader: false,
        error: true,
        errorMessage: 'Unable to fetch status logs for selected entity.'
      }
    });
  });
});
