import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardBody, CardTitle } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/free-solid-svg-icons';

const ImagePreviewer = ({ fileName, src, onClick }) => {
  return (
    <Card className="my-2 file-upload-preview">
      <embed className="file-thumbnail card-img-top" alt={fileName} src={src} onClick={onClick} />
      <CardBody className="p-1">
        <CardTitle className="file-title">
          <span className="text" title={fileName}>
            {fileName}
          </span>
          <a
            href={src}
            download={fileName}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary">
            <FontAwesomeIcon icon={faDownload} />
          </a>
        </CardTitle>
      </CardBody>
    </Card>
  );
};

ImagePreviewer.propTypes = {
  onClick: PropTypes.func,
  src: PropTypes.string.isRequired,
  fileName: PropTypes.string.isRequired
};

export default ImagePreviewer;
