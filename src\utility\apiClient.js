import {
  BASE_URL,
  URL_CONTEXT,
  RADIX,
  BAD_REQUEST,
  NOT_FOUND,
  FORBIDDEN,
  INTERNAL_SERVER_ERROR
} from 'constants/applicationConstants';
import { includes } from 'lodash';
import fetch from 'node-fetch';

function client({
  url,
  data,
  method = 'GET',
  badRequestMessage = '',
  notFoundMessage = '',
  headers = {},
  multipart = false
}) {
  const config = {
    method,
    body: multipart ? data : data ? JSON.stringify(data) : undefined,
    headers: {
      ...(data && !multipart && { 'Content-Type': 'application/json' }),
      ...(headers && headers)
    }
  };

  return fetch(BASE_URL + URL_CONTEXT + url, config).then(async (response) => {
    const isJsonResponse = includes(response.headers.get('content-type'), 'json');
    const respData = isJsonResponse ? await response.json() : await response.text();
    if (response.ok) {
      return respData;
    } else {
      return Promise.reject(
        errorHandling(
          {
            status: response.status,
            detail: respData?.detail
          },
          badRequestMessage ?? 'Unable to perform activity. Check request information',
          notFoundMessage ?? 'Service unavailable. Contact administrator'
        )
      );
    }
  });
}

function errorHandling(response, defaultBadRequest, defaultNotFound) {
  switch (parseInt(response.status, RADIX)) {
    case BAD_REQUEST:
      return {
        message: response.detail ?? defaultBadRequest
      };
    case FORBIDDEN:
      return {
        message: response.detail ?? 'Authentication error'
      };
    case NOT_FOUND:
      return { message: response.detail ?? defaultNotFound };
    case INTERNAL_SERVER_ERROR:
      return { message: 'Something went wrong. Please try again' };
    default:
      return { message: 'Something went wrong. Please try again' };
  }
}

export default client;
