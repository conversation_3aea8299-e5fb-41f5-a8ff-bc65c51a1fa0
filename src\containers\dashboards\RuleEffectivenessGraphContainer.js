import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleEffectiveness } from 'actions/ruleDashboardActions';
import RuleEffectivenessGraph from 'components/dashboards/RuleEffectivenessGraph';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleEffectiveness: state.ruleDashboard.effectiveness
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleEffectiveness: bindActionCreators(onFetchRuleEffectiveness, dispatch)
  };
};

const RuleEffectivenessGraphContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleEffectivenessGraph);

export default RuleEffectivenessGraphContainer;
