import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Checklist from 'components/ruleEngine/Checklist';
import { onFetchCheckListOptions, onFetchCheckList } from 'actions/ruleCreationActions';

const mapStateToProps = (state) => {
  return {
    checkListOptions: state.ruleCreation.checkListOptions,
    checkList: state.ruleCreation.checkList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCheckListOptions: bindActionCreators(onFetchCheckListOptions, dispatch),
    fetchCheckList: bindActionCreators(onFetchCheckList, dispatch)
  };
};

const ChecklistContainer = connect(mapStateToProps, mapDispatchToProps)(Checklist);

export default ChecklistContainer;
