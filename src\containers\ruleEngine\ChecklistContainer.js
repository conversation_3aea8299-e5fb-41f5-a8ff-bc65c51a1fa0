import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCheckListOptions, onFetchCheckList } from 'actions/ruleCreationActions';
import Checklist from 'components/ruleEngine/Checklist';

const mapStateToProps = (state) => ({
  checkListOptions: state.ruleCreation.checkListOptions,
  checkList: state.ruleCreation.checkList
});

const mapDispatchToProps = (dispatch) => ({
  fetchCheckListOptions: bindActionCreators(onFetchCheckListOptions, dispatch),
  fetchCheckList: bindActionCreators(onFetchCheckList, dispatch)
});

const ChecklistContainer = connect(mapStateToProps, mapDispatchToProps)(Checklist);

export default ChecklistContainer;
