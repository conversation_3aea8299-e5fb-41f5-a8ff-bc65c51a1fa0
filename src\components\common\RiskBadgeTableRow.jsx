import PropTypes from 'prop-types';
import React from 'react';
import { Badge } from 'reactstrap';

const RiskBadgeTableRow = ({ label, value, badge, formatter }) => {
  if (value === null || value === undefined || value === '') return null;

  return (
    <tr>
      <td className="border-0 p-1 align-middle">
        <strong>{label}</strong>
      </td>
      <td className="border-0 p-1 align-middle text-end">
        {badge ? (
          <Badge className="text-uppercase" color={badge}>
            {value}
          </Badge>
        ) : (
          <span>{formatter ? formatter(value) : value}</span>
        )}
      </td>
    </tr>
  );
};

RiskBadgeTableRow.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  badge: PropTypes.string,
  formatter: PropTypes.func
};

export default RiskBadgeTableRow;
