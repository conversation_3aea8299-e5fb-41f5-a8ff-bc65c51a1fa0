import PropTypes from 'prop-types';
import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { TabContent, Nav, NavItem, NavLink, Row, Col } from 'reactstrap';

const Tabs = forwardRef(
  (
    {
      tabNames,
      action,
      children,
      pills = false,
      getCurrentTab = undefined,
      tabChangeAction = undefined,
      vertical = false
    },
    ref
  ) => {
    const [activeTab, setActiveTab] = useState(0);
    const [activeTabName, setActiveTabName] = useState(tabNames[activeTab]);

    useEffect(() => {
      if (tabChangeAction) tabChangeAction(activeTab);
    }, [activeTab]);

    useEffect(() => {
      if (getCurrentTab) getCurrentTab(activeTab);
    }, [activeTab]);

    const changeTab = (i, tab) => {
      if (activeTab !== i) setActiveTab(i);
      if (activeTabName !== tab) setActiveTabName(tab);
    };

    useImperativeHandle(ref, () => ({ changeTab }));

    const dynamicTabs = tabNames.map((tab, i) => (
      <NavItem key={i}>
        <NavLink
          className={activeTab === i ? 'active-tab' : null}
          onClick={() => changeTab(i, tab)}>
          {tab}
        </NavLink>
      </NavItem>
    ));

    return (
      <Row>
        <Col md={vertical ? 4 : 12}>
          <div
            className={`d-flex justify-content-between tab-header ${
              vertical ? 'vertical-tab' : ''
            }`}>
            <Nav className={pills ? 'nav-pills' : 'nav-tab'} vertical={vertical}>
              {dynamicTabs}
            </Nav>
            {/* TODO: Need to change this as specific checks should not be added in generic components */}
            {activeTabName !== 'Compliance' && activeTabName !== 'Operations' ? action : null}
          </div>
        </Col>
        <Col md={vertical ? 8 : 12}>
          <TabContent activeTab={activeTab} className="pt-3">
            {children}
          </TabContent>
        </Col>
      </Row>
    );
  }
);

Tabs.propTypes = {
  pills: PropTypes.bool,
  vertical: PropTypes.bool,
  action: PropTypes.object,
  getCurrentTab: PropTypes.func,
  tabChangeAction: PropTypes.func,
  tabNames: PropTypes.array.isRequired,
  children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]).isRequired
};

export default Tabs;
