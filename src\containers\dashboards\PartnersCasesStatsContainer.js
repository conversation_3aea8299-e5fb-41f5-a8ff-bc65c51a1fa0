import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchPartnersCaseStats } from 'actions/slaDashboardActions';
import PartnersCasesStats from 'components/dashboards/PartnersCasesStats';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    partnersCaseStats: state.slaDashboard.partnersCaseStats
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchPartnersCaseStats: bindActionCreators(onFetchPartnersCaseStats, dispatch)
  };
};

const PartnersCasesStatsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(PartnersCasesStats);

export default PartnersCasesStatsContainer;
