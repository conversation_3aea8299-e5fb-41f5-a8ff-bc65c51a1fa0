export const getRiskLevelColor = (level, isReverse = false, isTransactionRisk = false) => {
  if (!level) return 'secondary';

  const risk = level.toLowerCase();

  // Special color mapping for Transaction Risk Level
  if (isTransactionRisk) {
    const transactionRiskMap = {
      low: 'warning', // yellow
      safe: 'success', // green
      high: 'danger' // red
    };
    return transactionRiskMap[risk] || 'secondary';
  }

  // Original logic for other cases
  if (isReverse) return risk === 'high' ? 'success' : 'danger';

  const riskMap = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  };

  return riskMap[risk] || 'secondary';
};

export const formatRiskScore = (score) => {
  if (typeof score !== 'number' || isNaN(score)) return 'N/A';
  return score;
};
