import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as actions from 'actions/incidentActions';
import { onToggleCPIFRFormModal } from 'actions/toggleActions';
import CPFIRForm from 'components/incidents/CPFIRForm';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  incident: state.incidents.incident,
  cpifrMaster: state.incidents.masters,
  displayModal: state.toggle.cpifrFormModal
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(actions, dispatch),
  toggleModal: bindActionCreators(onToggleCPIFRFormModal, dispatch)
});

const CPFIRFormContainer = connect(mapStateToProps, mapDispatchToProps)(CPFIRForm);

export default CPFIRFormContainer;
