import React from 'react';
import PropTypes from 'prop-types';
import { Card } from 'reactstrap';

import Card<PERSON>ontainer from 'components/common/CardContainer';
import TableFilterForm from 'components/common/TableFilterForm';
import UnverifiedCasesTableHOC from 'containers/incidents/UnverifiedCasesTableHOC';

function UnverifiedCasesTable({ data, hasProvisionalFields, fetchCases }) {
  return (
    <CardContainer title={'Unverified Cases'}>
      <TableFilterForm
        currentConf={data.conf}
        fetchCases={fetchCases}
        channel={'frm'}
        hasProvisionalFields={hasProvisionalFields}
      />
      <Card>
        <UnverifiedCasesTableHOC
          currentConf={data.conf}
          userRole={'investigator'}
          channel={'frm'}
        />
      </Card>
    </CardContainer>
  );
}

UnverifiedCasesTable.propTypes = {
  data: PropTypes.object.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  fetchCases: PropTypes.func.isRequired
};

export default UnverifiedCasesTable;
