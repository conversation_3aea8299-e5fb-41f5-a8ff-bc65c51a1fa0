import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onShowFailureAlert } from 'actions/alertActions';
import * as ruleConfiguratorActions from 'actions/ruleConfiguratorActions';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import { onFetchSnoozeRulesList } from 'actions/ruleSnoozeActions';
import * as toggleActions from 'actions/toggleActions';
import RuleListTable from 'components/ruleEngine/RuleListTable';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  ruleCreation: state.ruleCreation,
  role: state.auth.userCreds.roles
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
  ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
  fetchSnoozeRulesList: bindActionCreators(onFetchSnoozeRulesList, dispatch),
  ruleConfiguratorActions: bindActionCreators(ruleConfiguratorActions, dispatch)
});

const RuleListTableContainer = connect(mapStateToProps, mapDispatchToProps)(RuleListTable);

export default RuleListTableContainer;
