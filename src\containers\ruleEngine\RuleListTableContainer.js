import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as toggleActions from 'actions/toggleActions';
import { onShowFailureAlert } from 'actions/alertActions';
import { onFetchSnoozeRulesList } from 'actions/ruleSnoozeActions';
import * as ruleCreationActions from 'actions/ruleCreationActions';
import * as ruleConfiguratorActions from 'actions/ruleConfiguratorActions';
import RuleListTable from 'components/ruleEngine/RuleListTable';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    ruleCreation: state.ruleCreation,
    role: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    ruleCreationActions: bindActionCreators(ruleCreationActions, dispatch),
    fetchSnoozeRulesList: bindActionCreators(onFetchSnoozeRulesList, dispatch),
    ruleConfiguratorActions: bindActionCreators(ruleConfiguratorActions, dispatch)
  };
};

const RuleListTableContainer = connect(mapStateToProps, mapDispatchToProps)(RuleListTable);

export default RuleListTableContainer;
