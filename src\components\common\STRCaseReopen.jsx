import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolderOpen } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';

const STRCaseReopen = ({
  theme,
  role,
  usersList,
  userId,
  reOpenCase,
  caseRefNo,
  selectedCase,
  advanceSearchData,
  showText = false
}) => {
  const [display, setDisplay] = useState(false);
  const [reason, setReason] = useState('');
  const [selectedMaker, setSelectedMaker] = useState('');

  const strMakersList = _.filter(usersList, (user) => _.includes(user.channelRoles, 'str:maker'));

  useEffect(() => {
    clearModalValue();
  }, [display]);

  const clearModalValue = () => {
    setReason('');
    setSelectedMaker('');
  };

  const submit = (e) => {
    e.preventDefault();
    const data = {
      caseRefNo,
      reason,
      stageId: 1,
      assignedTo:
        role === 'maker'
          ? +userId
          : role === 'checker' && strMakersList.length === 1
          ? +strMakersList[0].id
          : +selectedMaker
    };
    reOpenCase(data, 'str', selectedCase, advanceSearchData);
    setDisplay(false);
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="success"
        className="ms-1"
        title="Re-Open case"
        onClick={() => setDisplay(!display)}>
        <FontAwesomeIcon icon={faFolderOpen} /> {showText ? ' Re-Open' : ''}
      </Button>
      <ModalContainer
        theme={theme}
        header={'Re-Open Case - ' + caseRefNo}
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form onSubmit={submit}>
          <FormGroup>
            <Label>Reason</Label>
            <Input
              type="textarea"
              name="reason"
              id="reason"
              placeholder="reason for re-opening case"
              onChange={(e) => setReason(e.target.value)}
              value={reason}
              required
            />
          </FormGroup>
          {role === 'checker' && strMakersList.length > 1 && (
            <FormGroup>
              <Label>Assign to </Label>
              <Input
                type="select"
                name="maker"
                id="maker"
                value={selectedMaker}
                onChange={(e) => setSelectedMaker(e.target.value)}
                required>
                <option key={0} value="">
                  Select analyst
                </option>
                {_.map(strMakersList, (maker) => (
                  <option key={maker.id} value={maker.id}>
                    {maker.userName}
                  </option>
                ))}
              </Input>
            </FormGroup>
          )}
          <FormGroup className="d-flex justify-content-end">
            <Button type="submit" size="sm" color="primary">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

STRCaseReopen.propTypes = {
  showText: PropTypes.bool,
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  userId: PropTypes.number.isRequired,
  usersList: PropTypes.array.isRequired,
  reOpenCase: PropTypes.func.isRequired,
  selectedCase: PropTypes.object,
  advanceSearchData: PropTypes.object
};

export default STRCaseReopen;
