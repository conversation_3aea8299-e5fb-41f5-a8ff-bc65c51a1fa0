import objectAssign from 'object-assign';
import PropTypes from 'prop-types';
import React, { useMemo, memo } from 'react';
import { Card } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableFilterForm from 'components/common/TableFilterForm';
import BucketCasesTableContainer from 'containers/common/BucketCasesTableContainer';
import BucketListDropdownContainer from 'containers/common/BucketListDropdownContainer';

const BucketCasesCard = ({
  data,
  userRole,
  fetchCases,
  channel,
  hasProvisionalFields,
  userList
}) => {
  const currentConf = objectAssign({}, data?.conf, { role: userRole });

  const bucketSelector = useMemo(
    () => <BucketListDropdownContainer conf={currentConf} channel={channel} userRole={userRole} />,
    [channel, currentConf, userRole]
  );

  const memoizedForm = useMemo(
    () => (
      <TableFilterForm
        currentConf={currentConf}
        fetchCases={fetchCases}
        channel={channel}
        hasProvisionalFields={hasProvisionalFields}
        userList={userList}
      />
    ),
    [channel, currentConf, fetchCases, hasProvisionalFields, userList]
  );

  const memoizedTable = useMemo(
    () => (
      <BucketCasesTableContainer currentConf={currentConf} userRole={userRole} channel={channel} />
    ),
    [channel, currentConf, userRole]
  );

  return (
    <CardContainer title={bucketSelector}>
      {memoizedForm}
      <Card className="mt-3">{memoizedTable}</Card>
    </CardContainer>
  );
};

BucketCasesCard.propTypes = {
  data: PropTypes.object.isRequired,
  userRole: PropTypes.string.isRequired,
  fetchCases: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  userList: PropTypes.array
};

export default memo(BucketCasesCard);
