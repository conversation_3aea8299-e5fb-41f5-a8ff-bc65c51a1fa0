import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Row, Col } from 'reactstrap';
import { isEmpty, map, filter, some } from 'lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faThumbsUp, faThumbsDown } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';

const RuleFeedbackForm = ({
  theme,
  rulesCodes,
  fetchRulesWithConditions,
  saveFeedback,
  channels,
  rulesWithConditions,
  ruleFeedbackModal,
  toggleRuleFeedbackModal,
  txnDetails
}) => {
  const [feedbackData, setFeedbackData] = useState([]);

  useEffect(() => {
    const formData = {
      rulesCodes
    };

    !isEmpty(rulesCodes) &&
      isEmpty(rulesWithConditions) &&
      fetchRulesWithConditions(formData, channels[0]);
  }, [rulesCodes]);

  useEffect(() => {
    const feedbackData = map(rulesWithConditions, (rule) => ({
      ruleCode: rule.ruleCode,
      ruleRating: 0,
      name: rule.name,
      ruleCondition: map(rule.ruleCondition, (condition) => ({
        ruleConditionId: condition.ruleConditionId,
        conditionRating: 0,
        condition: condition.condition
      }))
    }));

    setFeedbackData(feedbackData);
  }, [rulesWithConditions]);

  const getFilteredData = (feedbackData) => {
    const filteredRules = filter(feedbackData, (rule) => {
      const hasValidRuleRating = rule.ruleRating !== 0;
      const hasValidConditionRating = some(
        rule.ruleCondition,
        (condition) => condition.conditionRating !== 0
      );
      return hasValidRuleRating || hasValidConditionRating;
    });

    const filteredRuleAndConditions = map(filteredRules, (rule) => ({
      ...rule,
      ruleCondition: filter(rule.ruleCondition, (condition) => condition.conditionRating !== 0)
    }));

    return filteredRuleAndConditions;
  };

  const handleSaveFeedback = () => {
    const formData = {
      txnTimestamp: txnDetails?.transactionInfo?.txnTimestamp,
      feedbacks: getFilteredData(feedbackData)
    };

    saveFeedback(formData, channels[0]);
  };

  const handleRatingChange = (currentRating, type) => {
    return currentRating === 0 ? (type === 'up' ? 1 : -1) : 0;
  };

  const handleRuleFeedback = (ruleId, type) => {
    const updatedFeedbackData = map(feedbackData, (rule) => ({
      ...rule,
      ruleRating:
        rule.ruleCode === ruleId ? handleRatingChange(rule.ruleRating, type) : rule.ruleRating
    }));

    setFeedbackData(updatedFeedbackData);
  };

  const handleConditionFeedback = (ruleId, type, conditionId) => {
    const updatedFeedbackData = map(feedbackData, (rule) => ({
      ...rule,
      ruleCondition: map(rule.ruleCondition, (condition) => ({
        ...condition,
        conditionRating:
          condition.ruleConditionId === conditionId && rule.ruleCode === ruleId
            ? handleRatingChange(condition.conditionRating, type)
            : condition.conditionRating
      }))
    }));

    setFeedbackData(updatedFeedbackData);
  };

  return (
    <ModalContainer
      theme={theme}
      header="Rule feedback form"
      toggle={toggleRuleFeedbackModal}
      isOpen={ruleFeedbackModal}
      size="lg">
      <Row>
        {map(feedbackData, (rule) => (
          <Col md="6" className="mb-5 pb-5 feedback-buttons" key={rule.ruleCode}>
            <Row>
              <Col md="9" className="rule-name">
                {rule.name}
              </Col>
              <Col>
                <Button
                  size="sm"
                  color="success"
                  title="useful"
                  onClick={() => handleRuleFeedback(rule.ruleCode, 'up')}
                  outline={rule.ruleRating != 1}
                  className={
                    rule.ruleRating === 1
                      ? 'useful-active-no-hover-focus'
                      : 'useful-inactive-no-hover-focus'
                  }>
                  <FontAwesomeIcon icon={faThumbsUp} />
                </Button>
                <Button
                  size="sm"
                  color="danger"
                  title="Not useful"
                  onClick={() => handleRuleFeedback(rule.ruleCode, 'down')}
                  outline={rule.ruleRating != -1}
                  className={
                    rule.ruleRating === -1
                      ? 'not-useful-active-no-hover-focus'
                      : 'not-useful-inactive-no-hover-focus'
                  }>
                  <FontAwesomeIcon icon={faThumbsDown} />
                </Button>
              </Col>
            </Row>
            {(rule.ruleRating === 1 || rule.ruleRating === -1) && (
              <div className="mt-4">
                {map(rule.ruleCondition, (condition) => (
                  <Row key={condition.ruleConditionId + rule.ruleCode} className="mt-4">
                    <Col md="9" className="condition-name">
                      {condition.condition}
                    </Col>
                    <Col>
                      <Button
                        size="sm"
                        color="success"
                        title="useful"
                        onClick={() =>
                          handleConditionFeedback(rule.ruleCode, 'up', condition.ruleConditionId)
                        }
                        outline={condition.conditionRating != 1}
                        className={
                          condition.conditionRating === 1
                            ? 'useful-active-no-hover-focus'
                            : 'useful-inactive-no-hover-focus'
                        }>
                        <FontAwesomeIcon icon={faThumbsUp} />
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        title="Not useful"
                        onClick={() =>
                          handleConditionFeedback(rule.ruleCode, 'down', condition.ruleConditionId)
                        }
                        outline={condition.conditionRating != -1}
                        className={
                          condition.conditionRating === -1
                            ? 'not-useful-active-no-hover-focus'
                            : 'not-useful-inactive-no-hover-focus'
                        }>
                        <FontAwesomeIcon icon={faThumbsDown} />
                      </Button>
                    </Col>
                  </Row>
                ))}
              </div>
            )}
          </Col>
        ))}
      </Row>
      <div className="d-flex justify-content-end mt-3">
        <Button
          outline
          size="sm"
          type="button"
          color="success"
          className="me-2"
          onClick={toggleRuleFeedbackModal}>
          Cancel
        </Button>
        <Button
          size="sm"
          type="submit"
          color="primary"
          disabled={getFilteredData(feedbackData).length === 0}
          onClick={handleSaveFeedback}>
          Save
        </Button>
      </div>
    </ModalContainer>
  );
};

RuleFeedbackForm.propTypes = {
  theme: PropTypes.string.isRequired,
  rulesCodes: PropTypes.array.isRequired,
  fetchRulesWithConditions: PropTypes.func.isRequired,
  saveFeedback: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired,
  rulesWithConditions: PropTypes.array.isRequired,
  ruleFeedbackModal: PropTypes.bool.isRequired,
  toggleRuleFeedbackModal: PropTypes.func.isRequired,
  txnDetails: PropTypes.object.isRequired
};

export default RuleFeedbackForm;
