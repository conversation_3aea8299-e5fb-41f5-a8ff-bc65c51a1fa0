import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';

const SandboxLoader = ({ status }) => {
  let message;
  if (status === 'STARTED') message = 'Initializing sandbox. Please wait...';
  else if (status === 'PENDING') message = 'Sandbox testing in progress. Please wait...';
  else message = 'Sandbox testing FAILED!';

  return (
    <div className="no-data-div flex-column">
      <FontAwesomeIcon icon={faSpinner} className="fa-spin mb-3 fa-2x" />
      <p>{message}</p>
    </div>
  );
};

SandboxLoader.propTypes = {
  status: PropTypes.string
};

export default SandboxLoader;
