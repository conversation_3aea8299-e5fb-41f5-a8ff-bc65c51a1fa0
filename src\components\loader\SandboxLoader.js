import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

const SandboxLoader = ({ status }) => {
  return (
    <div className="no-data-div flex-column">
      <FontAwesomeIcon icon={faSpinner} className="fa-spin mb-3 fa-2x" />
      <p>
        {status === 'STARTED'
          ? 'Initializing sandbox. Please wait...'
          : status === 'PENDING'
          ? 'Sandbox testing in progress. Please wait...'
          : 'Sandbox testing FAILED!'}
      </p>
    </div>
  );
};

SandboxLoader.propTypes = {
  status: PropTypes.string
};

export default SandboxLoader;
