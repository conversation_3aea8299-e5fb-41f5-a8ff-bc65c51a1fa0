import {
  ON_<PERSON><PERSON><PERSON>_CASE_DOCUMENTS_LOADING,
  ON_FETCH_CASE_DOCUMENTS_SUCCESS,
  ON_FETCH_CASE_DOCUMENTS_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function caseDocumentReducer(state = initialState.caseDocument, action) {
  switch (action.type) {
    case ON_FETCH_CASE_DOCUMENTS_LOADING:
      return {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      };
    case ON_FETCH_CASE_DOCUMENTS_SUCCESS:
      return {
        data: action.response.files ?? [],
        loader: false,
        error: false,
        errorMessage: ''
      };
    case ON_FETCH_CASE_DOCUMENTS_FAILURE:
      return {
        data: [],
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      };
    default:
      return state;
  }
}
