'use strict';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { isEmpty, filter, includes, round } from 'lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle, faSpinner } from '@fortawesome/free-solid-svg-icons';
import {
  Label,
  Input,
  Alert,
  Button,
  FormGroup,
  ListGroup,
  FormFeedback,
  ListGroupItem,
  TabPane
} from 'reactstrap';
import Tabs from 'components/common/Tabs';

function RuleBuilder({
  channel,
  formName,
  helperList,
  prefilterLists,
  combinedRuleList,
  ruleData,
  validation,
  ruleCreationActions,
  updateRuleData,
  invalidateLogic,
  moduleType
}) {
  const { code, methodType, logic, comments } = ruleData;
  const [visible, setVisible] = useState({
    validationMessage: false,
    ruleFalsePositiveMessage: false,
    ruleSimilarityMessage: false
  });
  const [prefixSearch, setPrefixSearch] = useState('');
  const [parameterSearch, setParameterSearch] = useState('');
  const [eventSearch, setEventSearch] = useState('');
  const [operatorSearch, setOperatorSearch] = useState('');
  const [functionSearch, setFunctionSearch] = useState('');
  const [counterSearch, setCounterSearch] = useState('');
  const [specializedListSearch, setSpecializedListSearch] = useState('');
  const [invalidLogic, setInvalidLogic] = useState(false);
  const [validateLoading, setValidateLoading] = useState(false);

  useEffect(() => {
    const isInvalid =
      combinedRuleList.filter(
        (d) => d.logic == logic && (formName == 'edit' ? d.code != code : true)
      ).length > 0;
    setInvalidLogic(isInvalid);
    invalidateLogic(isInvalid);
  }, [logic]);

  useEffect(() => {
    isEmpty(helperList[channel]) && ruleCreationActions.onFetchDSLHelpers(channel);
    resetValidation();
  }, [methodType]);

  const appendDSL = (text) => {
    resetValidation();
    let updateLogic = logic + text + ' ';
    updateRuleData('logic', updateLogic);
  };

  const resetValidation = () => {
    setVisible({
      validationMessage: false,
      ruleFalsePositiveMessage: false,
      ruleSimilarityMessage: false
    });
    ruleCreationActions.onClearValidation();
  };

  const validateDSL = () => {
    let formData = { logic, channel };
    if (!invalidLogic) {
      setValidateLoading(true);
      ruleCreationActions.onVerifyDSL(formData);
      setValidateLoading(false);
      setVisible({
        validationMessage: true,
        ruleFalsePositiveMessage: true,
        ruleSimilarityMessage: true
      });
    }
  };

  const prefixList = filter(helperList[channel].prefix, (d) => includes(d.name, prefixSearch)).map(
    (item, i) => {
      return (
        <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
          <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
          {item.name}
        </ListGroupItem>
      );
    }
  );

  const functionsList = filter(
    helperList[channel].functions,
    (d) => includes(d.name, functionSearch) || includes(d.description, functionSearch)
  ).map((item, i) => {
    return (
      <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {item.name}
        <p>
          <small>{item.description}</small>
        </p>
      </ListGroupItem>
    );
  });

  const countersList = filter(
    helperList[channel].counters,
    (d) =>
      includes(d.name.toLowerCase(), counterSearch.toLowerCase()) ||
      includes(d.description.toLowerCase(), counterSearch.toLowerCase())
  ).map((item, i) => {
    return (
      <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {item.name}
        <p>
          <small>{item.description}</small>
        </p>
      </ListGroupItem>
    );
  });

  const parametersList = filter(
    helperList[channel].parameters,
    (d) => includes(d.name, parameterSearch) || includes(d.description, parameterSearch)
  ).map((item, i) => {
    return (
      <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {item.name}
        <p>
          <small>{item.description}</small>
        </p>
      </ListGroupItem>
    );
  });

  const eventList = filter(
    helperList[channel].events,
    (d) => includes(d.name, eventSearch) || includes(d.description, eventSearch)
  ).map((item, i) => {
    return (
      <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {item.name}
        <p>
          <small>{item.description}</small>
        </p>
      </ListGroupItem>
    );
  });

  const operatorsList = filter(
    helperList[channel].operators,
    (d) => includes(d.name, operatorSearch) || includes(d.description, operatorSearch)
  ).map((item, i) => {
    return (
      <ListGroupItem key={i} onClick={() => appendDSL(item.name)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {item.name}
        <p>
          <small>{item.description}</small>
        </p>
      </ListGroupItem>
    );
  });

  const specializedLists = filter(prefilterLists.data, (d) =>
    includes(`${d.categoryName} On "${d.listName}"`, specializedListSearch)
  ).map((item, i) => {
    return (
      <ListGroupItem
        key={i}
        onClick={() => appendDSL(`${item.categoryName} On "${item.listName}"`)}>
        <FontAwesomeIcon icon={faPlusCircle} className="me-1" />
        {`${item.categoryName} On "${item.listName}"`}
      </ListGroupItem>
    );
  });

  const handleToggleAlert = (name) => {
    setVisible((prev) => ({ ...prev, [name]: !visible[name] }));
  };

  return (
    <div className="mb-3">
      <Tabs
        pills
        vertical={true}
        tabNames={
          channel != 'str' && moduleType !== 'acquirer'
            ? [
                'Prefix',
                'Parameters',
                'Operators',
                'Functions',
                'Counters',
                'Specialized Lists',
                'Events'
              ]
            : ['Prefix', 'Parameters', 'Operators', 'Functions', 'Counters', 'Specialized Lists']
        }>
        <TabPane key={0} tabId={0}>
          <Label className="list-heading">Prefix</Label>
          <Input
            type="text"
            placeholder="search..."
            name="prefixSearch"
            value={prefixSearch}
            onChange={(event) => setPrefixSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{prefixList}</ListGroup>
        </TabPane>
        <TabPane key={1} tabId={1}>
          <Label className="list-heading">Parameters</Label>
          <Input
            type="text"
            placeholder="search..."
            name="parameterSearch"
            value={parameterSearch}
            onChange={(event) => setParameterSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{parametersList}</ListGroup>
        </TabPane>
        <TabPane key={2} tabId={2}>
          <Label className="list-heading">Operators</Label>
          <Input
            type="text"
            placeholder="search..."
            name="operatorSearch"
            value={operatorSearch}
            onChange={(event) => setOperatorSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{operatorsList}</ListGroup>
        </TabPane>
        <TabPane key={3} tabId={3}>
          <Label className="list-heading">Functions</Label>
          <Input
            type="text"
            placeholder="search..."
            name="functionSearch"
            value={functionSearch}
            onChange={(event) => setFunctionSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{functionsList}</ListGroup>
        </TabPane>
        <TabPane key={4} tabId={4}>
          <Label className="list-heading">Counters</Label>
          <Input
            type="text"
            placeholder="search..."
            name="counterSearch"
            value={counterSearch}
            onChange={(event) => setCounterSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{countersList}</ListGroup>
        </TabPane>
        <TabPane key={5} tabId={5}>
          <Label className="list-heading">Specialized Lists</Label>
          <Input
            type="text"
            placeholder="search..."
            name="specializedListSearch"
            value={specializedListSearch}
            onChange={(event) => setSpecializedListSearch(event.target.value)}
            bsSize="sm"
          />
          <ListGroup className="dsl-list-group">{specializedLists}</ListGroup>
        </TabPane>
        {channel != 'str' && moduleType !== 'acquirer' && (
          <TabPane key={6} tabId={6}>
            <Label className="list-heading">Events</Label>
            <Input
              type="text"
              placeholder="search..."
              name="eventSearch"
              value={eventSearch}
              onChange={(event) => setEventSearch(event.target.value)}
              bsSize="sm"
            />
            <ListGroup className="dsl-list-group">{eventList}</ListGroup>
          </TabPane>
        )}
      </Tabs>

      <FormGroup className="mt-3">
        <Label>Logic</Label>
        <Input
          type="textarea"
          name="logic"
          rows="3"
          value={logic}
          onChange={(event) => {
            updateRuleData('logic', event.target.value);
            resetValidation(event);
          }}
          required
          spellCheck={false}
          invalid={invalidLogic || undefined}
        />
        <FormFeedback invalid={invalidLogic ? 'invalid' : undefined}>
          Rule with same logic already exists
        </FormFeedback>
      </FormGroup>
      <Alert
        color={validation.status ? 'success' : 'danger'}
        isOpen={visible.validationMessage}
        toggle={() => handleToggleAlert('validationMessage')}>
        {validation.message}
      </Alert>
      {!isEmpty(validation.ruleFalsePositiveData) && (
        <Alert
          color={'warning'}
          isOpen={visible.ruleFalsePositiveMessage}
          toggle={() => handleToggleAlert('ruleFalsePositiveMessage')}>
          {`Rule false positive rate is ${
            validation.ruleFalsePositiveData.prediction ? 'high' : 'low'
          }, which is ${round(validation.ruleFalsePositiveData.probability, 0)}%.`}
        </Alert>
      )}
      {!isEmpty(validation.ruleSimilarityData) && (
        <Alert
          color={'warning'}
          isOpen={visible.ruleSimilarityMessage}
          toggle={() => handleToggleAlert('ruleSimilarityMessage')}>
          {`Above logic matches with logic of ${validation.ruleSimilarityData.name} rule by ${round(
            validation.ruleSimilarityData.percentage,
            0
          )}%.`}
        </Alert>
      )}
      <span className="d-flex justify-content-end">
        <Button
          color="success"
          type="button"
          size="sm"
          onClick={() => validateDSL()}
          disabled={validateLoading}>
          {validateLoading ? (
            <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
          ) : (
            'Validate'
          )}
        </Button>
      </span>
      {formName == 'edit' && (
        <FormGroup>
          <Label>Comment</Label>
          <Input
            type="textarea"
            name="comments"
            value={comments}
            onChange={(event) => updateRuleData('comments', event.target.value)}
          />
        </FormGroup>
      )}
    </div>
  );
}

RuleBuilder.propTypes = {
  channel: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  combinedRuleList: PropTypes.array.isRequired,
  ruleData: PropTypes.object.isRequired,
  validation: PropTypes.object.isRequired,
  helperList: PropTypes.object.isRequired,
  prefilterLists: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired,
  updateRuleData: PropTypes.func.isRequired,
  invalidateLogic: PropTypes.func.isRequired,
  moduleType: PropTypes.string.isRequired
};

export default RuleBuilder;
