'use strict';
import { isEmpty, round } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect, useCallback, useMemo, lazy, Suspense } from 'react';
import { Label, Input, Alert, Button, FormGroup, FormFeedback } from 'reactstrap';

import Loader from 'components/loader/Loader';

const DSLHelperTabsContainer = lazy(() => import('containers/ruleEngine/DSLHelperTabsContainer'));

function RuleBuilder({
  channel,
  formName,
  combinedRuleList,
  ruleData,
  validation,
  ruleCreationActions,
  updateRuleData,
  invalidateLogic
}) {
  const { code, methodType, logic, comments } = ruleData;
  const [visible, setVisible] = useState({
    validationMessage: false,
    ruleFalsePositiveMessage: false,
    ruleSimilarityMessage: false
  });
  const [invalidLogic, setInvalidLogic] = useState(false);
  const [validateLoading, setValidateLoading] = useState(false);

  // Memoize the invalid logic check for better performance
  const isInvalidLogic = useMemo(() => {
    if (!logic || !Array.isArray(combinedRuleList)) return false;
    return combinedRuleList.some(
      (rule) => rule.logic === logic && (formName !== 'edit' || rule.code !== code)
    );
  }, [combinedRuleList, logic, formName, code]);

  useEffect(() => {
    setInvalidLogic(isInvalidLogic);
    invalidateLogic(isInvalidLogic);
  }, [isInvalidLogic, invalidateLogic]);

  useEffect(() => {
    resetValidation();
  }, [methodType, resetValidation]);

  const appendDSL = useCallback(
    (text) => {
      if (visible.validationMessage) resetValidation();
      const updateLogic = `${logic + text} `;
      updateRuleData('logic', updateLogic);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [logic, resetValidation, updateRuleData]
  );

  const resetValidation = useCallback(() => {
    setVisible({
      validationMessage: false,
      ruleFalsePositiveMessage: false,
      ruleSimilarityMessage: false
    });
    ruleCreationActions.onClearValidation();
  }, [ruleCreationActions]);

  const validateDSL = useCallback(async () => {
    if (invalidLogic || !logic.trim()) return;

    const formData = { logic, channel };
    setValidateLoading(true);

    try {
      await ruleCreationActions.onVerifyDSL(formData);
      setVisible({
        validationMessage: true,
        ruleFalsePositiveMessage: true,
        ruleSimilarityMessage: true
      });
    } catch {
      // Error is handled by the action creator
    } finally {
      setValidateLoading(false);
    }
  }, [invalidLogic, logic, channel, ruleCreationActions]);

  const handleToggleAlert = (name) => {
    setVisible((prev) => ({ ...prev, [name]: !visible[name] }));
  };

  return (
    <div className="mb-3">
      <Suspense fallback={<Loader show={true} />}>
        <DSLHelperTabsContainer channel={channel} appendDSL={appendDSL} />
      </Suspense>
      <FormGroup className="mt-3">
        <Label>Logic</Label>
        <Input
          type="textarea"
          name="logic"
          rows="3"
          value={logic}
          onChange={(event) => {
            updateRuleData('logic', event.target.value);
            resetValidation(event);
          }}
          required
          spellCheck={false}
          invalid={invalidLogic || undefined}
        />
        <FormFeedback invalid={invalidLogic ? 'invalid' : undefined}>
          Rule with same logic already exists
        </FormFeedback>
      </FormGroup>
      <Alert
        color={validation.status ? 'success' : 'danger'}
        isOpen={visible.validationMessage}
        toggle={() => handleToggleAlert('validationMessage')}>
        {validation.message}
      </Alert>
      {!isEmpty(validation.ruleFalsePositiveData) && (
        <Alert
          color="warning"
          isOpen={visible.ruleFalsePositiveMessage}
          toggle={() => handleToggleAlert('ruleFalsePositiveMessage')}>
          {`Rule false positive rate is ${
            validation.ruleFalsePositiveData.prediction ? 'high' : 'low'
          }, which is ${round(validation.ruleFalsePositiveData.probability, 0)}%.`}
        </Alert>
      )}
      {!isEmpty(validation.ruleSimilarityData) && (
        <Alert
          color="warning"
          isOpen={visible.ruleSimilarityMessage}
          toggle={() => handleToggleAlert('ruleSimilarityMessage')}>
          {`Above logic matches with logic of ${validation.ruleSimilarityData.name} rule by ${round(
            validation.ruleSimilarityData.percentage,
            0
          )}%.`}
        </Alert>
      )}
      <span className="d-flex justify-content-end">
        <Button
          color="success"
          type="button"
          size="sm"
          onClick={() => validateDSL()}
          disabled={validateLoading}>
          {validateLoading ? 'Validating...' : 'Validate'}
        </Button>
      </span>
      {formName === 'edit' && (
        <FormGroup>
          <Label>Comment</Label>
          <Input
            type="textarea"
            name="comments"
            value={comments}
            onChange={(event) => updateRuleData('comments', event.target.value)}
          />
        </FormGroup>
      )}
    </div>
  );
}

RuleBuilder.propTypes = {
  channel: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  combinedRuleList: PropTypes.array.isRequired,
  ruleData: PropTypes.object.isRequired,
  validation: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired,
  updateRuleData: PropTypes.func.isRequired,
  invalidateLogic: PropTypes.func.isRequired
};

export default React.memo(RuleBuilder);
