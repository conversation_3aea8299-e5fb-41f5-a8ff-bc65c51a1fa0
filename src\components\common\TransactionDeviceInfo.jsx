import React from 'react';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getDeviceInfoList = (details) => {
  let deviceInfoList = [];

  addItemToList(
    details?.deviceInfo?.deviceId?.value,
    'Device ID',
    details?.deviceInfo?.deviceId?.value,
    deviceInfoList,
    details?.deviceInfo?.deviceId
  );

  addItemToList(
    details?.deviceInfo?.deviceOs?.value,
    'Device OS',
    details?.deviceInfo?.deviceOs?.value,
    deviceInfoList,
    details?.deviceInfo?.deviceOs
  );

  addItemToList(
    details?.deviceInfo?.customerIp?.value,
    'Device IP',
    details?.deviceInfo?.customerIp?.value,
    deviceInfoList,
    details?.deviceInfo?.customerIp
  );

  addItemToList(
    details?.deviceInfo?.terminalId?.value,
    'Terminal ID',
    details?.deviceInfo?.terminalId?.value,
    deviceInfoList,
    details?.deviceInfo?.terminalId
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.simId,
    'SIM ID',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.simId,
    deviceInfoList
  );

  addItemToList(
    details?.locationCoordinates?.latitude?.value,
    'Latitude',
    details?.locationCoordinates?.latitude?.value,
    deviceInfoList,
    details?.locationCoordinates?.latitude
  );

  addItemToList(
    details?.locationCoordinates?.longitude?.value,
    'Longitude',
    details?.locationCoordinates?.longitude?.value,
    deviceInfoList,
    details?.locationCoordinates?.longitude
  );

  addItemToList(
    details?.location?.locationName,
    'Location',
    details?.location?.locationName,
    deviceInfoList
  );

  return deviceInfoList;
};

function TransactionDeviceInfo({ details }) {
  const deviceInfoList = getDeviceInfoList(details);

  if (deviceInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Device Details</b>
      {renderDataColumnList(deviceInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionDeviceInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionDeviceInfo;
