import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { includes, intersection, map } from 'lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSun,
  faMoon,
  faUser,
  faPowerOff,
  faKey,
  faGear
} from '@fortawesome/free-solid-svg-icons';
import {
  Navbar,
  NavbarBrand,
  NavbarToggler,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Nav,
  NavItem,
  NavLink,
  Collapse,
  UncontrolledDropdown,
  Button
} from 'reactstrap';
import { useLocation, useHistory, Link } from 'react-router-dom';

import NotificationCounterContainer from 'containers/notifications/NotificationCounterContainer';
import ResetPasswordModal from 'components/auth/ResetPasswordModal';
import ModalContainer from 'components/common/ModalContainer';
import bankiq_logo_white from 'images/BankIQ_FRC_White.png';
import bankiq_logo_inverse from 'images/BankIQ_FRC_Gradient.png';
import { MODULES_LIST } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

const Header = ({
  theme,
  authDetails,
  hasHoldAndRelease,
  hasKnowageReports,
  hasLocalAuthentication,
  displayResetPasswordModal,
  toggleResetPasswordModal,
  toggleTheme,
  actions
}) => {
  const { userCreds, moduleType, loginType } = authDetails;
  const { roles, userName, channels } = userCreds;
  const location = useLocation();
  const history = useHistory();

  const [isOpen, setIsOpen] = useState(false);
  const [showConfirmAlert, setShowConfirmAlert] = useState(false);

  useEffect(() => {
    isCooperative &&
      userCreds.roles !== 'super-admin' &&
      userCreds.isFirstLogin &&
      toggleResetPasswordModal();
  }, [isCooperative, userCreds]);

  const onLogoutClick = (permLogout = false) => {
    actions.onLogout({ userName, effectiveRole: roles, channel: channels[0], permLogout });
  };

  let currentPage = location.pathname.split('/');

  const menuList = MODULES_LIST.filter((d) => {
    if (!includes(d.role, roles)) return false;
    if (
      roles !== 'super-admin' &&
      roles !== 'admin' &&
      intersection(d.channel, channels).length === 0
    )
      return false;
    if (!includes(d.moduleType, moduleType)) return false;
    if (hasHoldAndRelease === 0 && d.route === '/release-funds') return false;
    if (hasKnowageReports === 0 && d.route === '/reports') return false;
    if (isCooperative && !includes(d.loginType, loginType.toLowerCase())) return false;
    if (d.route === '/settings') return false;
    return true;
  }).map((d, i) => {
    let active = d.route === '/' + currentPage[1];
    let navLinkProps = { active };
    return (
      <NavItem key={i}>
        {d.children ? (
          <UncontrolledDropdown nav inNavbar {...navLinkProps}>
            <DropdownToggle nav caret>
              {d.name}
            </DropdownToggle>
            <DropdownMenu>
              {map(d.children, (item) => (
                <DropdownItem>
                  <NavLink
                    className="p-1"
                    onContextMenu={() => window.open(item.route)}
                    onClick={() => history.push(item.route)}>
                    {item.name}
                  </NavLink>
                </DropdownItem>
              ))}
            </DropdownMenu>
          </UncontrolledDropdown>
        ) : (
          <NavLink
            onContextMenu={() => window.open(d.route)}
            onClick={() => history.push(d.route)}
            {...navLinkProps}>
            {d.name}
          </NavLink>
        )}
      </NavItem>
    );
  });

  const notification =
    isCooperative && roles === 'investigator' ? (
      <NavItem className="ms-3 me-1">
        <NavLink
          onContextMenu={() => window.open('/notification')}
          onClick={() => history.push('/notification')}>
          <NotificationCounterContainer />
        </NavLink>
      </NavItem>
    ) : null;

  let mode =
    theme == 'light' ? (
      <span>
        <FontAwesomeIcon icon={faMoon} /> Dark Mode
      </span>
    ) : (
      <span>
        <FontAwesomeIcon icon={faSun} /> Light Mode
      </span>
    );

  let logo = theme == 'light' ? bankiq_logo_inverse : bankiq_logo_white;

  return (
    <>
      <div className={'header-container'}>
        <Navbar expand="md" className="d-flex header-navbar">
          <h1 className="mb-0">
            <NavbarBrand className={'d-flex align-items-center header-logo '}>
              <img alt="BankIQ IFRM" src={logo} />
            </NavbarBrand>
          </h1>
          <NavbarToggler className={isOpen ? 'active ' : ''} onClick={() => setIsOpen(!isOpen)} />
          <Collapse isOpen={isOpen} navbar>
            <Nav className={'ms-auto align-items-end'} navbar>
              {menuList.length > 1 ? menuList : null}
              {notification}
              <UncontrolledDropdown nav inNavbar className="session-dropdown">
                <DropdownToggle nav caret>
                  <FontAwesomeIcon icon={faUser} />
                </DropdownToggle>
                <DropdownMenu end className="session-dropdown-menu">
                  <DropdownItem>{userName}</DropdownItem>
                  <DropdownItem onClick={toggleTheme}>{mode}</DropdownItem>
                  {(hasLocalAuthentication === 1 || isCooperative) && (
                    <DropdownItem onClick={() => toggleResetPasswordModal()}>
                      <FontAwesomeIcon icon={faKey} /> Change Password
                    </DropdownItem>
                  )}
                  {(roles === 'admin' || roles === 'supervisor') && (
                    <DropdownItem>
                      <Link
                        className="btn btn-lg settings-btn"
                        to={{
                          pathname: '/settings'
                        }}>
                        <FontAwesomeIcon icon={faGear} /> Settings
                      </Link>
                    </DropdownItem>
                  )}
                  <DropdownItem
                    onClick={() =>
                      isCooperative ? onLogoutClick(true) : setShowConfirmAlert(!showConfirmAlert)
                    }>
                    <FontAwesomeIcon icon={faPowerOff} /> Sign Out
                  </DropdownItem>
                </DropdownMenu>
              </UncontrolledDropdown>
            </Nav>
          </Collapse>
        </Navbar>
      </div>
      <ModalContainer
        size="md"
        theme={theme}
        isOpen={showConfirmAlert}
        toggle={() => setShowConfirmAlert(!showConfirmAlert)}
        header={'Confirm Logout'}>
        <h3>Logging out for the day?</h3>
        {(roles === 'maker' || roles === 'checker') && (
          <em>Cases will not be assigned on permanent logout.</em>
        )}
        <div className="d-flex justify-content-end mt-4">
          <Button
            outline
            size="sm"
            className="me-2"
            color="primary"
            onClick={() => onLogoutClick(false)}>
            No
          </Button>
          <Button color="danger" size="sm" onClick={() => onLogoutClick(true)}>
            Yes
          </Button>
        </div>
      </ModalContainer>

      {(hasLocalAuthentication === 1 || isCooperative) && (
        <ResetPasswordModal
          theme={theme}
          userCreds={userCreds}
          showChangePassword={displayResetPasswordModal}
          toggleShowChangePassword={toggleResetPasswordModal}
          updatePassword={actions.onUpdatePassword}
        />
      )}
    </>
  );
};

Header.propTypes = {
  theme: PropTypes.string.isRequired,
  actions: PropTypes.object.isRequired,
  authDetails: PropTypes.object.isRequired,
  hasHoldAndRelease: PropTypes.number.isRequired,
  hasKnowageReports: PropTypes.number.isRequired,
  hasLocalAuthentication: PropTypes.number.isRequired,
  displayResetPasswordModal: PropTypes.bool.isRequired,
  toggleResetPasswordModal: PropTypes.func.isRequired,
  toggleTheme: PropTypes.func.isRequired
};

export default Header;
