import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchPastInvestigations, onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import * as toggleActions from 'actions/toggleActions';
import PastInvestigationCard from 'components/common/PastInvestigationCard';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    pastInvestigations: state.caseAssignment.pastInvestigations,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets,
    ruleNames: state.ruleConfigurator.ruleNames,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchPastInvestigations: bindActionCreators(onFetchPastInvestigations, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch),
    fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch),
    toggleActions: bindActionCreators(toggleActions, dispatch)
  };
};

const PastInvestigationCardContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)((props) => {
  if (props?.contextKey === 'entityProfilling') {
    return <PastInvestigationCard {...props} />;
  } else {
    return (
      <DateRangeProvider contextKey={props?.contextKey}>
        <PastInvestigationCard {...props} />
      </DateRangeProvider>
    );
  }
});

export default PastInvestigationCardContainer;
