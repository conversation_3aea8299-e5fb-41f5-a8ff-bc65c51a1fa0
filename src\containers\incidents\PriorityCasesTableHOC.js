import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as toggleActions from 'actions/toggleActions';
import { onFetchCloseCaseBuckets, onFetchFraudTypesList } from 'actions/caseReviewActions';
import { onFetchPriorityCases } from 'actions/incidentActions';
import * as caseActions from 'actions/caseAssignmentActions';
import { onFetchStages } from 'actions/userManagementActions';
import BucketCasesTable from 'components/common/BucketCasesTable';

const mapStateToProps = (state) => {
  return {
    loginType: state.auth.loginType,
    data: state.incidents.priorityList,
    fraudTypes: state.caseAssignment.fraudTypes,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets,
    stages: state.user.stages,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    caseActions: bindActionCreators(caseActions, dispatch),
    fetchStages: bindActionCreators(onFetchStages, dispatch),
    toggleActions: bindActionCreators(toggleActions, dispatch),
    fetchCases: bindActionCreators(onFetchPriorityCases, dispatch),
    fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
  };
};

const BucketCasesTableContainer = connect(mapStateToProps, mapDispatchToProps)(BucketCasesTable);

export default BucketCasesTableContainer;
