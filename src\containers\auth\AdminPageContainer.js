import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as userActions from 'actions/userManagementActions';
import { onToggleAddUserModal, onToggleUpdateUserRolesModal } from 'actions/toggleActions';
import AdminPage from 'components/auth/AdminPage';

const mapStateToProps = (state) => {
  return {
    channelslist: state.user.channels,
    userslist: state.user.userslist,
    unapprovedUserslist: state.user.unapprovedUserslist,
    userRoles: state.auth.userCreds.roles,
    theme: state.toggle.theme,
    toggleAddUserModal: state.toggle.addUserModal,
    toggleUpdateUserModal: state.toggle.updateUserRolesModal,
    partnerIdList: state.user.partnerIdList,
    allRoles: state.auth.allRoles,
    authDetails: state.auth,
    adminlist: state.user.adminlist,
    peerAdmin: state.user.configurations.peerAdmin,
    fnrUserCreation: state.user.configurations.fnrUserCreation
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    userActions: bindActionCreators(userActions, dispatch),
    toggleActions: bindActionCreators(onToggleAddUserModal, dispatch),
    toggleUpdateSupervisor: bindActionCreators(onToggleUpdateUserRolesModal, dispatch)
  };
};

const AdminPageContainer = connect(mapStateToProps, mapDispatchToProps)(AdminPage);

export default AdminPageContainer;
