import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import GraphContainer from 'components/common/GraphContainer';

function TransactionActionTrend({ theme, xchannelId, period, actionShare, fetchActionShare }) {
  const actionNames = Object.keys(actionShare?.data);

  useEffect(() => {
    fetchActionShare({ ...period, xchannelId });
  }, [period.startDate, xchannelId]);

  const chartData = actionNames?.map((d) => ({
    name: d,
    value: actionShare?.data?.[d]
  }));

  const config = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 30,
      bottom: 20,
      data: actionNames
    },
    series: [
      {
        name: 'Actions',
        type: 'pie',
        radius: [20, 110],
        center: ['25%', '50%'],
        itemStyle: {
          borderRadius: 5
        },
        roseType: 'radius',
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: chartData
      }
    ]
  };
  return (
    <GraphContainer
      theme={theme}
      config={config}
      title="Transaction share by actions"
      noData={actionNames.length === 0}
      loader={actionShare?.loader}
      error={{ flag: actionShare?.error, errorMessage: actionShare?.errorMessage }}
    />
  );
}

TransactionActionTrend.propTypes = {
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  xchannelId: PropTypes.string.isRequired,
  actionShare: PropTypes.object.isRequired,
  fetchActionShare: PropTypes.func.isRequired
};

export default TransactionActionTrend;
