import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as caseActions from 'actions/caseAssignmentActions';
import { onFetchUsersList, onFetchStages } from 'actions/userManagementActions';
import UserListDropdown from 'components/common/UserListDropdown';

const mapStateToProps = (state) => ({
  userslist: state.user.userslist,
  role: state.auth.userCreds.roles,
  userName: state.auth.userCreds.userName,
  stages: state.user.stages
});

const mapDispatchToProps = (dispatch) => ({
  caseActions: bindActionCreators(caseActions, dispatch),
  fetchUsersList: bindActionCreators(onFetchUsersList, dispatch),
  fetchStages: bindActionCreators(onFetchStages, dispatch)
});

const UserListDropdownContainer = connect(mapStateToProps, mapDispatchToProps)(UserListDropdown);

export default UserListDropdownContainer;
