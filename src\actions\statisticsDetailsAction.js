import {
  ON_<PERSON>ETCH_TRANSACTION_STATISTICS_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS,
  ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE,
  ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS,
  ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchTransactionStatisticsDetails(selected, channel) {
  return client({ url: `uds/${channel}/transaction/statistics/entityId/${selected.id}` });
}

function onFetchTransactionStatisticsDetailsLoading() {
  return { type: ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING };
}

function onSuccessfulFetchTransactionStatisticsDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS,
    response
  };
}

function onFailureFetchTransactionStatisticsDetails(response) {
  return {
    type: ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE,
    response
  };
}

function onFetchTransactionStatisticsDetails(selected, channel) {
  return function (dispatch) {
    dispatch(onFetchTransactionStatisticsDetailsLoading());
    return fetchTransactionStatisticsDetails(selected, channel).then(
      (success) => dispatch(onSuccessfulFetchTransactionStatisticsDetails(success)),
      (error) => dispatch(onFailureFetchTransactionStatisticsDetails(error))
    );
  };
}

function fetchCustomerStatisticsDetails(selected, channel) {
  return client({
    url: `uds/${channel}/customer/statistics/entityId/${selected.id}`
  });
}

function onFetchCustomerStatisticsDetailsLoading() {
  return { type: ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING };
}

function onSuccessfulFetchCustomerStatisticsDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS,
    response
  };
}

function onFailureFetchCustomerStatisticsDetails(response) {
  return {
    type: ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE,
    response
  };
}

function onFetchCustomerStatisticsDetails(selected, channel) {
  return function (dispatch) {
    dispatch(onFetchCustomerStatisticsDetailsLoading());
    return fetchCustomerStatisticsDetails(selected, channel).then(
      (success) => dispatch(onSuccessfulFetchCustomerStatisticsDetails(success)),
      (error) => dispatch(onFailureFetchCustomerStatisticsDetails(error))
    );
  };
}

export { onFetchTransactionStatisticsDetails, onFetchCustomerStatisticsDetails };
