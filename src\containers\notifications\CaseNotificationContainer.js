import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onAcknowledgeNotification, onResolveNotification } from 'actions/notificationActions';
import CaseNotification from 'components/notifications/CaseNotification';

const mapStateToProps = (state) => ({
  userName: state.auth.userCreds.userName,
  notifications: state.notifications.list
});

const mapDispatchToProps = (dispatch) => ({
  resolveNotification: bindActionCreators(onResolveNotification, dispatch),
  acknowledgeNotification: bindActionCreators(onAcknowledgeNotification, dispatch)
});

const CaseNotificationContainer = connect(mapStateToProps, mapDispatchToProps)(CaseNotification);

export default CaseNotificationContainer;
