import {
  ON_<PERSON>ETCH_TRANSACTION_STATISTICS_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS,
  ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE,
  ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS,
  ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function demographicDetailsReducer(state = initialState.statisticsDetails, action) {
  switch (action.type) {
    case ON_FETCH_TRANSACTION_STATISTICS_DETAILS_LOADING:
      return objectAssign({}, state, {
        transactionStatistics: {
          details: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_TRANSACTION_STATISTICS_DETAILS:
      return objectAssign({}, state, {
        transactionStatistics: {
          details: action.response.data,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_TRANSACTION_STATISTICS_DETAILS_FAILURE:
      return objectAssign({}, state, {
        transactionStatistics: {
          details: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_CUSTOMER_STATISTICS_DETAILS_LOADING:
      return objectAssign({}, state, {
        customerStatistics: {
          details: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_CUSTOMER_STATISTICS_DETAILS:
      return objectAssign({}, state, {
        customerStatistics: {
          details: action.response.data,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CUSTOMER_STATISTICS_DETAILS_FAILURE:
      return objectAssign({}, state, {
        customerStatistics: {
          details: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    default:
      return state;
  }
}
