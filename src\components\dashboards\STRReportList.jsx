import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Card, ButtonGroup, Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { useHistory } from 'react-router-dom';

import TableFilterForm from 'components/common/TableFilterForm';
import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';
import { getScreen } from 'constants/functions';

function STRReportList({ userRole, data, fetchCases, hasProvisionalFields }) {
  const history = useHistory();
  useEffect(() => {
    fetchCases({ filterCondition: [] });
  }, []);

  const memoizedForm = useMemo(
    () => (
      <TableFilterForm
        sortable={false}
        channelFilter={false}
        currentConf={data?.conf}
        fetchCases={fetchCases}
        channel="str"
        hasProvisionalFields={hasProvisionalFields}
      />
    ),
    []
  );

  const columns = [
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      fixed: true,
      minWidth: 40,
      style: { overflow: 'visible' },
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <ButtonGroup>
          <Button
            outline
            size="sm"
            title="view"
            color="primary"
            onClick={() => history.push(`${getScreen(userRole)}/str/${row.original.txnId}`)}
            onContextMenu={() => window.open(`${getScreen(userRole)}/str/${row.original.txnId}`)}>
            <FontAwesomeIcon icon={faChevronRight} />
          </Button>
        </ButtonGroup>
      )
    },
    { Header: 'Transaction Id', accessor: 'txnId' },
    { Header: 'Transaction Timestamp', accessor: 'txnTimestamp' },
    { Header: 'Entity Id', accessor: 'customerId' },
    { Header: 'Entity Name', accessor: 'customerName' },
    {
      Header: 'Report Version',
      accessor: 'fileVersion',
      Cell: ({ value }) => 'Version ' + value
    },
    { Header: 'Downloaded By', accessor: 'userName' },
    { Header: 'Downloaded On', accessor: 'creationDate' }
  ];

  return (
    <CardContainer title="STR Report List">
      {memoizedForm}
      <Card className="mt-3">
        {data.loader ? (
          <TableLoader />
        ) : data.error ? (
          <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>
        ) : (
          <ReactTable
            filterable
            columns={columns}
            data={data?.list}
            noDataText="No STR Reports found"
            showPaginationTop={true}
            showPaginationBottom={false}
            defaultPageSize={5}
            minRows={3}
            showPageJump={false}
            pageSizeOptions={[5, 10, 20, 30, 40, 50]}
            className={'-highlight -striped'}
          />
        )}
      </Card>
    </CardContainer>
  );
}

STRReportList.propTypes = {
  userRole: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  fetchCases: PropTypes.func.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired
};

export default STRReportList;
