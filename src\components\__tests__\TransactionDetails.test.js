import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import TransactionDetails from '../caseReview/TransactionDetails';
import Moment from 'moment';
import _ from 'lodash';
import initialState from 'reducers/initialState';

jest.mock('containers/common/AddToListButtonContainer', () =>
  jest.fn(({ categoryName }) => (
    <div>
      <p>{categoryName}</p>
    </div>
  ))
);

const initialStateStore = {
  ...initialState,
  toggle: {
    theme: 'light'
  },
  uds: {
    agent: ''
  }
};

const mockFetchEntityDetails = jest.fn();
const mockFetchViolationDetails = jest.fn();
const mockFetchTransactionDetails = jest.fn();
const mockOneViewCase = {
  txnId: 'txn123',
  entityCategory: 'Customer',
  entityId: 'entity123',
  caseRefNo: 'case456',
  assignedTo: 'testUser',
  investigationStatus: '',
  assignmentTimeStamp: Moment().subtract(10, 'minutes').toISOString(),
  closeTimestamp: Moment().subtract(5, 'minutes').toISOString(),
  childTxns: ['txn789'],
  bucketName: 'Fraud',
  remark: 'Suspicious transaction',
  channel: 'Web',
  ifrmVerdict: 'N/A',
  ifrmPostauthVerdictName: 'Accepted'
};
const mockTransactionDetails = {
  details: {
    transactionInfo: {
      txnId: 'txn123',
      txnCategoryName: 'Online Purchase',
      txnAmount: 1000,
      txnCurrency: '356',
      txnTimestamp: Moment().toISOString()
    },
    masterFields: {
      txnTypeName: 'Credit',
      channelName: 'Internet Banking',
      paymentMethodName: 'UPI',
      responseCodeName: 'Success',
      payeeMccCodeName: 'code'
    },
    reViolatedRules: ['Rule1', 'Rule2'],
    entityId: { value: 'entity123' },
    payerAccount: { payerVpa: { value: 'payer@upi' } },
    payeeAccount: {
      payeeId: { value: 'payee@upi' },
      payeeType: 'Merchant',
      payeeMcc: { value: '1234' },
      payeeVpa: { value: 'payee@upi' }
    },
    entityCategory: 'Customer'
  }
};
const mockCallDetails = {
  status: 'PLACED'
};
const mockMerchantInfo = {
  details: {
    personalDetails: { merchantName: 'MerchantName' },
    mccData: {
      mcc: { value: '123' },
      mccName: 'Some MCC Name'
    },
    txnCounterResponse: { partnerId: 'partner123' }
  }
};
const mockCustomerInfo = {
  details: {
    firstName: 'John',
    middleName: 'Aj',
    lastName: 'Doe',
    mccData: {
      mcc: { value: '123' },
      mccName: 'Some MCC Name'
    },
    txnCounterResponse: { partnerId: 'partner123' }
  }
};
const mockViolationDetails = {
  list: {
    rules: [
      { name: 'Rule1', description: 'Rule description 1', citationNames: 'Citation1~Citation2' }
    ]
  }
};
const mockAgentInfo = {
  details: {
    personalDetails: {
      merchantName: 'Agent Name'
    }
  }
};

const middlewares = [thunk];
const mockStore = configureStore(middlewares);
let store = mockStore(initialStateStore);

const renderWithProvider = (ui, { store }) => {
  return render(<Provider store={store}>{ui}</Provider>);
};

describe('TransactionDetails Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={mockOneViewCase}
        callDetails={mockCallDetails}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );
  });

  it('should render transaction details correctly', () => {
    const transactionDetailElements = screen.getAllByText(/Transaction Detail/i);
    expect(transactionDetailElements.length).toBeGreaterThan(0);
    expect(transactionDetailElements[0]).toBeInTheDocument();
    expect(screen.getByText(/\[txn123\]/i)).toBeInTheDocument();

    expect(screen.getByText(/Accepted/i)).toBeInTheDocument();
    expect(screen.getByText(/Assigned at/i)).toBeInTheDocument();

    expect(screen.getByText(/Closed at/i)).toBeInTheDocument();

    const timeOnCaseElements = screen.getAllByText(/Time on case/i);
    expect(timeOnCaseElements[0]).toBeInTheDocument();

    expect(screen.getByText(/Category/i)).toBeInTheDocument();
    expect(screen.getByText(/Channel/i)).toBeInTheDocument();
    expect(screen.getByText(/Method/i)).toBeInTheDocument();

    expect(screen.getByText(/Amount/i)).toBeInTheDocument();
    expect(screen.getByText(/Timestamp/i)).toBeInTheDocument();
    expect(screen.getByText(/Response/i)).toBeInTheDocument();

    expect(screen.getByText(/Entity ID/i)).toBeInTheDocument();
    const entity = screen.getAllByText(/Entity/i);
    expect(entity[0]).toBeInTheDocument();
    const name = screen.getAllByText(/Name/i);
    expect(name[0]).toBeInTheDocument();
    const vpa = screen.getAllByText(/VPA/i);
    expect(vpa[0]).toBeInTheDocument();

    expect(screen.getByText(/Payee ID/i)).toBeInTheDocument();
    expect(screen.getByText(/Payee Type/i)).toBeInTheDocument();
    expect(screen.getByText(/Payee MCC/i)).toBeInTheDocument();
  });

  it('should render assignedTo correctly', () => {
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={{ ...mockOneViewCase, assignedTo: 'spmeUser' }}
        callDetails={mockCallDetails}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );
    expect(screen.getByText(/Assigned to/i)).toBeInTheDocument();
  });

  it('should call fetch functions on component mount', () => {
    expect(mockFetchTransactionDetails).toHaveBeenCalledWith(
      mockOneViewCase.txnId,
      'frm',
      mockOneViewCase.data
    );
    expect(mockFetchEntityDetails).toHaveBeenCalledWith(
      _.lowerCase(mockOneViewCase.entityCategory),
      mockOneViewCase.entityId
    );
  });

  it('should fetch violation details when txnDetails change', () => {
    expect(mockFetchViolationDetails).toHaveBeenCalledWith(mockOneViewCase.txnId, 'frm', {
      txnTimestamp: mockTransactionDetails.details.transactionInfo.txnTimestamp,
      code: mockTransactionDetails.details.reViolatedRules
    });
  });

  it('should display violation rules correctly', () => {
    expect(screen.getByText(/Rule1/i)).toBeInTheDocument();
    expect(screen.getByText(/Rule description 1/i)).toBeInTheDocument();
    expect(screen.getByText(/Citation1/i)).toBeInTheDocument();
    expect(screen.getByText(/Citation2/i)).toBeInTheDocument();
  });

  it('should handle different entity categories', () => {
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={{ ...mockOneViewCase, entityCategory: 'Merchant' }}
        callDetails={mockCallDetails}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    expect(screen.getByText(/MerchantName/i)).toBeInTheDocument();

    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={{ ...mockOneViewCase, entityCategory: 'Agent' }}
        callDetails={mockCallDetails}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    expect(screen.getByText(/Agent Name/i)).toBeInTheDocument();
  });

  it('should update time spent on case every minute', async () => {
    jest.useFakeTimers();
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={mockOneViewCase}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    const timeOnCaseElements = screen.getAllByText(/Time on case/i);
    const initialTimeSpent = timeOnCaseElements[0].closest('span').querySelector('span')
      .textContent;

    jest.advanceTimersByTime(60000);

    expect(initialTimeSpent).toBe('5 mins');
    jest.useRealTimers();
  });

  it('renders Verdict badge with correct color based on frmVerdict value, Accepted', () => {
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={{ ...mockOneViewCase, ifrmVerdict: '' }}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    const verdictBadge = screen.getByText('Accepted');
    expect(verdictBadge).toBeInTheDocument();
  });

  it('renders Verdict badge with correct color based on frmVerdict value, Accepted', () => {
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={{ ...mockOneViewCase, ifrmVerdict: 'Rejected' }}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    const verdictBadge = screen.getByText('Rejected');
    expect(verdictBadge).toBeInTheDocument();
  });

  it('should render amount in INR format when currency is INR', () => {
    const mockTransactionDetails = {
      details: {
        transactionInfo: {
          txnAmount: 1000,
          txnCurrency: '356'
        }
      }
    };

    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={mockOneViewCase}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    const amountTxt = screen.getAllByText(/Amount/i);
    expect(amountTxt[0]).toBeInTheDocument();

    const amountElements = screen.getAllByText('1000 (INR)');
    expect(amountElements.length).toBeGreaterThan(0);
  });

  it('should render amount in currency format when currency is not INR', () => {
    const mockTransactionDetails = {
      details: {
        transactionInfo: {
          txnAmount: 1000,
          txnCurrency: '840'
        }
      }
    };

    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={mockOneViewCase}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );

    const amountElements = screen.getAllByText('1000 (840)');
    expect(amountElements.length).toBeGreaterThan(0);
  });

  test('should render MCC data column when mccData exists', () => {
    renderWithProvider(
      <TransactionDetails
        userName="testUser"
        agentInfo={mockAgentInfo}
        oneViewCase={mockOneViewCase}
        callDetails={{ ...mockCallDetails, status: 'PLACED' }}
        customerInfo={mockCustomerInfo}
        merchantInfo={mockMerchantInfo}
        violationDetails={mockViolationDetails}
        transactionDetails={mockTransactionDetails}
        fetchEntityDetails={mockFetchEntityDetails}
        fetchViolationDetails={mockFetchViolationDetails}
        fetchTransactionDetails={mockFetchTransactionDetails}
      />,
      { store }
    );
    const mcc = screen.getAllByText(/MCC/i);
    expect(mcc[0]).toBeInTheDocument();

    const mccName = screen.getAllByText(/Some MCC Name/i);
    expect(mccName[0]).toBeInTheDocument();
  });
});
