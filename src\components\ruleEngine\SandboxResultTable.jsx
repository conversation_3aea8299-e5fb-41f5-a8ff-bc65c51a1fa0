import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';

import TransactionTableContainer from 'containers/common/TransactionTableContainer';

const SandboxResultTable = ({ data, fetchViolationDetails }) => {
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(5);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(() => {
    data.date &&
      fetchViolationDetails({ date: data.date, pageNo: pageNo + 1, pageSize: pageRecords });
  }, [pageNo, pageRecords]);

  const tablePageCountProp = isEmpty(tableFilters)
    ? {
        pages: data.count / pageRecords > 1 ? Math.ceil(data.count / pageRecords) : 1
      }
    : {};

  return (
    <TransactionTableContainer
      minRows={3}
      page={pageNo}
      pageSize={pageRecords}
      filtered={tableFilters}
      displayRedirect={false}
      displayActions={false}
      displayCaseHeaders={false}
      data={data}
      onPageChange={(page) => setPageNo(page)}
      onPageSizeChange={(pageSize, page) => {
        setPageNo(page);
        setPageRecords(pageSize);
      }}
      onFilteredChange={(filtered) => setTableFilters(filtered)}
      {...tablePageCountProp}
    />
  );
};

SandboxResultTable.propTypes = {
  data: PropTypes.object.isRequired,
  fetchViolationDetails: PropTypes.func.isRequired
};

export default SandboxResultTable;
