/* eslint-disable react/prop-types */
import { faCheck, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import ReactTable from 'react-table';
import { Button } from 'reactstrap';

import ConfirmAlert from 'components/common/ConfirmAlert';
import { USER_LIST_HEADER } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

const ActiveUsersTable = ({
  userRoles,
  channelslist,
  userActions,
  userslist,
  partnerIdList,
  adminlist,
  theme,
  peerAdmin
}) => {
  const [tableFilters, setTableFilters] = useState([]);
  const [selectedUser, setSelectedUser] = useState({});
  const [isDeleteMode, setIsDeleteMode] = useState(false);

  useEffect(() => {
    if (_.isEmpty(channelslist)) userActions.onFetchChannels();
  }, [channelslist, userActions]);

  useEffect(() => {
    if (_.isEmpty(partnerIdList) && userRoles === 'super-admin') userActions.onFetchPartnerIdList();
    if (_.isEmpty(adminlist) && userRoles === 'admin') userActions.onFetchAdminList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filteredUserslist =
    userRoles === 'admin'
      ? userslist.filter((user) => !_.includes(user.channelRoles, 'admin'))
      : userslist;

  const moreThanTwoAdmins = (currentUser) => {
    const adminList = _.filter(
      filteredUserslist,
      (user) => user?.partnerId === currentUser.partnerId && _.includes(user?.channelRoles, 'admin')
    );

    return !(peerAdmin === 1 && adminList.length < 3);
  };

  const table_header = [
    {
      Header: 'Action',
      searchable: false,
      sortable: false,
      filterable: false,
      maxWidth: 100,
      Cell: (row) =>
        (
          _.includes(row.original.channelRoles, `admin`)
            ? userRoles === 'super-admin' && moreThanTwoAdmins(row.original)
            : userRoles === 'admin'
        ) ? (
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete User"
            onClick={() => {
              setSelectedUser(row.original);
              setIsDeleteMode(true);
            }}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        ) : null
    },
    ...USER_LIST_HEADER
  ];

  userRoles === 'admin'
    ? table_header.push({
        Header: 'Supervisor',
        accessor: 'channelRoles',
        searchable: false,
        filterable: false,
        sortable: false,
        Cell: ({ value }) => {
          const filteredchannelList = value
            .filter((channelRole) => _.includes(channelRole, 'supervisor'))
            .map((channelRole) => _.words(channelRole)[0]);
          const channels = _.uniq(filteredchannelList);
          if (channels.length > 0) {
            if (channelslist.length === 1)
              return <FontAwesomeIcon icon={faCheck} className="channel-check" />;
            return <span>{_.join(channels, ', ')}</span>;
          }
          return null;
        }
      })
    : table_header.push(
        {
          Header: 'Roles',
          accessor: 'channelRoles',
          Cell: ({ value }) => {
            const roles = value.map((channelRole) =>
              channelRole === 'admin' ? 'admin' : _.words(channelRole)[1]
            );
            return <span>{_.join(_.uniq(roles), ', ')}</span>;
          }
        },
        ...(isCooperative
          ? [
              {
                Header: 'Partner Name',
                accessor: 'partnerId',
                Cell: ({ value }) => {
                  const partner = partnerIdList.find((d) => +d.id === +value);
                  return !_.isEmpty(partner) ? <span>{partner.partnerName}</span> : null;
                },
                filterMethod: (filter, row) => +row[filter.id] === +filter.value,
                Filter: ({ onChange }) => (
                  <select
                    onChange={(event) => onChange(event.target.value)}
                    value={
                      !_.isEmpty(_.find(tableFilters, ['id', 'partnerId']))
                        ? _.find(tableFilters, ['id', 'partnerId']).value
                        : ''
                    }>
                    <option value="">All</option>
                    {partnerIdOptions}
                  </select>
                )
              }
            ]
          : [])
      );

  const partnerIdOptions = partnerIdList.map((partner) => (
    <option key={partner.id} value={partner.id}>
      {partner.partnerName}
    </option>
  ));

  function cancelDelete() {
    setIsDeleteMode(false);
    setSelectedUser({});
  }

  function confirmDelete() {
    userActions.onDeleteUser(selectedUser);
    setSelectedUser({});
    setIsDeleteMode(false);
  }

  return (
    <>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        data={filteredUserslist}
        columns={table_header}
        noDataText="No users found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        defaultPageSize={10}
        minRows={6}
        filtered={tableFilters}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        className="-highlight -striped"
      />

      <ConfirmAlert
        theme={theme}
        confirmAlertModal={isDeleteMode}
        toggleConfirmAlertModal={() => cancelDelete()}
        confirmationAction={() => confirmDelete()}
        confirmAlertTitle={`Are you sure you want to delete ${selectedUser.userName} user ?`}
      />
    </>
  );
};

ActiveUsersTable.propTypes = {
  theme: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  userRoles: PropTypes.string.isRequired,
  userActions: PropTypes.object.isRequired,
  partnerIdList: PropTypes.array.isRequired,
  adminlist: PropTypes.array.isRequired,
  peerAdmin: PropTypes.number.isRequired
};

export default ActiveUsersTable;
