/* eslint-disable react/prop-types */
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTrash } from '@fortawesome/free-solid-svg-icons';

import { USER_LIST_HEADER } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';
import ConfirmAlert from 'components/common/ConfirmAlert';

const ActiveUsersTable = ({
  userRoles,
  channelslist,
  userActions,
  userslist,
  partnerIdList,
  adminlist,
  theme,
  peerAdmin
}) => {
  const [tableFilters, setTableFilters] = useState([]);
  const [selectedUser, setSelectedUser] = useState({});
  const [isDeleteMode, setIsDeleteMode] = useState(false);

  useEffect(() => {
    if (_.isEmpty(channelslist)) userActions.onFetchChannels();
  }, [channelslist]);

  useEffect(() => {
    if (_.isEmpty(partnerIdList) && userRoles == 'super-admin') userActions.onFetchPartnerIdList();
    if (_.isEmpty(adminlist) && userRoles == 'admin') userActions.onFetchAdminList();
  }, []);

  let filteredUserslist =
    userRoles == 'admin'
      ? userslist.filter((user) => !_.includes(user.channelRoles, 'admin'))
      : userslist;

  const moreThanTwoAdmins = (currentUser) => {
    const adminList = _.filter(
      filteredUserslist,
      (user) => user?.partnerId == currentUser.partnerId && _.includes(user?.channelRoles, 'admin')
    );

    return !(peerAdmin == 1 && adminList.length < 3);
  };

  let table_header = [
    {
      Header: 'Action',
      searchable: false,
      sortable: false,
      filterable: false,
      maxWidth: 100,
      Cell: (row) => {
        return (
          _.includes(row.original.channelRoles, `admin`)
            ? userRoles == 'super-admin' && moreThanTwoAdmins(row.original)
            : userRoles == 'admin'
        ) ? (
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete User"
            onClick={() => {
              setSelectedUser(row.original);
              setIsDeleteMode(true);
            }}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        ) : null;
      }
    },
    ...USER_LIST_HEADER
  ];

  userRoles == 'admin'
    ? table_header.push({
        Header: 'Supervisor',
        accessor: 'channelRoles',
        searchable: false,
        filterable: false,
        sortable: false,
        Cell: ({ value }) => {
          let filteredchannelList = value
            .filter((channelRole) => _.includes(channelRole, 'supervisor'))
            .map((channelRole) => _.words(channelRole)[0]);

          let channels = _.uniq(filteredchannelList);

          return channels.length > 0 ? (
            channelslist.length == 1 ? (
              <FontAwesomeIcon icon={faCheck} className="channel-check" />
            ) : (
              <span>{_.join(channels, ', ')}</span>
            )
          ) : null;
        }
      })
    : table_header.push(
        {
          Header: 'Roles',
          accessor: 'channelRoles',
          Cell: ({ value }) => {
            let roles = value.map((channelRole) => {
              return channelRole == 'admin' ? 'admin' : _.words(channelRole)[1];
            });
            return <span>{_.join(_.uniq(roles), ', ')}</span>;
          }
        },
        ...(!isCooperative
          ? [
              {
                Header: 'Partner Name',
                accessor: 'partnerId',
                Cell: ({ value }) => {
                  const partner = partnerIdList.find((d) => d.id == value);
                  return !_.isEmpty(partner) ? <span>{partner.partnerName}</span> : null;
                },
                filterMethod: (filter, row) => row[filter.id] == filter.value,
                Filter: ({ onChange }) => (
                  <select
                    onChange={(event) => onChange(event.target.value)}
                    value={
                      !_.isEmpty(_.find(tableFilters, ['id', 'partnerId']))
                        ? _.find(tableFilters, ['id', 'partnerId']).value
                        : ''
                    }>
                    <option value="">All</option>
                    {partnerIdOptions}
                  </select>
                )
              }
            ]
          : [])
      );

  const partnerIdOptions = partnerIdList.map((partner) => (
    <option key={partner.id} value={partner.id}>
      {partner.partnerName}
    </option>
  ));

  function cancelDelete() {
    setIsDeleteMode(false);
    setSelectedUser({});
  }

  function confirmDelete() {
    userActions.onDeleteUser(selectedUser);
    setSelectedUser({});
    setIsDeleteMode(false);
  }

  return (
    <>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        data={filteredUserslist}
        columns={table_header}
        noDataText="No users found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        defaultPageSize={10}
        minRows={6}
        filtered={tableFilters}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        className={'-highlight -striped'}
      />

      <ConfirmAlert
        theme={theme}
        confirmAlertModal={isDeleteMode}
        toggleConfirmAlertModal={cancelDelete}
        confirmationAction={confirmDelete}
        confirmAlertTitle={`Are you sure you want to delete ${selectedUser.userName} user ?`}
      />
    </>
  );
};

ActiveUsersTable.propTypes = {
  theme: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  userRoles: PropTypes.string.isRequired,
  userActions: PropTypes.object.isRequired,
  partnerIdList: PropTypes.array.isRequired,
  adminlist: PropTypes.array.isRequired,
  peerAdmin: PropTypes.number.isRequired
};

export default ActiveUsersTable;
