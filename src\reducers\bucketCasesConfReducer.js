import _ from 'lodash';
import objectAssign from 'object-assign';

import { ON_SUCCESSFUL_LOGIN } from 'constants/actionTypes';

import initialState from './initialState';

const getRole = (channelRole) => {
  const universalUsers = ['super-admin', 'admin'];
  if (_.includes(universalUsers, channelRole[0])) return '';
  const arr = _.split(channelRole[0], ':');
  return arr[1];
};

export default function bucketCasesConfReducer(state = initialState.caseAssignment, action) {
  switch (action.type) {
    case ON_SUCCESSFUL_LOGIN:
      return objectAssign({}, state, { role: getRole(action.response.channelRoles) });
    default:
      return state;
  }
}
