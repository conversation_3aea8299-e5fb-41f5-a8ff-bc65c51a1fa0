import { range } from 'lodash';
import React from 'react';
import { Row, Col } from 'reactstrap';

const CustomerInfoLoader = () => (
  <Row className="txn-info">
    <Col md="4" sm="6" xs="12">
      <span className="customer-graph-placeholder" />
    </Col>
    <Col md="8" sm="6" xs="12">
      <Row className="txn-info">
        {range(12).map((d) => (
          <Col md="3" key={d} sm="4" xs="6" className="data-columns p-3">
            <span className="detail-placeholder" />
          </Col>
        ))}
      </Row>
    </Col>
  </Row>
);

export default CustomerInfoLoader;
