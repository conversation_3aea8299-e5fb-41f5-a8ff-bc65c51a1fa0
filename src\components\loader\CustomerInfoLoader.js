import React from 'react';
import { range } from 'lodash';
import { Row, Col } from 'reactstrap';

const CustomerInfoLoader = () => {
  return (
    <Row className="txn-info">
      <Col md="4" sm="6" xs="12">
        <span className="customer-graph-placeholder">{''}</span>
      </Col>
      <Col md="8" sm="6" xs="12">
        <Row className="txn-info">
          {range(12).map((d) => (
            <Col md="3" key={d} sm="4" xs="6" className="data-columns p-3">
              <span className="detail-placeholder">{''}</span>
            </Col>
          ))}
        </Row>
      </Col>
    </Row>
  );
};

export default CustomerInfoLoader;
