import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchTopMerchantCount, onFetchTopMerchantData } from 'actions/rfiReportActions';
import TopMerchantsReport from 'components/rfiReports/TopMerchantsReport';

const mapStateToProps = (state) => ({
  topMerchant: state.rfiReports.topMerchant
});

const mapDispatchToProps = (dispatch) => ({
  fetchTopMerchantCount: bindActionCreators(onFetchTopMerchantCount, dispatch),
  fetchTopMerchantData: bindActionCreators(onFetchTopMerchantData, dispatch)
});

const TopMerchantsReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(TopMerchantsReport);

export default TopMerchantsReportContainer;
