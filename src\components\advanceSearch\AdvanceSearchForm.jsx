import React, { useState, useEffect } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import { MultiSelect } from 'react-multi-select-component';
import { Label, Input, Button, Row, Col, FormGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronUp,
  faChevronDown,
  faSearch,
  faSearchPlus
} from '@fortawesome/free-solid-svg-icons';

import XChannelDropDownContainer from 'containers/common/XChannelDropDownContainer';
import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';

function AdvanceSearchForm({
  pageRecords,
  hasProvisionalFields,
  channels,
  ruleNames,
  searchData,
  formDataAdvanceSearch,
  setPageNo,
  setSearchData,
  showFailureAlert,
  advanceSearchTxn,
  fetchRuleNamesList
}) {
  const [isAdvancedSearch, setIsAdvancedSearch] = useState(false);

  useEffect(() => {
    channels.map((channel) => {
      if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
    });
  }, []);

  const disableSubmit =
    Object.getOwnPropertyNames(formDataAdvanceSearch)?.length <= 1 &&
    Object.getOwnPropertyNames(formDataAdvanceSearch.identifiers)?.length <= 1;

  const violationOptions = _.map(ruleNames.list[channels[0]], (d) => {
    return {
      label: d.name,
      value: d.code
    };
  });

  const handleSubmit = (event) => {
    event.preventDefault();
    if (!disableSubmit) {
      setPageNo(0);
      const formData = {
        filters: formDataAdvanceSearch,
        pageNo: 1,
        pageSize: pageRecords
      };
      advanceSearchTxn(formData);
    } else showFailureAlert({ message: 'Please add atleast 1 search query' });
  };

  const updateSearchData = (key, value, isIdentifier = false) =>
    setSearchData((prev) => {
      return {
        ...prev,
        ...(!isIdentifier
          ? { [key]: value }
          : { identifiers: { ...prev.identifiers, [key]: value } })
      };
    });

  const createFormItem = (label, key, data, isIdentifier = false, type = 'text') => (
    <Col lg={2} md={3} sm={4} xs={6}>
      <FormGroup>
        <Label>{label}</Label>
        <Input
          type={type}
          name={key}
          value={isIdentifier ? data?.identifiers?.[key] : data?.[key]}
          onChange={(e) => updateSearchData(key, e.target.value, isIdentifier)}
        />
      </FormGroup>
    </Col>
  );

  return (
    <form id="searchform" className="advance-search" onSubmit={handleSubmit}>
      <Row className="row-cols-auto g-3 align-items-end ">
        <Col lg={2} md={3} sm={4} xs={6}>
          <XChannelDropDownContainer
            value={searchData?.identifiers?.xchannelId}
            onChange={(value) => updateSearchData('xchannelId', value, true)}
          />
        </Col>
        {createFormItem('Entity ID', 'entityId', searchData, true)}
        {createFormItem('Transaction ID', 'txnId', searchData)}
        {hasProvisionalFields === 1 &&
          createFormItem(
            <ProvisionalFieldsValueContainer attrName="attribute2" />,
            'attribute2',
            searchData,
            true
          )}
        {createFormItem('Transaction Amount', 'txnAmount', searchData)}
        <Col lg={2} md={3} sm={4} xs={6}>
          <FormGroup>
            <Label>Start Date</Label>
            <Datetime
              name="startTimestamp"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={new Date(searchData?.startTimestamp)}
              onChange={(dateObj) => updateSearchData('startTimestamp', dateObj._d)}
            />
          </FormGroup>
        </Col>
        <Col lg={2} md={3} sm={4} xs={6}>
          <FormGroup>
            <Label>End Date</Label>
            <Datetime
              name="endTimestamp"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={new Date(searchData?.endTimestamp)}
              onChange={(dateObj) => updateSearchData('endTimestamp', dateObj._d)}
            />
          </FormGroup>
        </Col>
      </Row>
      {isAdvancedSearch && (
        <Row className="row-cols-auto g-3 align-items-end">
          {createFormItem('Payee ID', 'payeeId', searchData)}
          {createFormItem('Payee Account Number', 'payeeAccountNumber', searchData)}
          {createFormItem('Payee Card Number Mask', 'payeeCardNumberMask', searchData)}
          {createFormItem('Payee Card Number Hash', 'payeeCardNumberHash', searchData)}
          {createFormItem('Payee MCC', 'payeeMcc', searchData)}
          {createFormItem('Payee MMID', 'payeeMmid', searchData)}
          {createFormItem('Payer ID', 'payerId', searchData)}
          {createFormItem('Payer Account Number', 'payerAccountNumber', searchData)}
          {createFormItem('Payer Card Number Mask', 'payerCardNumberMask', searchData)}
          {createFormItem('Payer Card Number Hash', 'payerCardNumberHash', searchData)}
          {createFormItem('Payer MCC', 'payerMcc', searchData)}
          {createFormItem('Payer MMID', 'payerMmid', searchData)}
          {createFormItem('Agent ID', 'agentId', searchData, true)}
          {createFormItem('Transaction Type', 'txnType', searchData)}
          {createFormItem('Source Institution ID', 'sourceInstitutionId', searchData, true)}
          {createFormItem('Terminal ID', 'terminalId', searchData, true)}
          {createFormItem('Response Code', 'responseCode', searchData)}
          <Col lg={2} md={3} sm={4} xs={6}>
            <FormGroup>
              <Label>Violated Rules</Label>
              <MultiSelect
                options={violationOptions}
                labelledBy="select rule"
                name="violatedRules"
                value={searchData?.violatedRules}
                onChange={(value) => updateSearchData('violatedRules', value)}
              />
            </FormGroup>
          </Col>
        </Row>
      )}
      <div className="m-3">
        <Button size="sm" type="submit" color="primary" className="me-3" disabled={disableSubmit}>
          <FontAwesomeIcon icon={faSearch} /> Search
        </Button>
        <Button
          outline
          size="sm"
          color="secondary"
          className="advance-search-button"
          onClick={() => setIsAdvancedSearch(!isAdvancedSearch)}>
          <FontAwesomeIcon icon={faSearchPlus} className="me-2" />
          Advanced Search
          {isAdvancedSearch ? (
            <FontAwesomeIcon icon={faChevronUp} className="ms-2" />
          ) : (
            <FontAwesomeIcon icon={faChevronDown} className="ms-2" />
          )}
        </Button>
      </div>
    </form>
  );
}

AdvanceSearchForm.propTypes = {
  pageRecords: PropTypes.number.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  channels: PropTypes.array.isRequired,
  ruleNames: PropTypes.object.isRequired,
  searchData: PropTypes.object.isRequired,
  formDataAdvanceSearch: PropTypes.object.isRequired,
  setPageNo: PropTypes.func.isRequired,
  setSearchData: PropTypes.func.isRequired,
  showFailureAlert: PropTypes.func.isRequired,
  advanceSearchTxn: PropTypes.func.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired
};

export default AdvanceSearchForm;
