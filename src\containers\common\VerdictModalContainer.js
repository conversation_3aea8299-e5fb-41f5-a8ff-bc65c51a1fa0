import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  onCloseCase,
  onFetchLiabilityList,
  onFetchFraudTypesList,
  onRequestSupervisorApproval,
  onFetchCloseCaseBuckets,
  onFetchFraudTypesWithBuckets,
  onRequestPartnerApproval,
  onAddInvestigatorVerdict
} from 'actions/caseReviewActions';
import { onToggleVerdictModal } from 'actions/toggleActions';
import { onFetchSnoozeAttributesList } from 'actions/ruleSnoozeActions';
import { onFetchUsersList, onFetchExternalCheckerList } from 'actions/userManagementActions';
import VerdictModal from 'components/common/VerdictModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    role: state.auth.userCreds.roles,
    userslist: state.user.userslist,
    display: state.toggle.verdictModal.frm,
    liability: state.caseAssignment.liability,
    fraudTypes: state.caseAssignment.fraudTypes,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets,
    fraudTypesWithBuckets: state.caseAssignment.fraudTypesWithBuckets,
    externalCheckers: state.user.externalCheckers,
    attributeList: state.snoozeRules.attributes,
    moduleType: state.auth.moduleType,
    hasAcquirerPortals: state.user.configurations.acquirerPortals,
    customerInfo: state.uds.customer.details
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    closeCase: bindActionCreators(onCloseCase, dispatch),
    fetchUsersList: bindActionCreators(onFetchUsersList, dispatch),
    toggle: bindActionCreators(onToggleVerdictModal, dispatch),
    fetchLiabilityList: bindActionCreators(onFetchLiabilityList, dispatch),
    fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch),
    requestApproval: bindActionCreators(onRequestSupervisorApproval, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch),
    requestPartnerApproval: bindActionCreators(onRequestPartnerApproval, dispatch),
    fetchExternalCheckerList: bindActionCreators(onFetchExternalCheckerList, dispatch),
    fetchFraudTypesWithBuckets: bindActionCreators(onFetchFraudTypesWithBuckets, dispatch),
    fetchAttributeList: bindActionCreators(onFetchSnoozeAttributesList, dispatch),
    addInvestigatorVerdict: bindActionCreators(onAddInvestigatorVerdict, dispatch)
  };
};

const VerdictModalContainer = connect(mapStateToProps, mapDispatchToProps)(VerdictModal);

export default VerdictModalContainer;
