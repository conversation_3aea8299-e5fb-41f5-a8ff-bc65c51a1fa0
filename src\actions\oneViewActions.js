import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader } from 'actions/toggleActions';
import {
  ON_REVIEWER_PARK_CASE_SUCCESS,
  ON_FETCH_REVIEWER_CASE_LOADING,
  ON_FETCH_REVIEWER_CASE_SUCCESS,
  ON_FETCH_REVIEWER_CASE_FAILURE,
  ON_REVIEWER_CASE_CLOSE_SUCCESS,
  ON_REVIEWER_ADD_TO_CASE_SUCCESS,
  ON_REVIEWER_CASE_NOTIFICATION_SUCCESS
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchReviewerCase(channel, formData) {
  return client({
    method: 'POST',
    url: `casereview/case/reviewer/${channel}/bucket`,
    data: formData
  });
}

function onFetchReviewerCaseLoading() {
  return { type: ON_FETCH_REVIEWER_CASE_LOADING };
}

function onFetchReviewerCaseSuccess(response) {
  return { type: ON_FETCH_REVIEWER_CASE_SUCCESS, response };
}

function onFetchReviewerCaseFailure(response) {
  return { type: ON_FETCH_REVIEWER_CASE_FAILURE, response };
}

function onFetchReviewerCase(formData) {
  return function (dispatch, getState) {
    const { userCreds } = getState().auth;
    dispatch(onFetchReviewerCaseLoading());
    return fetchReviewerCase(userCreds.channels[0], formData).then(
      (success) => dispatch(onFetchReviewerCaseSuccess(success)),
      (error) => dispatch(onFetchReviewerCaseFailure(error))
    );
  };
}

function reviewerCaseClose(formData) {
  return client({
    method: 'PUT',
    url: `casereview/case/reviewer/close`,
    data: formData
  });
}

function onReviewerCaseCloseSuccess(response) {
  return { type: ON_REVIEWER_CASE_CLOSE_SUCCESS, response };
}

function onReviewerCaseClose(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return reviewerCaseClose(formData)
      .then(
        () => {
          dispatch(onReviewerCaseCloseSuccess(formData));
          dispatch(onShowSuccessAlert({ message: 'Case(s) closed successfully!' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onReviewerAddToCase(formData) {
  return { type: ON_REVIEWER_ADD_TO_CASE_SUCCESS, response: formData.txnIds };
}

function reviewerCaseNotification(formData) {
  return client({
    method: 'PUT',
    url: `casereview/case/reviewer/close`,
    data: formData
  });
}

function onReviewerCaseNotificationSuccess(response) {
  return { type: ON_REVIEWER_CASE_NOTIFICATION_SUCCESS, response };
}

function onReviewerCaseNotification(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return reviewerCaseNotification(formData)
      .then(
        () => {
          dispatch(onReviewerCaseNotificationSuccess(formData));
          dispatch(onShowSuccessAlert({ message: 'Customer notified successfully!' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onReviewerParkCase() {
  return { type: ON_REVIEWER_PARK_CASE_SUCCESS };
}

export {
  onFetchReviewerCase,
  onReviewerCaseClose,
  onReviewerAddToCase,
  onReviewerCaseNotification,
  onReviewerParkCase
};
