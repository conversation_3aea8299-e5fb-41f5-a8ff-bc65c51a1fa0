import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, CardTitle, CardSubtitle } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

function CaseStatusStats({ formData, slaKpis, fetchSlaKpis }) {
  useEffect(() => {
    if (formData.startDate && formData.endDate) fetchSlaKpis(formData);
  }, [formData]);

  return (
    <Row>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.unassignedCount ?? 0}</CardTitle>
          <CardSubtitle>Unassigned</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.assignedCount ?? 0}</CardTitle>
          <CardSubtitle>Assigned</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.resolvedCount ?? 0}</CardTitle>
          <CardSubtitle>Resolved</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{(slaKpis.data?.resolutionRate ?? 0) + '%'}</CardTitle>
          <CardSubtitle>Resolution Rate</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.openCount ?? 0}</CardTitle>
          <CardSubtitle>Open</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.holdCount ?? 0}</CardTitle>
          <CardSubtitle>Hold</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.fraudCount ?? 0}</CardTitle>
          <CardSubtitle>Fraud</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.notFraudCount ?? 0}</CardTitle>
          <CardSubtitle>Not Fraud</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.onTimeCloseCount ?? 0}</CardTitle>
          <CardSubtitle>In Time Close</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="4" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{slaKpis.data?.overTimeClose ?? 0}</CardTitle>
          <CardSubtitle>Over Time Close</CardSubtitle>
        </CardContainer>
      </Col>
      <Col md="3" lg="3">
        <CardContainer>
          <CardTitle className="text-info">{(slaKpis.data?.successRate ?? 0) + '%'}</CardTitle>
          <CardSubtitle>Success Rate</CardSubtitle>
        </CardContainer>
      </Col>
    </Row>
  );
}

CaseStatusStats.propTypes = {
  slaKpis: PropTypes.object.isRequired,
  formData: PropTypes.object.isRequired,
  fetchSlaKpis: PropTypes.func.isRequired
};

export default CaseStatusStats;
