import responses from 'mocks/responses';
import objectAssign from 'object-assign';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import oneViewReducer from 'reducers/oneViewReducer';

describe('oneView Reducer', () => {
  it('should return the intial state', () => {
    expect(oneViewReducer(undefined, {})).toEqual(initialState.oneView);
  });

  it('should handle ON_FETCH_REVIEWER_CASE_LOADING', () => {
    expect(
      oneViewReducer(
        {
          case: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_REVIEWER_CASE_LOADING
        }
      )
    ).toEqual({
      case: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_REVIEWER_CASE_SUCCESS', () => {
    expect(
      oneViewReducer(
        {
          case: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_REVIEWER_CASE_SUCCESS,
          response: responses.oneView.case
        }
      )
    ).toEqual({
      case: {
        data: responses.oneView.case,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_REVIEWER_CASE_FAILURE', () => {
    expect(
      oneViewReducer(
        {
          case: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_REVIEWER_CASE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      case: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_REVIEWER_PARK_CASE_SUCCESS', () => {
    expect(
      oneViewReducer(
        {
          case: {
            data: responses.oneView.case,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_REVIEWER_PARK_CASE_SUCCESS
        }
      )
    ).toEqual({
      case: {
        data: {
          caseId: 308,
          caseRefNo: 'af8e3388-30e4-43a8-8783-143123511dc4',
          txnId: 'issuerTxn20231016207',
          txnAmount: 110001,
          txnTimestamp: '2023-10-16T20:00:07',
          entityId: 'cust13',
          entityCategory: 'Customer',
          weight: 1,
          createdTimestamp: '2023-10-16T11:04:07.011',
          assignmentTimeStamp: '2023-10-16T16:38:41.265',
          investigationVerdict: 'NA',
          ifrmVerdict: 'REJECTED',
          investigationStatus: 'Parked',
          terminalId: '1189621',
          deviceId: '',
          payerId: 'cust4, cust13',
          payeeId: 'merchant15',
          payeeMcc: '4722, Travel Industry',
          channelId: '42, UPI',
          txnType: '00, Purchase',
          responseCode: ' , PreAuth',
          senderMaskedCard: 'NA',
          senderHashedCard: '****************',
          reViolatedRules: ['d6eae84d-8f3e-458e-bc23-0701797f66a7'],
          bucketId: 4,
          ifrmPostauthVerdictName: 'N/A',
          caseStage: 'Reviewer',
          assignedTo: 'akashm',
          partnerId: 1,
          channel: 'frm',
          makerAction: '',
          checkerAction: ''
        },
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_REVIEWER_CASE_CLOSE_SUCCESS', () => {
    const response = {
      caseRefNo: 'test1',
      bucketName: 'Confirmed Genuine',
      remark: 'remark'
    };
    expect(
      oneViewReducer(
        {
          case: {
            data: responses.oneView.case,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_REVIEWER_CASE_CLOSE_SUCCESS,
          response
        }
      )
    ).toEqual({
      case: {
        data: objectAssign({}, responses.oneView.case, {
          investigationStatus: 'Closed',
          closeTimestamp: new Date().toString(),
          ...response
        }),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_REVIEWER_ADD_TO_CASE_SUCCESS', () => {
    expect(
      oneViewReducer(
        {
          case: {
            data: responses.oneView.case,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_REVIEWER_ADD_TO_CASE_SUCCESS,
          response: 'txn23'
        }
      )
    ).toEqual({
      case: {
        data: objectAssign({}, responses.oneView.case, {
          childTxns: ['txn23']
        }),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });
});
