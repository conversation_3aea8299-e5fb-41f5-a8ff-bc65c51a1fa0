import IncidentsHomePage from 'components/incidents/IncidentsHomePage';
import NotificationPage from 'components/notifications/NotificationPage';
import PageNotFound from 'components/PageNotFound';
import ReportsPage from 'components/reports/ReportsPage';
import RFIReportsHomePage from 'components/rfiReports/RFIReportsHomePage';
import AdvanceSearchPageContainer from 'containers/advanceSearch/AdvanceSearchPageContainer';
import AuditCaseDetailsContainer from 'containers/audit/AuditCaseDetailsContainer';
import AuditHomePageContainer from 'containers/audit/AuditHomePageContainer';
import AdminPageContainer from 'containers/auth/AdminPageContainer';
import PartnerBanksPageContainer from 'containers/auth/PartnerBanksPageContainer';
import CaseOneViewContainer from 'containers/caseReview/CaseOneViewContainer';
import AnalystDashboardContainer from 'containers/dashboards/AnalystDashboardContainer';
import STRDashboardContainer from 'containers/dashboards/STRDashboardContainer';
import SupervisorDashboardContainer from 'containers/dashboards/SupervisorDashboardContainer';
import EntityProfilingHomePageContainer from 'containers/entityProfiling/EntityProfilingHomePageContainer';
import IndepthContainer from 'containers/investigation/IndepthContainer';
import IndepthSTRContainer from 'containers/investigation/IndepthSTRContainer';
import InvestigationHomePageContainer from 'containers/investigation/InvestigationHomePageContainer';
import MonitoringDetailPageContainer from 'containers/monitoring/MonitoringDetailPageContainer';
import MonitoringHomePageContainer from 'containers/monitoring/MonitoringHomePageContainer';
import PrefiltersMainContainer from 'containers/prefilters/PrefiltersMainContainer';
import ReleaseFundsContainer from 'containers/releaseFunds/ReleaseFundsContainer';
import RuleEngineContainer from 'containers/ruleEngine/RuleEngineContainer';
import ScpHomePageContainer from 'containers/scp/ScpHomePageContainer';
import SupervisorCaseDetailsContainer from 'containers/supervisor/SupervisorCaseDetailsContainer';
import SupervisorHomePageContainer from 'containers/supervisor/SupervisorHomePageContainer';
import SupervisorSTRCaseDetailsContainer from 'containers/supervisor/SupervisorSTRCaseDetailsContainer';
import UserManagementPageContainer from 'containers/userManagement/UserManagementPageContainer';

/**
 * Defines the routes for the dashboard module.
 * @type {object}
 */
const dashboardRoutes = {
  str: STRDashboardContainer,
  supervisor: SupervisorDashboardContainer,
  default: AnalystDashboardContainer,
};

/**
 * Defines the nested routes for the cases module.
 * @type {Array<object>}
 */
const caseRoutes = [
  { path: '/', exact: true, component: SupervisorHomePageContainer },
  { path: '/frm/:txnId', component: SupervisorCaseDetailsContainer },
  { path: '/str/:txnId', component: SupervisorSTRCaseDetailsContainer },
];

/**
 * Defines the nested routes for the investigation module.
 * @type {Array<object>}
 */
const investigationRoutes = [
  { path: '/', exact: true, component: InvestigationHomePageContainer },
  { path: '/frm/:txnId', component: IndepthContainer },
  { path: '/str/:txnId', component: IndepthSTRContainer },
];

/**
 * Defines the nested routes for the monitoring module.
 * @type {Array<object>}
 */
const monitoringRoutes = [
  { path: '/', exact: true, component: MonitoringHomePageContainer },
  { path: '/:channel?/:txnId', component: MonitoringDetailPageContainer },
];

/**
 * Defines the nested routes for the audit module.
 * @type {Array<object>}
 */
const auditRoutes = [
  { path: '/', exact: true, component: AuditHomePageContainer },
  { path: '/:channel?/:txnId?', component: AuditCaseDetailsContainer },
];

/**
 * @typedef {object} RouteConfig
 * @property {string} key - A unique key for the route.
 * @property {string} path - The path for the route.
 * @property {React.Component|object} component - The React component to render for the route, or an object for dashboard with different components based on role/channel.
 * @property {string} moduleRoute - The base module route, used for navigation and access control.
 * @property {Array<object>} [nestedRoutes] - An optional array of nested route configurations.
 * @property {string} [requiredRole] - Optional. Specifies a required role for the route.
 * @property {boolean} [isCooperativeRoute] - Optional. Custom flag for cooperative-specific route conditions.
 */

/**
 * Main application route configuration.
 * Each object defines a top-level route or a module with nested routes.
 * @type {Array<RouteConfig>}
 */
const routesConfig = [
  {
    key: 'dashboard',
    path: 'dashboard',
    component: dashboardRoutes,
    moduleRoute: '/dashboard',
  },
  {
    key: 'search',
    path: 'search',
    component: AdvanceSearchPageContainer,
    moduleRoute: '/search',
  },
  {
    key: 'users',
    path: 'users',
    component: AdminPageContainer,
    moduleRoute: '/users',
  },
  {
    key: 'banks',
    path: 'banks',
    component: PartnerBanksPageContainer,
    moduleRoute: '/banks',
  },
  {
    key: 'review',
    path: 'review/:channel?/:txnId?',
    component: CaseOneViewContainer,
    moduleRoute: '/review',
  },
  {
    key: 'cases',
    path: 'cases',
    moduleRoute: '/cases',
    nestedRoutes: caseRoutes,
  },
  {
    key: 'employees',
    path: 'employees',
    component: UserManagementPageContainer,
    moduleRoute: '/employees',
  },
  {
    key: 'dsl',
    path: 'dsl',
    component: RuleEngineContainer,
    moduleRoute: '/dsl',
  },
  {
    key: 'prefilters',
    path: 'prefilters',
    component: PrefiltersMainContainer,
    moduleRoute: '/prefilters',
  },
  {
    key: 'release-funds',
    path: 'release-funds',
    component: ReleaseFundsContainer,
    moduleRoute: '/release-funds',
  },
  {
    key: 'settings',
    path: 'settings',
    component: ScpHomePageContainer,
    moduleRoute: '/settings',
  },
  {
    key: 'investigation',
    path: 'investigation',
    moduleRoute: '/investigation',
    nestedRoutes: investigationRoutes,
  },
  {
    key: 'monitor',
    path: 'monitor',
    moduleRoute: '/monitor',
    nestedRoutes: monitoringRoutes,
  },
  {
    key: 'profiling',
    path: 'profiling/:type?/:value?',
    component: EntityProfilingHomePageContainer,
    moduleRoute: '/profiling',
  },
  {
    key: 'rfi',
    path: 'rfi',
    component: RFIReportsHomePage,
    moduleRoute: '/rfi',
  },
  {
    key: 'reports',
    path: 'reports',
    component: ReportsPage,
    moduleRoute: '/reports',
  },
  {
    key: 'incidents',
    path: 'incidents',
    component: IncidentsHomePage,
    moduleRoute: '/incidents',
  },
  {
    key: 'audit',
    path: 'audit',
    moduleRoute: '/audit',
    nestedRoutes: auditRoutes,
  },
  {
    key: 'notification',
    path: 'notification',
    component: NotificationPage,
    moduleRoute: '/notification',
    // This route has specific conditions: isCooperative && role === 'investigator'
    // These will be handled in the filtering hook.
    requiredRole: 'investigator',
    isCooperativeRoute: true, // Custom flag for isCooperative check
  },
  {
    key: 'not-found',
    path: '*',
    component: PageNotFound,
    moduleRoute: '/not-found',
  }
];

export default routesConfig;
