import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onUploadRuleList } from 'actions/ruleConfiguratorActions';
import RuleUploadButton from 'components/ruleEngine/RuleUploadButton';

const mapDispatchToProps = (dispatch) => ({
  uploadRuleList: bindActionCreators(onUploadRuleList, dispatch)
});

const RuleUploadButtonContainer = connect(null, mapDispatchToProps)(RuleUploadButton);

export default RuleUploadButtonContainer;
