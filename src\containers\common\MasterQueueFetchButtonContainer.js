import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCasesFromMasterQueue } from 'actions/caseReviewActions';
import MasterQueueFetchButton from 'components/common/MasterQueueFetchButton';

const mapDispatchToProps = (dispatch) => ({
  onFetchCasesFromMasterQueue: bindActionCreators(onFetchCasesFromMasterQueue, dispatch)
});

const MasterQueueFetchButtonContainer = connect(null, mapDispatchToProps)(MasterQueueFetchButton);

export default MasterQueueFetchButtonContainer;
