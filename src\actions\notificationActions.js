import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import {
  ON_FETCH_NOTIFICATIONS_LIST_LOADING,
  ON_FETCH_NOTIFICATIONS_LIST_SUCCESS,
  ON_FETCH_NOTIFICATIONS_LIST_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchNotifications(channel) {
  return client({
    url: `casereview/case/investigator/notification/${channel}/subscription`,
    badRequestMessage: 'Curently unable to fetch notifications'
  });
}

function onFetchNotificationsLoading() {
  return { type: ON_FETCH_NOTIFICATIONS_LIST_LOADING };
}

function onFetchNotificationsSuccess(response) {
  return {
    type: ON_FETCH_NOTIFICATIONS_LIST_SUCCESS,
    response
  };
}

function onFetchNotificationsFailure(response) {
  return {
    type: ON_FETCH_NOTIFICATIONS_LIST_FAILURE,
    response
  };
}

function onFetchNotifications(channel) {
  return function (dispatch) {
    dispatch(onFetchNotificationsLoading());
    return fetchNotifications(channel).then(
      (success) => dispatch(onFetchNotificationsSuccess(success)),
      (error) => dispatch(onFetchNotificationsFailure(error))
    );
  };
}

function acknowledgeNotification(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/notification/type/acknowledge`,
    data: formData,
    badRequestMessage: 'Currently unable to acknowledge notifications'
  });
}

function onAcknowledgeNotification(channel, formData) {
  return function (dispatch) {
    return acknowledgeNotification(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Notification acknowledged successfully' }));
        dispatch(onFetchNotifications(channel));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function resolveNotification(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/investigator/notification/type/resolve`,
    data: formData,
    badRequestMessage: 'Currently unable to resolve notification'
  });
}

function onResolveNotification(channel, formData) {
  return function (dispatch) {
    return resolveNotification(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Notification acknowledged successfully' }));
        dispatch(onFetchNotifications(channel));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export { onFetchNotifications, onAcknowledgeNotification, onResolveNotification };
