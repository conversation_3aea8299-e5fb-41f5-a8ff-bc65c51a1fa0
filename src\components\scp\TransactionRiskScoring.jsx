import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useCallback } from 'react';
import { FormGroup, Label, Input, Col } from 'reactstrap';

import HelpIcon from 'components/common/HelpIcon';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';
import DecisionMatrixTableContainer from 'containers/scp/DecisionMatrixTableContainer';

function TransactionRiskScoring({
  highlightText,
  saveConfigurations,
  configurationsData,
  toggleRow,
  openRows
}) {
  const [transactionRiskScore, setTransactionRiskScore] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  const isDisabled = transactionRiskScore?.activation?.value === 'disabled';
  const shouldInterceptIsOff = !transactionRiskScore?.shouldIntercept?.value;

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        transactionRiskScore,
        setTransactionRiskScore,
        saveConfigurations
      ),
    [configurationsData, transactionRiskScore, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, transactionRiskScore, setTransactionRiskScore),
    [transactionRiskScore]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setTransactionRiskScore),
    [configurationsData]
  );

  return (
    <ConfigFormWrapper
      configTitle="Transaction Risk Score"
      activationId="activationTransactionRiskScoring"
      data={transactionRiskScore}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      highlightText={highlightText}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}>
      <FormGroup switch row>
        <Label sm={4} md={3} lg={2} for="shouldIntercept" className="searchable">
          {highlightText('Intercept Rule Engine:')}
          {!isEmpty(transactionRiskScore?.shouldIntercept?.desc) && (
            <HelpIcon
              size="lg"
              id="interceptRuleEngine"
              text={transactionRiskScore?.shouldIntercept?.desc}
            />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="switch"
            role="switch"
            id="shouldIntercept"
            name="shouldIntercept"
            value={transactionRiskScore?.shouldIntercept?.value}
            onChange={(event) =>
              inputChangeDataHandler(event, transactionRiskScore, setTransactionRiskScore)
            }
            checked={transactionRiskScore?.shouldIntercept?.value}
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>

      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="highRiskThreshold" className="searchable">
          {highlightText('High risk threshold:')}
          {!isEmpty(transactionRiskScore?.highRiskThreshold?.desc) && (
            <HelpIcon
              size="lg"
              id="highRiskThreshold"
              text={transactionRiskScore?.highRiskThreshold?.desc}
            />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="range"
            name="highRiskThreshold"
            id="highRiskThreshold"
            onChange={(event) =>
              inputChangeDataHandler(event, transactionRiskScore, setTransactionRiskScore)
            }
            max={100}
            min={0}
            step={1}
            value={transactionRiskScore?.highRiskThreshold?.value}
            disabled={isDisabled || shouldInterceptIsOff}
          />

          {transactionRiskScore?.highRiskThreshold?.value}
        </Col>
      </FormGroup>
      <DecisionMatrixTableContainer
        transactionRiskScore={transactionRiskScore}
        setTransactionRiskScore={setTransactionRiskScore}
        isDisabled={isDisabled}
        highlightText={highlightText}
        shouldInterceptIsOff={shouldInterceptIsOff}
        configurationsData={configurationsData}
      />
    </ConfigFormWrapper>
  );
}

TransactionRiskScoring.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired
};

export default TransactionRiskScoring;
