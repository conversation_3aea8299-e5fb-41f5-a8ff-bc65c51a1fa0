import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Input, Label, FormGroup } from 'reactstrap';

function XChannelDropDown({
  onChange,
  xChannelData,
  getXChannelList,
  value = '',
  isRequired = false,
  isDisabled = false
}) {
  useEffect(() => {
    if (_.isEmpty(xChannelData?.list) && !xChannelData.loader) getXChannelList();
  }, []);

  const channelOptions = _.map(xChannelData?.list, (channel) => (
    <option key={channel.id} value={channel.id}>
      {channel.xchannelIdType}
    </option>
  ));

  const inputProps = {
    ...(isRequired && { required: true }),
    ...(isDisabled && { disabled: true })
  };

  return (
    <FormGroup>
      <Label htmlFor="xChannel">Channel</Label>
      <Input
        type="select"
        id="xChannel"
        name="xChannel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        {...inputProps}>
        <option value="" key="nullValue">
          -- SELECT --
        </option>
        {channelOptions}
      </Input>
    </FormGroup>
  );
}

XChannelDropDown.propTypes = {
  isRequired: PropTypes.bool,
  isDisabled: PropTypes.bool,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  xChannelData: PropTypes.object.isRequired,
  getXChannelList: PropTypes.func.isRequired
};

export default XChannelDropDown;
