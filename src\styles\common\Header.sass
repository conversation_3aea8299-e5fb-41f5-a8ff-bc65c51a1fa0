@use '../typography' as *;

.header-container
  position: relative
  width: 100%
  z-index: 1000

.header-navbar
  border-radius: 0
  margin-bottom: 0
  padding: 3px 3px 0 3px

h1
  font: $h1-typo
  .navbar-brand
    font-size: inherit
    font-weight: 600
    padding: 0

.header-logo
  img
    height: 45px
    margin-right: 0.8rem
    margin-bottom: 5px

ul.navbar-nav
  font: $header-typo
  .nav-link
    cursor: pointer
    padding-bottom: 12px
    padding-top: 12px
  .active
    font-weight: 600

  .session-dropdown-menu
    right: 1px

.navbar-toggler
  width: 70px
  height: 51px
  display: flex
  justify-content: center
  align-items: center
  cursor: pointer

  &:focus
    outline: none

  .navbar-toggler-icon
    position: relative
    width: 50px
    height: 2px
    border-radius: 1px
    transition: all .2s ease-out

  .navbar-toggler-icon::before,
  .navbar-toggler-icon::after
    content: ""
    position: absolute
    left: 0
    width: 44px
    height: 2px
    border-radius: 1px
    transition: all 500ms ease-in-out

  .navbar-toggler-icon::before
    transform: translateY(-12px)

  .navbar-toggler-icon::after
    transform:  translateY(12px)

.navbar-toggler.active .navbar-toggler-icon
  background: transparent !important
  box-shadow: none

.navbar-toggler.active .navbar-toggler-icon::before
  transform: rotate(45deg)

.navbar-toggler.active .navbar-toggler-icon::after
  transform: rotate(-45deg)

.notification-counter
  position: relative
  .badge
    border: 1px solid
    position: absolute
    top: -10px
    left: 10px
    font: $small-typo
    padding: 1px 5px
