import { faCog } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useState, useCallback } from 'react';
import { DropdownItem } from 'reactstrap';

import DropdownButton from 'components/common/DropdownButton';
import ModalContainer from 'components/common/ModalContainer';
import ActionOrderListContainer from 'containers/ruleEngine/ActionOrderListContainer';
import LabelOrderListContainer from 'containers/ruleEngine/LabelOrderListContainer';

function RuleEngineConfigurator({ theme, channel }) {
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [isLabelModalOpen, setIsLabelModalOpen] = useState(false);

  // Memoize toggle functions to prevent unnecessary re-renders
  const toggleActionModal = useCallback(() => setIsActionModalOpen((prev) => !prev), []);

  const toggleLabelModal = useCallback(() => setIsLabelModalOpen((prev) => !prev), []);

  return (
    <>
      <DropdownButton
        name={<FontAwesomeIcon icon={faCog} />}
        title="Rule Engine Settings"
        color="secondary">
        {channel === 'frm' && (
          <DropdownItem onClick={() => setIsActionModalOpen(true)}>Action Order</DropdownItem>
        )}
        <DropdownItem onClick={() => setIsLabelModalOpen(true)}>Label Order</DropdownItem>
      </DropdownButton>

      <ModalContainer
        size="sm"
        header="Action order"
        theme={theme}
        isOpen={isActionModalOpen}
        toggle={toggleActionModal}>
        <ActionOrderListContainer channel={channel} />
      </ModalContainer>

      <ModalContainer
        size="sm"
        header="Label order"
        theme={theme}
        isOpen={isLabelModalOpen}
        toggle={toggleLabelModal}>
        <LabelOrderListContainer channel={channel} />
      </ModalContainer>
    </>
  );
}

RuleEngineConfigurator.propTypes = {
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired
};

export default React.memo(RuleEngineConfigurator);
