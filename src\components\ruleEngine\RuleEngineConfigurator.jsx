import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { DropdownItem } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCog } from '@fortawesome/free-solid-svg-icons';

import DropdownButton from 'components/common/DropdownButton';
import ModalContainer from 'components/common/ModalContainer';
import LabelOrderListContainer from 'containers/ruleEngine/LabelOrderListContainer';
import ActionOrderListContainer from 'containers/ruleEngine/ActionOrderListContainer';

function RuleEngineConfigurator({ theme, channel }) {
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [isLabelModalOpen, setIsLabelModalOpen] = useState(false);
  return (
    <>
      <DropdownButton
        name={<FontAwesomeIcon icon={faCog} />}
        title="Rule Engine Settings"
        color="secondary">
        {channel === 'frm' && (
          <DropdownItem onClick={() => setIsActionModalOpen(true)}>Action Order</DropdownItem>
        )}
        <DropdownItem onClick={() => setIsLabelModalOpen(true)}>Label Order</DropdownItem>
      </DropdownButton>

      <ModalContainer
        size="sm"
        header="Action order"
        theme={theme}
        isOpen={isActionModalOpen}
        toggle={() => setIsActionModalOpen(!isActionModalOpen)}>
        <ActionOrderListContainer channel={channel} />
      </ModalContainer>

      <ModalContainer
        size="sm"
        header="Label order"
        theme={theme}
        isOpen={isLabelModalOpen}
        toggle={() => setIsLabelModalOpen(!isLabelModalOpen)}>
        <LabelOrderListContainer channel={channel} />
      </ModalContainer>
    </>
  );
}

RuleEngineConfigurator.propTypes = {
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired
};

export default RuleEngineConfigurator;
