'use strict';
import PropTypes from 'prop-types';
import React from 'react';
import { Button, FormGroup } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const ConfirmAlert = (props) => (
  <div>
    <ModalContainer
      size="md"
      theme={props.theme}
      isOpen={props.confirmAlertModal}
      toggle={props.toggleConfirmAlertModal}
      header="Confirm action">
      <p>{props.confirmAlertTitle}</p>
      <FormGroup className="d-flex justify-content-end mt-3">
        <Button
          outline
          size="sm"
          type="button"
          color="success"
          className="me-2"
          onClick={props.toggleConfirmAlertModal}>
          Cancel
        </Button>
        <Button size="sm" type="submit" color="primary" onClick={props.confirmationAction}>
          Yes
        </Button>
      </FormGroup>
    </ModalContainer>
  </div>
);

ConfirmAlert.propTypes = {
  theme: PropTypes.string.isRequired,
  confirmAlertModal: PropTypes.bool,
  toggleConfirmAlertModal: PropTypes.func,
  confirmAlertTitle: PropTypes.string.isRequired,
  confirmationAction: PropTypes.func
};

export default ConfirmAlert;
