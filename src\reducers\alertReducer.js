import objectAssign from 'object-assign';

import {
  ON_SUCCESS_ALERT,
  ON_FAILURE_ALERT,
  ON_CLEAR_ALERT,
  ON_WARNING_ALERT
} from 'constants/actionTypes';

import initialState from './initialState';

export default function alertReducer(state = initialState.alert, action) {
  switch (action.type) {
    case ON_SUCCESS_ALERT:
      return objectAssign({}, state, {
        type: 'alert-success',
        message: action.response.message,
        show: true
      });
    case ON_FAILURE_ALERT:
      return objectAssign({}, state, {
        type: 'alert-danger',
        message: action.response.message,
        show: true
      });
    case ON_WARNING_ALERT:
      return objectAssign({}, state, {
        type: 'alert-warning',
        message: action.response.message,
        show: true
      });
    case ON_CLEAR_ALERT:
      return initialState.alert;
    default:
      return state;
  }
}
