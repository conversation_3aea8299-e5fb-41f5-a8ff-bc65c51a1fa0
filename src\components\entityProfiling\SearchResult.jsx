import React from 'react';
import PropTypes from 'prop-types';
import { isEmpty, lowerCase } from 'lodash';
import { Row, Col } from 'reactstrap';

import StatisticsCard from 'components/common/StatisticsCard';
import AgentInfoCardContainer from 'containers/common/AgentInfoCardContainer';
import HistoryTxnTableContainer from 'containers/common/HistoryTxnTableContainer';
import MerchantInfoCardContainer from 'containers/common/MerchantInfoCardContainer';
import CustomerInfoCardContainer from 'containers/common/CustomerInfoCardContainer';
import PastInvestigationCardContainer from 'containers/common/PastInvestigationCardContainer';

const SearchResult = ({
  role,
  theme,
  selected,
  statisticsDetails,
  ruleLabel,
  caseStatus,
  primaryChannel,
  contextKey
}) => {
  if (isEmpty(selected)) return null;

  const entityType = lowerCase(selected.entityCategory);

  const renderInfoCard = () => {
    const infoCardComponents = {
      merchant: <MerchantInfoCardContainer merchantId={selected.id} channel={primaryChannel} />,
      customer: <CustomerInfoCardContainer customerId={selected.id} channel={primaryChannel} />,
      agent: <AgentInfoCardContainer agentId={selected.id} channel={primaryChannel} />
    };

    return infoCardComponents[entityType] || null;
  };

  const renderContent = (channel) => (
    <>
      {!ruleLabel && (
        <PastInvestigationCardContainer
          role={role}
          entityId={selected.id}
          channel={channel}
          ruleLabel={ruleLabel}
          caseStatus={caseStatus}
          contextKey={contextKey}
          showActions
        />
      )}
      <HistoryTxnTableContainer
        entityId={selected.id}
        entityCategory={entityType}
        channel={channel}
      />
    </>
  );

  const renderStatisticsCards = () => (
    <Row className="statistics-card">
      <Col md="6" sm="12" className="left-card">
        <StatisticsCard
          data={statisticsDetails.transactionStatistics}
          theme={theme}
          title="Transaction Statistics"
        />
      </Col>
      <Col md="6" sm="12" className="right-card">
        <StatisticsCard
          data={statisticsDetails.customerStatistics}
          theme={theme}
          title="Customer Statistics"
        />
      </Col>
    </Row>
  );

  return (
    <div className="selected-entity-result mt-4">
      {ruleLabel && (
        <PastInvestigationCardContainer
          role={role}
          entityId={selected.id}
          channel={primaryChannel}
          ruleLabel={ruleLabel}
          caseStatus={caseStatus}
          contextKey={contextKey}
        />
      )}
      {renderInfoCard()}
      {renderContent(primaryChannel)}
      {renderStatisticsCards()}
    </div>
  );
};

SearchResult.propTypes = {
  ruleLabel: PropTypes.string,
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  selected: PropTypes.object.isRequired,
  statisticsDetails: PropTypes.object.isRequired,
  caseStatus: PropTypes.array.isRequired,
  primaryChannel: PropTypes.string.isRequired
};

export default SearchResult;
