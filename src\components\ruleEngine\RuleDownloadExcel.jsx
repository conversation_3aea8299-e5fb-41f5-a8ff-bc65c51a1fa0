import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import ExcelDownload from 'components/common/ExcelDownload';

function RuleDownloadExcel({ rulesList, alertCategories }) {
  let excelHeaders = [
    { label: 'Name', value: 'name' },
    { label: 'Channels', value: 'channels' },
    { label: 'Fraud Category', value: 'fraudCategory' },
    {
      label: 'Alert Category',
      value: (col) => {
        const category =
          _.find(alertCategories, (category) => category.id == col.alertCategoryId) || {};
        return category?.categoryName || '';
      }
    },
    { label: 'Logic', value: 'logic' },
    { label: 'Description', value: 'description' },
    { label: 'Rule Order', value: 'order' },
    { label: 'Method', value: 'methodType' },
    { label: 'Status', value: 'status' },
    { label: 'Active', value: 'active' },
    {
      label: 'Create Case',
      value: (col) => (col?.explicit ? 'Explicit' : 'Normal')
    },
    { label: 'Action Code', value: 'actionCode' },
    { label: 'Action Name', value: 'actionName' },
    { label: 'Label', value: 'label' },
    { label: 'Assignment Priority', value: 'assignmentPriority' },
    { label: 'Created By', value: 'createdBy' },
    { label: 'Created Date', value: 'createdAt' },
    { label: 'Updated By', value: 'updatedBy' },
    { label: 'Updated Date', value: 'updatedAt' },
    {
      label: 'Merchant Specific',
      value: (col) => (col?.isMerchantSpecific ? 'Yes' : 'No')
    },
    { label: 'Low Level Outcome', value: 'lowLevelOutcome' },
    { label: 'Med Level Outcome', value: 'medLevelOutcome' },
    { label: 'High Level Outcome', value: 'highLevelOutcome' },
    {
      label: 'Citation Names',
      value: (col) => _.join(col?.citationNames, ', ')
    },
    {
      label: 'Is Delete',
      value: (col) => (col?.isDelete ? 'Yes' : 'No')
    },
    { label: 'Code', value: 'code' },
    { label: 'Type', value: 'ruleType' }
  ];

  return (
    <ExcelDownload
      data={rulesList}
      sheetName="Production Rules"
      headers={excelHeaders}
      disabled={rulesList.length == 0}
    />
  );
}

RuleDownloadExcel.propTypes = {
  rulesList: PropTypes.array.isRequired,
  alertCategories: PropTypes.array.isRequired
};

export default RuleDownloadExcel;
