import {
  ON_FAILURE_VIOLATED_RULES_FETCH,
  ON_SUCCESSFUL_VIOLATED_RULES_FETCH,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS,
  ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchViolatedRules(txnId, channel, data) {
  return client({ method: 'POST', url: `uds/${channel}/violated-rules/${txnId}`, data });
}

function onSuccessfulFetchViolatedRules(transactionId, response) {
  return {
    type: ON_SUCCESSFUL_VIOLATED_RULES_FETCH,
    response: {
      transactionId,
      list: response
    }
  };
}

function onFetchViolatedRulesFailure(response) {
  return { type: ON_FAILURE_VIOLATED_RULES_FETCH, response };
}

function onFetchViolatedRules(transactionId, channel, data) {
  return function (dispatch) {
    return fetchViolatedRules(transactionId, channel, data).then(
      (success) => dispatch(onSuccessfulFetchViolatedRules(transactionId, success)),
      (error) => dispatch(onFetchViolatedRulesFailure(error))
    );
  };
}

function fetchViolatedRulesTransactions(txnId, channel, data) {
  return client({
    method: 'POST',
    url: `uds/${channel}/txn-details/violated-rules/${txnId}`,
    data
  });
}

function onFetchViolatedRulesTransactionsLoading(ruleCode) {
  return { type: ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING, ruleCode };
}

function onFetchViolatedRulesTransactionsSuccess(ruleCode, response) {
  return {
    type: ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS,
    ruleCode,
    response
  };
}

function onFetchViolatedRulesTransactionsFailure(ruleCode) {
  return { type: ON_FETCH_RULE_VIOLATION_TRANSACTIONS_FAILURE, ruleCode };
}

function onFetchViolatedRulesTransactions(transactionId, channel, data) {
  return function (dispatch) {
    dispatch(onFetchViolatedRulesTransactionsLoading(data.code));
    return fetchViolatedRulesTransactions(transactionId, channel, data).then(
      (success) => dispatch(onFetchViolatedRulesTransactionsSuccess(data.code, success)),
      () => dispatch(onFetchViolatedRulesTransactionsFailure(data.code))
    );
  };
}

export { onFetchViolatedRules, onFetchViolatedRulesTransactions };
