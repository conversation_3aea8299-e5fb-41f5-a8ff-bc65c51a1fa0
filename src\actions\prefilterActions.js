import { onShowFailure<PERSON>lert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader, onTogglePrefilterModal } from 'actions/toggleActions';
import {
  ON_FETCH_FILTER_FAILURE,
  ON_FETCH_FILTER_LOADING,
  ON_FETCH_FILTER_SUCCESS,
  ON_FETCH_TPS_LIMIT_FAILURE,
  ON_FETCH_TPS_LIMIT_LOADING,
  ON_FETCH_TPS_LIMIT_SUCCESS,
  ON_FETCH_FILTER_CATEGORY_FAILURE,
  ON_FETCH_FILTER_CATEGORY_LOADING,
  ON_FETCH_FILTER_CATEGORY_SUCCESS
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchTps() {
  return client({ url: `prefilter/tps` });
}

function onFetchTpsLoading() {
  return { type: ON_FETCH_TPS_LIMIT_LOADING };
}

function onFetchTpsSuccess(response) {
  return {
    type: ON_FETCH_TPS_LIMIT_SUCCESS,
    response
  };
}

function onFetchTpsFailure(response) {
  return {
    type: ON_FETCH_TPS_LIMIT_FAILURE,
    response
  };
}

function onFetchTps() {
  return function (dispatch) {
    dispatch(onFetchTpsLoading());
    return fetchTps().then(
      (success) => dispatch(onFetchTpsSuccess(success)),
      (error) => dispatch(onFetchTpsFailure(error))
    );
  };
}

function setTps(formData, method) {
  return client({
    method,
    url: `prefilter/tps`,
    data: formData,
    badRequestMessage: 'Unable to set TPS. Please provide valid input'
  });
}

function onSetTps(formData, method) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return setTps(formData, method)
      .then(
        () => {
          dispatch(onFetchTps());
          dispatch(onShowSuccessAlert({ message: 'TPS limit set successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteTps(channelId) {
  return client({
    method: 'DELETE',
    url: `prefilter/tps/channel/${channelId}`
  });
}

function onDeleteTps(channelId) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteTps(channelId)
      .then(
        () => {
          dispatch(onFetchTps());
          dispatch(onShowSuccessAlert({ message: 'TPS limit removed successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCategory() {
  return client({ url: `prefilter/category` });
}

function onFetchCategoryLoading() {
  return { type: ON_FETCH_FILTER_CATEGORY_LOADING };
}

function onFetchCategorySuccess(response) {
  return {
    type: ON_FETCH_FILTER_CATEGORY_SUCCESS,
    response
  };
}

function onFetchCategoryFailure(response) {
  return {
    type: ON_FETCH_FILTER_CATEGORY_FAILURE,
    response
  };
}

function onFetchCategory() {
  return function (dispatch) {
    dispatch(onFetchCategoryLoading());
    return fetchCategory().then(
      (success) => dispatch(onFetchCategorySuccess(success)),
      (error) => {
        dispatch(onFetchCategoryFailure(error));
      }
    );
  };
}

function fetchFilter() {
  return client({ url: `prefilter/filter` });
}

function onFetchFilterLoading() {
  return { type: ON_FETCH_FILTER_LOADING };
}

function onFetchFilterSuccess(response) {
  return {
    type: ON_FETCH_FILTER_SUCCESS,
    response
  };
}

function onFetchFilterFailure(response) {
  return {
    type: ON_FETCH_FILTER_FAILURE,
    response
  };
}

function onFetchFilter() {
  return function (dispatch) {
    dispatch(onFetchFilterLoading());
    return fetchFilter().then(
      (success) => dispatch(onFetchFilterSuccess(success)),
      (error) => dispatch(onFetchFilterFailure(error))
    );
  };
}

function setFilter(formData, method) {
  return client({
    method,
    url: `prefilter/filter`,
    data: formData,
    badRequestMessage: 'Unable to add filter. Please check input data'
  });
}

function onSetFilter(formData, method, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return setFilter(formData, method)
      .then(
        () => {
          dispatch(onFetchFilter());
          dispatch(onTogglePrefilterModal(channel));
          dispatch(
            onShowSuccessAlert({
              message:
                method === 'POST' ? 'Filter added successfully' : 'Filter updated successfully'
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteFilter(filterId) {
  return client({
    method: 'DELETE',
    url: `prefilter/filter/id/${filterId}`
  });
}

function onDeleteFilter(filterId) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteFilter(filterId)
      .then(
        () => {
          dispatch(onFetchFilter());
          dispatch(onShowSuccessAlert({ message: 'Filter removed successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchTps,
  onSetTps,
  onDeleteTps,
  onFetchCategory,
  onFetchFilter,
  onSetFilter,
  onDeleteFilter
};
