import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Switch, Route, useRouteMatch } from 'react-router-dom';

import PageNotFound from 'components/PageNotFound';
import { MODULES_LIST } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';
import { filterAccessibleRoutes } from 'utility/routeUtils';

import routesConfig from '../config/routesConfig';

const Routes = ({ role, channel, moduleType, loginType, hasHoldAndRelease, hasKnowageReports }) => {
  const match = useRouteMatch();
  const [screens, setScreen] = useState([]);

  useEffect(() => {
    const allScreens = filterAccessibleRoutes(
      MODULES_LIST,
      role,
      channel,
      moduleType,
      loginType,
      hasHoldAndRelease,
      hasKnowageReports
    ).map((module) => module.route);
    setScreen(_.uniq(allScreens));
  }, [channel, hasHoldAndRelease, hasKnowageReports, loginType, moduleType, role]);

  return (
    <Switch>
      {routesConfig.map((route) => {
        const isRouteActive = _.includes(screens, route.moduleRoute);

        // Handle specific conditions for notification route
        if (route.key === 'notification' && (!isCooperative || role !== route.requiredRole))
          return null;

        if (isRouteActive)
          if (route.nestedRoutes)
            return (
              <Route
                key={route.key}
                path={`${match.path}${route.path}`}
                render={({ match: nestedMatch }) => (
                  <Switch>
                    {route.nestedRoutes.map((nestedRoute) => (
                      <Route
                        key={`${route.key}-${nestedRoute.path}`}
                        exact={nestedRoute.exact}
                        path={`${nestedMatch.url}${nestedRoute.path}`}
                        component={nestedRoute.component}
                      />
                    ))}
                  </Switch>
                )}
              />
            );
          else if (route.component) {
            // Handle dashboard component selection based on channel/role
            let ComponentToRender = route.component;
            if (route.key === 'dashboard')
              if (channel[0] === 'str') ComponentToRender = route.component.str;
              else if (role === 'supervisor') ComponentToRender = route.component.supervisor;
              else ComponentToRender = route.component.default;

            return (
              <Route
                key={route.key}
                path={`${match.path}${route.path}`}
                component={ComponentToRender}
              />
            );
          }

        return null;
      })}
      <Route component={PageNotFound} />
    </Switch>
  );
};

Routes.propTypes = {
  hasHoldAndRelease: PropTypes.number.isRequired,
  hasKnowageReports: PropTypes.number.isRequired,
  role: PropTypes.string.isRequired,
  loginType: PropTypes.string.isRequired,
  channel: PropTypes.array.isRequired,
  moduleType: PropTypes.string.isRequired
};

export default Routes;
