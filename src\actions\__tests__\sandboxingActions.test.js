import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/sandboxingActions';
import responses from 'mocks/responses';

const mockedStore = {
  sandboxing: {}
};

describe('sandboxing actions', () => {
  it('should Fetch Sandbox Date Range', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SANDBOX_DATE_RANGE_LOADING },
      {
        type: types.ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS,
        response: responses.sandboxing.dateRange
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSandboxDateRange()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Test Sandbox Rules', () => {
    let formData = {
      rules: [
        {
          code: null,
          ruleType: 'Dsl',
          logic: 'less than terminalId ',
          name: 'sgr',
          description: 'rg',
          order: 0,
          assignmentPriority: 0,
          explicit: false,
          actionCode: '01',
          actionName: 'ACCEPTED',
          methodType: 'Pre',
          comments: '',
          isMerchantSpecific: false,
          lowLevelOutcome: null,
          medLevelOutcome: null,
          highLevelOutcome: null,
          alertCategoryId: 1
        },
        {
          code: '15dd9b65-549d-44e1-b922-aa065d98766e',
          name: 'BenfordLawCheck',
          logic: '',
          description:
            'Checks whether the leading digit of all transacting amounts for a particular user follows Benfords Law within certain confidence level.',
          order: 0,
          assignmentPriority: 0,
          ruleType: 'Dsl',
          status: 'Activated',
          active: 'Enable',
          explicit: false,
          actionCode: '01',
          actionName: 'ACCEPTED',
          createdBy: 'admin',
          createdAt: '2019-10-11 15:08:31',
          updatedBy: '',
          isMerchantSpecific: true,
          lowLevelOutcome: 'ACCEPTED',
          medLevelOutcome: 'ACCEPTED',
          highLevelOutcome: 'REJECTED',
          alertCategoryId: 1,
          groupId: 1
        }
      ],
      txnAmount: '50000',
      isOnUs: '1, 0'
    };

    const expectedActions = [
      { type: types.ON_TEST_SANDBOX_RULES_LOADING },
      {
        type: types.ON_TEST_SANDBOX_RULES_SUCCESS,
        response: responses.sandboxing.testingMsg,
        ruleName: 'BenfordLawCheck'
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onTestSandboxRules(formData, 'BenfordLawCheck')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Sandbox Test Status', () => {
    const expectedActions = [
      {
        type: types.ON_FETCH_SANDBOX_STATUS_SUCCESS,
        response: responses.sandboxing.testing
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSandboxTestStatus('BenfordLawCheck', 1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Sandbox Violation Details', () => {
    let formData = { date: '2023-07-20', pageNo: 1, pageSize: 5 };
    const expectedActions = [
      { type: types.ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING },
      {
        type: types.ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS,
        response: responses.sandboxing.violationDetails,
        date: '2023-07-20'
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSandboxViolationDetails(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Sandbox History', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SANDBOX_HISTORY_LOADING },
      {
        type: types.ON_FETCH_SANDBOX_HISTORY_SUCCESS,
        response: responses.sandboxing.testHistory
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSandboxHistory()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
