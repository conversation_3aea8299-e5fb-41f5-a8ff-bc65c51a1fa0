'use strict';
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Button, Form } from 'reactstrap';
import ActiveList from 'containers/prefilters/specializedLists/ActiveListContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faSpinner, faTrash, faPencil, faList } from '@fortawesome/free-solid-svg-icons';
import ModalContainer from 'components/common/ModalContainer';
import ReactTable from 'react-table';
import ConfirmAlert from 'components/common/ConfirmAlert';

const SpecializedLists = ({ actions, toggle, prefiltersList, toggleActions, role }) => {
  const [prefilterId, setPrefilterId] = useState('');
  const [currentPrefilterList, setCurrentPrefilterList] = useState('');
  const [listName, setListName] = useState('');

  const [newListName, setNewListName] = useState('');
  const [deleteMode, setDeleteMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedList, setSelectedList] = useState({});
  const [showAllLists, setShowAllLists] = useState(false);

  useEffect(() => {
    actions.onFetchSpecializedListTypes();
  }, []);

  const handleSelectPrefilterList = (event, prefilterLists) => {
    const target = event.target;
    let currentFilterObject = _.filter(prefilterLists, (o) => {
      return o.prefilterId == target.value;
    });

    setPrefilterId(target.value);
    setCurrentPrefilterList(currentFilterObject[0]);
  };

  const resetForm = () => {
    setListName('');
    setNewListName('');
    setEditMode(false);
    setSelectedList({});
  };
  const setUpdatedValue = (data) => {
    setNewListName(data.prefilterValue);
    setSelectedList(data);
  };

  const handleToggleCreateListModal = (type, data) => {
    type == 'edit' ? setUpdatedValue(data) : resetForm();
    toggleActions.onToggleCreateListModal();
  };

  const handleCreateList = (e) => {
    e.preventDefault();
    let formData = {
      listName
    };

    editMode
      ? actions.onUpdateList({
          listName: selectedList.prefilterValue,
          newListName
        })
      : actions.onCreateNewList(formData);
  };

  let prefilterLists = prefiltersList.specializedListTypes.data.map((listType) => {
    return {
      prefilterName: `${listType.listName} List`,
      prefilterId: listType.id,
      prefilterValue: listType.listName
    };
  });

  let prefilterOptions = prefilterLists.map((prefilter) => (
    <option key={prefilter.prefilterId} value={prefilter.prefilterId}>
      {prefilter.prefilterName}
    </option>
  ));

  const listTableHeader = [
    {
      Header: 'Actions',
      maxWidth: 100,
      filterable: false,
      sortable: false,
      show: role == 'checker',
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="Edit"
            className="me-2"
            onClick={() => {
              handleToggleCreateListModal('edit', row.original);
              setEditMode(true);
            }}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            onClick={() => {
              setSelectedList(row.original);
              setDeleteMode(true);
            }}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    { Header: 'List Name', accessor: 'prefilterName' }
  ];

  function cancelDelete() {
    setDeleteMode(false);
    setSelectedList({});
  }

  function confirmDelete() {
    actions.onDeleteList(selectedList.prefilterValue);
    setSelectedList({});
    setDeleteMode(false);
  }

  return (
    <>
      {prefiltersList.specializedListTypes.data.loader ? (
        <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
      ) : prefiltersList.specializedListTypes.data.error ? (
        <div className="no-data-div">{prefiltersList.specializedListTypes.data.errorMessage}</div>
      ) : (
        <div className={`${toggle.theme} prefilter-container`}>
          <div>
            <FormGroup className="select-pefilter specialized-list">
              <Label for="prefilterId">Select Specialized List</Label>
              <Input
                type="select"
                id="prefilterId"
                name="prefilterId"
                className="prefilter-list"
                value={prefilterId}
                onChange={(e) => handleSelectPrefilterList(e, prefilterLists)}
                required>
                {currentPrefilterList == '' && <option value="">-- select --</option>}
                {prefilterOptions}
              </Input>
              {role == 'checker' && (
                <Button
                  className="add-list"
                  size="sm"
                  color="primary"
                  onClick={() => handleToggleCreateListModal('add')}
                  title="Create New List">
                  <FontAwesomeIcon icon={faPlus} className="list-icon" />
                </Button>
              )}
              {role == 'checker' && (
                <Button
                  className="add-list"
                  size="sm"
                  outline
                  color="primary"
                  onClick={() => setShowAllLists(true)}
                  title="Show All Lists">
                  <FontAwesomeIcon icon={faList} className="list-icon" />
                </Button>
              )}
            </FormGroup>

            <ModalContainer
              theme={toggle.theme}
              isOpen={toggle.prefiltersListModal.createListModal}
              header={editMode ? 'Update list' : 'Create new list'}
              size="md"
              toggle={handleToggleCreateListModal}>
              <Form onSubmit={handleCreateList}>
                <FormGroup>
                  <Label for="listName">List Name</Label>
                  <Input
                    type="text"
                    name="listName"
                    id="listName"
                    // pattern="[A-Za-z]{1,20}"
                    onChange={(event) =>
                      editMode
                        ? setNewListName(event.target.value)
                        : setListName(event.target.value)
                    }
                    value={editMode ? newListName : listName}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Button size="sm" color="primary" className="d-flex ms-auto">
                    Submit
                  </Button>
                </FormGroup>
              </Form>
            </ModalContainer>

            <ModalContainer
              theme={toggle.theme}
              isOpen={showAllLists}
              header={'Specialized Lists'}
              size="md"
              toggle={() => setShowAllLists(false)}>
              <ReactTable
                filterable={true}
                columns={listTableHeader}
                data={prefilterLists}
                defaultPageSize={5}
                minRows={2}
                showPaginationTop={true}
                showPaginationBottom={false}
                className={'-highlight  -striped mb-3'}
                defaultFilterMethod={(filter, row) =>
                  row[filter.id] &&
                  _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
                }
                noDataText="No shift found"
                pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              />
            </ModalContainer>
            <ConfirmAlert
              theme={toggle.theme}
              confirmAlertModal={deleteMode}
              toggleConfirmAlertModal={cancelDelete}
              confirmationAction={confirmDelete}
              confirmAlertTitle={`Are you sure you want to delete this list ?`}
            />

            {currentPrefilterList !== '' && (
              <ActiveList
                currentPrefilterList={currentPrefilterList}
                listType={'specializedList'}
                role={role}
              />
            )}
          </div>
        </div>
      )}
    </>
  );
};

SpecializedLists.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  role: PropTypes.string.isRequired
};

export default SpecializedLists;
