import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onToggleStatusLogModal } from 'actions/toggleActions';
import { onFetchCustomerDetailsWithAllAccounts, onFetchFacctumDetails } from 'actions/udsActions';
import CustomerInfoCard from 'components/common/CustomerInfoCard';

const mapStateToProps = (state) => ({
  data: state.uds.customer,
  facctumData: state.uds.facctumData,
  demographicDetails: state.demographicDetails,
  isFacctum: state.user.configurations.facctum
});

const mapDispatchToProps = (dispatch) => ({
  fetchDetails: bindActionCreators(onFetchCustomerDetailsWithAllAccounts, dispatch),
  toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
  fetchFacctumDetails: bindActionCreators(onFetchFacctumDetails, dispatch)
});

const CustomerInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(CustomerInfoCard);

export default CustomerInfoCardContainer;
