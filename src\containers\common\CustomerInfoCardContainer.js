import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCustomerDetailsWithAllAccounts, onFetchFacctumDetails } from 'actions/udsActions';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import CustomerInfoCard from 'components/common/CustomerInfoCard';

const mapStateToProps = (state) => {
  return {
    data: state.uds.customer,
    facctumData: state.uds.facctumData,
    demographicDetails: state.demographicDetails,
    isFacctum: state.user.configurations.facctum
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchDetails: bindActionCreators(onFetchCustomerDetailsWithAllAccounts, dispatch),
    toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
    fetchFacctumDetails: bindActionCreators(onFetchFacctumDetails, dispatch)
  };
};

const CustomerInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(CustomerInfoCard);

export default CustomerInfoCardContainer;
