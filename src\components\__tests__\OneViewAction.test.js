import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import OneViewActions from '../caseReview/OneViewActions';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';

jest.mock('components/common/ConfirmAlert', () =>
  jest.fn(({ toggleConfirmAlertModal, confirmationAction, confirmAlertTitle }) => (
    <div>
      <button onClick={toggleConfirmAlertModal}>Mock ConfirmAlert</button>
      <button onClick={confirmationAction}>Mock handleSelectItem</button>
      <p>{confirmAlertTitle}</p>
    </div>
  ))
);

jest.mock('containers/caseReview/NotificationButtonsContainer', () =>
  jest.fn(() => (
    <div>
      <span>Block request sent for mockPayee</span>
      <span>Block request sent for mockPayer</span>
    </div>
  ))
);

const defaultProps = {
  theme: 'dark',
  userId: 'user123',
  userName: '<PERSON>',
  stages: [{ id: 1, stageName: 'reviewer' }],
  parkCase: jest.fn(),
  fetchCase: jest.fn(),
  addVerdict: jest.fn(),
  fetchStages: jest.fn(),
  assignNewCase: jest.fn(),
  fetchChannelCounterpartyId: jest.fn(),
  txnDetails: {
    masterFields: { channelName: 'mockChannel' },
    payeeAccount: {},
    payerAccount: {},
    deviceInfo: {}
  },
  oneViewData: {
    caseRefNo: 'case123',
    investigationStatus: 'Open',
    assignedTo: 'John Doe',
    channel: 'mockChannel',
    entityId: 'entity123'
  },
  prefiltersList: {
    specializedListTypes: { data: [{ listName: 'Blocked', groupName: 'specializedList' }] }
  },
  prefilterActions: {
    onFetchSpecializedListTypes: jest.fn(),
    onDeleteSpecializedListItem: jest.fn(),
    onAddSingleItemToSpecializedList: jest.fn()
  },
  channelCounterpartyId: {
    list: [
      {
        channel: 'mockChannel',
        payeeInfo: { payeeId: 'payee123', payeeAttribute: 'mockPayee' },
        payerInfo: { payerId: 'payee123', payerAttribute: 'mockPayer' }
      }
    ],
    loader: false,
    error: false
  },
  txnId: undefined
};

describe('OneViewActions Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component', () => {
    render(<OneViewActions {...defaultProps} />);
    expect(screen.getByText(/Park Case/)).toBeInTheDocument();
  });

  it('fetches specialized list types if not present', () => {
    const prefiltersList = { specializedListTypes: { data: [] } };
    render(<OneViewActions {...defaultProps} prefiltersList={prefiltersList} />);
    expect(defaultProps.prefilterActions.onFetchSpecializedListTypes).toHaveBeenCalled();
  });

  it('fetches channel counterparty id if list is empty', () => {
    const channelCounterpartyId = { list: [], loader: false, error: false };
    render(<OneViewActions {...defaultProps} channelCounterpartyId={channelCounterpartyId} />);
    expect(defaultProps.fetchChannelCounterpartyId).toHaveBeenCalled();
  });

  it('fetches stages if not present', () => {
    const stages = [];
    render(<OneViewActions {...defaultProps} stages={stages} />);
    expect(defaultProps.fetchStages).toHaveBeenCalled();
  });

  it('toggles block modal on button click', () => {
    render(<OneViewActions {...defaultProps} />);

    fireEvent.click(screen.getByText(/Mock ConfirmAlert/));
    expect(
      screen.getByText(/Are you sure you want to add undefined to Blocked list ?/)
    ).toBeInTheDocument();

    fireEvent.click(screen.getByText(/Mock handleSelectItem/));
  });

  it('assigns case to self on button click', () => {
    const oneViewData = { ...defaultProps.oneViewData, assignedTo: 'Someone Else' };
    render(<OneViewActions {...defaultProps} oneViewData={oneViewData} />);
    fireEvent.click(screen.getByText(/Self Assign/));
    expect(defaultProps.assignNewCase).toHaveBeenCalledWith(
      {
        stageId: 1,
        caseRefNo: 'case123',
        assignedTo: 'user123',
        assignedToName: 'John Doe'
      },
      false,
      'mockChannel'
    );
  });

  it('calls parkCase on button click if assigned to user', () => {
    render(<OneViewActions {...defaultProps} />);
    fireEvent.click(screen.getByText(/Park Case/));
    expect(defaultProps.parkCase).toHaveBeenCalledWith(
      {
        stageId: 1,
        caseRefNo: 'case123',
        reason: 'Called the customer - Customer not available'
      },
      {},
      'frm'
    );
  });

  it('does not call parkCase on button click if not assigned to user', () => {
    const oneViewData = { ...defaultProps.oneViewData, assignedTo: 'Someone Else' };
    render(<OneViewActions {...defaultProps} oneViewData={oneViewData} />);
    fireEvent.click(screen.getByTestId('park-case-btn'));
    expect(defaultProps.parkCase).not.toHaveBeenCalled();
  });

  it('calls addVerdict on Fraud button click', () => {
    render(<OneViewActions {...defaultProps} />);
    fireEvent.click(screen.getByTestId('fraud-btn'));
    expect(defaultProps.addVerdict).toHaveBeenCalledWith('Fraud');
  });

  it('calls addVerdict on Not Fraud button click', () => {
    render(<OneViewActions {...defaultProps} />);
    fireEvent.click(screen.getByTestId('not-fraud-btn'));
    expect(defaultProps.addVerdict).toHaveBeenCalledWith('Not Fraud');
  });

  it('calls fetchCaseOrRedirect on Next button click', () => {
    const oneViewData = { ...defaultProps.oneViewData, assignedTo: 'Someone Else' };
    render(<OneViewActions {...defaultProps} oneViewData={oneViewData} />);
    fireEvent.click(screen.getByTestId('next-btn'));
    expect(defaultProps.fetchCase).toHaveBeenCalled();
  });

  it('redirects to /review if txnId is present', () => {
    const history = createMemoryHistory();
    history.location.pathname = '/review';

    render(
      <Router history={history}>
        <OneViewActions {...defaultProps} txnId={'123'} />
      </Router>
    );
    fireEvent.click(screen.getByTestId('next-btn'));
    expect(history.location.pathname).toBe('/review');
  });

  it('renders block and unblock buttons for payee and payer correctly', () => {
    const txnDetails = {
      payeeAccount: {
        mockChannel: { listTypeData: ['Blocked'], categoryName: 'Payee' },
        payee123: { listTypeData: ['Blocked'], categoryName: 'Payee' }
      },
      deviceInfo: {
        mockChannel: { listTypeData: ['Blocked'], categoryName: 'Payee' },
        payee123: { listTypeData: ['Blocked'], categoryName: 'Payee' }
      },
      payerAccount: {
        mockChannel: { listTypeData: ['Blocked'], categoryName: 'Payer' },
        payee123: { listTypeData: ['Blocked'], categoryName: 'Payee' }
      },
      masterFields: {
        channelName: 'mockChannel'
      }
    };

    const blockedList = [{ listName: 'Blocked', groupName: 'specializedList' }];
    const oneViewData = { investigationStatus: 'Open', assignedTo: 'John Doe' };
    const stages = [{ id: 'stage123', stageName: 'Reviewer' }];
    const prefiltersList = { specializedListTypes: { data: blockedList } };

    render(
      <OneViewActions
        {...defaultProps}
        txnDetails={txnDetails}
        oneViewData={oneViewData}
        prefiltersList={prefiltersList}
        stage={stages}
      />
    );

    expect(screen.getByText('Unblock mockPayee')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Unblock mockPayee'));
  });

  it('renders block and unblock buttons for payee and payer correctly', () => {
    const txnDetails = {
      payeeAccount: {
        mockChannel: { listTypeData: ['Unblock'], categoryName: 'Payee' },
        payee123: { listTypeData: ['Unblock'], categoryName: 'Payee' },
        payer123: { listTypeData: ['Unblock'], categoryName: 'Payee' }
      },
      deviceInfo: {
        mockChannel: { listTypeData: ['Unblock'], categoryName: 'Payee' },
        payee123: { listTypeData: ['Unblock'], categoryName: 'Payee' }
      },
      payerAccount: {
        mockChannel: { listTypeData: ['Unblock'], categoryName: 'Payer' },
        payee123: { listTypeData: ['Unblock'], categoryName: 'Payee' }
      },
      payerInfo: {},
      masterFields: {
        channelName: 'mockChannel'
      }
    };

    const blockedList = [{ listName: 'Blocked', groupName: 'specializedList' }];
    const oneViewData = { investigationStatus: 'Open', assignedTo: 'John Doe' };
    const stages = [{ id: 'stage123', stageName: 'Reviewer' }];
    const prefiltersList = { specializedListTypes: { data: blockedList } };

    render(
      <OneViewActions
        {...defaultProps}
        txnDetails={txnDetails}
        oneViewData={oneViewData}
        prefiltersList={prefiltersList}
        stage={stages}
      />
    );

    expect(screen.getByText('Block mockPayee')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Block mockPayee'));
    expect(screen.getByText('Block request sent for mockPayee')).toBeInTheDocument();

    expect(screen.getByText('Block mockPayer')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Block mockPayer'));
    expect(screen.getByText('Block request sent for mockPayer')).toBeInTheDocument();

    expect(screen.getByTestId('permanent-block-dropdown-item-payeeId')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('permanent-block-dropdown-item-payeeId'));

    expect(screen.getByTestId('block-for-dropdown-payeeId')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('block-for-dropdown-payeeId'));

    expect(screen.getByTestId('block-for-dropdown-payerId')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('block-for-dropdown-payerId'));

    expect(screen.getByTestId('permanent-block-dropdown-item-payerId')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('permanent-block-dropdown-item-payerId'));
  });
});
