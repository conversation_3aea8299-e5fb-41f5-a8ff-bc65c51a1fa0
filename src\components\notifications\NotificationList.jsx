import React from 'react';
import PropTypes from 'prop-types';
import { ListGroup } from 'reactstrap';
import NotificationListItem from './NotificationListItem';

function NotificationList({ list }) {
  return (
    <ListGroup flush>
      {list.map((notification) => (
        <NotificationListItem notification={notification} key={notification.id} />
      ))}
    </ListGroup>
  );
}

NotificationList.propTypes = {
  list: PropTypes.array.isRequired
};

export default NotificationList;
