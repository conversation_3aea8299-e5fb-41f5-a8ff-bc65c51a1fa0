import { has, isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import ReactTable from 'react-table';
import { Label, Row, Col, Card } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

const STRReport = ({ caseRefNo, strReportDetails, strReportMasters, getMasters, getSTRReport }) => {
  const { data, loader, error } = strReportDetails;
  const getDataFromReport = (key) =>
    !loader && !error && !isEmpty(data) && has(data, key) && data[key];

  useEffect(() => {
    isEmpty(strReportMasters?.data) && getMasters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    caseRefNo && getSTRReport(caseRefNo);
  }, [caseRefNo, getSTRReport]);

  const formStructuredInput = (id, label, masterField) => (
    <Row className="ms-5 me-5">
      <Col md="6">
        <Label for={id}>{label}</Label>
      </Col>
      <Col md="6">
        {masterField
          ? masterField.find((d) => d.key === getDataFromReport(id))?.value
          : getDataFromReport(id)}
      </Col>
    </Row>
  );

  const transactionTableHeaders = [
    { Header: 'Type of Transaction', accessor: 'typeOfTransaction' },
    { Header: 'Total Debit', accessor: 'totalDebit' },
    { Header: 'Total Credit', accessor: 'totalCredit' },
    { Header: 'Total Transaction Count', accessor: 'totalTxnCount' },
    { Header: 'Amount (in INR)', accessor: 'amount' }
  ];

  const accountTableHeaders = [
    { Header: 'Account Number', accessor: 'accountNo' },
    {
      Header: 'Total debit in last 12 months',
      columns: [
        { Header: 'Count', accessor: 'noOFDebitTxns' },
        { Header: 'Amount', accessor: 'debitAmount' }
      ]
    },
    {
      Header: 'Total credit in last 12 months',
      columns: [
        { Header: 'Count', accessor: 'noOfCreditTxns' },
        { Header: 'Amount', accessor: 'creditAmount' }
      ]
    },
    {
      Header: 'Total cash transactions in last 12 months',
      columns: [
        { Header: 'Count', accessor: 'noOfCashTxns' },
        { Header: 'Deposit', accessor: 'depositAmount' },
        { Header: 'Withdrawal', accessor: 'withdrawalAmount' }
      ]
    }
  ];

  return (
    <CardContainer title="Report">
      <Card className="p-2">
        <b className="mb-2">KYC Details</b>
        {formStructuredInput('name', 'Name')}
        {formStructuredInput('address', 'Address')}
        {formStructuredInput('pan', 'PAN')}
        {formStructuredInput('uniqueId', 'Unique ID')}
        {formStructuredInput('risk', 'Risk')}
        {formStructuredInput('profession', 'Profession / LOB')}
        {formStructuredInput('income', 'Income (per year)')}
        {formStructuredInput('roleMainAssociate', 'Role (Main person / associate)')}
      </Card>
      <Card className="p-2">
        <b className="mb-2">Transaction Details</b>
        <ReactTable
          filterable={false}
          columns={transactionTableHeaders}
          data={
            !isEmpty(strReportDetails?.data?.txnDetails) ? strReportDetails?.data?.txnDetails : []
          }
          defaultPageSize={20}
          minRows={strReportDetails?.data?.txnDetails?.length || 2}
          showPaginationTop={false}
          showPaginationBottom={false}
          className="-highlight  -striped"
        />
      </Card>
      <Card className="p-2">
        <b className="mb-2">Account Details</b>
        <ReactTable
          filterable={false}
          columns={accountTableHeaders}
          data={!isEmpty(strReportDetails?.data) ? [strReportDetails?.data] : []}
          defaultPageSize={20}
          minRows={2}
          showPaginationTop={false}
          showPaginationBottom={false}
          className="-highlight  -striped"
        />
      </Card>
      <Card className="p-2">
        <b className="mb-2">Suspicion Details</b>
        <div>
          {formStructuredInput('sourceOfFunds', 'Source of Funds')}
          {formStructuredInput('destinationOfFunds', 'Destination of Funds')}
          {formStructuredInput('suspicionDueTo', 'Suspicion due to ')}
          {formStructuredInput('sourceOfAlert', 'Source of Alert')}
          {formStructuredInput('redFlagIndicator', 'Red Flag Indicators')}
          {getDataFromReport('redFlagIndicator') === 'Others' &&
            formStructuredInput('otherRedFlagIndicator', 'Other Red Flag Indicators')}
          {formStructuredInput('typeOfSuspicion', 'Type of Suspicion')}
          {formStructuredInput('narration', 'Narration')}
          {formStructuredInput('files', 'Additional Documents')}
        </div>
      </Card>
    </CardContainer>
  );
};

STRReport.propTypes = {
  caseRefNo: PropTypes.string.isRequired,
  getMasters: PropTypes.func.isRequired,
  getSTRReport: PropTypes.func.isRequired,
  strReportDetails: PropTypes.object.isRequired,
  strReportMasters: PropTypes.object.isRequired
};

export default STRReport;
