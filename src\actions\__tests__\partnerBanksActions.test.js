import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/partnerBanksActions';
import responses from 'mocks/responses';

const mockedStore = {
  partnerBanks: {}
};

describe('partner Banks actions', () => {
  it('should Fetch Reviewer Case', () => {
    const expectedActions = [
      { type: types.ON_FETCH_PARTNER_BANK_LIST_LOADING },
      {
        type: types.ON_FETCH_PARTNER_BANK_LIST_SUCCESS,
        response: responses.partnerBanks
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchPartnerBanksList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Add Partner Bank', () => {
    const formData = {
      bankData: {
        partnerName: 'YES BANK'
      }
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_PARTNER_BANK_LIST_LOADING },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Partner bank added successfully' }
      },
      {
        type: 'ON_TOGGLE_ADD_BANK_MODAL'
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onAddPartnerBank(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
