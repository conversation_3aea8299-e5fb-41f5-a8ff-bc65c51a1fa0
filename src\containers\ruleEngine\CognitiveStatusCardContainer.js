import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import CognitiveStatusCard from 'components/ruleEngine/CognitiveStatusCard';
import * as cognitiveStatusAction from 'actions/cognitiveStatusAction';
const mapStateToProps = (state) => {
  return {
    cognitiveStatus: state.cognitiveStatus
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    cognitiveStatusAction: bindActionCreators(cognitiveStatusAction, dispatch)
  };
};
const CognitiveStatusCardContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CognitiveStatusCard);

export default CognitiveStatusCardContainer;
