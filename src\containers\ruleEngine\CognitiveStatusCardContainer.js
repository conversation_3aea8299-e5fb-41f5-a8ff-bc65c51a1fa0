import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as cognitiveStatusAction from 'actions/cognitiveStatusAction';
import CognitiveStatusCard from 'components/ruleEngine/CognitiveStatusCard';
const mapStateToProps = (state) => ({
  cognitiveStatus: state.cognitiveStatus
});

const mapDispatchToProps = (dispatch) => ({
  cognitiveStatusAction: bindActionCreators(cognitiveStatusAction, dispatch)
});
const CognitiveStatusCardContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CognitiveStatusCard);

export default CognitiveStatusCardContainer;
