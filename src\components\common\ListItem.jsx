import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useCallback } from 'react';
import { ListGroupItem } from 'reactstrap';

const ListItem = React.memo(({ icon, item, onClick }) => {
  const handleClick = useCallback(() => onClick(item.name), [item.name, onClick]);

  return (
    <ListGroupItem onClick={handleClick}>
      {icon && <FontAwesomeIcon icon={icon} className="me-1" />}
      {item.name}
      {item?.description && (
        <p>
          <small>{item.description}</small>
        </p>
      )}
    </ListGroupItem>
  );
});

ListItem.propTypes = {
  icon: PropTypes.object,
  item: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired
};

export default ListItem;
