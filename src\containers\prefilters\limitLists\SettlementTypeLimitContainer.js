import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SettlementTypeLimit from 'components/prefilters/limitLists/SettlementTypeLimit';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    prefiltersList: state.prefiltersList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const BlockedListContainer = connect(mapStateToProps, mapDispatchToProps)(SettlementTypeLimit);

export default BlockedListContainer;
