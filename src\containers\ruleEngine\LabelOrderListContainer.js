import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onUpdateLabelOrder } from 'actions/ruleCreationActions';
import LabelOrderList from 'components/ruleEngine/LabelOrderList';

const mapStateToProps = (state) => {
  return {
    labelList: state.ruleCreation.ruleLabels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    updateLabelOrderList: bindActionCreators(onUpdateLabelOrder, dispatch)
  };
};

const LabelOrderListContainer = connect(mapStateToProps, mapDispatchToProps)(LabelOrderList);

export default LabelOrderListContainer;
