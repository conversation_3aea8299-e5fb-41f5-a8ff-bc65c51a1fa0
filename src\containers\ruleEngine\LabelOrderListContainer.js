import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onUpdateLabelOrder } from 'actions/ruleCreationActions';
import LabelOrderList from 'components/ruleEngine/LabelOrderList';

const mapStateToProps = (state) => ({
  labelList: state.ruleCreation.ruleLabels
});

const mapDispatchToProps = (dispatch) => ({
  updateLabelOrderList: bindActionCreators(onUpdateLabelOrder, dispatch)
});

const LabelOrderListContainer = connect(mapStateToProps, mapDispatchToProps)(LabelOrderList);

export default LabelOrderListContainer;
