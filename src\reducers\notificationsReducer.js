import objectAssign from 'object-assign';

import {
  ON_FETCH_NOTIFICATIONS_LIST_LOADING,
  ON_FETCH_NOTIFICATIONS_LIST_SUCCESS,
  ON_FETCH_NOTIFICATIONS_LIST_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function notificationsReducer(state = initialState.notifications, action) {
  switch (action.type) {
    case ON_FETCH_NOTIFICATIONS_LIST_LOADING:
      return objectAssign({}, state, {
        loader: true,
        error: false,
        errorMessage: ''
      });
    case ON_FETCH_NOTIFICATIONS_LIST_SUCCESS:
      return objectAssign({}, state, {
        loader: false,
        list: action.response
      });
    case ON_FETCH_NOTIFICATIONS_LIST_FAILURE:
      return objectAssign({}, state, {
        loader: false,
        error: true,
        errorMessage: action.response.message
      });
    default:
      return state;
  }
}
