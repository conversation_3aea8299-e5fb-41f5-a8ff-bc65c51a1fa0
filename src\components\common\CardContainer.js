import PropTypes from 'prop-types';
import React from 'react';
import { Card, CardBody, CardHeader, CardTitle } from 'reactstrap';

const CardContainer = ({
  children,
  title = '',
  action = undefined,
  kpi = undefined,
  withAddToList = false,
  color = 'default',
  outline = false,
  className = ''
}) => (
  <Card color={color} className={`card-main ${className}`} {...(outline && { outline: true })}>
    <CardHeader>
      <CardTitle className="d-flex align-items-center justify-content-between flex-wrap ">
        {title}
        {kpi}
        {action}
      </CardTitle>
    </CardHeader>
    <CardBody className="p-3 card-container">
      <div className={`${withAddToList ? 'card-content card-with-overflow' : 'card-content'}`}>
        {children}
      </div>
    </CardBody>
  </Card>
);

CardContainer.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  action: PropTypes.object,
  kpi: PropTypes.object,
  withAddToList: PropTypes.bool,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]).isRequired,
  color: PropTypes.string,
  outline: PropTypes.bool,
  className: PropTypes.string
};

export default CardContainer;
