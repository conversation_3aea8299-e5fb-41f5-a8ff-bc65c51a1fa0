import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as userActions from 'actions/userManagementActions';
import { onToggleUserCaseCriteriaModal } from 'actions/toggleActions';
import CaseAssignmentModal from 'components/userManagement/CaseAssignmentModal';
import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import { onFetchAlertCategories } from 'actions/ruleCreationActions';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    attributeList: state.user.attributesList,
    showCaseCriteriaModal: state.toggle.userCaseCriteriaModal,
    ruleList: state.ruleConfigurator.productionRules.list.frm,
    categoryList: state.ruleCreation.alertCategories
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    userActions: bindActionCreators(userActions, dispatch),
    toggleCaseCriteriaModal: bindActionCreators(onToggleUserCaseCriteriaModal, dispatch),
    fetchRules: bindActionCreators(onFetchRulesList, dispatch),
    fetchCategory: bindActionCreators(onFetchAlertCategories, dispatch)
  };
};

const CaseAssignmentModalContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CaseAssignmentModal);

export default CaseAssignmentModalContainer;
