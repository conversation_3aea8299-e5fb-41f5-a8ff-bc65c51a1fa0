import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onParkCase } from 'actions/caseAssignmentActions';
import ParkCaseModal from 'components/common/ParkCaseModal';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  parkCase: bindActionCreators(onParkCase, dispatch)
});

const ParkCaseModalContainer = connect(mapStateToProps, mapDispatchToProps)(ParkCaseModal);

export default ParkCaseModalContainer;
