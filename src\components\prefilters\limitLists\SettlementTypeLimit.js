'use strict';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencil, faTrash, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { ButtonGroup, Button, FormGroup, Label, Input, TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import ConfirmAlert from 'components/common/ConfirmAlert';
import CardContainer from 'components/common/CardContainer';
import ExcelDownload from 'components/common/ExcelDownload';
import ModalContainer from 'components/common/ModalContainer';
import { MAX_AMOUNT } from 'constants/applicationConstants';

const SettlementTypeLimit = ({
  toggle,
  toggleActions,
  actions,
  prefiltersList,
  currentPrefilterList,
  listType,
  role
}) => {
  const [settlementType, setSettlementType] = useState('');
  const [txnAmountLimit, setTxnAmountLimit] = useState('');
  const [dailyAmountLimit, setDailyAmountLimit] = useState('');
  const [dailyCountLimit, setDailyCountLimit] = useState('');
  const [monthlyAmountLimit, setMonthlyAmountLimit] = useState('');
  const [monthlyCountLimit, setMonthlyCountLimit] = useState('');
  const [fileData, setFileData] = useState('');
  const [singleUploadLoading, setSingleUploadLoading] = useState(false);
  const [isEditmode, setIsEditmode] = useState(false);

  useEffect(() => {
    actions.onFetchLimitList(currentPrefilterList);
  }, []);

  const removeFilter = () => {
    let formData = {
      type: settlementType,
      keyName: 'settlementType'
    };

    actions.onDeleteLimitListItem(formData, currentPrefilterList, listType);
  };

  const toggleConfirmAlertModal = (settlementType) => {
    settlementType && setSettlementType(settlementType);
    toggleActions.onToggleConfirmAlertModal(listType);
  };

  const setUpdatedValue = (filterData) => {
    setSettlementType(filterData.settlementType);
    setTxnAmountLimit(filterData.txnAmountLimit);
    setMonthlyAmountLimit(filterData.monthlyAmountLimit);
    setMonthlyCountLimit(filterData.monthlyCountLimit);
    setDailyCountLimit(filterData.dailyCountLimit);
    setDailyAmountLimit(filterData.dailyAmountLimit);
    setIsEditmode(true);
  };

  const clearUpdatedValue = () => {
    setSettlementType('');
    setTxnAmountLimit('');
    setMonthlyAmountLimit('');
    setMonthlyCountLimit('');
    setDailyCountLimit('');
    setDailyAmountLimit('');
    setIsEditmode(false);
  };

  const toggleFilterModal = (type, filterData) => {
    type == 'edit' ? setUpdatedValue(filterData) : clearUpdatedValue();

    toggleActions.onTogglePrefiltersListModal(listType);
  };

  const submitSingleEntry = (e) => {
    e.preventDefault();
    setSingleUploadLoading(true);
    let formData = {
      settlementType,
      txnAmountLimit: parseFloat(txnAmountLimit),
      monthlyAmountLimit: parseFloat(monthlyAmountLimit),
      monthlyCountLimit: parseInt(monthlyCountLimit),
      dailyAmountLimit: parseFloat(dailyAmountLimit),
      dailyCountLimit: parseInt(dailyCountLimit)
    };

    if (isEditmode) {
      actions.onUpdateLimitList(formData, currentPrefilterList, listType);
    } else {
      actions.onAddSingleItemToLimitList(formData, currentPrefilterList, listType);
    }

    setSingleUploadLoading(false);
    setSettlementType('');
    setTxnAmountLimit('');
    setMonthlyAmountLimit('');
    setMonthlyCountLimit('');
    setDailyAmountLimit('');
    setDailyCountLimit('');
  };

  const addInBulkEntity = (e) => {
    e.preventDefault();
    let formData = {
      [currentPrefilterList.prefilterValue]: fileData
    };
    actions.onAddItemsInBulkToLimitList(formData, currentPrefilterList, listType);
    setFileData('');
  };

  const header = [
    {
      Header: 'Actions',
      minWidth: 50,
      filterable: false,
      sortable: false,
      show: role == 'checker',
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="edit"
            onClick={() => toggleFilterModal('edit', row.original)}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            onClick={() => toggleConfirmAlertModal(row.original.settlementType)}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    { Header: 'Settlement Type', accessor: 'settlementType' },
    { Header: 'Daily Count Limit', accessor: 'dailyCountLimit' },
    { Header: 'Daily Amount Limit', accessor: 'dailyAmountLimit' },
    { Header: 'Monthly Count Limit', accessor: 'monthlyCountLimit' },
    { Header: 'Monthly Amount Limit', accessor: 'monthlyAmountLimit' },
    { Header: 'Transaction Amount Limit', accessor: 'txnAmountLimit' }
  ];

  let excelHeaders = [
    { label: 'Settlement Type', value: 'settlementType' },
    { label: 'Daily Count Limit', value: 'dailyCountLimit' },
    { label: 'Daily Amount Limit', value: 'dailyAmountLimit' },
    { label: 'Monthly Count Limit', value: 'monthlyCountLimit' },
    { label: 'Monthly Amount Limit', value: 'monthlyAmountLimit' },
    { label: 'Transaction Amount Limit', value: 'txnAmountLimit' }
  ];

  let action = (
    <ButtonGroup>
      <ExcelDownload
        data={prefiltersList.limitList.data}
        sheetName="SettlementTypeLimit"
        headers={excelHeaders}
      />
      {role == 'checker' && (
        <Button size="sm" color="primary" className="ms-1" onClick={() => toggleFilterModal('add')}>
          {`Add ${currentPrefilterList.prefilterName}`}
        </Button>
      )}
    </ButtonGroup>
  );

  return (
    <div>
      <CardContainer title={currentPrefilterList.prefilterName} action={action}>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] &&
            row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          columns={header}
          data={prefiltersList.limitList.data}
          noDataText="No data found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={3}
          className={'-highlight  -striped'}
        />
      </CardContainer>

      <ModalContainer
        size="md"
        theme={toggle.theme}
        isOpen={toggle.prefiltersListModal[listType]}
        toggle={() => toggleFilterModal('close')}
        header={`Add ${currentPrefilterList.prefilterName}`}>
        <Tabs tabNames={isEditmode ? ['Single'] : ['Single', 'Bulk']}>
          <TabPane tabId={0}>
            <form onSubmit={submitSingleEntry}>
              <FormGroup>
                <Label for="settlementType">Settlement Type</Label>
                <Input
                  type="text"
                  id="settlementType"
                  name="settlementType"
                  value={settlementType}
                  onChange={(event) => {
                    setSettlementType(event.target.value);
                  }}
                  required
                  readOnly={isEditmode}
                />
              </FormGroup>
              <FormGroup>
                <Label for="dailyCountLimit">Daily Count Limit</Label>
                <Input
                  type="number"
                  id="dailyCountLimit"
                  name="dailyCountLimit"
                  value={dailyCountLimit}
                  onChange={(event) => {
                    setDailyCountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  pattern="[0-9]*"
                />
              </FormGroup>
              <FormGroup>
                <Label for="dailyAmountLimit">Daily Amount Limit</Label>
                <Input
                  type="number"
                  id="dailyAmountLimit"
                  name="dailyAmountLimit"
                  value={dailyAmountLimit}
                  onChange={(event) => {
                    setDailyAmountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup>
                <Label for="monthlyCountLimit">Monthly Count Limit</Label>
                <Input
                  type="number"
                  id="monthlyCountLimit"
                  name="monthlyCountLimit"
                  value={monthlyCountLimit}
                  onChange={(event) => {
                    setMonthlyCountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  pattern="[0-9]*"
                />
              </FormGroup>
              <FormGroup>
                <Label for="monthlyAmountLimit">Monthly Amount Limit</Label>
                <Input
                  type="number"
                  id="monthlyAmountLimit"
                  name="monthlyAmountLimit"
                  value={monthlyAmountLimit}
                  onChange={(event) => {
                    setMonthlyAmountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup>
                <Label for="txnAmountLimit">Transaction Amount Limit</Label>
                <Input
                  type="number"
                  id="txnAmountLimit"
                  name="txnAmountLimit"
                  value={txnAmountLimit}
                  onChange={(event) => {
                    setTxnAmountLimit(event.target.value);
                  }}
                  required
                  min={0}
                  max={MAX_AMOUNT}
                  step={0.01}
                  pattern="^[0-9]*\.[0-9]{0,2}$"
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={singleUploadLoading}>
                  {singleUploadLoading ? (
                    <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
                  ) : (
                    'Save'
                  )}
                </Button>
              </FormGroup>
            </form>
          </TabPane>
          <TabPane tabId={1}>
            <form onSubmit={addInBulkEntity}>
              <FormGroup>
                <Input
                  name="fileUpload"
                  accept="text/csv, .csv"
                  type="file"
                  files={fileData}
                  onChange={(event) => setFileData(event.target.files[0])}
                  required
                />
              </FormGroup>
              <FormGroup className="d-flex justify-content-end">
                <Button size="sm" type="submit" color="success" disabled={toggle.uploadLoader}>
                  Upload
                </Button>
              </FormGroup>
            </form>
          </TabPane>
        </Tabs>
      </ModalContainer>

      <ConfirmAlert
        theme={toggle.theme}
        confirmAlertModal={toggle.confirmAlertModal[listType]}
        toggleConfirmAlertModal={toggleConfirmAlertModal}
        confirmationAction={removeFilter}
        confirmAlertTitle={'Are you sure you want to delete it ?'}
      />
    </div>
  );
};

SettlementTypeLimit.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  listType: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired
};

export default SettlementTypeLimit;
