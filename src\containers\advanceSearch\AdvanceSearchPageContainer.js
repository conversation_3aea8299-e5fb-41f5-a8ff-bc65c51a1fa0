import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onAdvanceSearchTxn } from 'actions/transactionHistorySearchActions';
import AdvanceSearchPage from 'components/advanceSearch/AdvanceSearchPage';

const mapStateToProps = (state) => {
  return {
    channels: state.auth.userCreds.channels,
    advanceSearchTxns: state.advanceSearchTxns,
    ruleNames: state.ruleConfigurator.ruleNames
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    advanceSearchTxn: bindActionCreators(onAdvanceSearchTxn, dispatch)
  };
};

const AdvanceSearchPageContainer = connect(mapStateToProps, mapDispatchToProps)(AdvanceSearchPage);

export default AdvanceSearchPageContainer;
