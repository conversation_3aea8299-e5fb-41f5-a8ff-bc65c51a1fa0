import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchActionShare } from 'actions/businessDashboardActions';
import TransactionActionTrend from 'components/dashboards/TransactionActionTrend';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  actionShare: state.businessDashboard.actionShare
});

const mapDispatchToProps = (dispatch) => ({
  fetchActionShare: bindActionCreators(onFetchActionShare, dispatch)
});

const TransactionActionTrendContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(TransactionActionTrend);

export default TransactionActionTrendContainer;
