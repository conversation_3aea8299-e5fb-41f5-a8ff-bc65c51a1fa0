import { faTableList } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { UncontrolledDropdown, DropdownToggle, DropdownMenu, DropdownItem } from 'reactstrap';

import ConfirmAlert from 'components/common/ConfirmAlert';
import { isCooperative } from 'constants/publicKey';

const AddToListButton = ({
  actions,
  prefiltersList,
  categoryName,
  identifier,
  listTypeData,
  theme,
  role,
  partnerId
}) => {
  const [currentList, setCurrentList] = useState(listTypeData || []);
  const [isConfirmAlert, setIsConfirmAlert] = useState(false);
  const [toggle, setToggle] = useState(false);
  const [selectedList, setSelectedList] = useState('');

  const isDropDownDisabled = () =>
    isCooperative
      ? ['reviewer', 'maker', 'principal-officer', 'supervisor'].includes(role)
      : role !== 'checker';

  const handleDropdownToggleClick = () => {
    setIsConfirmAlert(true);
    if (_.isEmpty(prefiltersList.specializedListTypes.data)) actions.onFetchSpecializedListTypes();
  };

  const prepareFormData = (listName, groupName, index) => ({
    currentListInfo: {
      prefilterName: `${listName} List`,
      prefilterValue: listName,
      prefilterListType: groupName
    },
    currentIndex: index,
    currentCategoryName: categoryName,
    currentIdentifier: identifier,
    currentPartnerId: isCooperative ? partnerId : ''
  });

  const toggleConfirmAlertModal = (isOpen, prefilterList) => {
    if (isOpen && prefilterList) {
      const { listName, groupName } = prefilterList;
      const index = currentList.indexOf(listName);
      const formData = prepareFormData(listName, groupName, index);
      actions.onAddToListItemClick(formData);
    }

    setSelectedList(currentList.listName || '');
    setIsConfirmAlert(!!isOpen);
    setToggle((prevToggle) => !prevToggle);
  };

  const getListType = (prefilterListType) => {
    let listType = null;

    if (prefilterListType === 'special') listType = 'specializedList';
    else if (prefilterListType === 'custom') listType = 'customizedList';

    return listType;
  };

  const handleSelectListItem = () => {
    const {
      currentListInfo,
      currentIndex,
      currentCategoryName,
      currentIdentifier,
      currentPartnerId
    } = prefiltersList.addToListCurrentItems;

    const tempCurrentList = [...currentList];
    const listType = getListType(currentListInfo.prefilterListType);

    let operationType;
    let formData = {
      categoryName: currentCategoryName,
      identifier: currentIdentifier,
      ...(isCooperative && partnerId && { partnerId: parseInt(currentPartnerId) })
    };

    if (currentIndex > -1) {
      // Remove item from list
      tempCurrentList.splice(currentIndex, 1);
      operationType = 'onDeleteSpecializedListItem';
    } else {
      // Add item to list
      tempCurrentList.push(currentListInfo.prefilterValue);
      operationType = 'onAddSingleItemToSpecializedList';

      formData = {
        ...formData,
        isActive: 1,
        isHashed: true
      };
    }

    setCurrentList(tempCurrentList);

    actions[operationType](formData, currentListInfo, 'addToListAction', listType);

    // Reset state
    setIsConfirmAlert(false);
    setToggle(false);
    setSelectedList('');
  };

  const prefilterListOptions = !_.isEmpty(prefiltersList.specializedListTypes.data) ? (
    prefiltersList.specializedListTypes.data.map((prefilterList) => (
      <DropdownItem
        value={prefilterList.listName}
        key={prefilterList.id + prefilterList.listName}
        disabled={isDropDownDisabled()}
        onClick={() => toggleConfirmAlertModal(isConfirmAlert, prefilterList)}
        className={
          currentList && currentList.includes(prefilterList.listName) ? 'active-item' : ''
        }>
        {prefilterList.listName}
      </DropdownItem>
    ))
  ) : (
    <span className="dropdown-item">No data available</span>
  );

  return (
    <>
      <UncontrolledDropdown className="add-to-list" direction="right">
        <DropdownToggle
          title="Add to list"
          className={currentList && currentList.length !== 0 ? 'with-list' : ''}
          onClick={handleDropdownToggleClick}>
          <FontAwesomeIcon icon={faTableList} />
        </DropdownToggle>
        <DropdownMenu end>{prefilterListOptions}</DropdownMenu>
      </UncontrolledDropdown>
      {isConfirmAlert && (
        <ConfirmAlert
          theme={theme}
          confirmAlertModal={toggle}
          toggleConfirmAlertModal={() => setToggle(!toggle)}
          confirmationAction={handleSelectListItem}
          confirmAlertTitle={
            prefiltersList.addToListCurrentItems.currentIndex > -1
              ? `Are you sure you want to remove ${categoryName} from ${selectedList} list ?`
              : `Are you sure you want to add ${categoryName} to ${selectedList} list ?`
          }
        />
      )}
    </>
  );
};

AddToListButton.propTypes = {
  theme: PropTypes.string.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  categoryName: PropTypes.string.isRequired,
  identifier: PropTypes.string.isRequired,
  listTypeData: PropTypes.array.isRequired,
  toggleActions: PropTypes.object.isRequired,
  role: PropTypes.string.isRequired,
  partnerId: PropTypes.number.isRequired
};

export default AddToListButton;
