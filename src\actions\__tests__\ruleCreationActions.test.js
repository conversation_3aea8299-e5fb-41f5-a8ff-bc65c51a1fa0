import * as types from 'constants/actionTypes';
import * as actions from 'actions/ruleCreationActions';
import { mockStore } from 'store/mockStoreConfiguration';
import responses from 'mocks/responses';

const { rules } = responses;

describe('rule creation actions', () => {
  it('should fetch dsl components', () => {
    const expectedActions = [
      { type: types.ON_FETCH_DSL_HELPERS_LOADING },
      { type: types.ON_SUCCESSFUL_FETCH_DSL_HELPERS, channel: 'frm', response: rules.components }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onFetchDSLHelpers('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should clear dsl validation', () => {
    expect(actions.onClearValidation()).toEqual({
      type: types.ON_CLEAR_DSL_VALIDATION
    });
  });

  it('should verify rule', () => {
    const formData = {
      channel: 'frm',
      logic: 'Transactions'
    };

    const expectedActions = [
      { type: types.ON_SUCCESSFUL_DSL_VERIFY, response: { message: rules.verify } }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onVerifyDSL(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should create rule', () => {
    const formData = {
      rule: {
        ruleType: 'Dsl',
        logic: 'Transactions',
        name: 'Transactions',
        description: 'description.value',
        weight: 10,
        priority: 1,
        explicit: true,
        actionCode: 'action[0]',
        actionName: 'action[1]',
        comments: 'comments && comments.value'
      },
      channel: 'frm'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: rules.createRule } },
      { type: types.ON_FETCH_RULE_LIST_LOADING },
      { type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING },
      { type: types.ON_TOGGLE_RULE_CREATE_MODAL, channel: 'frm' },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onCreateRule(formData, 'create')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update rule', () => {
    const formData = {
      rule: {
        ruleType: 'Dsl',
        logic: 'Transactions',
        name: 'Transactions',
        description: 'description.value',
        weight: 10,
        priority: 1,
        explicit: true,
        actionCode: 'action[0]',
        actionName: 'action[1]',
        comments: 'comments && comments.value'
      },
      channel: 'frm',
      editUrl: `supervisor/123/update`
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: rules.createRule } },
      { type: types.ON_FETCH_RULE_LIST_LOADING },
      { type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING },
      { type: types.ON_TOGGLE_RULE_EDIT_MODAL, channel: 'frm' },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onUpdateRule(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch NonProduction rules list', () => {
    const formData = {
      channel: 'frm',
      role: 'supervisor'
    };

    const expectedActions = [
      { type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING },
      {
        type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS,
        response: rules.approval,
        channel: formData.channel
      }
    ];

    const store = mockStore({ ruleCreation: {} });

    return store
      .dispatch(actions.onFetchNonProductionRulesList(formData.channel, formData.role))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should fetch action list', () => {
    const expectedActions = [
      { type: types.ON_SUCCESSFUL_FETCH_ACTION_LIST, response: rules.actionCode }
    ];

    const store = mockStore({ ruleCreation: {} });

    return store.dispatch(actions.onFetchActionList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should rule approval', () => {
    const formData = {
      ruleApprovalId: '1234abc',
      isApproved: true,
      comments: 'This rule has been approved/rejected due to some reason'
    };

    const argData = {
      channel: 'frm',
      role: 'supervisor'
    };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Rule approved successfully!' } },
      { type: types.ON_FETCH_RULE_LIST_LOADING },
      { type: types.ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ ruleConfigurator: {}, ruleCreation: {} });

    return store
      .dispatch(actions.onRuleApproval(argData.channel, argData.role, formData))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should Fetch Alert Categories', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES,
        response: responses.rules.alertCategories
      }
    ];

    const store = mockStore({ ruleConfigurator: {}, ruleCreation: {} });

    return store.dispatch(actions.onFetchAlertCategories('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Options of checklist', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS,
        response: []
      }
    ];

    const store = mockStore({ ruleConfigurator: {}, ruleCreation: {} });

    return store.dispatch(actions.onFetchCheckListOptions('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch CheckList', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_CHECKLIST,
        response: []
      }
    ];

    const store = mockStore({ ruleConfigurator: {}, ruleCreation: {} });

    return store.dispatch(actions.onFetchCheckList('frm', '123')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Update ActionList', () => {
    const formData = {
      updatedRuleOrder: 1,
      actionCode: '01',
      actionName: 'ACCEPTED'
    };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_UPDATE_ACTION_LIST, response: 1 },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ ruleConfigurator: {}, ruleCreation: {} });

    return store.dispatch(actions.onUpdateActionList('frm', formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
