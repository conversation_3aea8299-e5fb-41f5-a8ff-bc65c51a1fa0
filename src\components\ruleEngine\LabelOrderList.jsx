import PropTypes from 'prop-types';
import React from 'react';

import ReOrderingList from 'components/common/ReOrderingList';

function LabelOrderList({ channel, labelList, updateLabelOrderList }) {
  const formatter = (listItem) => listItem.name;

  const submitList = (channel) => (updatedRuleLabelOrder) =>
    updateLabelOrderList(channel, { updatedRuleLabelOrder });

  const curriedSubmit = submitList(channel);

  return (
    <ReOrderingList
      idKey="id"
      formatter={formatter}
      list={labelList}
      submitUpdatedOrder={(list) => curriedSubmit(list)}
    />
  );
}

LabelOrderList.propTypes = {
  channel: PropTypes.string.isRequired,
  labelList: PropTypes.array.isRequired,
  updateLabelOrderList: PropTypes.func.isRequired
};

export default LabelOrderList;
