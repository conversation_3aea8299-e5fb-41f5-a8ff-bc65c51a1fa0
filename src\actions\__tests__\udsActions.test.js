import responses from 'mocks/responses';

import * as actions from 'actions/udsActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('uds actions', () => {
  it('should fetch UDS merchant details', () => {
    const entity = 'merchant';
    const entityId = '987';

    const expectedActions = [
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity },
      {
        type: types.ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
        entity,
        response: responses.demographics.merchant
      }
    ];
    const store = mockStore({ uds: {} });

    return store.dispatch(actions.onFetchUDSEntityDetails(entity, entityId)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Facctum Details', () => {
    const formData = {
      entity_type: 'U'
    };

    const expectedActions = [
      { type: types.ON_FETCH_FACCTUM_DETAILS_LOADING },
      {
        type: types.ON_FETCH_FACCTUM_DETAILS_SUCCESS,
        response: {}
      }
    ];
    const store = mockStore({ uds: {} });

    return store.dispatch(actions.onFetchFacctumDetails(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Vulnerability Details', () => {
    const formData = {
      entity_type: 'U'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS,
        response: 'high'
      }
    ];
    const store = mockStore({ uds: {} });

    return store.dispatch(actions.onFetchVulnerabilityDetails(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Customer Details With All Accounts', () => {
    const entity = 'customer';

    const expectedActions = [
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity },
      {
        type: types.ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
        entity,
        response: responses.demographics.customerAllAccounts
      }
    ];
    const store = mockStore({ uds: {} });

    return store.dispatch(actions.onFetchCustomerDetailsWithAllAccounts('987')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
