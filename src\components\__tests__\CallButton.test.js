import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import moment from 'moment';
import React from 'react';

import CallButton from '../caseReview/CallButton';

const mockCallCustomer = jest.fn();
const mockCommunicationLogs = {
  data: [
    {
      media: 'CALL',
      mode: 'OUTGOING',
      commStatus: 'call placed',
      timestamp: moment().subtract(1, 'days').toISOString()
    },
    {
      media: 'CALL',
      mode: 'INCOMING',
      commStatus: 'call received',
      timestamp: moment().toISOString()
    }
  ]
};

const defaultProps = {
  channel: 'mockChannel',
  caseRefNo: 'mockCaseRefNo',
  entityId: 'mockEntityId',
  callCustomer: mockCallCustomer,
  communicationLogs: mockCommunicationLogs,
  disabled: false
};

describe('CallButton Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    render(<CallButton {...defaultProps} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText(/Last call/)).toBeInTheDocument();
  });

  it('displays correct call counts and last call on hover', async () => {
    render(<CallButton {...defaultProps} />);

    fireEvent.mouseOver(screen.getByRole('button'));
    await waitFor(() => {
      expect(screen.getByText(/Outgoing calls: 1/)).toBeInTheDocument();
      expect(screen.getByText(/Incoming calls: 1/)).toBeInTheDocument();
    });
  });

  it('calls `callCustomer` when button is clicked', () => {
    render(<CallButton {...defaultProps} />);

    fireEvent.click(screen.getByRole('button'));
    expect(mockCallCustomer).toHaveBeenCalledWith('mockChannel', 'mockCaseRefNo', 'mockEntityId');
  });

  it('displays and hides popover correctly on mouse events', async () => {
    render(<CallButton {...defaultProps} />);

    const button = screen.getByRole('button');

    fireEvent.mouseOver(button);
    await waitFor(() => {
      expect(screen.getByText(/Call counts/)).toBeInTheDocument();
    });

    fireEvent.mouseOut(button);
    await waitFor(() => {
      expect(screen.queryByText(/Call counts/)).not.toBeInTheDocument();
    });
  });

  it('does not call `callCustomer` when button is disabled', () => {
    render(<CallButton {...defaultProps} disabled={true} />);

    fireEvent.click(screen.getByRole('button'));
    expect(mockCallCustomer).not.toHaveBeenCalled();
  });
});
