import React from 'react';
import PropTypes from 'prop-types';
import { addItemToList } from 'constants/functions';
import { renderDataColumnList } from 'utility/customRenders';

const getApplicationInfoList = (details) => {
  let applicationInfoList = [];

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.applicationID,
    'App ID',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.applicationID,
    applicationInfoList
  );

  addItemToList(
    details?.deviceInfo?.xDeviceApp,
    'App Name',
    details?.deviceInfo?.xDeviceApp,
    applicationInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.userID,
    'User ID',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.userID,
    applicationInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.userName,
    'User Name',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.userName,
    applicationInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.branchCode ||
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.branchName,
    'Branch',
    `${details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.branchCode || ''}, ${
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.branchName || ''
    }`,
    applicationInfoList,
    null,
    true
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.areaName ||
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.stateName ||
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.countryName,
    'Location',
    `${details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.areaName || ''}, ${
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.stateName || ''
    }, ${details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.countryName || ''}`,
    applicationInfoList
  );

  return applicationInfoList;
};

function TransactionApplicationInfo({ details }) {
  const applicationInfoList = getApplicationInfoList(details);

  if (applicationInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Application Details</b>
      {renderDataColumnList(applicationInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionApplicationInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionApplicationInfo;
