import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchAgentDemographicDetails } from 'actions/demographicDetailsAction';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import { onFetchUDSEntityDetails } from 'actions/udsActions';
import AgentInfoCard from 'components/common/AgentInfoCard';

const mapStateToProps = (state) => ({
  data: state.uds.agent,
  demographicDetails: state.demographicDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchDetails: bindActionCreators(onFetchUDSEntityDetails, dispatch),
  toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
  fetchAgentDemographicDetails: bindActionCreators(onFetchAgentDemographicDetails, dispatch)
});

const AgentInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(AgentInfoCard);

export default AgentInfoCardContainer;
