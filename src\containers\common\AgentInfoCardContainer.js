import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchUDSEntityDetails } from 'actions/udsActions';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import AgentInfoCard from 'components/common/AgentInfoCard';
import { onFetchAgentDemographicDetails } from 'actions/demographicDetailsAction';

const mapStateToProps = (state) => {
  return {
    data: state.uds.agent,
    demographicDetails: state.demographicDetails
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchDetails: bindActionCreators(onFetchUDSEntityDetails, dispatch),
    toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
    fetchAgentDemographicDetails: bindActionCreators(onFetchAgentDemographicDetails, dispatch)
  };
};

const AgentInfoCardContainer = connect(mapStateToProps, mapDispatchToProps)(AgentInfoCard);

export default AgentInfoCardContainer;
