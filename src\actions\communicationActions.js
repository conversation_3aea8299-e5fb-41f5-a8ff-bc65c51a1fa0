import client from 'utility/apiClient';
import {
  ON_CUSTOMER_CALL_PLACED_SUCCESS,
  ON_CUSTOMER_CALL_END_SUCCESS,
  ON_FETCH_CALL_DISPOSITION_LIST_LOADING,
  ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS,
  ON_FETCH_CALL_DISPOSITION_LIST_FAILURE,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS,
  ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from './alertActions';

function callCustomer(channel, caseRefNo, customerId) {
  return client({
    method: 'POST',
    url: `casereview/${channel}/case/${caseRefNo}/call/${customerId}`
  });
}

function onCallCustomerSuccess(caseRefNo) {
  return { type: ON_CUSTOMER_CALL_PLACED_SUCCESS, caseRefNo };
}

function onCallCustomer(channel, caseRefNo, customerId) {
  return function (dispatch) {
    return callCustomer(channel, caseRefNo, customerId).then(
      () => {
        dispatch(onCallCustomerSuccess(caseRefNo));
        dispatch(onShowSuccessAlert({ message: 'Placing call to customer...' }));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function emailCustomer({ channel, caseRefNo, customerId, data }) {
  return client({
    method: 'POST',
    url: `casereview/${channel}/case/${caseRefNo}/email/${customerId}`,
    data
  });
}

function onEmailCustomer(formData) {
  return function (dispatch) {
    return emailCustomer(formData).then(
      () => dispatch(onShowSuccessAlert({ message: 'Email sent to customer' })),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function smsCustomer({ channel, caseRefNo, customerId, data }) {
  return client({
    method: 'POST',
    url: `casereview/${channel}/case/${caseRefNo}/sms/${customerId}`,
    data
  });
}

function onSMSCustomer(formData) {
  return function (dispatch) {
    return smsCustomer(formData).then(
      () => dispatch(onShowSuccessAlert({ message: 'SMS sent to customer' })),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchCustomerCommunicationLogs(channel, caseRefNo) {
  return client({ url: `casereview/${channel}/case/${caseRefNo}/commdetails` });
}

function onFetchCustomerCommunicationLogsLoading() {
  return { type: ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING };
}

function onFetchCustomerCommunicationLogsSuccess(response) {
  return { type: ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS, response };
}

function onFetchCustomerCommunicationLogsFailure(response) {
  return { type: ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_FAILURE, response };
}

function onFetchCustomerCommunicationLogs(channel, caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchCustomerCommunicationLogsLoading());
    return fetchCustomerCommunicationLogs(channel, caseRefNo).then(
      (success) => dispatch(onFetchCustomerCommunicationLogsSuccess(success)),
      (error) => dispatch(onFetchCustomerCommunicationLogsFailure(error))
    );
  };
}

function fetchCallDispositionList() {
  return client({ url: `casereview/case/call/fetch/disposition-status` });
}

function onFetchCallDispositionListLoading() {
  return { type: ON_FETCH_CALL_DISPOSITION_LIST_LOADING };
}

function onFetchCallDispositionListSuccess(response) {
  return { type: ON_FETCH_CALL_DISPOSITION_LIST_SUCCESS, response };
}

function onFetchCallDispositionListFailure(response) {
  return { type: ON_FETCH_CALL_DISPOSITION_LIST_FAILURE, response };
}

function onFetchCallDispositionList() {
  return function (dispatch) {
    dispatch(onFetchCallDispositionListLoading());
    return fetchCallDispositionList().then(
      (success) => dispatch(onFetchCallDispositionListSuccess(success)),
      (error) => dispatch(onFetchCallDispositionListFailure(error))
    );
  };
}

function endCustomerCall(channel, caseRefNo, disposition) {
  return client({ url: `casereview/${channel}/case/${caseRefNo}/call/hangup/${disposition}` });
}

function onEndCustomerCallSuccess() {
  return { type: ON_CUSTOMER_CALL_END_SUCCESS };
}

function onEndCustomerCall(channel, caseRefNo, disposition) {
  return function (dispatch) {
    return endCustomerCall(channel, caseRefNo, disposition).then(
      () => {
        dispatch(onEndCustomerCallSuccess());
        dispatch(onFetchCustomerCommunicationLogs(channel, caseRefNo));
        dispatch(onShowSuccessAlert({ message: 'Hanging up the call..' }));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export {
  onCallCustomer,
  onEmailCustomer,
  onSMSCustomer,
  onFetchCustomerCommunicationLogs,
  onFetchCallDispositionList,
  onEndCustomerCall
};
