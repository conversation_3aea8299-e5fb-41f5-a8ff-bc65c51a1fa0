import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';

import RuleForm from '../RuleForm';

// Mock child components to isolate RuleForm testing
jest.mock('components/common/FormStepper', () =>
  jest.fn(({ children, active }) => (
    <div data-testid="form-stepper" data-active={active}>
      {children}
    </div>
  ))
);

jest.mock('containers/ruleEngine/ChecklistContainer', () =>
  jest.fn(() => <div data-testid="checklist">Checklist Component</div>)
);

jest.mock('containers/ruleEngine/RuleBuilderContainer', () =>
  jest.fn(() => <div data-testid="rule-builder">RuleBuilder Component</div>)
);

jest.mock('containers/ruleEngine/RuleDetailsFormContainer', () =>
  jest.fn(() => <div data-testid="rule-details-form">RuleDetailsForm Component</div>)
);

jest.mock('containers/ruleEngine/SandboxContainer', () =>
  jest.fn(() => <div data-testid="sandbox">Sandbox Component</div>)
);

describe('RuleForm Component', () => {
  const defaultProps = {
    channel: 'frm',
    formName: 'create',
    formData: {},
    ruleList: {
      frm: [
        { code: '1', name: 'Rule 1', logic: 'test logic 1' },
        { code: '2', name: 'Rule 2', logic: 'test logic 2' }
      ]
    },
    ruleCreation: {
      loader: false,
      error: false,
      errorMessage: '',
      actionList: [],
      alertCategories: [],
      ruleChannels: [],
      fraudCategories: [],
      ruleLabels: [],
      validation: { status: true },
      nonProductionRules: {
        list: { frm: [] }
      }
    },
    fetchPrefilterLists: jest.fn(),
    ruleCreationActions: {
      onFetchActionList: jest.fn(),
      onFetchAlertCategories: jest.fn(),
      onFetchRuleChannelsList: jest.fn(),
      onFetchRuleFraudCategoriesList: jest.fn(),
      onFetchRuleLabels: jest.fn()
    },
    submit: jest.fn(),
    hasSandbox: 0,
    moduleType: 'acquirer',
    isSandbox: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    render(<RuleForm {...defaultProps} />);
    expect(screen.getByTestId('form-stepper')).toBeInTheDocument();
  });

  it('should display loader when ruleCreation.loader is true', () => {
    const propsWithLoader = {
      ...defaultProps,
      ruleCreation: {
        ...defaultProps.ruleCreation,
        loader: true
      }
    };

    render(<RuleForm {...propsWithLoader} />);
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument(); // FontAwesome spinner
  });

  it('should display error message when ruleCreation.error is true', () => {
    const propsWithError = {
      ...defaultProps,
      ruleCreation: {
        ...defaultProps.ruleCreation,
        error: true,
        errorMessage: 'Test error message'
      }
    };

    render(<RuleForm {...propsWithError} />);
    expect(screen.getByText('Test error message')).toBeInTheDocument();
  });

  it('should render sandbox when isSandbox is true and conditions are met', () => {
    const sandboxProps = {
      ...defaultProps,
      channel: 'frm',
      hasSandbox: 1,
      isSandbox: true
    };

    render(<RuleForm {...sandboxProps} />);
    expect(screen.getByTestId('sandbox')).toBeInTheDocument();
  });

  it('should return null when isSandbox is true but conditions are not met', () => {
    const sandboxProps = {
      ...defaultProps,
      channel: 'str', // Different channel
      hasSandbox: 1,
      isSandbox: true
    };

    const { container } = render(<RuleForm {...sandboxProps} />);
    expect(container.firstChild).toBeNull();
  });

  it('should call fetchPrefilterLists and other actions on mount', () => {
    render(<RuleForm {...defaultProps} />);

    expect(defaultProps.fetchPrefilterLists).toHaveBeenCalled();
    expect(defaultProps.ruleCreationActions.onFetchActionList).toHaveBeenCalledWith('frm');
    expect(defaultProps.ruleCreationActions.onFetchAlertCategories).toHaveBeenCalledWith('frm');
    expect(defaultProps.ruleCreationActions.onFetchRuleChannelsList).toHaveBeenCalledWith('frm');
    expect(defaultProps.ruleCreationActions.onFetchRuleFraudCategoriesList).toHaveBeenCalledWith(
      'frm'
    );
    expect(defaultProps.ruleCreationActions.onFetchRuleLabels).toHaveBeenCalledWith('frm');
  });

  it('should include Sandbox step when channel is not str and hasSandbox is 1', () => {
    const propsWithSandbox = {
      ...defaultProps,
      channel: 'frm',
      hasSandbox: 1
    };

    render(<RuleForm {...propsWithSandbox} />);
    // The steppers array should include 'Sandbox' step
    // This is tested indirectly through the FormStepper component
    expect(screen.getByTestId('form-stepper')).toBeInTheDocument();
  });

  it('should not re-render unnecessarily with same props', () => {
    const { rerender } = render(<RuleForm {...defaultProps} />);

    // Clear mock calls from initial render
    jest.clearAllMocks();

    // Re-render with same props
    rerender(<RuleForm {...defaultProps} />);

    // Should not call actions again due to React.memo optimization
    expect(defaultProps.ruleCreationActions.onFetchActionList).not.toHaveBeenCalled();
  });

  it('should handle form submission correctly', async () => {
    render(<RuleForm {...defaultProps} />);

    const form = screen.getByRole('form');
    fireEvent.submit(form);

    await waitFor(() => {
      expect(defaultProps.submit).toHaveBeenCalled();
    });
  });
});
