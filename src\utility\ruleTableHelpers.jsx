/* eslint-disable react/prop-types */
import _ from 'lodash';
import Moment from 'moment';
import React from 'react';

import { getFilterValue } from 'utility/utils';

export const SelectFilter = ({ options, value, onChange }) => (
  <select onChange={(e) => onChange(e.target.value)} value={value}>
    <option value="">All</option>
    {options}
  </select>
);

export const NumberFilter = ({ value, onChange }) => (
  <input type="number" min="0" max="100" onChange={(e) => onChange(e.target.value)} value={value} />
);

export const getCoreHeaders = () => [
  { Header: 'Name', accessor: 'name', minWidth: 200 },
  { Header: 'Logic', accessor: 'logic', minWidth: 400 }
];

export const getFRMHeaders = ({ channel, moduleType, actionsOptions, tableFilters }) => {
  if (channel !== 'frm') return [];

  const filterFor = (accessor) => ({
    filterMethod: (filter, row) => row[filter.id] === filter.value,
    Filter: ({ onChange }) => (
      <SelectFilter
        options={actionsOptions}
        value={getFilterValue(accessor, tableFilters)}
        onChange={onChange}
      />
    )
  });

  const headers = [
    {
      Header: 'Transaction Type',
      accessor: 'methodType',
      Cell: ({ value }) => (value ? `${value} Auth` : ''),
      Filter: ({ onChange }) => (
        <SelectFilter
          options={[
            <option key="pre" value="Pre">
              Pre Auth
            </option>,
            <option key="post" value="Post">
              Post Auth
            </option>
          ]}
          value={getFilterValue('methodType', tableFilters)}
          onChange={onChange}
        />
      )
    },
    {
      Header: 'Violation Action',
      accessor: 'actionName',
      ...filterFor('actionName')
    }
  ];

  if (moduleType === 'acquirer')
    headers.push(
      {
        Header: 'Low Risk Action',
        accessor: 'lowLevelOutcome',
        ...filterFor('lowLevelOutcome')
      },
      {
        Header: 'Medium Risk Action',
        accessor: 'medLevelOutcome',
        ...filterFor('medLevelOutcome')
      },
      {
        Header: 'High Risk Action',
        accessor: 'highLevelOutcome',
        ...filterFor('highLevelOutcome')
      }
    );

  return headers;
};

export const getAssignmentHeader = ({ channel, hasMakerChecker, tableFilters }) => {
  if (channel !== 'frm' || !hasMakerChecker) return [];

  return [
    {
      Header: 'Assign To',
      accessor: 'assignmentPriority',
      Cell: ({ value }) => {
        if (value === 0) return 'Maker';
        if (value === 1) return 'Checker';
        return '';
      },
      filterMethod: (filter, row) => row[filter.id] === filter.value,
      Filter: ({ onChange }) => (
        <SelectFilter
          options={[
            <option key="0" value={0}>
              Maker
            </option>,
            <option key="1" value={1}>
              Checker
            </option>
          ]}
          value={getFilterValue('assignmentPriority', tableFilters)}
          onChange={onChange}
        />
      )
    }
  ];
};

export const getRuleOrderHeader = (tableFilters) => ({
  Header: 'Rule Order',
  accessor: 'order',
  Cell: ({ value }) => (value ? value : ''),
  filterMethod: (filter, row) =>
    !isNaN(row[filter.id]) && parseInt(row[filter.id]) >= parseInt(filter.value),
  Filter: ({ onChange }) => (
    <NumberFilter value={getFilterValue('order', tableFilters)} onChange={onChange} />
  )
});

export const getLookupHeader = ({ Header, accessor, map, filterList, tableFilters }) => ({
  Header,
  accessor,
  Cell: ({ value }) => <span>{map[value]?.name || ''}</span>,
  filterMethod: (filter, row) => row[filter.id] === filter.value,
  Filter: ({ onChange }) => (
    <SelectFilter
      options={filterList}
      value={getFilterValue(accessor, tableFilters)}
      onChange={onChange}
    />
  )
});

export const getChannelsHeader = (channelOptions, tableFilters) => ({
  Header: 'Channels',
  accessor: 'channels',
  filterMethod: (filter, row) => _.includes(row[filter.id], filter.value),
  Filter: ({ onChange }) => (
    <SelectFilter
      options={channelOptions}
      value={getFilterValue('channels', tableFilters)}
      onChange={onChange}
    />
  )
});

export const subHeaders = [
  { Header: 'Description', accessor: 'description', minWidth: 300 },
  { Header: 'Created/Edited By', accessor: 'createdBy', minWidth: 50 },
  {
    Header: 'Created/Edited Date',
    accessor: 'createdAt',
    minWidth: 80,
    Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : '')
  },
  { Header: 'Updated By', accessor: 'updatedBy', minWidth: 50 },
  {
    Header: 'Updated Date',
    accessor: 'updatedAt',
    minWidth: 80,
    Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : '')
  }
];
