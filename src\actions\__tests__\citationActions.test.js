import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/citationActions';
import responses from 'mocks/responses';

const mockedStore = {
  citations: {}
};

describe('citation actions', () => {
  it('should fetch citations', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';

    const expectedActions = [
      { type: types.ON_FETCH_CASE_CITATION_LOADING },
      {
        type: types.ON_FETCH_CASE_CITATION_SUCCESS,
        response: responses.citations
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCaseCitations(caseRefNo)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add citation comment', () => {
    let formData = {
      caseRefNo: '123',
      citationId: 45,
      response: 'test',
      txnId: 'iucaaTxn20230421063',
      userId: '1',
      userName: 'admin',
      userRole: 'principal-officer'
    };

    const expectedActions = [
      { type: types.ON_ADD_CITATION_COMMENT_LOADING },
      {
        type: types.ON_ADD_CITATION_COMMENT_SUCCESS,
        response: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onAddCitationComment(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
