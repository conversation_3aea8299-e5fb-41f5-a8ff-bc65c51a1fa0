import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { CSVLink } from 'react-csv';
import { Button } from 'reactstrap';

const CSVDownloader = ({ data, sheetName, headers, disabled }) => (
  <Button
    outline
    className="ms-1 download-csv-btn"
    color="primary"
    size="sm"
    disabled={disabled}
    title={`Download ${sheetName}`}>
    <CSVLink
      data={data}
      headers={headers}
      filename={`${sheetName.split(' ').join('_')}`}
      enclosingCharacter=""
      separator="|">
      <FontAwesomeIcon icon={faDownload} className="me-1" /> Download {sheetName}
    </CSVLink>
  </Button>
);

CSVDownloader.propTypes = {
  data: PropTypes.array.isRequired,
  headers: PropTypes.array.isRequired,
  sheetName: PropTypes.string.isRequired,
  disabled: PropTypes.bool.isRequired
};

export default CSVDownloader;
