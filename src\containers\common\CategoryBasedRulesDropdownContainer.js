import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import { onFetchAlertCategories } from 'actions/ruleCreationActions';
import CategoryBasedRulesDropdown from 'components/common/CategoryBasedRulesDropdown';

const mapStateToProps = (state) => {
  return {
    ruleList: state.ruleConfigurator.productionRules.list.frm,
    categoryList: state.ruleCreation.alertCategories
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRules: bindActionCreators(onFetchRulesList, dispatch),
    fetchCategory: bindActionCreators(onFetchAlertCategories, dispatch)
  };
};

const CategoryBasedRulesDropdownContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CategoryBasedRulesDropdown);

export default CategoryBasedRulesDropdownContainer;
