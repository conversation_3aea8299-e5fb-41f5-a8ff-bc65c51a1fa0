import React, { useEffect, useState, useMemo, Suspense, lazy, useRef } from 'react';
import PropTypes from 'prop-types';
import { TabPane } from 'reactstrap';
import { isEmpty } from 'lodash';
import Loader from 'components/loader/Loader';
import { useHistory } from 'react-router-dom';
import { useDateRange } from 'context/DateRangeContext';
import DurationSelector from 'components/common/DurationSelector';
import Tabs from 'components/common/Tabs';
import ShiftDashboard from 'components/dashboards/ShiftDashboard';

const BusinessDashboard = lazy(() => import('components/dashboards/BusinessDashboard'));
const RuleEfficiencyDashboard = lazy(() =>
  import('containers/dashboards/RuleEfficiencyDashboardContainer')
);

const InvestigationsReport = lazy(() => import('components/incidents/IncidentsHomePage'));
const OperationsDashboard = lazy(() => import('components/dashboards/OperationsDashboard'));
import { isCooperative } from 'constants/publicKey';

function SupervisorDashboard({
  role,
  theme,
  shiftslist,
  fetchShifts,
  userslist,
  shiftDetails,
  hasKnowageReport,
  moduleType,
  contextKey
}) {
  const history = useHistory();
  const { startDate, endDate } = useDateRange(contextKey);
  const [currentTab, setCurrentTab] = useState([0]);
  const dashboardRef = useRef(null);

  useEffect(() => {
    isEmpty(shiftslist) && fetchShifts();

    document.title = 'BANKiQ FRC | SLA Dashboard';
    if (role !== 'supervisor') history.goBack();

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const shiftTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          <ShiftDashboard
            period={{ startDate, endDate }}
            theme={theme}
            userslist={userslist}
            shiftslist={shiftslist}
            shiftDetails={shiftDetails}
          />
        </TabPane>
      ),
      [startDate, endDate, shiftslist]
    );

  const ruleEfficiencyTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <RuleEfficiencyDashboard
                dashboardRef={dashboardRef}
                period={{ startDate, endDate }}
                history={history}
              />
            </Suspense>
          )}
        </TabPane>
      ),
      [startDate, endDate, currentTab]
    );

  const complianceTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <InvestigationsReport period={{ startDate, endDate }} />
            </Suspense>
          )}
        </TabPane>
      ),
      [currentTab]
    );

  const businessTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <BusinessDashboard dashboardRef={dashboardRef} period={{ startDate, endDate }} />
            </Suspense>
          )}
        </TabPane>
      ),
      [startDate, endDate, currentTab]
    );

  const operationsTab = (index) =>
    useMemo(
      () => (
        <TabPane tabId={index} key={index}>
          {currentTab.includes(index) && (
            <Suspense fallback={<Loader show={true} />}>
              <OperationsDashboard period={{ startDate, endDate }} />
            </Suspense>
          )}
        </TabPane>
      ),
      [startDate, endDate, currentTab]
    );

  const updateCurrentTab = (tabId) =>
    !currentTab.includes(tabId) && setCurrentTab((prev) => [...prev, tabId]);

  let tabs =
    moduleType === 'acquirer'
      ? [shiftTab(0), ruleEfficiencyTab(1), businessTab(2)]
      : [shiftTab(0), ruleEfficiencyTab(1), complianceTab(2), businessTab(3)];

  hasKnowageReport === 1 && tabs.push(operationsTab(moduleType === 'acquirer' ? 3 : 4));

  let supervisorTabs =
    moduleType === 'acquirer'
      ? ['Analyst Performance', 'Rule Efficiency', 'Business']
      : ['Analyst Performance', 'Rule Efficiency', 'Compliance', 'Business'];

  hasKnowageReport === 1 && supervisorTabs.push('Operations');

  return (
    <div className="content-wrapper">
      <div id="dashboard-content" ref={dashboardRef}>
        <Tabs
          tabNames={isCooperative ? ['Analyst Performance'] : supervisorTabs}
          action={<DurationSelector contextKey={contextKey} />}
          getCurrentTab={updateCurrentTab}>
          {isCooperative ? [shiftTab(0)] : tabs}
        </Tabs>
      </div>
    </div>
  );
}

SupervisorDashboard.propTypes = {
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  shiftslist: PropTypes.array.isRequired,
  shiftDetails: PropTypes.object.isRequired,
  hasKnowageReport: PropTypes.number.isRequired,
  moduleType: PropTypes.string.isRequired,
  fetchShifts: PropTypes.func.isRequired
};

export default SupervisorDashboard;
