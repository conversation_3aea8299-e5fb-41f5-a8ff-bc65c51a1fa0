import { isEmpty, capitalize, lowerCase } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useMemo, Suspense, lazy, useRef, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { TabPane } from 'reactstrap';

import DurationSelector from 'components/common/DurationSelector';
import Tabs from 'components/common/Tabs';
import ShiftDashboard from 'components/dashboards/ShiftDashboard';
import Loader from 'components/loader/Loader';
import { isCooperative } from 'constants/publicKey';
import { useDateRange } from 'context/DateRangeContext';

const BusinessDashboard = lazy(() => import('components/dashboards/BusinessDashboard'));
const RuleEfficiencyDashboard = lazy(
  () => import('containers/dashboards/RuleEfficiencyDashboardContainer')
);
const InvestigationsReport = lazy(() => import('components/incidents/IncidentsHomePage'));
const OperationsDashboard = lazy(() => import('components/dashboards/OperationsDashboard'));

const SupervisorDashboard = ({
  role,
  theme,
  shiftslist,
  fetchShifts,
  userslist,
  shiftDetails,
  hasKnowageReport,
  moduleType,
  contextKey
}) => {
  const history = useHistory();
  const { startDate, endDate } = useDateRange(contextKey);
  const [currentTab, setCurrentTab] = useState([0]);
  const dashboardRef = useRef(null);

  useEffect(() => {
    if (isEmpty(shiftslist)) fetchShifts();
    if (role !== 'supervisor') history.goBack();

    const originalTitle = document.title;
    document.title = 'BANKiQ FRC | SLA Dashboard';
    return () => {
      document.title = originalTitle;
    };
  }, [fetchShifts, history, role, shiftslist]);

  const period = useMemo(() => ({ startDate, endDate }), [startDate, endDate]);

  const updateCurrentTab = useCallback(
    (tabId) => {
      if (!currentTab.includes(tabId)) setCurrentTab((prev) => [...prev, tabId]);
    },
    [currentTab]
  );

  const dashboardComponents = useMemo(
    () => ({
      analystPerformance: (
        <ShiftDashboard
          period={period}
          theme={theme}
          userslist={userslist}
          shiftslist={shiftslist}
          shiftDetails={shiftDetails}
        />
      ),
      ruleEfficiency: (
        <RuleEfficiencyDashboard dashboardRef={dashboardRef} period={period} history={history} />
      ),
      compliance: <InvestigationsReport period={period} />,
      business: <BusinessDashboard dashboardRef={dashboardRef} period={period} />,
      operations: <OperationsDashboard period={period} />
    }),
    [period, theme, userslist, shiftslist, shiftDetails, history]
  );

  const tabKeys = useMemo(() => {
    const baseTabs = ['analystPerformance', 'ruleEfficiency'];
    if (moduleType === 'acquirer') baseTabs.push('business');
    else baseTabs.push('compliance', 'business');
    if (!isCooperative && hasKnowageReport === 1) baseTabs.push('operations');
    return baseTabs;
  }, [moduleType, hasKnowageReport]);

  const tabNames = useMemo(() => tabKeys.map((tab) => capitalize(lowerCase(tab))), [tabKeys]);

  const tabPanes = useMemo(
    () =>
      tabKeys.map((key, index) => (
        <TabPane tabId={index} key={key}>
          {currentTab.includes(index) ? (
            <Suspense fallback={<Loader show />}>{dashboardComponents[key]}</Suspense>
          ) : null}
        </TabPane>
      )),
    [tabKeys, currentTab, dashboardComponents]
  );

  return (
    <div className="content-wrapper">
      <div id="dashboard-content" ref={dashboardRef}>
        <Tabs
          tabNames={isCooperative ? ['Analyst Performance'] : tabNames}
          action={<DurationSelector contextKey={contextKey} />}
          getCurrentTab={updateCurrentTab}>
          {isCooperative ? [tabPanes[0]] : tabPanes}
        </Tabs>
      </div>
    </div>
  );
};

SupervisorDashboard.propTypes = {
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  shiftslist: PropTypes.array.isRequired,
  shiftDetails: PropTypes.object.isRequired,
  hasKnowageReport: PropTypes.number.isRequired,
  moduleType: PropTypes.string.isRequired,
  fetchShifts: PropTypes.func.isRequired
};

export default SupervisorDashboard;
