import { onShow<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, onShowSuc<PERSON><PERSON><PERSON>t } from 'actions/alertActions';
import {
  onToggle<PERSON>oader,
  onToggleShiftModal,
  onToggleAddUserModal,
  onToggleAssignShiftModal,
  onToggleUpdateUserRolesModal,
  onToggleUserCaseCriteriaModal
} from 'actions/toggleActions';
import {
  ON_SUCCESSFUL_FETCH_ROLES,
  ON_SUCCESSFUL_FETCH_SHIFTS,
  ON_SUCCESSFUL_FETCH_STAGES,
  ON_SUCCESSFUL_FETCH_CHANNELS,
  ON_SUCCESSFUL_USER_LIST_FETCH,
  ON_SUCCESSFUL_UPDATE_USER_ROLES,
  ON_SUCCESSFUL_USER_ASSIGN_SHIFTS,
  ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST,
  ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS,
  ON_FETCH_EXTERNAL_CHECKER_LIST_FAIL<PERSON><PERSON>,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE,
  ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS,
  ON_USER_TOGGLE_AUTO_CASE_SUCCESS,
  ON_FETCH_CONFIGURATIONS_SUCCESS,
  ON_SUCCESSFUL_FETCH_ADMIN_LIST,
  ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchUsersList(role) {
  return client({
    url: `useraccessmanagement/user/${role}`,
    badRequestMessage: 'Unable to get Users list for current id'
  });
}

function onSuccessfulFetchUsersList(response) {
  return {
    type: ON_SUCCESSFUL_USER_LIST_FETCH,
    response
  };
}

function onFetchUsersList() {
  return function (dispatch, getState) {
    const { roles } = getState().auth.userCreds;
    return fetchUsersList(roles).then(
      (success) => dispatch(onSuccessfulFetchUsersList(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchUnapprovedUsersList() {
  return client({
    url: `useraccessmanagement/unapproved/user`,
    badRequestMessage: 'Unable to get Unapproved Users list for current id'
  });
}

function onSuccessfulFetchUnapprovedUsersList(response) {
  return {
    type: ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH,
    response
  };
}

function onFetchUnapprovedUsersList() {
  return function (dispatch) {
    return fetchUnapprovedUsersList().then(
      (success) => dispatch(onSuccessfulFetchUnapprovedUsersList(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchRoles() {
  return client({
    url: 'useraccessmanagement/role',
    badRequestMessage: 'Currently unable to fetch roles'
  });
}

function onFetchRolesSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_ROLES,
    response
  };
}

function onFetchRoles() {
  return function (dispatch) {
    return fetchRoles().then(
      (success) => dispatch(onFetchRolesSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchShifts() {
  return client({
    url: 'useraccessmanagement/shift',
    badRequestMessage: 'Currently unable to fetch shifts'
  });
}

function onFetchShiftsSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_SHIFTS,
    response
  };
}

function onFetchShifts() {
  return function (dispatch) {
    return fetchShifts().then(
      (success) => dispatch(onFetchShiftsSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchChannels() {
  return client({
    url: 'useraccessmanagement/channel',
    badRequestMessage: 'Unable to fetch channels'
  });
}

function onFetchChannelsSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CHANNELS,
    response
  };
}

function onFetchChannels() {
  return function (dispatch) {
    return fetchChannels().then(
      (success) => dispatch(onFetchChannelsSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addUser(formData) {
  const addURL = {
    user: 'useraccessmanagement/user',
    admin: 'useraccessmanagement/user/admin',
    supervisor: 'useraccessmanagement/user/supervisor',
    fnr: 'useraccessmanagement/fnr/user'
  };
  return client({
    method: 'POST',
    url: addURL[formData.type],
    data: formData.userData
  });
}

function onAddUser(formData) {
  return function (dispatch, getState) {
    const { peerAdmin } = getState().user.configurations;
    dispatch(onToggleLoader(true));
    return addUser(formData)
      .then(
        () => {
          dispatch(onFetchUsersList());
          peerAdmin === 1 && formData.type !== 'admin' && dispatch(onFetchUnapprovedUsersList());
          dispatch(
            onShowSuccessAlert({
              message: `${formData.type?.toUpperCase()} added successfully`
            })
          );
          dispatch(onToggleAddUserModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateStageUser(formData) {
  const addURL = {
    user: 'useraccessmanagement/stage/user',
    supervisor: 'useraccessmanagement/stage/supervisor'
  };
  return client({
    method: 'PUT',
    url: addURL[formData.type],
    data: formData.userData
  });
}

function onUpdateStageUser(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateStageUser(formData)
      .then(
        () => {
          dispatch(onFetchUnapprovedUsersList());
          dispatch(
            onShowSuccessAlert({
              message:
                formData.type === 'supervisor'
                  ? `Supervisor Updated successfully`
                  : `User Updated successfully`
            })
          );
          dispatch(onToggleAddUserModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateUserRoles(formData) {
  return client({
    method: 'PUT',
    url: 'useraccessmanagement/user',
    data: formData.userData,
    badRequestMessage: 'Currently unable to update user roles.'
  });
}

function onUpdateUserRolesSuccess(user) {
  return {
    type: ON_SUCCESSFUL_UPDATE_USER_ROLES,
    user
  };
}

function onUpdateUserRoles(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateUserRoles(formData)
      .then(
        () => {
          dispatch(onUpdateUserRolesSuccess(formData.userData));
          dispatch(onShowSuccessAlert({ message: 'User roles updated successfully' }));
          dispatch(onToggleUpdateUserRolesModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function addShift(formData) {
  return client({
    method: 'POST',
    url: 'useraccessmanagement/shift',
    data: formData,
    badRequestMessage: 'Currently unable to add shift'
  });
}

function onAddShift(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addShift(formData)
      .then(
        () => {
          dispatch(onToggleShiftModal());
          dispatch(onFetchShifts());
          dispatch(onShowSuccessAlert({ message: 'Shift added successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function userAssignShifts(formData) {
  return client({
    method: 'POST',
    url: 'useraccessmanagement/shift/user/assign',
    data: formData,
    badRequestMessage: 'Currently unable to update user shifts.'
  });
}

function onUserAssignShiftsSuccess(userShifts) {
  return {
    type: ON_SUCCESSFUL_USER_ASSIGN_SHIFTS,
    userShifts
  };
}

function onUserAssignShifts(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return userAssignShifts(formData)
      .then(
        () => {
          dispatch(onUserAssignShiftsSuccess(formData));
          dispatch(onShowSuccessAlert({ message: 'Shift assigned successfully' }));
          dispatch(onToggleAssignShiftModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchPartnerIdList() {
  return client({
    url: 'useraccessmanagement/getPartners',
    badRequestMessage: 'Currently unable to fetch partnerId list'
  });
}

function onFetchPartnerIdListSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST,
    response
  };
}

function onFetchPartnerIdList() {
  return function (dispatch) {
    return fetchPartnerIdList().then(
      (success) => dispatch(onFetchPartnerIdListSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchAdminList() {
  return client({
    url: 'useraccessmanagement/admin',
    badRequestMessage: 'Currently unable to fetch admin list'
  });
}

function onFetchAdminListSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_ADMIN_LIST,
    response
  };
}

function onFetchAdminList() {
  return function (dispatch) {
    return fetchAdminList().then(
      (success) => dispatch(onFetchAdminListSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchExternalCheckerList() {
  return client({
    url: `useraccessmanagement/external/checker/user`,
    badRequestMessage: 'Unable to get Users list for current id'
  });
}

function onSuccessfulFetchExternalCheckerList(response) {
  return {
    type: ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS,
    response
  };
}

function onFetchExternalCheckerListFailure(response) {
  return {
    type: ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE,
    response
  };
}

function onFetchExternalCheckerList() {
  return function (dispatch) {
    return fetchExternalCheckerList().then(
      (success) => dispatch(onSuccessfulFetchExternalCheckerList(success)),
      (error) => dispatch(onFetchExternalCheckerListFailure(error))
    );
  };
}

function fetchStages() {
  return client({
    url: 'casereview/case/stage',
    badRequestMessage: 'Currently unable to fetch stages'
  });
}

function onFetchStagesSuccess(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_STAGES,
    response
  };
}

function onFetchStages() {
  return function (dispatch) {
    return fetchStages().then(
      (success) => dispatch(onFetchStagesSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchCaseCriteriaAttributesList() {
  return client({
    url: `useraccessmanagement/caseCriteria`,
    badRequestMessage: 'Unable to get attributes list for case assignment criteria'
  });
}

function onFetchCaseCriteriaAttributesListLoading() {
  return { type: ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING };
}

function onFetchCaseCriteriaAttributesListSuccess(response) {
  return {
    type: ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS,
    response
  };
}

function onFetchCaseCriteriaAttributesListFailure(response) {
  return {
    type: ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE,
    response
  };
}

function onFetchCaseCriteriaAttributesList() {
  return function (dispatch) {
    dispatch(onFetchCaseCriteriaAttributesListLoading());
    return fetchCaseCriteriaAttributesList().then(
      (success) => dispatch(onFetchCaseCriteriaAttributesListSuccess(success)),
      (error) => dispatch(onFetchCaseCriteriaAttributesListFailure(error))
    );
  };
}

function userAssignCaseCriteria(formData) {
  return client({
    method: 'POST',
    url: 'useraccessmanagement/user/caseCriteria',
    data: formData,
    badRequestMessage: 'Currently unable to update user case criteria.'
  });
}

function onUserAssignCaseCriteriaSuccess(response) {
  return {
    type: ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS,
    response
  };
}

function onUserAssignCaseCriteria(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return userAssignCaseCriteria(formData)
      .then(
        () => {
          dispatch(onUserAssignCaseCriteriaSuccess(formData));
          dispatch(onShowSuccessAlert({ message: 'Case criteria assigned successfully' }));
          dispatch(onToggleUserCaseCriteriaModal());
          dispatch(onFetchUsersList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function userToggleAutoCase(formData) {
  return client({
    method: 'PUT',
    url: 'useraccessmanagement/user/autocase',
    data: formData,
    badRequestMessage: 'Currently unable to update user case criteria.'
  });
}

function onUserToggleAutoCaseSuccess(response) {
  return {
    type: ON_USER_TOGGLE_AUTO_CASE_SUCCESS,
    response
  };
}

function onUserToggleAutoCase(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return userToggleAutoCase(formData)
      .then(
        () => {
          dispatch(onUserToggleAutoCaseSuccess(formData));
          dispatch(
            onShowSuccessAlert({
              message: `Auto case assignment ${
                formData.pauseAutoCase === 1 ? 'PAUSED' : 'RESUMED'
              } for user - ${formData.userName}`
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchConfigurations() {
  return client({
    url: 'casereview/fetch/product/feature/details',
    badRequestMessage: 'Currently unable to fetch configuration'
  });
}

function onFetchConfigurationsSuccess(response) {
  return {
    type: ON_FETCH_CONFIGURATIONS_SUCCESS,
    response
  };
}

function onFetchConfigurations() {
  return function (dispatch) {
    return fetchConfigurations().then(
      (success) => dispatch(onFetchConfigurationsSuccess(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function deleteCaseCriteriaForUser(formData) {
  return client({
    method: 'POST',
    url: `useraccessmanagement/criteria/delete`,
    data: formData
  });
}

function onDeleteCaseCriteriaForUser(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteCaseCriteriaForUser(formData)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Case criteria deleted successfully!' }));
          dispatch(onToggleUserCaseCriteriaModal());
          dispatch(onFetchUsersList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteUser(selectedUser) {
  return client({
    method: 'DELETE',
    url: `useraccessmanagement/user/${selectedUser.userName}`
  });
}

function onDeleteUser(selectedUser) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteUser(selectedUser)
      .then(
        () => {
          dispatch(onFetchUsersList());
          dispatch(onShowSuccessAlert({ message: 'User deleted successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteShift(selectedShift) {
  return client({
    method: 'DELETE',
    url: `useraccessmanagement/shift/${selectedShift.id}`
  });
}

function onDeleteShift(selectedShift) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteShift(selectedShift)
      .then(
        () => {
          dispatch(onFetchShifts());
          dispatch(onShowSuccessAlert({ message: 'Shift deleted successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateShift(formData) {
  return client({
    method: 'PUT',
    url: 'useraccessmanagement/shift',
    data: formData,
    badRequestMessage: 'Currently unable to update shift.'
  });
}

function onUpdateShift(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateShift(formData)
      .then(
        () => {
          dispatch(onToggleShiftModal());
          dispatch(onFetchShifts());
          dispatch(onShowSuccessAlert({ message: 'Shift updated successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function approveUser(formData) {
  return client({
    method: 'POST',
    url: `useraccessmanagement/approve/user`,
    data: formData
  });
}

function onApproveUser(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return approveUser(formData)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: `User ${formData.isApproved ? 'approved' : 'rejected'} successfully!`
            })
          );
          formData.isApproved && dispatch(onFetchUsersList());
          dispatch(onFetchUnapprovedUsersList());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchUsersList,
  onFetchRoles,
  onFetchShifts,
  onFetchStages,
  onFetchChannels,
  onAddUser,
  onUpdateUserRoles,
  onAddShift,
  onUserAssignShifts,
  onFetchPartnerIdList,
  onFetchExternalCheckerList,
  onFetchCaseCriteriaAttributesList,
  onUserAssignCaseCriteria,
  onUserToggleAutoCase,
  onFetchConfigurations,
  onDeleteCaseCriteriaForUser,
  onDeleteUser,
  onDeleteShift,
  onUpdateShift,
  onFetchAdminList,
  onFetchUnapprovedUsersList,
  onApproveUser,
  onUpdateStageUser
};
