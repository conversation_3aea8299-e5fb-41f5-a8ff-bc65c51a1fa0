/* eslint-disable react/prop-types */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';

import performanceMonitor from 'utility/performanceMonitor';

import Sandbox from '../Sandbox';

// Mock dependencies
jest.mock(
  'react-datetime',
  () =>
    function MockDateTime({ value, onChange }) {
      return (
        <input
          data-testid="datetime-picker"
          value={value ? value.toString() : ''}
          onChange={(e) => onChange && onChange(new Date(e.target.value))}
        />
      );
    }
);

jest.mock('react-multi-select-component', () => ({
  MultiSelect: ({ options, value, onChange }) => (
    <select
      data-testid="multi-select"
      multiple
      value={value.map((v) => v.value)}
      onChange={(e) => {
        const selectedValues = Array.from(e.target.selectedOptions, (option) =>
          options.find((opt) => opt.value === option.value)
        );
        onChange(selectedValues);
      }}>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )
}));

jest.mock('containers/ruleEngine/SandboxResultContainer', () =>
  jest.fn(() => <div data-testid="sandbox-result">Sandbox Result</div>)
);

// Mock useInterval hook
jest.mock('hooks/useInterval', () => jest.fn());

describe('Sandbox Performance Tests', () => {
  beforeEach(() => {
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const createMockRuleList = (count) =>
    Array.from({ length: count }, (_, i) => ({
      code: `RULE_${i}`,
      name: `Rule ${i}`,
      logic: `TXN.AMOUNT > ${1000 + i}`
    }));

  const defaultProps = {
    selectedRule: { code: 'MAIN_RULE', name: 'Main Rule', logic: 'TXN.AMOUNT > 1000' },
    ruleList: createMockRuleList(50),
    comparisonRules: [
      { code: 'COMP_1', label: 'Comparison Rule 1' },
      { code: 'COMP_2', label: 'Comparison Rule 2' }
    ],
    dateRange: {
      data: {
        startTimestamp: '2023-01-01T00:00:00Z',
        endTimestamp: '2023-12-31T23:59:59Z'
      }
    },
    testing: {
      status: 'IDLE',
      testId: null,
      ruleName: '',
      ruleCode: ''
    },
    sandboxingActions: {
      onFetchSandboxDateRange: jest.fn(),
      onFetchSandboxTestStatus: jest.fn(),
      onStartSandboxTest: jest.fn().mockResolvedValue({}),
      onStopSandboxTest: jest.fn().mockResolvedValue({})
    }
  };

  it('should render with large rule lists efficiently', () => {
    const largeRuleListProps = {
      ...defaultProps,
      ruleList: createMockRuleList(1000)
    };

    performanceMonitor.startTiming('large_rule_list_render');

    render(<Sandbox {...largeRuleListProps} />);

    const duration = performanceMonitor.endTiming('large_rule_list_render');
    expect(duration).toBeLessThan(200);
  });

  it('should memoize result headers efficiently', () => {
    const { rerender } = render(<Sandbox {...defaultProps} />);

    performanceMonitor.startTiming('result_headers_memoization');

    // Re-render with same selectedRule and comparisonRules
    rerender(<Sandbox {...defaultProps} />);

    const duration = performanceMonitor.endTiming('result_headers_memoization');
    expect(duration).toBeLessThan(20);
  });

  it('should handle filtered rule list memoization', () => {
    const { rerender } = render(<Sandbox {...defaultProps} />);

    performanceMonitor.startTiming('filtered_rule_list_memoization');

    // Re-render with same ruleList and selectedRule
    rerender(<Sandbox {...defaultProps} />);

    const duration = performanceMonitor.endTiming('filtered_rule_list_memoization');
    expect(duration).toBeLessThan(15);
  });

  it('should handle violation options computation efficiently', () => {
    const largeRuleListProps = {
      ...defaultProps,
      ruleList: createMockRuleList(500)
    };

    performanceMonitor.startTiming('violation_options_computation');

    render(<Sandbox {...largeRuleListProps} />);

    const duration = performanceMonitor.endTiming('violation_options_computation');
    expect(duration).toBeLessThan(100);
  });

  it('should handle date validation efficiently', () => {
    render(<Sandbox {...defaultProps} />);

    const startDatePicker = screen.getAllByTestId('datetime-picker')[0];

    performanceMonitor.startTiming('date_validation');

    fireEvent.change(startDatePicker, { target: { value: '2023-06-01' } });

    const duration = performanceMonitor.endTiming('date_validation');
    expect(duration).toBeLessThan(30);
  });

  it('should handle sandbox test start efficiently', async () => {
    const mockStartTest = jest.fn().mockResolvedValue({ testId: '123' });
    const propsWithMockStart = {
      ...defaultProps,
      sandboxingActions: {
        ...defaultProps.sandboxingActions,
        onStartSandboxTest: mockStartTest
      }
    };

    render(<Sandbox {...propsWithMockStart} />);

    const startButton = screen.getByText('Start Test');

    performanceMonitor.startTiming('sandbox_test_start');

    fireEvent.click(startButton);

    const duration = performanceMonitor.endTiming('sandbox_test_start');
    expect(duration).toBeLessThan(50);

    await waitFor(() => {
      expect(mockStartTest).toHaveBeenCalled();
    });
  });

  it('should handle comparison rule selection efficiently', () => {
    render(<Sandbox {...defaultProps} />);

    const multiSelect = screen.getByTestId('multi-select');

    performanceMonitor.startTiming('comparison_rule_selection');

    fireEvent.change(multiSelect, { target: { selectedOptions: [{ value: 'RULE_1' }] } });

    const duration = performanceMonitor.endTiming('comparison_rule_selection');
    expect(duration).toBeLessThan(40);
  });

  it('should not cause memory leaks during test lifecycle', () => {
    const { rerender } = render(<Sandbox {...defaultProps} />);

    performanceMonitor.takeMemorySnapshot('before_test_lifecycle');

    // Simulate test lifecycle changes
    const testStates = ['STARTED', 'PENDING', 'COMPLETED', 'FAILED'];
    testStates.forEach((status) => {
      const newProps = {
        ...defaultProps,
        testing: { ...defaultProps.testing, status }
      };
      rerender(<Sandbox {...newProps} />);
    });

    performanceMonitor.takeMemorySnapshot('after_test_lifecycle');
    performanceMonitor.checkMemoryLeaks();

    expect(true).toBe(true);
  });

  it('should handle complex date range operations efficiently', () => {
    const complexDateRangeProps = {
      ...defaultProps,
      dateRange: {
        data: {
          startTimestamp: '2020-01-01T00:00:00Z',
          endTimestamp: '2023-12-31T23:59:59Z'
        }
      }
    };

    performanceMonitor.startTiming('complex_date_range_render');

    render(<Sandbox {...complexDateRangeProps} />);

    const duration = performanceMonitor.endTiming('complex_date_range_render');
    expect(duration).toBeLessThan(80);
  });

  it('should maintain performance with frequent status updates', () => {
    const { rerender } = render(<Sandbox {...defaultProps} />);

    performanceMonitor.startTiming('frequent_status_updates');

    // Simulate frequent status updates (like polling)
    for (let i = 0; i < 50; i++) {
      const newProps = {
        ...defaultProps,
        testing: {
          ...defaultProps.testing,
          status: i % 2 === 0 ? 'PENDING' : 'STARTED',
          testId: `test_${i}`
        }
      };
      rerender(<Sandbox {...newProps} />);
    }

    const duration = performanceMonitor.endTiming('frequent_status_updates');
    expect(duration).toBeLessThan(200);
  });

  it('should handle large comparison rule sets efficiently', () => {
    const largeComparisonProps = {
      ...defaultProps,
      comparisonRules: Array.from({ length: 200 }, (_, i) => ({
        code: `COMP_${i}`,
        label: `Comparison Rule ${i} with long description`
      }))
    };

    performanceMonitor.startTiming('large_comparison_rules_render');

    render(<Sandbox {...largeComparisonProps} />);

    const duration = performanceMonitor.endTiming('large_comparison_rules_render');
    expect(duration).toBeLessThan(150);
  });

  it('should optimize useCallback dependencies correctly', () => {
    const { rerender } = render(<Sandbox {...defaultProps} />);

    // Change unrelated props that shouldn't affect memoized callbacks
    const newProps = {
      ...defaultProps,
      testing: { ...defaultProps.testing, testId: 'new_test_id' }
    };

    performanceMonitor.startTiming('callback_dependency_optimization');

    rerender(<Sandbox {...newProps} />);

    const duration = performanceMonitor.endTiming('callback_dependency_optimization');
    expect(duration).toBeLessThan(25);
  });
});
