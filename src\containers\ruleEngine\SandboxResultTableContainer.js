import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSandboxViolationDetails } from 'actions/sandboxingActions';
import SandboxResultTable from 'components/ruleEngine/SandboxResultTable';

const mapStateToProps = (state) => ({
  data: state.sandboxing.violationDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchViolationDetails: bindActionCreators(onFetchSandboxViolationDetails, dispatch)
});
const SandboxResultTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(SandboxResultTable);

export default SandboxResultTableContainer;
