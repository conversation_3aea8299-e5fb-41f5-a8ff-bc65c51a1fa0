import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/dynamicCountersAction';
import responses from 'mocks/responses';

const mockedStore = {
  dynamicCounters: {}
};

describe('dynamic Counters actions', () => {
  it('should fetch Dynamic Counters List', () => {
    const expectedActions = [
      { type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING },
      {
        type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS,
        response: responses.dynamicCounters.counters
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchDynamicCountersList('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Create Dynamic Counter', () => {
    const formData = {
      identifier: 'merchantCummulativeMonthlyAmt2',
      description: 'calculate',
      period: 'Monthly',
      primaryAttribute: 'entityId',
      isSingleAttribute: 1,
      conditionsList: [{ field: 'isLien', operator: '=', value: "'11'" }],
      channel: 'frm'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: {} }
      },
      { type: types.ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING },
      { type: types.ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL },
      { type: types.ON_FETCH_DSL_HELPERS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onCreateDynamicCounter(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Conditional Attributes', () => {
    const expectedActions = [
      { type: types.ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING },
      {
        type: types.ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS,
        response: responses.dynamicCounters.conditionalAttributes
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchConditionalAttributes('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch All Attributes', () => {
    const expectedActions = [
      { type: types.ON_FETCH_ALL_ATTRIBUTES_LOADING },
      {
        type: types.ON_FETCH_ALL_ATTRIBUTES_SUCCESS,
        response: responses.dynamicCounters.allAttributes
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchAllAttributes('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Sub Attributes', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SUB_ATTRIBUTES_LOADING },
      {
        type: types.ON_FETCH_SUB_ATTRIBUTES_SUCCESS,
        response: responses.dynamicCounters.subAttributes
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSubAttributes('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
