/**
 * Test Suite Configuration for RuleEngine Performance and Integration Tests
 *
 * This configuration file sets up performance thresholds, test environments,
 * and utilities for comprehensive testing of the ruleEngine module.
 */

import performanceMonitor from 'utility/performanceMonitor';

// Performance thresholds (in milliseconds)
export const PERFORMANCE_THRESHOLDS = {
  // Component render times
  COMPONENT_RENDER: {
    SMALL: 50, // Components with < 10 items
    MEDIUM: 100, // Components with 10-100 items
    LARGE: 200, // Components with 100-500 items
    XLARGE: 400 // Components with 500+ items
  },

  // User interaction response times
  USER_INTERACTION: {
    CLICK: 30,
    INPUT_CHANGE: 40,
    FORM_SUBMIT: 100,
    SEARCH_FILTER: 50
  },

  // Data processing times
  DATA_PROCESSING: {
    FILTER_OPERATION: 30,
    SORT_OPERATION: 50,
    BULK_OPERATION: 100,
    VALIDATION: 80
  },

  // Memory thresholds (in MB)
  MEMORY: {
    GROWTH_WARNING: 10,
    GROWTH_ERROR: 25,
    LEAK_DETECTION: 5
  }
};

// Test data generators
export const TEST_DATA_GENERATORS = {
  createRuleList: (count, prefix = 'RULE') =>
    Array.from({ length: count }, (_, i) => ({
      code: `${prefix}_${i}`,
      name: `${prefix} ${i}`,
      logic: `TXN.AMOUNT > ${1000 + i * 100}`,
      order: i + 1,
      status: ['ACTIVE', 'INACTIVE', 'PENDING'][i % 3],
      createdBy: `user${i % 5}`,
      createdTimestamp: new Date(Date.now() - i * 86400000).toISOString(),
      tags: [`tag${i % 3}`, `category${i % 4}`]
    })),

  createActionList: (count) =>
    Array.from({ length: count }, (_, i) => ({
      actionCode: `${i + 1}`.padStart(2, '0'),
      actionName: `ACTION_${i + 1}`,
      description: `Action ${i + 1} description`
    })),

  createHelperList: (count, type = 'function') =>
    Array.from({ length: count }, (_, i) => ({
      name: `${type.toUpperCase()}_${i}`,
      description: `${type} ${i} description`,
      category: type,
      complexity: i % 3
    })),

  createComplexRuleData: (count) =>
    Array.from({ length: count }, (_, i) => ({
      code: `COMPLEX_${i}`,
      name: `Complex Rule ${i}`,
      logic: `(TXN.AMOUNT > ${1000 + i} AND CARD.TYPE == "CREDIT") OR (MERCHANT.CATEGORY == "HIGH_RISK")`,
      metadata: {
        complexity: 'HIGH',
        conditions: Array.from({ length: (i % 5) + 1 }, (_, j) => ({
          field: `field${j}`,
          operator: ['>', '<', '==', '!='][j % 4],
          value: `value${j}`
        })),
        tags: Array.from({ length: (i % 3) + 1 }, (_, k) => `tag${k}`)
      }
    }))
};

// Mock store factory
export const createMockStore = (customState = {}) => {
  const defaultState = {
    ruleCreation: {
      loader: false,
      error: false,
      errorMessage: '',
      actionList: TEST_DATA_GENERATORS.createActionList(20),
      alertCategories: Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        categoryName: `Category ${i + 1}`
      })),
      ruleChannels: [{ name: 'FRM' }, { name: 'STR' }, { name: 'RPSL' }],
      fraudCategories: Array.from({ length: 15 }, (_, i) => ({
        id: i + 1,
        name: `Fraud Category ${i + 1}`
      })),
      ruleLabels: Array.from({ length: 25 }, (_, i) => ({
        id: i + 1,
        name: `Label ${i + 1}`
      })),
      validation: { status: true, message: '' },
      helperList: {
        frm: {
          prefix: TEST_DATA_GENERATORS.createHelperList(50, 'prefix'),
          functions: TEST_DATA_GENERATORS.createHelperList(30, 'function'),
          counters: TEST_DATA_GENERATORS.createHelperList(40, 'counter'),
          parameters: TEST_DATA_GENERATORS.createHelperList(35, 'parameter'),
          operators: [
            { name: '>', description: 'Greater than' },
            { name: '<', description: 'Less than' },
            { name: '==', description: 'Equal to' },
            { name: '!=', description: 'Not equal to' }
          ]
        }
      },
      nonProductionRules: {
        list: {
          frm: TEST_DATA_GENERATORS.createRuleList(25, 'NPR'),
          str: TEST_DATA_GENERATORS.createRuleList(15, 'STR_NPR')
        }
      }
    },
    ruleConfigurator: {
      productionRules: {
        list: {
          frm: TEST_DATA_GENERATORS.createRuleList(100, 'PROD'),
          str: TEST_DATA_GENERATORS.createRuleList(75, 'STR_PROD')
        }
      },
      archievedRules: {
        list: {
          frm: TEST_DATA_GENERATORS.createRuleList(50, 'ARCH')
        }
      }
    },
    auth: {
      moduleType: 'acquirer',
      userCreds: {
        roles: 'supervisor',
        channels: ['frm', 'str', 'rpsl']
      }
    },
    user: {
      hasMakerChecker: true,
      configurations: {
        sandbox: 1,
        cognitive: 1,
        acquirerPortals: 1
      }
    },
    toggle: {
      theme: 'light',
      ruleCreateModal: { frm: false, str: false },
      ruleEditModal: { frm: false, str: false },
      ruleDuplicateModal: { frm: false, str: false }
    },
    prefiltersList: {
      allLists: {
        data: Array.from({ length: 20 }, (_, i) => ({
          categoryName: `Category ${i}`,
          listName: `List ${i}`
        }))
      }
    },
    snoozeRules: {
      list: {
        frm: Array.from({ length: 10 }, (_, i) => ({
          ruleCode: `SNOOZE_${i}`,
          reason: `Snooze reason ${i}`
        }))
      }
    },
    sandboxing: {
      testHistory: Array.from({ length: 30 }, (_, i) => ({
        testId: `test_${i}`,
        ruleName: `Test Rule ${i}`,
        status: ['COMPLETED', 'FAILED', 'PENDING'][i % 3]
      }))
    }
  };

  return { ...defaultState, ...customState };
};

// Performance test utilities
export const performanceTestUtils = {
  /**
   * Measure component render time
   */
  measureRender: (
    renderFunction,
    componentName,
    threshold = PERFORMANCE_THRESHOLDS.COMPONENT_RENDER.MEDIUM
  ) => {
    performanceMonitor.startTiming(`${componentName}_render`);
    const result = renderFunction();
    const duration = performanceMonitor.endTiming(`${componentName}_render`);

    expect(duration).toBeLessThan(threshold);
    return { result, duration };
  },

  /**
   * Measure user interaction response time
   */
  measureInteraction: (
    interactionFunction,
    interactionName,
    threshold = PERFORMANCE_THRESHOLDS.USER_INTERACTION.CLICK
  ) => {
    performanceMonitor.startTiming(`${interactionName}_interaction`);
    const result = interactionFunction();
    const duration = performanceMonitor.endTiming(`${interactionName}_interaction`);

    expect(duration).toBeLessThan(threshold);
    return { result, duration };
  },

  /**
   * Check for memory leaks
   */
  checkMemoryLeaks: (testFunction, testName) => {
    performanceMonitor.takeMemorySnapshot(`${testName}_before`);
    testFunction();
    performanceMonitor.takeMemorySnapshot(`${testName}_after`);
    performanceMonitor.checkMemoryLeaks();
  },

  /**
   * Stress test with large datasets
   */
  stressTest: (testFunction, dataSize, componentName) => {
    const threshold =
      // eslint-disable-next-line no-nested-ternary
      dataSize > 500
        ? PERFORMANCE_THRESHOLDS.COMPONENT_RENDER.XLARGE
        : dataSize > 100
          ? PERFORMANCE_THRESHOLDS.COMPONENT_RENDER.LARGE
          : PERFORMANCE_THRESHOLDS.COMPONENT_RENDER.MEDIUM;

    return performanceTestUtils.measureRender(
      testFunction,
      `${componentName}_stress_${dataSize}`,
      threshold
    );
  }
};

// Test environment setup
export const setupTestEnvironment = () => {
  // Enable performance monitoring for tests
  performanceMonitor.setEnabled(true);

  // Mock console methods to avoid noise in tests
  const originalConsole = { ...console };
  // eslint-disable-next-line no-console
  console.warn = jest.fn();
  // eslint-disable-next-line no-console
  console.error = jest.fn();

  return {
    cleanup: () => {
      performanceMonitor.setEnabled(false);
      performanceMonitor.reset();
      Object.assign(console, originalConsole);
    }
  };
};

// Test categories for organization
export const TEST_CATEGORIES = {
  PERFORMANCE: 'performance',
  INTEGRATION: 'integration',
  MEMORY: 'memory',
  STRESS: 'stress',
  REGRESSION: 'regression'
};

// Export default configuration
export default {
  PERFORMANCE_THRESHOLDS,
  TEST_DATA_GENERATORS,
  createMockStore,
  performanceTestUtils,
  setupTestEnvironment,
  TEST_CATEGORIES
};
