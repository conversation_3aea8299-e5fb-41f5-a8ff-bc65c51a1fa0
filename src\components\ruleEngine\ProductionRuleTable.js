'use strict';
import {
  faPencil,
  faClone,
  faChartBar,
  faStopwatch,
  faTrash
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useCallback } from 'react';
import { useLocation, withRouter } from 'react-router-dom';
import { Button, Input, FormGroup } from 'reactstrap';

import ConfirmAlert from 'components/common/ConfirmAlert';
import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';
import RuleSnoozeModalContainer from 'containers/common/SnoozeRuleModalContainer';
import RuleForm from 'containers/ruleEngine/RuleFormContainer';
import RuleTableContainer from 'containers/ruleEngine/RuleTableContainer';
import { getFilterValue } from 'utility/utils';

import DeactivateRuleModal from './DeactivateRuleModal';

const ProductionRuleTable = ({
  role,
  theme,
  toggle,
  channel,
  snoozelist,
  hasSandbox,
  toggleEditModal,
  clearValidation,
  ruleList,
  handleCreateSubmit,
  ruleConfiguratorActions,
  toggleRuleDuplicateModal,
  updateSelectedProductionRules,
  snoozeActions
}) => {
  const { state } = useLocation();
  const [selectedDuplicateRule, setSelectedDuplicateRule] = useState({});
  const [tableFilters, setTableFilters] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedRules, setSelectedRules] = useState([]);
  const [deleteRule, setDeleteRule] = useState({});
  const [toggleDelete, setToggleDelete] = useState(false);
  const [displayModal, setDisplayModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [toggleDirectSnooze, setToggleDirectSnooze] = useState(false);
  const [ruleActivationFormData, setRuleActivationFormData] = useState(null);

  useEffect(() => {
    ruleConfiguratorActions.onFetchRulesList(channel);
    state?.name && setTableFilters([{ id: 'name', value: state?.name }]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    updateSelectedProductionRules(selectedRules);
  }, [selectedRules, updateSelectedProductionRules]);

  useEffect(() => {
    !_.isEmpty(tableFilters) &&
      !_.isEmpty(selectedRules) &&
      setSelectedRules(filteredRuleList(selectedRules));
    selectAll && setSelectAll(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableFilters]);

  // Memoize filtered rule list for better performance
  const filteredRuleList = useCallback(
    (ruleList) => {
      if (_.isEmpty(tableFilters)) return ruleList;

      return _.filter(ruleList, (rule) =>
        _.every(
          tableFilters,
          (filter) =>
            _.has(rule, filter.id) &&
            _.includes(_.lowerCase(rule[filter.id]), _.lowerCase(filter.value))
        )
      );
    },
    [tableFilters]
  );

  const handleSelectAll = useCallback(() => {
    if (!selectAll) {
      const rulesToSelect = _.isEmpty(tableFilters)
        ? ruleList.list[channel]
        : filteredRuleList(ruleList.list[channel]);
      setSelectedRules(rulesToSelect);
    } else setSelectedRules([]);

    setSelectAll(!selectAll);
  }, [selectAll, tableFilters, ruleList.list, channel, filteredRuleList]);

  const handleCheckboxChange = (rule) => {
    if (_.includes(selectedRules, rule)) {
      setSelectedRules((prev) => _.filter(prev, (prevRule) => prevRule.code !== rule.code));
      setSelectAll(false);
    } else {
      const newList = [...selectedRules, rule];
      setSelectedRules(newList);
      setSelectAll(newList.length === ruleList.list[channel].length);
    }
  };

  const toggleRuleStatus = (code, prevState) => {
    const status = prevState === 'Activated' ? 'Suspended' : 'Activated';
    const formData = { code, status, channel };
    ruleConfiguratorActions.onToggleRuleStatus(formData);
  };

  const toggleRuleActivation = (code, prevState) => {
    const active = prevState == 'Enable' ? 'Disable' : 'Enable';
    const formData = { code, active, channel, ruleDeactivationDate: '' };
    setRuleActivationFormData(formData);

    if (prevState === 'Enable') setToggleDirectSnooze(!toggleDirectSnooze);
    else ruleConfiguratorActions.onToggleRuleActivation(formData);
  };

  const handleDeactivateRule = (ruleDeactivationDate) => {
    if (ruleDeactivationDate) {
      const formData = {
        ...ruleActivationFormData,
        ruleDeactivationDate
      };
      ruleConfiguratorActions.onToggleRuleActivation(formData);
    }
  };

  const snoozeRule = ({ ruleCode, ruleName }) => {
    setDisplayModal(!displayModal);
    setSelectedRule({ ruleCode, ruleName });
  };

  const toggleRuleExplicit = (rule) => {
    const formData = { ...rule, channel, explicit: !rule.explicit };
    ruleConfiguratorActions.onToggleRuleExplicit(formData);
  };

  const toggleDuplicateModal = (rule) => {
    if (!toggle.ruleDuplicateModal[channel]) setSelectedDuplicateRule(rule);
    clearValidation();
    toggleRuleDuplicateModal(channel);
  };

  const snoozedRuleCodes = _.chain(snoozelist[channel])
    .filter((rule) => rule.isApproved === 1)
    .map((rule) => rule.code)
    .value();

  const confirmDelete = (isOpen, rule) => {
    if (!_.isEmpty(rule) && !isOpen) {
      setDeleteRule(rule);
      setToggleDelete(true);
    } else {
      setDeleteRule({});
      setToggleDelete(false);
    }
  };

  const deleteSelectedRule = (rule, channel) => {
    ruleConfiguratorActions.onDeleteRule(rule, channel);
    setToggleDelete(false);
  };

  const actionHeaders = _.includes(['checker', 'investigator', 'supervisor'], role)
    ? [
        {
          Header: (
            <Input
              bsSize="md"
              type="checkbox"
              id="selectAll"
              onChange={handleSelectAll}
              checked={selectAll}
              disabled={ruleList.list[channel].length === 0}
            />
          ),
          accessor: 'caseRefNo',
          searchable: false,
          sortable: false,
          filterable: false,
          minWidth: 50,

          Cell: (row) => (
            <Input
              type="checkbox"
              value={row.original.code}
              id={row.original.code}
              name="tableSelect[]"
              checked={_.includes(selectedRules, row.original)}
              onChange={() => handleCheckboxChange(row.original)}
            />
          )
        },
        {
          Header: 'Actions',
          filterable: false,
          sortable: false,
          minWidth: 180,
          Cell: (row) => {
            const renderCheckerActions = () => {
              if (role === 'checker')
                return (
                  <>
                    <Button
                      outline
                      size="sm"
                      color="warning"
                      title="Edit rule"
                      onClick={() => toggleEditModal(row.original, 'production')}>
                      <FontAwesomeIcon icon={faPencil} />
                    </Button>
                    <Button
                      outline
                      size="sm"
                      color="primary"
                      className="ms-1"
                      title="Duplicate rule"
                      onClick={() => toggleDuplicateModal(row.original)}>
                      <FontAwesomeIcon icon={faClone} />
                    </Button>
                  </>
                );

              return null;
            };

            const renderSupervisorActions = () => {
              // Supervisor: delete button
              if (role === 'supervisor')
                return (
                  <Button
                    outline
                    size="sm"
                    color="danger"
                    className="ms-1"
                    title="Delete rule"
                    onClick={() => confirmDelete(toggleDelete, row.original)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                );

              // Checker: snooze button
              const isSnoozed = row.original.code === 1;
              const canSnooze = !isCooperative && channel !== 'str' && role === 'checker';

              if (canSnooze) {
                const snoozeProps = {
                  outline: !isSnoozed,
                  size: 'sm',
                  color: 'primary',
                  className: 'ms-1',
                  disabled: isSnoozed,
                  title: isSnoozed ? 'Rule is Snoozed!' : 'Snooze Rule',
                  onClick: () =>
                    snoozeRule({
                      ruleCode: row.original.code,
                      ruleName: row.original.name
                    })
                };

                return (
                  <Button {...snoozeProps}>
                    <FontAwesomeIcon icon={faStopwatch} />
                  </Button>
                );
              }

              // Default: no action
              return null;
            };

            return (
              <>
                {renderCheckerActions()}
                {renderSupervisorActions()}
                {channel === 'frm' && hasSandbox === 1 && role === 'checker' && (
                  <Button
                    outline
                    size="sm"
                    color="success"
                    className="ms-1"
                    title="Sandbox testing"
                    onClick={() => toggleEditModal(row.original, 'production', true)}>
                    <FontAwesomeIcon icon={faChartBar} />
                  </Button>
                )}
              </>
            );
          }
        }
      ]
    : [];

  !isCooperative &&
    actionHeaders.push({
      Header: '',
      filterable: false,
      accessor: 'code',
      minWidth: 40,
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) =>
        _.includes(snoozedRuleCodes, value) ? (
          <FontAwesomeIcon className="text-info" size="xl" icon={faStopwatch} title="Snoozed" />
        ) : null
    });

  const typeHeaders = [
    {
      Header: 'Status',
      accessor: 'status',
      Cell: (row) => (
        <FormGroup switch>
          <Input
            type="switch"
            role="switch"
            id={`statusSwitch${row.original.code}`}
            name="statusSwitch[]"
            value={row.original.code}
            disabled={
              _.includes(['investigator'], role) || _.includes(snoozedRuleCodes, row.original.code)
            }
            checked={row.original.status === 'Activated'}
            onChange={() => toggleRuleStatus(row.original.code, row.original.status)}
          />
        </FormGroup>
      ),
      filterMethod: (filter, row) => row[filter.id] === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('status', tableFilters)}>
          <option value="">All</option>
          <option value="Activated">Activated</option>
          <option value="Suspended">Suspended</option>
        </select>
      )
    },
    {
      Header: 'Active',
      accessor: 'active',
      Cell: (row) => (
        <FormGroup switch>
          <Input
            type="switch"
            role="switch"
            id={`activeSwitch${row.original.code}`}
            name="activeSwitch[]"
            value={row.original.code}
            checked={row.original.active === 'Enable'}
            disabled={
              row.original.status === 'Suspended' ||
              _.includes(['checker', 'investigator'], role) ||
              _.includes(snoozedRuleCodes, row.original.code)
            }
            onChange={() => toggleRuleActivation(row.original.code, row.original.active)}
          />
        </FormGroup>
      ),
      filterMethod: (filter, row) => row[filter.id] === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('active', tableFilters)}>
          <option value="">All</option>
          <option value="Enable">Enabled</option>
          <option value="Disable">Disabled</option>
        </select>
      )
    }
  ];

  channel === 'frm' &&
    typeHeaders.push({
      Header: 'Create Case',
      accessor: 'explicit',
      Cell: (row) => (
        <FormGroup switch>
          <Input
            type="switch"
            role="switch"
            id={`explicitSwitch${row.original.code}`}
            name="explicitSwitch[]"
            value={row.original.code}
            disabled={_.includes(['checker', 'investigator'], role)}
            checked={row.original.explicit}
            onChange={() => toggleRuleExplicit(row.original)}
          />
        </FormGroup>
      ),
      filterMethod: (filter, row) => row[filter.id] === (filter.value === 'true'),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={getFilterValue('explicit', tableFilters)}>
          <option value="">All</option>
          <option value={true}>Yes</option>
          <option value={false}>No</option>
        </select>
      )
    });

  return (
    <>
      <RuleTableContainer
        channel={channel}
        data={ruleList}
        actionHeaders={actionHeaders}
        typeHeaders={typeHeaders}
        tableFilters={tableFilters}
        setTableFilters={setTableFilters}
        defaultSort={[
          {
            id: 'order',
            desc: false
          }
        ]}
        defaultSortMethod={(a, b) => {
          // force null and undefined to the bottom
          a = a === null || a === undefined ? '' : a;
          b = b === null || b === undefined ? '' : b;
          // force any string values to lowercase
          a = typeof a === 'string' ? a.toLowerCase() : a;
          b = typeof b === 'string' ? b.toLowerCase() : b;
          if (a === '' && b !== '') return 1;
          if (b === '' && a !== '') return -1;
          // Return either 1 or -1 to indicate a sort priority
          if (a > b) return 1;

          if (a < b) return -1;

          // returning 0, undefined or any falsey value will use subsequent sorts or
          // the index as a tiebreaker
          return 0;
        }}
      />

      <DeactivateRuleModal
        isOpen={toggleDirectSnooze}
        toggleModal={() => setToggleDirectSnooze(!toggleDirectSnooze)}
        onSubmit={handleDeactivateRule}
      />

      <ModalContainer
        size="xl"
        theme={`${toggle.theme} rule-modal`}
        header="Duplicate Rule"
        isOpen={toggle.ruleDuplicateModal[channel]}
        toggle={() => toggleDuplicateModal()}>
        <RuleForm
          formName="duplicate"
          channel={channel}
          formData={selectedDuplicateRule}
          submit={handleCreateSubmit}
        />
      </ModalContainer>
      <ConfirmAlert
        theme={theme}
        confirmAlertModal={toggleDelete}
        toggleConfirmAlertModal={() => confirmDelete(toggleDelete)}
        confirmationAction={() => deleteSelectedRule(deleteRule, channel)}
        confirmAlertTitle={`Are you sure you want to delete rule - ${deleteRule?.name || ''} ?`}
      />

      {selectedRule && (
        <RuleSnoozeModalContainer
          show={displayModal}
          onToggle={() => setDisplayModal(!displayModal)}
          ruleCode={selectedRule.ruleCode}
          ruleName={selectedRule.ruleName}
          channel={channel}
        />
      )}
    </>
  );
};

ProductionRuleTable.propTypes = {
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  toggle: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  clearValidation: PropTypes.func.isRequired,
  handleCreateSubmit: PropTypes.func.isRequired,
  ruleList: PropTypes.object.isRequired,
  toggleRuleDuplicateModal: PropTypes.func.isRequired,
  ruleConfiguratorActions: PropTypes.object.isRequired,
  toggleEditModal: PropTypes.func.isRequired,
  snoozelist: PropTypes.object.isRequired,
  updateSelectedProductionRules: PropTypes.func.isRequired,
  snoozeActions: PropTypes.object.isRequired
};

export default React.memo(withRouter(ProductionRuleTable));
