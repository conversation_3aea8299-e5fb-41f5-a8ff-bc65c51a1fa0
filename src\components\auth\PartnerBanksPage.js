/* eslint-disable react/prop-types */
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import ReactTable from 'react-table';
import { Button, Form, FormGroup, Label, Input, FormFeedback, Alert, InputGroup } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';

const PartnerBanksPage = ({
  userRoles,
  actions,
  partnerBanks,
  toggleAddBankModal,
  theme,
  addBankModal
}) => {
  const history = useHistory();
  const [partnerName, setPartnerName] = useState('');
  const [alert, setalert] = useState({
    visible: false,
    message: '',
    type: ''
  });
  const [invalidPartnerName, setInvalidPartnerName] = useState(false);
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(() => {
    document.title = 'BANKiQ FRC | Banks';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  useEffect(() => {
    if (_.isEmpty(partnerBanks.list) && !partnerBanks.loader) actions.onFetchPartnerBanksList();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (userRoles !== 'super-admin') history.goBack();
  }, [history, userRoles]);

  useEffect(() => {
    const existingUser = partnerBanks.list.filter((bank) => bank.partnerName === partnerName);
    setInvalidPartnerName(!_.isEmpty(existingUser));
  }, [partnerBanks.list, partnerName]);

  const toggleAlert = (message = '', type = '') =>
    setalert({
      type: !_.isEmpty(message) ? type : '',
      visible: !_.isEmpty(message),
      message
    });

  const addPartnerSubmit = (e) => {
    e.preventDefault();
    if (invalidPartnerName) {
      toggleAlert('Invalid partner name', 'danger');
      return false;
    }

    const bankData = {
      partnerName
    };
    const formData = { bankData };
    actions.onAddPartnerBank(formData);
    clearModalValue();
  };

  const clearModalValue = () => {
    setPartnerName('');
    setalert({
      visible: false,
      message: '',
      type: ''
    });
    setInvalidPartnerName(false);
  };

  const action = (
    <Button
      size="sm"
      color="primary"
      onClick={() => {
        toggleAddBankModal();
        clearModalValue();
      }}>
      Add partner bank
    </Button>
  );

  const table_header = [
    { Header: 'Partner Id', accessor: 'id' },
    { Header: 'Partner Name', accessor: 'partnerName' },
    {
      Header: 'Registration Date',
      accessor: 'registrationDate',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] && moment(row[filter.id]).format('YYYY-MM-DD') === filter.value,
      Filter: ({ onChange }) => (
        <input
          type="date"
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'registrationDate']))
              ? _.find(tableFilters, ['id', 'registrationDate']).value
              : ''
          }
        />
      )
    },
    {
      Header: 'Active',
      accessor: 'isActive',
      Cell: ({ value }) => (value === 1 ? 'Active' : 'Inactive'),
      filterMethod: (filter, row) => +row[filter.id] === +filter.value,
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'isActive']))
              ? _.find(tableFilters, ['id', 'isActive']).value
              : ''
          }>
          <option value="">All</option>
          <option value={1}>Active</option>
          <option value={0}>Inactive</option>
        </select>
      )
    }
  ];

  return (
    <div className="content-wrapper ">
      <CardContainer title="Bank List" action={action}>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
          }
          data={partnerBanks.list}
          columns={table_header}
          noDataText="No banks found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={6}
          filtered={tableFilters}
          onFilteredChange={(filtered) => setTableFilters(filtered)}
          className="-highlight"
        />
      </CardContainer>

      <ModalContainer
        theme={theme}
        isOpen={addBankModal}
        header="Add partner bank"
        size="md"
        toggle={() => toggleAddBankModal()}>
        <Form onSubmit={addPartnerSubmit} autoComplete="off" autoSave="off">
          <Alert color={alert.type} isOpen={alert.visible} toggle={() => toggleAlert()}>
            {alert.message}
          </Alert>
          <FormGroup>
            <Label for="partnerName">Partner Name</Label>
            <InputGroup>
              <Input
                type="text"
                id="partnerName"
                name="partnerName"
                title="Invalid partner name"
                onChange={(event) => setPartnerName(event.target.value)}
                value={partnerName}
                required
                invalid={invalidPartnerName || undefined}
              />
            </InputGroup>
            <FormFeedback
              invalid={invalidPartnerName || undefined}
              className={invalidPartnerName ? 'block' : 'none'}>
              Partner already registered.
            </FormFeedback>
          </FormGroup>
          <FormGroup>
            <Button size="sm" color="primary" className="d-flex ms-auto">
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </div>
  );
};

PartnerBanksPage.propTypes = {
  partnerBanks: PropTypes.object.isRequired,
  userRoles: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  addBankModal: PropTypes.bool.isRequired,
  actions: PropTypes.object.isRequired,
  toggleAddBankModal: PropTypes.func.isRequired
};

export default PartnerBanksPage;
