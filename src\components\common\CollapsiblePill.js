import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { Collapse, Button, ButtonGroup } from 'reactstrap';

const CollapsiblePill = ({ name, children, addOn = null, onClick = null }) => {
  const [collapse, setCollapse] = useState(false);

  const onClickAction = () => {
    setCollapse(!collapse);
    if (onClick) onClick();
  };

  return (
    <div className="collapsible-pill-container">
      <ButtonGroup>
        <Button color="primary" onClick={onClickAction}>
          {name}
        </Button>
        {addOn}
      </ButtonGroup>
      <Collapse isOpen={collapse}>{children}</Collapse>
    </div>
  );
};

CollapsiblePill.propTypes = {
  onClick: PropTypes.func,
  addOn: PropTypes.element,
  name: PropTypes.string.isRequired,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]).isRequired
};

export default CollapsiblePill;
