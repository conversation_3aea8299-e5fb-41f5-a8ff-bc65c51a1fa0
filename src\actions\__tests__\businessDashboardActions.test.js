import responses from 'mocks/responses';

import * as actions from 'actions/businessDashboardActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  auth: { userCreds },
  businessDashboard: {}
};

describe('business dashboard actions', () => {
  it('should fetch business kpis', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      xchannelId: '1'
    };

    const expectedActions = [
      { type: types.ON_FETCH_BUSINESS_KPI_LOADING },
      {
        type: types.ON_FETCH_BUSINESS_KPI_SUCCESS,
        response: responses.businessDashboard.businessKpis
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchBusinessKpis(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Action Share', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      xchannelId: '1'
    };

    const expectedActions = [
      { type: types.ON_FETCH_ACTIONS_SHARE_LOADING },
      {
        type: types.ON_FETCH_ACTIONS_SHARE_SUCCESS,
        response: responses.businessDashboard.actionShare
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchActionShare(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch High Alert Customer', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00'
    };

    const expectedActions = [
      { type: types.ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING },
      {
        type: types.ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS,
        response: responses.businessDashboard.highAlertCustomers
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchHighAlertCustomer(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Rule Category Trend', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      xchannelId: '1'
    };

    const expectedActions = [
      { type: types.ON_FETCH_RULE_CATEGORY_TREND_LOADING },
      {
        type: types.ON_FETCH_RULE_CATEGORY_TREND_SUCCESS,
        response: responses.businessDashboard.ruleCategoryTrend
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchRuleCategoryTrend(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch No Violation Fraud', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      xchannelId: '1'
    };

    const expectedActions = [
      { type: types.ON_FETCH_NO_VIOLATION_FRAUD_LOADING },
      {
        type: types.ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS,
        response: responses.businessDashboard.noViolationFraud
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchNoViolationFraud(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
