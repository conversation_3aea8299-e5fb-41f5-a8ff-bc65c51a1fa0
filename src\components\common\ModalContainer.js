import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, ModalBody } from 'reactstrap';

const ModalContainer = ({ isOpen, toggle, size, children, header, theme, allowClose = true }) => {
  const emptyButton = <span>{''}</span>;
  const props = {
    ...(!allowClose && { close: emptyButton })
  };

  return (
    <div>
      <Modal
        className={theme}
        isOpen={isOpen}
        toggle={allowClose ? toggle : () => null}
        size={size}>
        <ModalHeader className={theme} toggle={allowClose ? toggle : () => null} {...props}>
          {header}
        </ModalHeader>
        <ModalBody className={theme}>{children}</ModalBody>
      </Modal>
    </div>
  );
};

ModalContainer.propTypes = {
  size: PropTypes.string,
  allowClose: PropTypes.bool,
  isOpen: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired,
  theme: PropTypes.string.isRequired,
  header: PropTypes.string.isRequired,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.element
  ]).isRequired
};

export default ModalContainer;
