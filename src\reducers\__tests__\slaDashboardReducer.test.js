import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import slaDashboardReducer from 'reducers/slaDashboardReducer';

describe('slaDashboard Reducer', () => {
  it('should return the intial state', () => {
    expect(slaDashboardReducer(undefined, {})).toEqual(initialState.slaDashboard);
  });

  it('should handle ON_FETCH_SLA_KPI_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          slaKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_KPI_LOADING
        }
      )
    ).toEqual({
      slaKpis: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SLA_KPI_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          slaKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_KPI_SUCCESS,
          response: responses.slaDashboard.slaKpis
        }
      )
    ).toEqual({
      slaKpis: {
        data: responses.slaDashboard.slaKpis,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SLA_KPI_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          slaKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_KPI_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      slaKpis: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_EMPLOYEE_SLA_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          employeeSla: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_EMPLOYEE_SLA_LOADING
        }
      )
    ).toEqual({
      employeeSla: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_EMPLOYEE_SLA_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          employeeSla: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_EMPLOYEE_SLA_SUCCESS,
          response: responses.slaDashboard.employeeSla
        }
      )
    ).toEqual({
      employeeSla: {
        data: responses.slaDashboard.employeeSla,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_EMPLOYEE_SLA_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          employeeSla: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_EMPLOYEE_SLA_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      employeeSla: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_OCCUPANCY_RATE_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          occupancyRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_OCCUPANCY_RATE_LOADING
        }
      )
    ).toEqual({
      occupancyRate: {
        data: 0,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_OCCUPANCY_RATE_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          occupancyRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_OCCUPANCY_RATE_SUCCESS,
          response: responses.slaDashboard.occupancyRate
        }
      )
    ).toEqual({
      occupancyRate: {
        data: responses.slaDashboard.occupancyRate,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_OCCUPANCY_RATE_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          occupancyRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_OCCUPANCY_RATE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      occupancyRate: {
        data: 0,
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SLA_BREACH_CASES_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          slaBreachCases: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_BREACH_CASES_LOADING
        }
      )
    ).toEqual({
      slaBreachCases: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SLA_BREACH_CASES_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          slaBreachCases: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_BREACH_CASES_SUCCESS,
          response: responses.slaDashboard.slaBreachCases
        }
      )
    ).toEqual({
      slaBreachCases: {
        data: responses.slaDashboard.slaBreachCases.transactions,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SLA_BREACH_CASES_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          slaBreachCases: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SLA_BREACH_CASES_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      slaBreachCases: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_FIRST_CONTACT_RATE_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          firstContactRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FIRST_CONTACT_RATE_LOADING
        }
      )
    ).toEqual({
      firstContactRate: {
        data: 0,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FIRST_CONTACT_RATE_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          firstContactRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FIRST_CONTACT_RATE_SUCCESS,
          response: responses.slaDashboard.firstContactRate
        }
      )
    ).toEqual({
      firstContactRate: {
        data: responses.slaDashboard.firstContactRate.resolutionRate,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_FIRST_CONTACT_RATE_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          firstContactRate: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_FIRST_CONTACT_RATE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      firstContactRate: {
        data: 0,
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SHIFT_DETAILS_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          shiftDetails: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SHIFT_DETAILS_LOADING
        }
      )
    ).toEqual({
      shiftDetails: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SHIFT_DETAILS_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          shiftDetails: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SHIFT_DETAILS_SUCCESS,
          response: responses.slaDashboard.slaBreachCases
        }
      )
    ).toEqual({
      shiftDetails: {
        data: responses.slaDashboard.slaBreachCases,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SHIFT_DETAILS_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          shiftDetails: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SHIFT_DETAILS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      shiftDetails: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_ANALYST_TAT_LOADING', () => {
    expect(
      slaDashboardReducer(
        {
          analystTAT: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ANALYST_TAT_LOADING
        }
      )
    ).toEqual({
      analystTAT: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ANALYST_TAT_SUCCESS', () => {
    expect(
      slaDashboardReducer(
        {
          analystTAT: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ANALYST_TAT_SUCCESS,
          response: responses.slaDashboard.analystTAT
        }
      )
    ).toEqual({
      analystTAT: {
        data: responses.slaDashboard.analystTAT.slaTimeTaken,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ANALYST_TAT_FAILURE', () => {
    expect(
      slaDashboardReducer(
        {
          analystTAT: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ANALYST_TAT_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      analystTAT: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
