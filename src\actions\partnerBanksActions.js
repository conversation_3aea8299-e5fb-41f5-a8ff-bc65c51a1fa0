import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader, onToggleAddBankModal } from 'actions/toggleActions';
import {
  ON_FETCH_PARTNER_BANK_LIST_LOADING,
  ON_FETCH_PARTNER_BANK_LIST_SUCCESS,
  ON_FETCH_PARTNER_BANK_LIST_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchPartnerBanksList() {
  return client({
    url: `useraccessmanagement/getPartners`,
    badRequestMessage: 'Unable to fetch partner banks.'
  });
}

function onFetchPartnerBanksListLoading() {
  return { type: ON_FETCH_PARTNER_BANK_LIST_LOADING };
}

function onFetchPartnerBanksListSuccess(response) {
  return {
    type: ON_FETCH_PARTNER_BANK_LIST_SUCCESS,
    response
  };
}

function onFetchPartnerBanksListFailure(response) {
  return {
    type: ON_FETCH_PARTNER_BANK_LIST_FAILURE,
    response
  };
}

function onFetchPartnerBanksList() {
  return function (dispatch) {
    dispatch(onFetchPartnerBanksListLoading());
    return fetchPartnerBanksList().then(
      (success) => dispatch(onFetchPartnerBanksListSuccess(success)),
      (error) => dispatch(onFetchPartnerBanksListFailure(error))
    );
  };
}

function addPartnerBank(formData) {
  return client({
    method: 'POST',
    url: `useraccessmanagement/partner/register`,
    data: formData.bankData,
    badRequestMessage: 'Unable to add partner bank. Please check input data'
  });
}

function onAddPartnerBank(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addPartnerBank(formData)
      .then(
        () => {
          dispatch(onFetchPartnerBanksList());
          dispatch(
            onShowSuccessAlert({
              message: 'Partner bank added successfully'
            })
          );
          dispatch(onToggleAddBankModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export { onFetchPartnerBanksList, onAddPartnerBank };
