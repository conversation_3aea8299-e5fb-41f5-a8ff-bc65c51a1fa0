import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';

const ApprovalModal = ({
  role,
  theme,
  caseId,
  status,
  channel,
  userList,
  bulkCaseIds,
  caseApproval,
  singleType = '',
  makerAction = ''
}) => {
  const [display, setDisplay] = useState(false);
  const [approval, setApproval] = useState('Rejected');
  const [comment, setComment] = useState('');

  useEffect(() => {
    clearModalValue();
  }, [display]);

  const poList = userList?.filter((user) => user.channelRoles?.includes('str:principal-officer'));

  const clearModalValue = () => {
    setApproval('Rejected');
    setComment('');
  };

  const submitApproval = (e) => {
    e.preventDefault();
    let formData = {
      comment,
      isApproved: approval == 'Approved',
      investigationStatus: status,
      ...(channel === 'str' &&
        approval == 'Approved' &&
        role === 'checker' && {
          poToBeEscalatedTo: +poList[0].id
        })
    };

    !isEmpty(bulkCaseIds) ? (formData.caseRefNos = bulkCaseIds) : (formData.caseRefNo = caseId);
    caseApproval(formData, channel, !isEmpty(bulkCaseIds) ? '/bulk' : singleType);
    clearModalValue();
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="success"
        title="Approve case"
        className="ms-1"
        disabled={isEmpty(bulkCaseIds) && caseId == ''}
        onClick={() => setDisplay(!display)}>
        <FontAwesomeIcon icon={faCheck} /> {isEmpty(singleType) && 'Approve Case'}
      </Button>
      <ModalContainer
        size={
          channel === 'str' &&
          role === 'checker' &&
          makerAction === 'File STR' &&
          approval === 'Approved'
            ? 'lg'
            : 'sm'
        }
        theme={theme}
        header={(channel == 'str' ? 'STR filing ' : 'Case verdict ') + 'approval'}
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form onSubmit={submitApproval}>
          <FormGroup>
            <Label>Approval</Label>
            <Input
              type="select"
              name="approval"
              id="approval"
              onChange={(e) => setApproval(e.target.value)}
              value={approval}
              required>
              <option>Rejected</option>
              <option>Approved</option>
            </Input>
          </FormGroup>
          <FormGroup>
            <Label>Comment</Label>
            <Input
              type="textarea"
              name="comment"
              id="comment"
              placeholder="comment"
              onChange={(e) => setComment(e.target.value)}
              value={comment}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button type="submit" size="sm" color="primary">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

ApprovalModal.propTypes = {
  singleType: PropTypes.string,
  bulkCaseIds: PropTypes.array,
  caseDetails: PropTypes.object,
  makerAction: PropTypes.string,
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  status: PropTypes.string.isRequired,
  caseId: PropTypes.string.isRequired,
  userList: PropTypes.array.isRequired,
  channel: PropTypes.string.isRequired,
  getMasters: PropTypes.func.isRequired,
  caseApproval: PropTypes.func.isRequired,
  strReportMasters: PropTypes.object.isRequired
};

export default ApprovalModal;
