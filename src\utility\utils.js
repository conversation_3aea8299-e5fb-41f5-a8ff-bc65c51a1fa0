import { JSEncrypt } from 'jsencrypt';
import _ from 'lodash';

import { PUB_KEY } from 'constants/publicKey';

export const findValueInTxnDetail = (key, txnDetail) => {
  if (_.isObject(txnDetail) && !_.isEmpty(txnDetail))
    return _.reduce(
      txnDetail,
      (result, value, k) => {
        if (result !== undefined) return result; // Return early if key is already found

        if (k === key)
          // Return 'value' property if it exists and is an object
          return _.isObject(value) && 'value' in value ? value.value : value;

        // Recursively search nested objects
        return _.isObject(value) ? findValueInTxnDetail(key, value) : undefined;
      },
      undefined
    );

  return undefined; // Return undefined if the object is not valid
};

export const encryptData = (data) => {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(PUB_KEY);
  return encrypt.encrypt(data);
};

// Retrieves the filter value for a given filter ID from the tableFilters array.
export const getFilterValue = (filterId, tableFilters) =>
  _.find(tableFilters, ['id', filterId])?.value || '';
