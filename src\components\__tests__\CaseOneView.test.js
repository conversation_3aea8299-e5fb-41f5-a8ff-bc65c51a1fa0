import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';
import configureStore from 'redux-mock-store';
import { BrowserRouter as Router } from 'react-router-dom';
import CaseOneView from '../caseReview/CaseOneView';
import moment from 'moment';
import initialState from 'reducers/initialState';

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

const defaultProps = {
  userId: 1,
  userName: 'testUser',
  oneViewData: {
    case: {
      loader: false,
      error: false,
      data: {
        txnId: '12345',
        caseRefNo: '67890',
        channel: 'frm',
        partnerId: 1,
        txnTimestamp: moment().toISOString(),
        childTxns: [],
        assignedTo: 'testUser',
        investigationStatus: 'Open',
        entityId: 'entity123'
      }
    }
  },
  clearHistory: jest.fn(),
  oneViewActions: {
    onFetchReviewerCase: jest.fn(),
    onReviewerCaseClose: jest.fn()
  },
  closeCaseBuckets: {
    list: []
  },
  communicationActions: {
    onFetchCustomerCommunicationLogs: jest.fn()
  },
  fetchCloseCaseBuckets: jest.fn(),
  parkCase: jest.fn(),
  theme: 'light'
};

const initialStateStore = {
  ...initialState,
  caseAssignment: {
    closeCaseBuckets: {
      list: [
        { id: 1, name: 'Fraud' },
        { id: 2, name: 'Non Fraud' }
      ],
      loader: false,
      error: false,
      errorMessage: ''
    }
  }
};

let store = mockStore(initialStateStore);

jest.mock('containers/caseReview/TransactionDetailsContainer', () => ({
  __esModule: true,
  default: ({ oneViewCase }) => (
    <div>
      <p>Transaction Details</p>
      <p data-testid="txnId">{oneViewCase.txnId}</p>
    </div>
  )
}));

jest.mock('containers/caseReview/OneViewActionsContainer', () => ({
  __esModule: true,
  default: jest.fn(({ addVerdict, fetchCase }) => (
    <div>
      <p>OneViewActionsContainer</p>
      <button onClick={() => addVerdict('fraud')} data-testid="add-verdict">
        Add Verdict
      </button>
      <button onClick={fetchCase} data-testid="fetch-case">
        Fetch Cases
      </button>
    </div>
  ))
}));

jest.mock('containers/common/HistoryTxnTablewActionsContainer', () => ({
  __esModule: true,
  default: jest.fn(() => (
    <div>
      <p>History Transactions</p>
    </div>
  ))
}));

const e = {
  target: {
    commentOption: {
      value: 'abcd'
    },
    comment: {
      value: ''
    }
  }
};

jest.mock('containers/caseReview/CaseOneVerdictModalContainer', () => ({
  __esModule: true,
  default: jest.fn(({ closeCase, toggleModal }) => (
    <div>
      <p>OneViewVerdictModalContainer</p>
      <button onClick={() => toggleModal()} data-testid="toggle-modal">
        Toggle Modal
      </button>
      <button onClick={() => closeCase(e)} data-testid="close-case">
        Close Cases
      </button>
    </div>
  ))
}));

const renderWithProviders = (ui) => {
  return render(
    <Provider store={store}>
      <Router>{ui}</Router>
    </Provider>
  );
};

let history;

beforeEach(() => {
  history = createMemoryHistory();
  history.goBack = jest.fn();
});

describe('CaseOneView Component', () => {
  it('should render CaseOneView correctly when case data is available', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(screen.getByText(/OneViewActionsContainer/)).toBeInTheDocument();
  });

  it('should display a loader when case data is loading', () => {
    const loadingProps = {
      ...defaultProps,
      oneViewData: {
        case: {
          loader: true,
          error: false,
          data: {}
        }
      }
    };
    renderWithProviders(<CaseOneView {...loadingProps} />);
    const loader = document.getElementsByClassName('loader')[0];
    expect(loader).toBeInTheDocument();
  });

  it('should display an error message when case data fetch fails', () => {
    const errorProps = {
      ...defaultProps,
      oneViewData: {
        case: {
          loader: false,
          error: true,
          errorMessage: 'Error fetching case data',
          data: {}
        }
      }
    };
    renderWithProviders(<CaseOneView {...errorProps} />);
    expect(screen.getByText(/Error fetching case data/)).toBeInTheDocument();
  });

  it('should call fetchCase and clearHistory on mount', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(defaultProps.clearHistory).toHaveBeenCalled();
    expect(defaultProps.oneViewActions.onFetchReviewerCase).toHaveBeenCalledWith({
      userId: 1,
      userName: 'testUser',
      channel: 'frm'
    });
  });

  it('should update document title on mount and unmount', () => {
    const { unmount } = renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(document.title).toBe('BANKiQ FRC | Review - 67890');
    unmount();
    expect(document.title).toBe('BANKiQ FRC | Review');
  });

  it('should call fetchCloseCaseBuckets if the list is empty', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(defaultProps.fetchCloseCaseBuckets).toHaveBeenCalled();
  });

  it('should fetch customer communication logs when case data is available', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(defaultProps.communicationActions.onFetchCustomerCommunicationLogs).toHaveBeenCalledWith(
      'frm',
      '67890'
    );
  });

  it('should call clearHistory on unmount', () => {
    const { unmount } = renderWithProviders(<CaseOneView {...defaultProps} />);
    unmount();
    expect(defaultProps.clearHistory).toHaveBeenCalled();
  });

  it('should render TransactionDetailsContainer with correct props', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(screen.getByText(/Transaction Details/)).toBeInTheDocument();
    expect(screen.getByTestId('txnId')).toHaveTextContent('12345');
  });

  it('should render OneViewActionsContainer with correct props', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(screen.getByText(/OneViewActionsContainer/)).toBeInTheDocument();
    expect(screen.getByTestId('fetch-case')).toBeInTheDocument();
    expect(screen.getByTestId('add-verdict')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('fetch-case'));
    fireEvent.click(screen.getByTestId('add-verdict'));
  });

  it('should render OneViewVerdictModalContainer with correct props', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);

    fireEvent.click(screen.getByTestId('fetch-case'));
    fireEvent.click(screen.getByTestId('add-verdict'));
    expect(screen.getByText(/OneViewVerdictModalContainer/)).toBeInTheDocument();

    expect(screen.getByTestId('close-case')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('toggle-modal'));
    fireEvent.click(screen.getByTestId('toggle-modal'));
    fireEvent.click(screen.getByTestId('close-case'));
  });

  it('should render HistoryTxnTableContainer with correct props', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    expect(screen.getByText(/History Transactions/)).toBeInTheDocument();
  });

  it('should call fetchCase function when "Fetch Cases" button is clicked', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    fireEvent.click(screen.getByTestId('fetch-case'));
    expect(defaultProps.oneViewActions.onFetchReviewerCase).toHaveBeenCalledWith({
      userId: 1,
      userName: 'testUser',
      channel: 'frm'
    });
  });

  it('should call addVerdict function when "Add Verdict" button is clicked', () => {
    renderWithProviders(<CaseOneView {...defaultProps} />);
    fireEvent.click(screen.getByTestId('add-verdict'));
    expect(defaultProps.oneViewActions.onFetchReviewerCase).toHaveBeenCalledWith({
      userId: 1,
      userName: 'testUser',
      channel: 'frm'
    });
  });
});
