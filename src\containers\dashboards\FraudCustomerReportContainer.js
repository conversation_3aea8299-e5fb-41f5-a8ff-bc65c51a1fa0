import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchHighAlertCustomer } from 'actions/businessDashboardActions';
import FraudCustomerReport from 'components/dashboards/FraudCustomerReport';

const mapStateToProps = (state) => ({
  highAlertCustomers: state.businessDashboard.highAlertCustomers
});

const mapDispatchToProps = (dispatch) => ({
  fetchHighAlertCustomer: bindActionCreators(onFetchHighAlertCustomer, dispatch)
});

const FraudCustomerReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(FraudCustomerReport);

export default FraudCustomerReportContainer;
