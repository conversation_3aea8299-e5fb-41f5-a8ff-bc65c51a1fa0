import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as actions from 'actions/alertActions';
import Snackbar from 'components/common/Snackbar';

const mapStateToProps = (state) => ({
  alert: state.alert,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(actions, dispatch)
});

const SnackbarContainer = connect(mapStateToProps, mapDispatchToProps)(Snackbar);

export default SnackbarContainer;
