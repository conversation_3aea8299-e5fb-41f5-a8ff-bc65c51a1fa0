import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/alertActions';
import Snackbar from 'components/common/Snackbar';

const mapStateToProps = (state) => {
  return {
    alert: state.alert,
    theme: state.toggle.theme
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch)
  };
};

const SnackbarContainer = connect(mapStateToProps, mapDispatchToProps)(Snackbar);

export default SnackbarContainer;
