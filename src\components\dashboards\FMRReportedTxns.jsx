import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import ReactTable from 'react-table';
import { Row, Col, CardTitle, CardSubtitle, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';
import { getScreen } from 'constants/functions';
import CombinedViolationDropdownContainer from 'containers/common/CombinedViolationDropdownContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';

const FMRReportedTxns = ({
  userRole,
  closeCaseBuckets,
  fmrReportedCases,
  fetchFmrReportedCases,
  fetchCloseCaseBuckets
}) => {
  const history = useHistory();
  const [expanded, setExpanded] = useState({});

  useEffect(() => {
    if (_.isEmpty(closeCaseBuckets.list)) fetchCloseCaseBuckets();
    fetchFmrReportedCases();
  }, []);

  const getPage = (role, caseItem) => `${getScreen(role)}/frm/${caseItem.txnId}`;

  const bucketOptions =
    !closeCaseBuckets.error &&
    closeCaseBuckets.list.map((bucket) => (
      <option key={bucket.id} value={bucket.id}>
        {bucket.name}
      </option>
    ));

  const headers = [
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      fixed: true,
      minWidth: 120,
      style: { overflow: 'visible' },
      Cell: (row) => (
        <Button
          outline
          size="sm"
          title="view"
          color="primary"
          onClick={() => history.push(getPage(userRole, row.original))}
          onContextMenu={() => window.open(getPage(userRole, row.original))}>
          <FontAwesomeIcon icon={faChevronRight} />
        </Button>
      )
    },
    {
      Header: 'Transaction Timestamp',
      accessor: 'txnTimestamp',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Transaction ID', accessor: 'txnId' },
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      style: { textAlign: 'right' },
      headerStyle: { textAlign: 'right' },
      filterMethod: (filter, row) =>
        row[filter.id] && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    { Header: 'Customer ID', accessor: 'customerId' },
    { Header: 'Beneficiary ID', accessor: 'beneficiaryId', width: 140 },
    { Header: 'MCC', accessor: 'payeeMcc', width: 140 },
    { Header: 'Sender Masked Card', accessor: 'senderMaskedCard' },
    { Header: 'Sender Hashed Card', accessor: 'senderHashedCard' },
    { Header: 'Channel', accessor: 'channel' },
    { Header: 'Type', accessor: 'txnType', width: 140 },
    {
      Header: 'Violated Rules',
      accessor: 'reViolatedRules',
      Cell: ({ value }) => (!_.isEmpty(value) ? _.split(value, ',').length : 0),
      filterMethod: (filter, row) => row[filter.id] && _.includes(row[filter.id], filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <CombinedViolationDropdownContainer
          onChange={(value) => onChange(value)}
          defaultOption="All"
        />
      )
    },
    { Header: 'Response Code', accessor: 'responseCode' },
    {
      Header: 'IFRM Pre Auth Action',
      accessor: 'ifrmVerdict',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    {
      Header: 'Bucket ID',
      accessor: 'bucketId',
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => {
        const bucket = closeCaseBuckets.list.find((d) => d.id === value);
        return !_.isEmpty(bucket) ? <span>{bucket.name}</span> : null;
      },
      filterMethod: (filter, row) => row[filter.id] === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          {bucketOptions}
        </select>
      )
    },
    { Header: 'Stage', accessor: 'caseStage' },
    { Header: 'Assigned To', accessor: 'assignedTo' },
    { Header: 'Entity Category', accessor: 'entityCategory' },
    {
      Header: 'Assignment Timestamp',
      accessor: 'assignmentTimeStamp',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    }
  ];

  return (
    <div>
      <Row className="mb-2">
        <Col md="3" lg="3">
          <CardContainer>
            <CardTitle className="text-info">{fmrReportedCases.list?.length ?? 0}</CardTitle>
            <CardSubtitle>FMR Reports</CardSubtitle>
          </CardContainer>
        </Col>
      </Row>
      <CardContainer title="FMR Reported Cases">
        {(() => {
          if (fmrReportedCases?.loader) return <TableLoader />;

          if (fmrReportedCases?.error)
            return <div className="no-data-div">{fmrReportedCases?.errorMessage}</div>;

          return (
            <ReactTable
              defaultFilterMethod={(filter, row) =>
                row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
              }
              columns={headers}
              data={fmrReportedCases?.list}
              SubComponent={(row) => (
                <ViolatedRuleNameBadgeContainer
                  violatedRulesList={row.original?.reViolatedRules || ''}
                  taggedRulesList={row.original?.taggedRule || ''}
                />
              )}
              noDataText={fmrReportedCases?.errorMessage || 'No cases found'}
              expanded={expanded}
              onExpandedChange={(expanded) => setExpanded(expanded)}
              showPaginationTop={true}
              showPaginationBottom={false}
              pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              minRows={5}
              showPageJump={false}
              className="-highlight -striped"
            />
          );
        })()}
      </CardContainer>
    </div>
  );
};

FMRReportedTxns.propTypes = {
  userRole: PropTypes.string.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fmrReportedCases: PropTypes.object.isRequired,
  fetchFmrReportedCases: PropTypes.func.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired
};

export default FMRReportedTxns;
