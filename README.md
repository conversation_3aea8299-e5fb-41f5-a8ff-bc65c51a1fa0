# IFRM-UI

## Getting Started ##

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites ###

Download the code or clone the repo. You should have npm already installed on your local machine.

### Installing ###

Open command prompt / terminal in project folder and enter following command:

```bash
npm install
```

### Running the Project ###

Use one of the options to start project in developement mode:

```bash
npm start -k\-r\-s
```

## Built With ##

1. React - Javascript library to build reactive ui
2. Redux - Javascript library for managing application state
3. React-Router-Dom - Navigational components for react
4. Redux-Thunk - Thunk middleware allows you to write action creators that return a function instead of an action
5. Redux-persist - Persists the store on localStorage
6. Bootstrap - Responsive front-end web framework
7. jQuery - Fast, small, and feature-rich JavaScript library
8. Webpack - javascript module bundler
