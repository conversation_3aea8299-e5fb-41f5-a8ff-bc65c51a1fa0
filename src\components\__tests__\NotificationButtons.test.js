import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';

import NotificationButtons from '../caseReview/NotificationButtons';

describe('NotificationButtons Component', () => {
  const mockCommunicationActions = {
    onSMSCustomer: jest.fn(),
    onEmailCustomer: jest.fn()
  };

  const defaultProps = {
    channel: 'web',
    entityId: 'entity123',
    caseRefNo: 'case456',
    caseDetails: { detail: 'some detail' },
    communicationLogs: {
      data: [{ media: 'EMAIL' }, { media: 'EMAIL' }, { media: 'SMS' }]
    },
    communicationActions: mockCommunicationActions,
    disabled: false
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly with communication logs', () => {
    render(<NotificationButtons {...defaultProps} />);

    // Check if the buttons are rendered
    expect(screen.getByText(/Notify via SMS/)).toBeInTheDocument();
    expect(screen.getByText(/Notify via Email/)).toBeInTheDocument();

    // Check the counts inside the buttons
    expect(screen.getByText(/Notify via SMS/)).toHaveTextContent('(1)');
    expect(screen.getByText(/Notify via Email/)).toHaveTextContent('(2)');
  });

  it('should render correctly with empty communication logs', () => {
    const emptyLogsProps = {
      ...defaultProps,
      communicationLogs: {
        data: []
      }
    };
    render(<NotificationButtons {...emptyLogsProps} />);

    expect(screen.getByText(/Notify via SMS/)).toBeInTheDocument();
    expect(screen.getByText(/Notify via Email/)).toBeInTheDocument();

    expect(screen.getByText(/Notify via SMS/)).not.toHaveTextContent('(1)');
    expect(screen.getByText(/Notify via Email/)).not.toHaveTextContent('(2)');
  });

  it('should render correctly with undefined communication logs', () => {
    const undefinedLogsProps = {
      ...defaultProps,
      communicationLogs: {}
    };
    render(<NotificationButtons {...undefinedLogsProps} />);

    expect(screen.getByText(/Notify via SMS/)).toBeInTheDocument();
    expect(screen.getByText(/Notify via Email/)).toBeInTheDocument();

    expect(screen.getByText(/Notify via SMS/)).not.toHaveTextContent('(1)');
    expect(screen.getByText(/Notify via Email/)).not.toHaveTextContent('(2)');
  });

  it('should call onSMSCustomer when Notify via SMS button is clicked', () => {
    render(<NotificationButtons {...defaultProps} />);

    fireEvent.click(screen.getByText(/Notify via SMS/));

    expect(mockCommunicationActions.onSMSCustomer).toHaveBeenCalledWith({
      channel: 'web',
      caseRefNo: 'case456',
      customerId: 'entity123',
      data: { detail: 'some detail' }
    });
  });

  it('should call onEmailCustomer when Notify via Email button is clicked', () => {
    render(<NotificationButtons {...defaultProps} />);

    fireEvent.click(screen.getByText(/Notify via Email/));

    expect(mockCommunicationActions.onEmailCustomer).toHaveBeenCalledWith({
      channel: 'web',
      caseRefNo: 'case456',
      customerId: 'entity123',
      data: { detail: 'some detail' }
    });
  });

  it('should disable buttons when disabled prop is true', () => {
    render(<NotificationButtons {...defaultProps} disabled={true} />);

    expect(screen.getByText(/Notify via SMS/)).toBeDisabled();
    expect(screen.getByText(/Notify via Email/)).toBeDisabled();
  });

  it('should not call communication actions when buttons are disabled', () => {
    render(<NotificationButtons {...defaultProps} disabled={true} />);

    fireEvent.click(screen.getByText(/Notify via SMS/));
    fireEvent.click(screen.getByText(/Notify via Email/));

    expect(mockCommunicationActions.onSMSCustomer).not.toHaveBeenCalled();
    expect(mockCommunicationActions.onEmailCustomer).not.toHaveBeenCalled();
  });
});
