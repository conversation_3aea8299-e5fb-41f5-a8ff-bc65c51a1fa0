import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onSearchTxn } from 'actions/transactionHistorySearchActions';
import HistoryTxnTable from 'components/common/HistoryTxnTable';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => {
  return {
    historyData: state.investigation.transactionHistorySearch,
    contextKey: 'transactionHistory'
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchHistoryTransactions: bindActionCreators(onSearchTxn, dispatch)
  };
};

const HistoryTxnTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)((props) => (
  <DateRangeProvider contextKey="transactionHistory">
    <HistoryTxnTable {...props} />
  </DateRangeProvider>
));

export default HistoryTxnTableContainer;
