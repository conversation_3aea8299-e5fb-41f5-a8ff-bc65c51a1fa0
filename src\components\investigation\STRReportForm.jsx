import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Input, Label, Row, Col } from 'reactstrap';

import Loader from 'components/loader/Loader';

function STRReportForm({ violations, caseDocument, strReportMasters, getMasters }) {
  const [roleMainAssociate, setRoleMainAssociate] = useState('');
  const [sourceOfFunds, setSourceOfFunds] = useState('');
  const [destinationOfFunds, setDestinationOfFunds] = useState('');
  const [suspicionDueTo, setSuspicionDueTo] = useState('');
  const [sourceOfAlert, setSourceOfAlert] = useState('');
  const [redFlagIndicator, setRedFlagIndicator] = useState('');
  const [otherRedFlagIndicator, setOtherRedFlagIndicator] = useState('');
  const [typeOfSuspicion, setTypeOfSuspicion] = useState('');
  const [narration, setNarration] = useState('');
  const [kycType, setKycType] = useState('');
  const [selectedFile, setSelectedFile] = useState([]);

  useEffect(() => {
    let tempAlertIndicator = _.map(violations.list?.rules, (rule) => rule?.name);
    !_.isEmpty(tempAlertIndicator) && setRedFlagIndicator(tempAlertIndicator);
  }, [violations]);

  useEffect(() => {
    if (_.isEmpty(strReportMasters?.data)) {
      !strReportMasters.loader && getMasters();
    }
  }, []);

  const getOptionsForType = (type) =>
    strReportMasters.loader ? (
      <option value="" disabled>
        Loading...
      </option>
    ) : strReportMasters.error ? (
      <option value="" disabled>
        {strReportMasters.errorMessage}
      </option>
    ) : !_.isEmpty(strReportMasters?.data) ? (
      strReportMasters.data[type]?.map((d) => (
        <option key={_.camelCase(type === 'sourceOfAlert' ? d.alert : d)}>
          {type === 'sourceOfAlert' ? d.alert : d}
        </option>
      ))
    ) : (
      <option value="" disabled>
        No values found
      </option>
    );

  const getRFIforAlert = (sourceOfAlert) =>
    strReportMasters.error ? (
      <option value="" disabled>
        {strReportMasters.errorMessage}
      </option>
    ) : !_.isEmpty(strReportMasters?.data) ? (
      strReportMasters.data.sourceOfAlert
        .find((d) => d.alert === sourceOfAlert)
        ?.redFlagIndicator?.map((d) => <option key={_.camelCase(d)}>{d}</option>)
    ) : (
      <option value="" disabled>
        No values found
      </option>
    );

  const toggleFileCheckbox = (fileName) => {
    if (_.includes(selectedFile, fileName)) {
      setSelectedFile(_.filter(selectedFile, (file) => file !== fileName));
    } else {
      setSelectedFile((prev) => [...prev, fileName]);
    }
  };

  return strReportMasters.loader ? (
    <Loader show={true} />
  ) : (
    <Row>
      <Col md="6">
        <FormGroup>
          <Label for="roleMainAssociate">Role (Main / associate)</Label>
          <Input
            type="text"
            id="roleMainAssociate"
            name="roleMainAssociate"
            value={roleMainAssociate}
            onChange={(e) => setRoleMainAssociate(e.target.value)}
            required
          />
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="sourceOfFunds">Source of Funds</Label>
          <Input
            type="text"
            id="sourceOfFunds"
            name="sourceOfFunds"
            value={sourceOfFunds}
            onChange={(e) => setSourceOfFunds(e.target.value)}
            required
          />
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="sourceOfFunds">Destination of Funds</Label>
          <Input
            type="text"
            id="destinationOfFunds"
            name="destinationOfFunds"
            value={destinationOfFunds}
            onChange={(e) => setDestinationOfFunds(e.target.value)}
            required
          />
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="suspicionDueTo">Suspicion due to</Label>
          <Input
            type="select"
            id="suspicionDueTo"
            name="suspicionDueTo"
            value={suspicionDueTo}
            onChange={(e) => setSuspicionDueTo(e.target.value)}>
            <option value=""> -- SELECT -- </option>
            {getOptionsForType('suspicionDueTo')}
          </Input>
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="sourceOfAlert">Source of Alert</Label>
          <Input
            type="select"
            id="sourceOfAlert"
            name="sourceOfAlert"
            value={sourceOfAlert}
            onChange={(e) => setSourceOfAlert(e.target.value)}
            required>
            <option value=""> -- SELECT -- </option>
            {getOptionsForType('sourceOfAlert')}
          </Input>
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="redFlagIndicator">Red Flag Indicators</Label>
          <Input
            type="select"
            id="redFlagIndicator"
            name="redFlagIndicator"
            value={redFlagIndicator}
            onChange={(e) => setRedFlagIndicator(e.target.value)}
            required>
            <option value=""> -- SELECT -- </option>
            {getRFIforAlert(sourceOfAlert)}
          </Input>
          {_.lowerCase(redFlagIndicator) === 'others' && (
            <Input
              type="text"
              id="otherRedFlagIndicator"
              name="otherRedFlagIndicator"
              value={otherRedFlagIndicator}
              onChange={(e) => setOtherRedFlagIndicator(e.target.value)}
              required
            />
          )}
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="sourceOfAlert">Type of Suspicion</Label>
          <Input
            type="select"
            id="typeOfSuspicion"
            name="typeOfSuspicion"
            value={typeOfSuspicion}
            onChange={(e) => setTypeOfSuspicion(e.target.value)}
            required>
            <option value=""> -- SELECT -- </option>
            {getOptionsForType('typeOfSuspicion')}
          </Input>
        </FormGroup>
      </Col>
      <Col md="6">
        <FormGroup>
          <Label for="kycType">KYC Type</Label>
          <Input
            type="select"
            id="kycType"
            name="kycType"
            value={kycType}
            onChange={(e) => setKycType(e.target.value)}
            required>
            <option value=""> -- SELECT -- </option>
            <option value={'KC1'}>Individual Customers (KC1)</option>
            <option value={'KC2'}>Non-Individual Customers (KC2)</option>
            <option value={'KCS1'}>Individuals Non-Customers (KCS1)</option>
            <option value={'KCS2'}>Non-Individuals Non-Customers (KCS2)</option>
          </Input>
        </FormGroup>
      </Col>

      <Col md="12">
        <FormGroup>
          <Label for="narration">Narration</Label>
          <Input
            type="textarea"
            id="narration"
            name="narration"
            value={narration}
            onChange={(e) => setNarration(e.target.value)}
            required
          />
        </FormGroup>
        {caseDocument.data.length > 0 && (
          <FormGroup>
            <Label>Attach Documents</Label>
            {_.map(caseDocument.data, (file, i) => (
              <FormGroup key={file.fileName} check>
                <Label>
                  <Input
                    type="checkbox"
                    id={'fileCheckbox' + i}
                    name="fileCheckbox"
                    value={file.fileName}
                    onChange={() => toggleFileCheckbox(file.fileName)}
                    checked={_.includes(selectedFile, file.fileName)}
                  />
                  {file.fileName}
                </Label>
              </FormGroup>
            ))}
          </FormGroup>
        )}
      </Col>
    </Row>
  );
}

STRReportForm.propTypes = {
  violations: PropTypes.object.isRequired,
  caseDocument: PropTypes.object.isRequired,
  strReportMasters: PropTypes.object.isRequired,
  getMasters: PropTypes.func.isRequired
};

export default STRReportForm;
