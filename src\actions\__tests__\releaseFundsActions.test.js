import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/releaseFundsActions';
import responses from 'mocks/responses';

describe('release funds actions', () => {
  it('should fetch release funds list', () => {
    const expectedActions = [
      { type: types.ON_FETCH_RELEASE_FUNDS_LIST_LOADING },
      { type: types.ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST, response: responses.release.list }
    ];
    const store = mockStore({ releaseFunds: {} });

    return store.dispatch(actions.onFetchReleaseFundsList(1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should release funds in bulk', () => {
    const formData = [
      {
        merchantId: 'mchnt0123',
        releaseDesc: 'This is the description',
        txnIds: ['1232353455', '1234245345', '324535345456']
      },
      {
        merchantId: 'mchnt0456',
        releaseDesc: 'This is the description',
        txnIds: ['1232353455', '1234245345', '324535345456']
      }
    ];

    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Funds released successfully' } },
      { type: types.ON_FETCH_RELEASE_FUNDS_LIST_LOADING },
      { type: types.ON_TOGGLE_RELEASE_FUNDS_MODAL }
    ];
    const store = mockStore({ releaseFunds: {} });

    return store.dispatch(actions.onReleaseFundsInBulk(formData, 1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should release single fund', () => {
    const formData = {
      caseRefNo: 'atma42020-01-08T04:48:12.000+05:30',
      txnId: 'abc01:tasdahritgvn:129723',
      comment: 'case is released due to this reason',
      userId: 1
    };
    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Fund released successfully' } },
      { type: types.ON_FETCH_RELEASE_FUNDS_LIST_LOADING },
      { type: types.ON_TOGGLE_RELEASE_FUNDS_MODAL }
    ];
    const store = mockStore({ releaseFunds: {} });

    return store.dispatch(actions.onReleaseSingleFund(formData, 1)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch document status', () => {
    const expectedActions = [
      { type: types.ON_FETCH_DOCUMENT_STATUS_LOADING },
      { type: types.ON_FETCH_DOCUMENT_STATUS_SUCCESS, response: responses.release.docStatus }
    ];
    const store = mockStore({ releaseFunds: {} });

    return store
      .dispatch(actions.onFetchDocumentStatus(responses.caseAssignment.selectedCase))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should case hold', () => {
    const formData = {
      caseRefNo: 'atma42020-01-08T04:48:12.000+05:30',
      txnId: 'abc01:tasdahritgvn:129723',
      comment: 'case is released due to this reason',
      userId: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Case kept on hold successfully' } },
      { type: types.ON_TOGGLE_HOLD_CASE_MODAL },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_DOCUMENT_STATUS_LOADING },
      { type: types.ON_FETCH_CASE_NOTATION_LIST_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ releaseFunds: {}, caseDetails: {} });

    return store
      .dispatch(actions.onCaseHold(formData, responses.caseAssignment.selectedCase))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should request document', () => {
    const formData = {
      caseRefNo: 'atma42020-01-08T04:48:12.000+05:30',
      txnId: '1234',
      comment: 'case is released due to this reason',
      userId: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Verification documents requested successfully' }
      },
      { type: types.ON_TOGGLE_REQUEST_DOCUMENT_MODAL },
      { type: types.ON_FETCH_DOCUMENT_STATUS_LOADING },
      { type: types.ON_FETCH_CASE_NOTATION_LIST_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ releaseFunds: {}, notations: {} });

    return store
      .dispatch(actions.onRequestDocument(formData, responses.caseAssignment.selectedCase))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });
});
