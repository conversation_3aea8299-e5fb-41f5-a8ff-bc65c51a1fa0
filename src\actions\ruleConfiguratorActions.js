import {
  ON_<PERSON>ET<PERSON>_RULE_LIST_LOADING,
  ON_FETCH_RULE_LIST_FAILURE,
  ON_SUCCESSFUL_FETCH_RULE_LIST,
  ON_FETCH_RULE_NAMES_LIST_LOADING,
  ON_<PERSON>ETCH_RULE_NAMES_LIST_SUCCESS,
  ON_FETCH_RULE_NAMES_LIST_FAILURE,
  ON_SUCCESSFUL_TOGGLE_RULE_STATUS,
  ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT,
  ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION,
  ON_DELETE_RULE_SUCCESS,
  ON_FETCH_RULES_WITH_CONDITIONS_LOADING,
  ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS,
  ON_FETCH_RULES_WITH_CONDITIONS_FAILURE,
  ON_RESTORE_RULE_SUCCESS
} from 'constants/actionTypes';
import { onFetchNonProductionRulesList } from 'actions/ruleCreationActions';
import { onShowFailureAlert, onShowSuc<PERSON>Alert } from 'actions/alertActions';
import { onToggleLoader, onToggleRuleFeedbackModal } from 'actions/toggleActions';
import client from 'utility/apiClient';

function fetchRulesList(channel) {
  return client({
    url: `${channel}/ruleengine/rule`,
    badRequestMessage: 'Curently unable to fetch rules'
  });
}

function onFetchRulesListLoading() {
  return { type: ON_FETCH_RULE_LIST_LOADING };
}

function onSuccessfulFetchRulesList(channel, response) {
  return {
    type: ON_SUCCESSFUL_FETCH_RULE_LIST,
    channel,
    response
  };
}

function onFetchRulesListFailure(response) {
  return {
    type: ON_FETCH_RULE_LIST_FAILURE,
    response
  };
}

function onFetchRulesList(channel) {
  return function (dispatch) {
    dispatch(onFetchRulesListLoading());
    return fetchRulesList(channel).then(
      (success) => dispatch(onSuccessfulFetchRulesList(channel, success)),
      (error) => dispatch(onFetchRulesListFailure(error))
    );
  };
}

function fetchRuleNamesList(channel) {
  return client({
    url: `${channel}/ruleengine/rulenames`,
    badRequestMessage: 'Curently unable to fetch rules'
  });
}

function onFetchRuleNamesListLoading() {
  return { type: ON_FETCH_RULE_NAMES_LIST_LOADING };
}

function onSuccessfulFetchRuleNamesList(channel, response) {
  return {
    type: ON_FETCH_RULE_NAMES_LIST_SUCCESS,
    channel,
    response
  };
}

function onFetchRuleNamesListFailure(response) {
  return {
    type: ON_FETCH_RULE_NAMES_LIST_FAILURE,
    response
  };
}

function onFetchRuleNamesList(channel) {
  return function (dispatch) {
    dispatch(onFetchRuleNamesListLoading());
    return fetchRuleNamesList(channel).then(
      (success) => dispatch(onSuccessfulFetchRuleNamesList(channel, success)),
      (error) => dispatch(onFetchRuleNamesListFailure(error))
    );
  };
}

function toggleRuleStatus(formData) {
  return client({
    method: 'PUT',
    url: `${formData.channel}/ruleengine/rule/${formData.code}/activate`,
    badRequestMessage: 'Currently unable to enable/disable rules'
  });
}

function onToggleRuleStatusSuccess(ruleCode, channel, status) {
  return {
    type: ON_SUCCESSFUL_TOGGLE_RULE_STATUS,
    ruleCode,
    channel,
    status
  };
}

function onToggleRuleStatus(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return toggleRuleStatus(formData)
      .then(
        () => {
          dispatch(onToggleRuleStatusSuccess(formData.code, formData.channel, formData.status));
          formData.status !== 'Activated'
            ? (dispatch(onToggleRuleActivationSuccess(formData.code, formData.channel, 'Disable')),
              dispatch(onShowSuccessAlert({ message: 'Rule suspended successfully' })))
            : dispatch(onShowSuccessAlert({ message: 'Rule activated successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function toggleRuleActivation(formData) {
  return client({
    method: 'PUT',
    url: `${formData.channel}/ruleengine/rule/${formData.code}/enable`,
    data: formData,
    badRequestMessage: 'Currently unable to activate/suspend rule'
  });
}

function onToggleRuleActivationSuccess(ruleCode, channel, active) {
  return {
    type: ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION,
    ruleCode,
    channel,
    active
  };
}

function onSuccessfulToggleRuleActivation(newState) {
  return newState === 'Enable'
    ? onShowSuccessAlert({ message: 'Rule enabled successfully' })
    : onShowSuccessAlert({ message: 'Rule disabled successfully' });
}

function onToggleRuleActivation(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return toggleRuleActivation(formData)
      .then(
        () => {
          dispatch(onToggleRuleActivationSuccess(formData.code, formData.channel, formData.active));
          dispatch(onSuccessfulToggleRuleActivation(formData.active));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function toggleRuleExplicit(formData) {
  return client({
    method: 'PUT',
    url: `${formData.channel}/ruleengine/rule/${formData.code}/explicit`,
    data: formData,
    badRequestMessage: 'Currently unable to mark rule for explicit case creation.'
  });
}

function onToggleRuleExplicitSuccess(ruleCode, channel, explicit) {
  return {
    type: ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT,
    ruleCode,
    channel,
    explicit
  };
}

function onToggleRuleExplicit(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return toggleRuleExplicit(formData)
      .then(
        () => {
          dispatch(onToggleRuleExplicitSuccess(formData.code, formData.channel, formData.explicit));
          dispatch(
            onShowSuccessAlert({
              message: formData.explicit
                ? 'Rule enabled for explicit case creation successfully'
                : 'Rule disabled for explicit case creation successfully'
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function deleteRule(formData, channel) {
  return client({
    method: 'DELETE',
    url: `${channel}/ruleengine/rule/${formData.code}/delete`,
    badRequestMessage: 'Currently unable to delete rule.'
  });
}

function onDeleteRuleSuccess(rule, channel) {
  return {
    type: ON_DELETE_RULE_SUCCESS,
    rule,
    channel
  };
}

function onDeleteRule(formData, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return deleteRule(formData, channel)
      .then(
        () => {
          dispatch(onDeleteRuleSuccess(formData, channel));
          dispatch(onShowSuccessAlert({ message: 'Rule deleted successfully' }));
          dispatch(onFetchRulesList(channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function uploadRuleList(formData, channel) {
  let bulkUpload = new FormData();
  bulkUpload.append('file', formData.file);
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/rules/rule-bulk-upload`,
    multipart: true,
    headers: {
      processData: false,
      contentType: false
    },
    data: bulkUpload,
    badRequestMessage: `Unable to upload rules`
  });
}

function onUploadRuleList(formData, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return uploadRuleList(formData, channel)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'File uploaded successfully' }));
          dispatch(onFetchRulesList(channel));
          dispatch(onFetchRuleNamesList(channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .finally(() => dispatch(onToggleLoader(false)));
  };
}

function fetchRulesWithConditions(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/case/rule/fetchRuleWithCondition`,
    data: formData
  });
}

function onFetchRulesWithConditionsLoading() {
  return {
    type: ON_FETCH_RULES_WITH_CONDITIONS_LOADING
  };
}

function onFetchRulesWithConditionsSuccess(response) {
  return {
    type: ON_FETCH_RULES_WITH_CONDITIONS_SUCCESS,
    response
  };
}

function onFetchRulesWithConditionsFailure(response) {
  return {
    type: ON_FETCH_RULES_WITH_CONDITIONS_FAILURE,
    response
  };
}

function onFetchRulesWithConditions(formData, channel) {
  return function (dispatch) {
    dispatch(onFetchRulesWithConditionsLoading());
    return fetchRulesWithConditions(formData, channel).then(
      (success) => dispatch(onFetchRulesWithConditionsSuccess(success)),
      (error) => dispatch(onFetchRulesWithConditionsFailure(error))
    );
  };
}

function saveFeedback(formData, channel) {
  return client({
    method: 'POST',
    url: `${channel}/case/feedback/createFeedBack`,
    data: formData,
    badRequestMessage: 'Unable to submit feedback'
  });
}

function onSaveFeedback(formData, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return saveFeedback(formData, channel)
      .then(
        () => {
          dispatch(onShowSuccessAlert({ message: 'Thanks for your feedback!' }));
          dispatch(onToggleRuleFeedbackModal());
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function restoreRule(rule, channel) {
  const formData = { code: rule.code };
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/rule/restore`,
    data: formData,
    badRequestMessage: 'Currently unable to restore rule.'
  });
}

function onRestoreRuleSuccess(rule, channel) {
  return {
    type: ON_RESTORE_RULE_SUCCESS,
    rule,
    channel
  };
}

function onRestoreRule(rule, channel) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return restoreRule(rule, channel)
      .then(
        () => {
          dispatch(onRestoreRuleSuccess(rule, channel));
          dispatch(onShowSuccessAlert({ message: 'Rule restore successfully' }));
          dispatch(onFetchNonProductionRulesList(channel, 'checker'));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchRulesList,
  onFetchRuleNamesList,
  onToggleRuleStatus,
  onToggleRuleActivation,
  onToggleRuleExplicit,
  onDeleteRule,
  onUploadRuleList,
  onFetchRulesWithConditions,
  onSaveFeedback,
  onRestoreRule
};
