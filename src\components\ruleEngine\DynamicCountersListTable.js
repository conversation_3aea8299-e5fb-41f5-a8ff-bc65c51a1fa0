/* eslint-disable react/prop-types */

'use strict';
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button, ButtonGroup, Input } from 'reactstrap';
import Moment from 'moment';
import TableLoader from 'components/loader/TableLoader';
import CardContainer from 'components/common/CardContainer';
import ModalContainer from 'components/common/ModalContainer';
import DynamicCountersForm from 'containers/ruleEngine/DynamicCountersFormContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faUpload, faTrash } from '@fortawesome/free-solid-svg-icons';
import CSVDownloader from 'components/common/CSVDownloader';
import ConfirmAlert from 'components/common/ConfirmAlert';

const DynamicCountersListTable = ({
  toggleActions,
  dynamicCountersAction,
  dynamicCounters,
  toggle,
  channel,
  role
}) => {
  const [tableFilters, setTableFilters] = useState([]);
  const [fileData, setFileData] = useState(null);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedCounters, setSelectedCounters] = useState([]);
  const [deleteCounter, setDeleteCounter] = useState({});
  const [toggleDelete, setToggleDelete] = useState(false);

  useEffect(() => {
    if (_.isEmpty(dynamicCounters.counters.list))
      dynamicCountersAction.onFetchDynamicCountersList(channel);
    setTableFilters([]);
  }, []);

  useEffect(() => {
    !_.isEmpty(tableFilters) &&
      !_.isEmpty(selectedCounters) &&
      setSelectedCounters(filteredCounterList(selectedCounters));
    selectAll && setSelectAll(false);
  }, [tableFilters]);

  useEffect(() => {
    if (fileData) {
      dynamicCountersAction.onUploadDynamicCounters({ channel: channel, file: fileData });
      setFileData(null);
    }
  }, [fileData]);

  const handleSelectAll = () => {
    if (!selectAll)
      setSelectedCounters(
        _.isEmpty(tableFilters)
          ? dynamicCounters.counters.list
          : filteredCounterList(dynamicCounters.counters.list)
      );
    else setSelectedCounters([]);
    setSelectAll(!selectAll);
  };

  const filteredCounterList = (counterList) => {
    const filteredArray = _.filter(counterList, (counter) =>
      _.every(
        tableFilters,
        (filter) =>
          _.has(counter, filter.id) &&
          _.includes(_.lowerCase(counter[filter.id]), _.lowerCase(filter.value))
      )
    );

    return filteredArray;
  };

  const handleCheckboxChange = (counter) => {
    if (_.includes(selectedCounters, counter)) {
      setSelectedCounters((prev) =>
        _.filter(prev, (prevCounter) => prevCounter.identifier !== counter.identifier)
      );
      setSelectAll(false);
    } else {
      const newList = [...selectedCounters, counter];
      setSelectedCounters(newList);
      setSelectAll(newList.length === dynamicCounters.counters.list.length);
    }
  };

  const getFormData = (form, conditionsList) => {
    const {
      identifier,
      description,
      period,
      ttl,
      primaryAttribute,
      secondaryAttribute,
      isSingleAttribute,
      isPostAuth
    } = form;
    let counterData = {
      identifier: identifier.value,
      description: description.value,
      period: period.value,
      ...(period.value == 'Custom Days' && ttl && { ttl: parseInt(ttl.value) }),
      primaryAttribute: primaryAttribute.value,
      ...(parseInt(isSingleAttribute.value) == 0 && {
        secondaryAttribute: secondaryAttribute.value
      }),
      isSingleAttribute: parseInt(isSingleAttribute.value),
      ...(!_.isEmpty(conditionsList) && { conditionsList }),
      isPostAuth: parseInt(isPostAuth.value)
    };

    return {
      counter: counterData,
      channel
    };
  };

  const toggleCreateModal = () => {
    toggleActions.onToggleDynamicCountersCreateModal();
  };

  const handleCreateSubmit = (e, conditionsList) => {
    e.preventDefault();
    const form = e.target;
    const formData = getFormData(form, conditionsList);
    dynamicCountersAction.onCreateDynamicCounter(formData);
  };

  const confirmDelete = (isOpen, counter) => {
    if (!_.isEmpty(counter) && !isOpen) {
      setDeleteCounter(counter);
      setToggleDelete(true);
    } else {
      setDeleteCounter({});
      setToggleDelete(false);
    }
  };

  const deleteSelectedCounter = (counter, channel) => {
    let formData = {
      identifier: counter?.identifier,
      primaryAttribute: counter?.primaryAttribute || '',
      secondaryAttribute: counter?.secondaryAttribute || ''
    };

    dynamicCountersAction.onDeleteCounter(formData, channel);
    setToggleDelete(false);
  };

  let header = [
    {
      Header: (
        <Input
          bsSize="md"
          type="checkbox"
          id="selectAll"
          onChange={handleSelectAll}
          checked={selectAll}
          disabled={dynamicCounters.counters.list.length === 0}
        />
      ),
      accessor: '',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 50,

      Cell: (row) => (
        <Input
          type="checkbox"
          value={row.original.identifier}
          id={row.original.identifier}
          name="tableSelect[]"
          checked={_.includes(selectedCounters, row.original)}
          onChange={() => handleCheckboxChange(row.original)}
        />
      )
    },
    {
      Header: 'Actions',
      filterable: false,
      sortable: false,
      minWidth: 180,
      show: role === 'supervisor',
      Cell: (row) => (
        <Button
          outline
          size="sm"
          color="danger"
          className="ms-1"
          title="Delete counter"
          onClick={() => confirmDelete(toggleDelete, row.original)}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    },
    { Header: 'Identifier', accessor: 'identifier', minWidth: 150 },
    { Header: 'Description', accessor: 'description', minWidth: 200 },
    { Header: 'Period', accessor: 'period' },
    { Header: 'Days', accessor: 'ttl' },
    { Header: 'Primary Attribute', accessor: 'primaryAttribute' },
    { Header: 'Secondary Attribute', accessor: 'secondaryAttribute' },
    {
      Header: 'ConditionList',
      accessor: 'conditionList',
      Cell: ({ value }) => {
        let conditions = value.map((condition, i) => (
          <li key={i} className="conditions-list">
            {condition}
          </li>
        ));
        return <ul>{conditions}</ul>;
      },
      minWidth: 180
    },
    {
      Header: 'Is Post Auth',
      accessor: 'isPostAuth',
      Cell: ({ value }) => (value == 1 ? 'Yes' : 'No'),
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'isPostAuth']))
              ? _.find(tableFilters, ['id', 'isPostAuth']).value
              : ''
          }>
          <option value="">All</option>
          <option value={1}>Yes</option>
          <option value={0}>No</option>
        </select>
      )
    },
    { Header: 'CreatedBy', accessor: 'createdBy' },
    {
      Header: 'Creation Date',
      accessor: 'creationDate',
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] && Moment(row[filter.id]).format('YYYY-MM-DD') === filter.value,
      Filter: ({ onChange }) => (
        <input
          type="date"
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'creationDate']))
              ? _.find(tableFilters, ['id', 'creationDate']).value
              : ''
          }
        />
      )
    }
  ];

  let csvHeaders = [
    { label: 'Identifier', key: 'identifier' },
    { label: 'Description', key: 'description' },
    { label: 'Period', key: 'period' },
    { label: 'Days', key: 'ttl' },
    { label: 'Primary Attribute', key: 'primaryAttribute' },
    { label: 'Secondary Attribute', key: 'secondaryAttribute' },
    { label: 'Is Single Attribute', key: 'isSingleAttribute' },
    { label: 'Fields', key: 'fields' },
    { label: 'Operators', key: 'operators' },
    { label: 'Values', key: 'values' },
    { label: 'Is Post Auth', key: 'isPostAuth' }
  ];

  let createAction = (
    <span>
      {role === 'supervisor' && (
        <label htmlFor="dynamicCounterfileUploader" className="btn ms-1 rule-upload-btn">
          <Input
            type="file"
            name="dynamicCounterfileUploader"
            id="dynamicCounterfileUploader"
            accept="text/csv, .csv"
            files={fileData}
            onChange={(event) => setFileData(event.target.files[0])}
            hidden
          />
          <FontAwesomeIcon icon={faUpload} /> Upload Dynamic Counters
        </label>
      )}
      <CSVDownloader
        data={selectedCounters}
        sheetName="Dynamic Counters"
        headers={csvHeaders}
        disabled={selectedCounters == 0}
      />
      <ButtonGroup>
        {_.includes(['checker', 'investigator'], role) && (
          <Button className="ms-1" color="primary" size="sm" onClick={() => toggleCreateModal()}>
            <FontAwesomeIcon icon={faPlus} /> Create Counter
          </Button>
        )}
      </ButtonGroup>
    </span>
  );

  return (
    <div>
      <CardContainer title={_.upperCase(channel) + ' Dynamic Counters List'} action={createAction}>
        {dynamicCounters.counters.loader ? (
          <TableLoader />
        ) : dynamicCounters.counters.error && _.isEmpty(dynamicCounters.counters.list) ? (
          <div className="no-data-div">{dynamicCounters.counters.errorMessage}</div>
        ) : (
          <ReactTable
            filterable
            defaultFilterMethod={(filter, row) =>
              row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
            }
            columns={header}
            data={dynamicCounters.counters.list}
            noDataText="No data found"
            pageSizeOptions={[5, 10, 20, 30, 40, 50]}
            defaultPageSize={10}
            minRows={5}
            showPaginationTop={true}
            showPaginationBottom={false}
            className={'-highlight  -striped'}
            filtered={tableFilters}
            onFilteredChange={(filtered) => setTableFilters(filtered)}
          />
        )}
      </CardContainer>
      <ModalContainer
        size="lg"
        theme={`${toggle.theme} dynamic-counter-modal`}
        header={'Create Counter'}
        isOpen={toggle.dynamicCountersCreateModal}
        toggle={() => toggleCreateModal()}>
        <DynamicCountersForm formName="create" channel={channel} submit={handleCreateSubmit} />
      </ModalContainer>

      <ConfirmAlert
        theme={toggle.theme}
        confirmAlertModal={toggleDelete}
        toggleConfirmAlertModal={() => confirmDelete(toggleDelete)}
        confirmationAction={() => deleteSelectedCounter(deleteCounter, channel)}
        confirmAlertTitle={`Are you sure you want to delete counter - ${
          deleteCounter?.identifier || ''
        } ?`}
      />
    </div>
  );
};

DynamicCountersListTable.propTypes = {
  role: PropTypes.string.isRequired,
  toggle: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  toggleActions: PropTypes.object.isRequired,
  dynamicCounters: PropTypes.object.isRequired,
  dynamicCountersAction: PropTypes.object.isRequired
};

export default DynamicCountersListTable;
