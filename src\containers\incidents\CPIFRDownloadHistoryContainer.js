import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCPIFRReportHistory } from 'actions/incidentActions';
import CPIFRDownloadHistory from 'components/incidents/CPIFRDownloadHistory';

const mapStateToProps = (state) => {
  return {
    data: state.incidents.history
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchDownloadHistory: bindActionCreators(onFetchCPIFRReportHistory, dispatch)
  };
};

const CPIFRDownloadHistoryContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CPIFRDownloadHistory);

export default CPIFRDownloadHistoryContainer;
