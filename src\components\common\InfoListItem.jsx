import React from 'react';
import PropTypes from 'prop-types';
import { ListGroupItem, Badge } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

/**
 * A reusable list item component for displaying key-value pairs with
 * consistent styling and optional icons/badges.
 *
 * @example
 * <InfoListItem
 *   label="Industry"
 *   value="Technology"
 *   icon={faIndustry}
 *   variant="success"
 *   badge={{text: 'Active', variant: 'success'}}
 * />
 */
const InfoListItem = ({
  label,
  value,
  icon,
  variant = 'default',
  badge,
  rightContent,
  className = ''
}) => {
  const statusClass =
    variant === 'success'
      ? 'text-success'
      : variant === 'danger'
        ? 'text-danger'
        : variant === 'warning'
          ? 'text-warning'
          : 'text-dark';

  return (
    <ListGroupItem
      className={`d-flex justify-content-between align-items-start rounded shadow-sm mb-2 ${className}`}>
      <div className="d-flex align-items-start">
        {icon && (
          <FontAwesomeIcon
            icon={icon}
            className={`text-muted me-2 mt-1 ${variant === 'important' ? 'fw-bold' : ''}`}
          />
        )}
        <span className="text-muted small">{label}</span>
      </div>
      <div className={`text-end ${statusClass}`}>
        {rightContent || badge ? (
          <>
            {rightContent}
            {badge && (
              <Badge bg={badge.variant || 'primary'} pill className="ms-2">
                {badge.text}
              </Badge>
            )}
          </>
        ) : (
          <strong>{value !== null && value !== undefined ? value : 'Not available'}</strong>
        )}
      </div>
    </ListGroupItem>
  );
};

InfoListItem.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.node,
  icon: PropTypes.object,
  variant: PropTypes.oneOf(['default', 'success', 'danger', 'warning', 'important']),
  badge: PropTypes.shape({
    text: PropTypes.string.isRequired,
    variant: PropTypes.string
  }),
  rightContent: PropTypes.node,
  className: PropTypes.string
};

export default InfoListItem;
