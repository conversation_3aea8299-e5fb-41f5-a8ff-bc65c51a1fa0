import {
  ON_SELECT_ENTITY,
  ON_FETCH_ENTITY_LIST_FAILURE,
  ON_FETCH_ENTITY_LIST_LOADING,
  ON_FETCH_ENTITY_LIST_SUCCESS,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING,
  ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS,
  ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

import { unionBy } from 'lodash';

export default function profilingReducer(state = initialState.profiling, action) {
  switch (action.type) {
    case ON_FETCH_ENTITY_LIST_LOADING:
      return objectAssign({}, state, {
        profilingList: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ENTITY_LIST_SUCCESS:
      return objectAssign({}, state, {
        profilingList: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ENTITY_LIST_FAILURE:
      return objectAssign({}, state, {
        profilingList: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_SELECT_ENTITY:
      return objectAssign({}, state, {
        profilingList: {
          selected: action.entity,
          list: []
        }
      });
    case ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_LOADING:
      return objectAssign({}, state, {
        customerTransactionSummary: objectAssign({}, state.customerTransactionSummary, {
          count: state.customerTransactionSummary.count,
          isLastPage: state.customerTransactionSummary.isLastPage,
          list: state.customerTransactionSummary.list,
          loader: true
        })
      });
    case ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_SUCCESS:
      return objectAssign({}, state, {
        customerTransactionSummary: objectAssign({}, state.customerTransactionSummary, {
          count: action.response.count,
          isLastPage: action.response.isLastPage,
          list:
            action.calledBy === 'byPagination'
              ? unionBy(state.customerTransactionSummary.list, action.response.entities, 'entityId')
              : action.response.entities,
          loader: false
        })
      });
    case ON_FETCH_CUSTOMER_TRANSACTION_SUMMARY_FAILURE:
      return objectAssign({}, state, {
        customerTransactionSummary: objectAssign({}, state.customerTransactionSummary, {
          count: state.customerTransactionSummary.count,
          isLastPage: state.customerTransactionSummary.isLastPage,
          list: state.customerTransactionSummary.list,
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_PROFILING_SEARCH_CONDITIONS_LOADING:
      return objectAssign({}, state, {
        searchConditions: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_PROFILING_SEARCH_CONDITIONS_SUCCESS:
      return objectAssign({}, state, {
        searchConditions: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_PROFILING_SEARCH_CONDITIONS_FAILURE:
      return objectAssign({}, state, {
        searchConditions: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    default:
      return state;
  }
}
