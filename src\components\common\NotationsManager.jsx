import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencil, faTrash, faCheck, faClose, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Button, Col, Form, Input, Label, ListGroup, ListGroupItem, Row } from 'reactstrap';

import ListLoader from 'components/loader/ListLoader';
import ConfirmAlert from 'components/common/ConfirmAlert';

const notationsInitialState = { id: '', notation: '', category: '' };

function NotationsManager({ theme, notations, notationActions }) {
  const [selectedNotation, setSelectedNotation] = useState(notationsInitialState);
  const [addMode, setAddMode] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [deleteMode, setDeleteMode] = useState(false);

  useEffect(() => {
    if (notations?.list.length === 0 && (!notations.loader || !notations.error))
      notationActions.onFetchNotationsList();
  }, []);

  const notationsByVerdict = _.partition(notations?.list, ['category', 'Fraud']);

  function editNotation(notation) {
    setSelectedNotation(notation);
    setEditMode(true);
  }

  function resetNotationForm(mode) {
    setSelectedNotation(notationsInitialState);
    if (mode === 'add') setAddMode('');
    else setEditMode(false);
  }

  function submitNotation(e, mode) {
    e.preventDefault();
    if (mode === 'add') {
      notationActions.onAddNotationToMaster(selectedNotation);
      setAddMode('');
    } else {
      notationActions.onUpdateNotationFromMaster(selectedNotation);
      setEditMode(false);
    }
    setSelectedNotation(notationsInitialState);
  }

  function deleteNotation(notation) {
    setSelectedNotation(notation);
    setDeleteMode(true);
  }

  function cancelDelete() {
    setSelectedNotation(notationsInitialState);
    setDeleteMode(false);
  }

  function confirmDelete() {
    notationActions.onDeleteNotationFromMaster(selectedNotation.id);
    setSelectedNotation(notationsInitialState);
    setDeleteMode(false);
  }

  const notationForm = (selectedNotation, category, mode) => (
    <Form onSubmit={(e) => submitNotation(e, mode)}>
      <Row className="g-3 align-items-center">
        <Col lg="10" md="10" sm="9" xs="12">
          <Label className="visually-hidden" for="notation">
            Update Notation
          </Label>
          <Input
            name="notation"
            id="notation"
            value={selectedNotation.notation}
            onChange={(e) =>
              setSelectedNotation((prev) => ({ ...prev, category, notation: e.target.value }))
            }
          />
        </Col>
        <Col>
          <Button outline size="sm" color="success" title="Submit updated notation">
            <FontAwesomeIcon icon={faCheck} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            className="ms-1"
            type="button"
            onClick={() => resetNotationForm(mode)}
            title="Cancel update">
            <FontAwesomeIcon icon={faClose} />
          </Button>
        </Col>
      </Row>
    </Form>
  );

  const renderList = (list, type) =>
    list?.map((notation) => (
      <ListGroupItem key={notation.id}>
        {editMode && selectedNotation.id === notation.id ? (
          notationForm(selectedNotation, type, 'edit')
        ) : (
          <>
            <Button
              outline
              size="sm"
              color="warning"
              onClick={() => editNotation(notation)}
              disabled={deleteMode}>
              <FontAwesomeIcon icon={faPencil} />
            </Button>
            <Button
              outline
              size="sm"
              color="danger"
              className="ms-1"
              disabled={editMode}
              onClick={() => deleteNotation(notation)}>
              <FontAwesomeIcon icon={faTrash} />
            </Button>
            <span className="ms-3">{notation.notation}</span>
          </>
        )}
      </ListGroupItem>
    ));

  const addButton = (category) => (
    <Button
      outline
      size="sm"
      color="primary"
      className="ms-1"
      disabled={editMode}
      onClick={() => setAddMode(category)}>
      <FontAwesomeIcon icon={faPlus} />
    </Button>
  );

  if (notations.loader)
    return (
      <div className="no-data-div">
        <ListLoader />
      </div>
    );

  if (notations.error) return <div className="no-data-div">{notations.errorMessage}</div>;

  return (
    <div>
      <h6 className="d-flex justify-content-between align-items-end">
        Fraud Notations {addButton('Fraud')}
      </h6>
      <ListGroup>
        {renderList(notationsByVerdict?.[0], 'Fraud')}
        {addMode === 'Fraud' && notationForm(selectedNotation, 'Fraud', 'add')}
      </ListGroup>
      <h6 className="d-flex justify-content-between align-items-end mt-3">
        Non Fraud Notations {addButton('NotFraud')}
      </h6>
      <ListGroup>
        {renderList(notationsByVerdict?.[1], 'NotFraud')}
        {addMode === 'NotFraud' && notationForm(selectedNotation, 'NotFraud', 'add')}
      </ListGroup>
      <ConfirmAlert
        theme={theme}
        confirmAlertModal={deleteMode}
        toggleConfirmAlertModal={cancelDelete}
        confirmationAction={confirmDelete}
        confirmAlertTitle={`Are you sure you want to remove this notation -
          ${selectedNotation.notation} ?`}
      />
    </div>
  );
}

NotationsManager.propTypes = {
  theme: PropTypes.string.isRequired,
  notations: PropTypes.object.isRequired,
  notationActions: PropTypes.object.isRequired
};

export default NotationsManager;
