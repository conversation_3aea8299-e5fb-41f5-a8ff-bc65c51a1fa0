import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchNBFCTrxnsCount, onFetchNBFCTrxnsData } from 'actions/rfiReportActions';
import NBFCReport from 'components/rfiReports/NBFCReport';

const mapStateToProps = (state) => {
  return {
    nbfcTrxns: state.rfiReports.nbfcTrxns
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchNBFCTrxnsCount: bindActionCreators(onFetchNBFCTrxnsCount, dispatch),
    fetchNBFCTrxnsData: bindActionCreators(onFetchNBFCTrxnsData, dispatch)
  };
};

const NBFCReportContainer = connect(mapStateToProps, mapDispatchToProps)(NBFCReport);

export default NBFCReportContainer;
