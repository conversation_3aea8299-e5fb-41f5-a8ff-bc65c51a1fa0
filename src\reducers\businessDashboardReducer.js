import {
  ON_<PERSON>ET<PERSON>_BUSINESS_KPI_LOADING,
  ON_<PERSON>ETCH_BUSINESS_KPI_SUCCESS,
  ON_FETCH_BUSINESS_KPI_FAILURE,
  ON_FETCH_ACTIONS_SHARE_LOADING,
  ON_FETCH_ACTIONS_SHARE_SUCCESS,
  ON_FETCH_ACTIONS_SHARE_FAILURE,
  ON_FETCH_NO_VIOLATION_FRAUD_LOADING,
  ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS,
  ON_FETCH_NO_VIOLATION_FRAUD_FAILURE,
  ON_FETCH_RULE_CATEGORY_TREND_LOADING,
  ON_FETCH_RULE_CATEGORY_TREND_SUCCESS,
  ON_FETCH_RULE_CATEGORY_TREND_FAILURE,
  ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING,
  ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS,
  ON_<PERSON>ET<PERSON>_HIGH_ALERT_CUSTOMERS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function businessDashboardReducer(state = initialState.businessDashboard, action) {
  switch (action.type) {
    case ON_FETCH_BUSINESS_KPI_LOADING:
      return objectAssign({}, state, {
        businessKpis: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_BUSINESS_KPI_SUCCESS:
      return objectAssign({}, state, {
        businessKpis: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_BUSINESS_KPI_FAILURE:
      return objectAssign({}, state, {
        businessKpis: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.errorMessage
        }
      });
    case ON_FETCH_ACTIONS_SHARE_LOADING:
      return objectAssign({}, state, {
        actionShare: {
          data: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ACTIONS_SHARE_SUCCESS:
      return objectAssign({}, state, {
        actionShare: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ACTIONS_SHARE_FAILURE:
      return objectAssign({}, state, {
        actionShare: {
          data: {},
          loader: false,
          error: true,
          errorMessage: action.response.errorMessage
        }
      });
    case ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING:
      return objectAssign({}, state, {
        highAlertCustomers: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS:
      return objectAssign({}, state, {
        highAlertCustomers: {
          data: action.response.customers,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE:
      return objectAssign({}, state, {
        highAlertCustomers: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.errorMessage
        }
      });
    case ON_FETCH_NO_VIOLATION_FRAUD_LOADING:
      return objectAssign({}, state, {
        noViolationFraud: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS:
      return objectAssign({}, state, {
        noViolationFraud: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_NO_VIOLATION_FRAUD_FAILURE:
      return objectAssign({}, state, {
        noViolationFraud: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.errorMessage
        }
      });
    case ON_FETCH_RULE_CATEGORY_TREND_LOADING:
      return objectAssign({}, state, {
        ruleCategoryTrend: {
          data: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_CATEGORY_TREND_SUCCESS:
      return objectAssign({}, state, {
        ruleCategoryTrend: {
          data: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RULE_CATEGORY_TREND_FAILURE:
      return objectAssign({}, state, {
        ruleCategoryTrend: {
          data: [],
          loader: false,
          error: true,
          errorMessage: action.response.errorMessage
        }
      });
    default:
      return state;
  }
}
