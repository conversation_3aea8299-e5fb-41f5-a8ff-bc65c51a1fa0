import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onUpdateActionList } from 'actions/ruleCreationActions';
import ActionOrderList from 'components/ruleEngine/ActionOrderList';

const mapStateToProps = (state) => {
  return {
    actionList: state.ruleCreation.actionList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    updateActionList: bindActionCreators(onUpdateActionList, dispatch)
  };
};

const ActionOrderListContainer = connect(mapStateToProps, mapDispatchToProps)(ActionOrderList);

export default ActionOrderListContainer;
