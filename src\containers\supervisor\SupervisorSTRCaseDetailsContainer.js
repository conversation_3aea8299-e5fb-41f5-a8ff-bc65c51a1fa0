import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onClearSelectedCase, onSelectCase } from 'actions/caseReviewActions';
import SupervisorSTRCaseDetails from 'components/supervisor/SupervisorSTRCaseDetails';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    selectCase: bindActionCreators(onSelectCase, dispatch),
    clearSelectedCase: bindActionCreators(onClearSelectedCase, dispatch)
  };
};

const SupervisorSTRCaseDetailsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(SupervisorSTRCaseDetails);

export default SupervisorSTRCaseDetailsContainer;
