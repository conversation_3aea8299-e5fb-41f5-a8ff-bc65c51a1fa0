import PropTypes from 'prop-types';
import React from 'react';
import { Button, TabContent } from 'reactstrap';

const FormStepper = ({ steps, children, active }) => {
  const steppers = steps.map((step, idx) => (
    <div className="steps-step" key={idx}>
      <Button
        type="button"
        size="sm"
        className="btn-circle"
        color={idx === active ? 'primary' : 'secondary'}>
        {idx + 1}
      </Button>
      <p className={idx === active ? 'text-active' : ''}>{step}</p>
    </div>
  ));

  return (
    <div>
      <div className="steps-form">
        <div className="steps-row setup-panel">{steppers}</div>
      </div>
      <TabContent activeTab={active}>{children}</TabContent>
    </div>
  );
};

FormStepper.propTypes = {
  active: PropTypes.number.isRequired,
  steps: PropTypes.array.isRequired,
  children: PropTypes.array.isRequired
};

export default FormStepper;
