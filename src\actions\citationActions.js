import {
  ON_FETCH_CASE_CITATION_LOADING,
  ON_FETCH_CASE_CITATION_SUCCESS,
  ON_FETCH_CASE_CITATION_FAILURE,
  ON_ADD_CITATION_COMMENT_LOADING,
  ON_ADD_CITATION_COMMENT_SUCCESS,
  ON_ADD_CITATION_COMMENT_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';
import { onToggleLoader } from './toggleActions';
import { onShowFailureAlert, onShowSuccessAlert } from './alertActions';

function fetchCaseCitations(caseRefNo) {
  return client({
    url: `casereview/case/${caseRefNo}/citations/fetch/all`,
    badRequestMessage: 'Unable to fetch citation response.'
  });
}

function onFetchCaseCitationsLoading() {
  return { type: ON_FETCH_CASE_CITATION_LOADING };
}

function onFetchCaseCitationsSuccess(response) {
  return {
    type: ON_<PERSON>ETCH_CASE_CITATION_SUCCESS,
    response
  };
}

function onFetchCaseCitationsFailure(response) {
  return {
    type: ON_FETCH_CASE_CITATION_FAILURE,
    response
  };
}

function onFetchCaseCitations(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchCaseCitationsLoading());
    return fetchCaseCitations(caseRefNo).then(
      (success) => dispatch(onFetchCaseCitationsSuccess(success)),
      (error) => dispatch(onFetchCaseCitationsFailure(error))
    );
  };
}

function addCitationComment(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/capture/citation/response`,
    data: formData,
    badRequestMessage: 'Unable to add citation response.'
  });
}

function onAddCitationCommentLoading() {
  return { type: ON_ADD_CITATION_COMMENT_LOADING };
}

function onAddCitationCommentSuccess(response) {
  return {
    type: ON_ADD_CITATION_COMMENT_SUCCESS,
    response
  };
}

function onAddCitationCommentFailure(response) {
  return {
    type: ON_ADD_CITATION_COMMENT_FAILURE,
    response
  };
}

function onAddCitationComment(formData) {
  return function (dispatch) {
    dispatch(onAddCitationCommentLoading());
    return addCitationComment(formData).then(
      () => dispatch(onAddCitationCommentSuccess(formData)),
      (error) => dispatch(onAddCitationCommentFailure(error))
    );
  };
}

function addCaseCitation(caseRefNo, citations) {
  return client({
    method: 'POST',
    url: `casereview/add/case/citation`,
    data: { caseRefNo, citations },
    badRequestMessage: 'Unable to add citation response.'
  });
}

function onAddCaseCitation(caseRefNo, citations) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addCaseCitation(caseRefNo, citations)
      .then(
        () => {
          dispatch(onFetchCaseCitations(caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'Case citations added successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export { onFetchCaseCitations, onAddCitationComment, onAddCaseCitation };
