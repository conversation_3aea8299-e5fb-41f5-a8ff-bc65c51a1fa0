import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';

import { dataColumn } from 'utility/customRenders';

function CustomerAccountsTable({ accountsData, partnerId }) {
  const headers = [
    {
      Header: '#',
      accessor: 'accountNo',
      fixed: true,
      style: { overflow: 'visible' },
      Cell: ({ value }) => dataColumn('', value.value, { entityDetails: value, partnerId })
    },
    { Header: 'Type', accessor: 'accountType' },
    {
      Header: 'Opening Date',
      accessor: 'accountOpeningDate',
      Cell: ({ value }) => value ? moment(value).format('YYYY-MM-DD') : null
    },
    { Header: 'Branch', accessor: 'branch' },
    {
      Header: 'IFSC',
      accessor: 'ifscCode',
      fixed: true,
      style: { overflow: 'visible' },
      Cell: ({ value }) => dataColumn('', value.value, { entityDetails: value, partnerId })
    },
    { Header: 'Branch Address', accessor: 'branch_address' },
    { Header: 'Is <PERSON><PERSON><PERSON>?', accessor: 'isDormant', Cell: ({ value }) => (value ? 'Yes' : 'No') },
    { Header: 'KYC Type', accessor: 'kycType' },
    { Header: 'Nominee Name', accessor: 'nominee' },
    { Header: 'Nominee Relationship', accessor: 'nomineeRelation' },
    { Header: 'Branch Manager email', accessor: 'branch_manager_email' },
    { Header: 'RM email', accessor: 'rel_manager_email' }
  ];

  return (
    <ReactTable
      columns={headers}
      data={accountsData}
      noDataText="No accounts found"
      showPaginationTop={false}
      showPaginationBottom={false}
      pageSizeOptions={[5, 10, 20, 30, 40, 50]}
      defaultPageSize={10}
      minRows={3}
      className={'-highlight'}
    />
  );
}

CustomerAccountsTable.propTypes = {
  partnerId: PropTypes.number,
  accountsData: PropTypes.array.isRequired
};

export default CustomerAccountsTable;
