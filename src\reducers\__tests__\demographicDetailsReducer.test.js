import demographicDetailsReducer from 'reducers/demographicDetailsReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';

describe('Demographic Details reducer', () => {
  it('should return the intial state', () => {
    expect(demographicDetailsReducer(undefined, {})).toEqual(initialState.demographicDetails);
  });

  it('should handle ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING
        }
      )
    ).toEqual({
      merchant: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS', () => {
    const response = {};
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS,
          response
        }
      )
    ).toEqual({
      merchant: {
        details: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      merchant: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING
        }
      )
    ).toEqual({
      agent: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS', () => {
    const response = {};
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS,
          response
        }
      )
    ).toEqual({
      agent: {
        details: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      agent: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_LOADING
        }
      )
    ).toEqual({
      customer: {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS', () => {
    const response = {};
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS,
          response
        }
      )
    ).toEqual({
      customer: {
        details: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE', () => {
    expect(
      demographicDetailsReducer(
        {},
        {
          type: types.ON_FETCH_CUSTOMER_DEMOGRAPHIC_DETAILS_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      customer: {
        details: {},
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });
});
