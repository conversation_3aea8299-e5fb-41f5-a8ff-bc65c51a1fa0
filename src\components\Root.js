import React, { Component, Profiler } from 'react';
import PropTypes from 'prop-types';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import Loader from 'components/loader/Loader';
import App from './App';

function onRenderCallback(
  _id,
  _phase,
  _actualDuration,
  _baseDuration,
  _startTime,
  _commitTime,
  _interactions
) {}

export default class Root extends Component {
  render() {
    const { store, persistor } = this.props;
    return (
      <Provider store={store}>
        <PersistGate loading={<Loader show={true} />} persistor={persistor}>
          <BrowserRouter>
            <Profiler id="IFRM" onRender={onRenderCallback}>
              <App />
            </Profiler>
          </BrowserRouter>
        </PersistGate>
      </Provider>
    );
  }
}

Root.propTypes = {
  store: PropTypes.object.isRequired,
  persistor: PropTypes.object.isRequired
};
