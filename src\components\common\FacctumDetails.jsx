import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Badge } from 'reactstrap';

function FacctumDetails({
  facctumData,
  fetchFacctumDetails,
  pan = undefined,
  name = undefined,
  aadhar = undefined
}) {
  useEffect(() => {
    if (name || aadhar || pan) {
      const formData = {
        entity_type: 'U',
        ...(name && { name }),
        ...(aadhar ||
          (pan && {
            national_id: aadhar || pan
          }))
      };

      fetchFacctumDetails(formData);
    }
  }, [name, aadhar, pan]);

  const facctumDetailsBadge = (facctumItem) => {
    const badge = (
      <div className="data-columns no-break p-1">
        <span>
          <Badge color="danger">{facctumItem} list</Badge>
        </span>
      </div>
    );

    return badge;
  };

  return (
    <div>
      <b>Facctum Details</b>
      {facctumData.details?.is_sanctioned && facctumDetailsBadge('Sanctioned')}
      {facctumData.details?.is_PEP && facctumDetailsBadge('PEP')}
      {facctumData.details?.is_ECR && facctumDetailsBadge('ECR')}
      {facctumData.details?.is_SI && facctumDetailsBadge('SI')}
      {facctumData.details?.is_SCO && facctumDetailsBadge('SCO')}
      {facctumData.details?.is_RCA && facctumDetailsBadge('RCA')}
    </div>
  );
}

FacctumDetails.propTypes = {
  pan: PropTypes.string,
  name: PropTypes.string,
  aadhar: PropTypes.string,
  facctumData: PropTypes.object.isRequired,
  fetchFacctumDetails: PropTypes.func.isRequired
};

export default FacctumDetails;
