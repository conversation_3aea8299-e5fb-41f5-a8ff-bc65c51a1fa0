import { faAsterisk, faTrash, faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import Datetime from 'react-datetime';
import ReactTable from 'react-table';
import { Button, FormGroup, Label, Input, Row, Col, FormFeedback } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';

const VerdictModal = ({
  liability,
  fraudTypes,
  fetchLiabilityList,
  fetchFraudTypesList,
  userslist,
  fetchUsersList,
  caseId,
  closeCase,
  requestApproval,
  theme,
  display,
  toggle,
  channel,
  role,
  bulkCaseIds,
  caseDetails,
  fetchCloseCaseBuckets,
  closeCaseBuckets,
  fraudTypesWithBuckets,
  fetchFraudTypesWithBuckets,
  externalCheckers,
  fetchExternalCheckerList,
  partnerId,
  requestPartnerApproval,
  attributeList,
  fetchAttributeList,
  moduleType,
  hasAcquirerPortals,
  addInvestigatorVerdict,
  customerInfo,
  violatedRules = [],
  singleType = '',
  bucket = '',
  txnDetails = {}
}) => {
  const [verdict, setVerdict] = useState('');
  const [fraudType, setFraudType] = useState('');
  const [checker, setChecker] = useState('');
  const [explanation, setExplanation] = useState('');
  const [investigationLiability, setInvestigationLiability] = useState('');
  const [approval, setApproval] = useState(false);
  const [externalApproval, setExternalApproval] = useState(false);
  const [partnerChecker, setPartnerChecker] = useState('');
  const [bucketId, setBucketId] = useState('');
  const [repeat, setRepeat] = useState(false);
  const [attribute, setAttribute] = useState({});
  const [operator, setOperator] = useState('');
  const [value, setValue] = useState('');
  const [invalidValue, setInvalidValue] = useState(false);
  const [invalidValueMessage, setInvalidValueMessage] = useState('');
  const [conditionsList, setConditionsList] = useState([]);
  const [repeatUntil, setRepeatUntil] = useState();
  const [duplicateCondition, setDuplicateCondition] = useState(false);
  const validDate = (current) => current.isAfter(new Date());

  useEffect(() => {
    if (_.isEmpty(liability.list)) fetchLiabilityList();
    if (_.isEmpty(fraudTypes.list)) fetchFraudTypesList();
    if (_.isEmpty(closeCaseBuckets.list)) fetchCloseCaseBuckets();
    if (_.isEmpty(fraudTypesWithBuckets.list)) fetchFraudTypesWithBuckets();
    if (_.isEmpty(userslist)) fetchUsersList();
    if (_.isEmpty(externalCheckers.list) && moduleType === 'acquirer') fetchExternalCheckerList();
    if (_.isEmpty(attributeList.list)) fetchAttributeList(channel);
  }, []);

  const operators = [
    { key: '=', value: 'equalTo', dataType: ['text', 'int', 'numeric'] },
    { key: '>', value: 'greaterThan', dataType: ['int', 'numeric'] },
    { key: '<', value: 'lessThan', dataType: ['int', 'numeric'] },
    { key: '>=', value: 'greaterThanEqualTo', dataType: ['int', 'numeric'] },
    { key: '<=', value: 'lessThanEqualTo', dataType: ['int', 'numeric'] },
    { key: '!=', value: 'notEqualTo', dataType: ['text', 'int', 'numeric'] }
  ];

  const pattern = {
    alphanumeric: /^[\w\-\d]+$/,
    integer: /^[+-]?[\d]+$/,
    decimal: /^[+-]?\d+(\.\d+)?$/,
    default: /^$/
  };

  useEffect(() => {
    clearModalValue();
  }, [display]);

  const clearModalValue = () => {
    setVerdict('');
    setFraudType('');
    setExplanation('');
    setInvestigationLiability('');
    setBucketId('');
    setApproval(false);
    setChecker('');
  };

  const handleValueValidationProps = (dataType) => {
    switch (true) {
      case dataType === 'text':
        return {
          pattern: pattern.alphanumeric,
          title: 'Please enter an alphanumeric value'
        };
      case dataType === 'int':
        return {
          pattern: pattern.integer,
          title: 'Please enter an integer'
        };
      case dataType === 'numeric':
        return {
          pattern: pattern.decimal,
          title: 'Please enter a decimal'
        };
      default:
        return {
          pattern: pattern.default,
          title: ''
        };
    }
  };

  const validateValue = (dataType, value) => {
    const applicableRegex = handleValueValidationProps(dataType);
    setInvalidValue(!applicableRegex.pattern.test(value));
    setInvalidValueMessage(applicableRegex.title);
  };

  const validateDuplicateCondition = (condition) => {
    if (_.find(conditionsList, (d) => _.isEqual(d, condition)) !== undefined) {
      setDuplicateCondition(true);
      setInvalidValueMessage('Condition already exists!');
    } else setDuplicateCondition(false);
  };

  const operatorOptions = operators
    .filter((operator) => _.includes(operator.dataType, _.toLower(attribute?.dataType)))
    .map((operator) => (
      <option key={operator.value} value={operator.key}>
        {operator.value}
      </option>
    ));

  const handleFieldChange = (e) => {
    const selectedField = attributeList.list.find((d) => d.value === e.target.value);
    setAttribute(selectedField);
    setOperator(operators[0].key);
    setValue(caseDetails[selectedField.value].toString() || '');
    validateDuplicateCondition({
      attribute: selectedField.value,
      operator: operators[0].key,
      value: caseDetails[selectedField.value].toString() || ''
    });
  };

  const handleAddConditions = () => {
    const condition = { attribute: attribute?.value, operator, value };
    setConditionsList((conditionsList) => [...conditionsList, condition]);
    setAttribute({});
    setOperator('');
    setValue('');
  };

  const handleRemoveConditions = (selectedCondition) => {
    if (confirm('Are you sure you wish to remove this condition ?')) {
      const newConditionsList =
        conditionsList &&
        conditionsList.filter((condition) => !_.isEqual(condition, selectedCondition));
      setConditionsList(newConditionsList);
    }
  };

  const conditionsTableheader = [
    {
      Header: 'Attribute',
      accessor: 'attribute',
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => <span>{value}</span>
    },
    { Header: 'Operator', accessor: 'operator' },
    { Header: 'Values', accessor: 'value' },
    {
      Header: 'Actions',
      filterable: false,
      sortable: false,
      maxWidth: 80,

      Cell: (row) => (
        <Button
          size="sm"
          color="danger"
          title="Delete"
          onClick={() => handleRemoveConditions(row.original)}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    }
  ];

  const conditionalAttributesOptions =
    attributeList?.list &&
    _.map(attributeList?.list, (attribute) => (
      <option key={attribute.key} value={attribute.value}>
        {attribute.key}
      </option>
    ));

  const getNegativeDBData = () => ({
    fraudName: fraudType.fraudName,
    entityId: caseDetails.entityId,
    txnTimestamp: caseDetails.txnTimestamp,
    txnId: caseDetails.txnId,
    channelName: txnDetails?.masterFields?.channelName.split(',')[0] || null,

    customerIp: txnDetails?.deviceInfo?.customerIp?.value || null,
    deviceId: txnDetails?.deviceInfo?.deviceId?.value || null,
    latitude: txnDetails?.locationCoordinates?.latitude?.value || null,
    longitude: txnDetails?.locationCoordinates?.longitude?.value || null,

    payeeMobileNumber: txnDetails?.payeeAccount?.payeeMmidMobileNumber || null,
    payeeAccountNumber: txnDetails?.payeeAccount?.payeeAccountNumber?.value || null,
    payerAccountNumber: txnDetails?.payerAccount?.payerAccountNumber?.value || null,
    payerMobileNumber: txnDetails?.payerAccount?.payerMmidMobileNumber || null,

    email: customerInfo?.email || null
  });

  const submitVerdict = (e) => {
    e.preventDefault();
    const formData = {
      remark: explanation,
      investigationLiability: 1,
      bucketId: parseInt(bucketId),
      investigationVerdictId: parseInt(verdict),
      merchantId: caseDetails?.entityId,
      partnerId: caseDetails?.partnerId || 0,
      ...(!_.isEmpty(bulkCaseIds) ? { caseRefNos: bulkCaseIds } : { caseRefNo: caseId }),
      ...(fraudType && { fraudTypeId: parseInt(fraudType?.id) }),
      ...(checker && { checkerToBeEscalatedTo: parseInt(checker) }),
      ...(partnerChecker && { extCheckerToBeEscalatedTo: parseInt(partnerChecker) }),
      ...(repeat && {
        violatedRules,
        snoozeConditions: conditionsList,
        automateUntil: moment(repeatUntil).format('YYYY-MM-DD HH:mm:ss')
      }),
      ...((bucket === 'Unverified' || bucket === 'Priority') &&
        channel === 'frm' &&
        role === 'investigator' && { bucketType: bucket })
    };

    if (
      (isCooperative && role === 'investigator' && caseDetails.currentStatus === 'Closed') ||
      (bucket === 'Unverified' && role === 'investigator' && channel === 'frm') ||
      (bucket === 'Priority' && role === 'investigator' && channel === 'frm')
    )
      addInvestigatorVerdict(formData);
    else if (partnerChecker) requestPartnerApproval(formData, 'frm', singleType);
    else if ((role === 'maker' && approval) || caseDetails.isHold === 1)
      requestApproval(formData, 'frm', !_.isEmpty(bulkCaseIds) ? '/bulk' : singleType);
    else {
      let path;
      let negativeDBpayload = {};

      if (!_.isEmpty(bulkCaseIds)) path = '/bulk';
      else {
        path = singleType;
        if (!_.isEmpty(fraudType)) negativeDBpayload = getNegativeDBData();
      }

      closeCase(formData, 'frm', path, negativeDBpayload);
    }
  };

  const verdictOptions =
    !fraudTypes.error &&
    fraudTypes.list.map((verdict) => (
      <option key={verdict.id} value={verdict.id}>
        {verdict.verdict}
      </option>
    ));

  const selectedVerdict =
    !(fraudTypes.error || _.isEmpty(verdict)) &&
    fraudTypes.list.find((verdictItem) => +verdictItem.id === +verdict);

  const fraudTypeOptions =
    selectedVerdict &&
    selectedVerdict.types.map((fraud) => (
      <option key={fraud.id} value={JSON.stringify(fraud)}>
        {fraud.fraudName}
      </option>
    ));

  const liabilityOptions =
    !liability.error &&
    liability.list.map((liability) => (
      <option key={liability.id} value={liability.id}>
        {liability.liabilityType}
      </option>
    ));

  const checkerOptions = userslist
    .filter((user) => _.includes(user.channelRoles, `${channel}:checker`))
    .map((user) => (
      <option key={user.id} value={user.id}>
        {user.userName}
      </option>
    ));

  const getExternalCheckerOptions = () => {
    if (!partnerId) return null;

    if (externalCheckers.error)
      return (
        <option key={1} value="" disabled>
          {externalCheckers.errorMessage}
        </option>
      );

    return externalCheckers.list
      ?.filter((d) => +d.partnerId === +partnerId)
      ?.map((user) => (
        <option key={user.id} value={user.id}>
          {user.userName}
        </option>
      ));
  };

  const selectedVerdictWithBuckets =
    !(fraudTypesWithBuckets.error || _.isEmpty(verdict)) &&
    fraudTypesWithBuckets.list.find((verdictItem) => +verdictItem.id === +verdict);

  const bucketOptions =
    selectedVerdictWithBuckets &&
    selectedVerdictWithBuckets.buckets.map((bucket) => (
      <option key={bucket.id} value={bucket.id}>
        {bucket.name}
      </option>
    ));

  return (
    <ModalContainer
      theme={theme}
      size="lg"
      header={!_.isEmpty(bulkCaseIds) ? 'Bulk Close Cases' : 'Close Case'}
      isOpen={display}
      toggle={() => toggle('frm')}>
      <form onSubmit={submitVerdict}>
        <FormGroup>
          <Label>Verdict</Label>
          <FontAwesomeIcon icon={faAsterisk} className="required-star" />
          <Input
            type="select"
            name="verdict"
            id="verdict"
            onChange={(e) => setVerdict(e.target.value)}
            value={verdict}
            required>
            <option value="">-- SELECT --</option>
            {verdictOptions}
          </Input>
        </FormGroup>
        {selectedVerdict && selectedVerdict.types[0].id !== 9 && (
          <FormGroup>
            <Label>Fraud type</Label>
            <FontAwesomeIcon icon={faAsterisk} className="required-star" />
            <Input
              type="select"
              name="fraudType"
              id="fraudType"
              onChange={(e) => setFraudType(JSON.parse(e.target.value))}
              value={fraudType ? JSON.stringify(fraudType) : ''}
              required>
              <option value="">-- SELECT --</option>
              {fraudTypeOptions}
            </Input>
          </FormGroup>
        )}
        {investigationLiability !== 1 && investigationLiability !== '' && (
          <FormGroup>
            <Label>Liability</Label>
            <FontAwesomeIcon icon={faAsterisk} className="required-star" />
            <Input
              type="select"
              name="investigationLiability"
              id="investigationLiability"
              onChange={(e) => setInvestigationLiability(e.target.value)}
              value={investigationLiability}
              required>
              <option value="">-- SELECT --</option>
              {liabilityOptions}
            </Input>
          </FormGroup>
        )}
        <FormGroup>
          <Label>Explanation</Label>
          <FontAwesomeIcon icon={faAsterisk} className="required-star" />
          <Input
            type="textarea"
            name="explanation"
            id="explanation"
            placeholder="Explanation"
            onChange={(e) => setExplanation(e.target.value)}
            value={explanation}
            required
          />
        </FormGroup>

        <FormGroup>
          <Label>Move to bucket</Label>
          <FontAwesomeIcon icon={faAsterisk} className="required-star" />
          <Input
            type="select"
            name="bucketId"
            id="bucketId"
            onChange={(e) => setBucketId(e.target.value)}
            value={bucketId}
            required>
            <option value="">-- SELECT --</option>
            {bucketOptions}
          </Input>
        </FormGroup>
        {role === 'maker' && (
          <FormGroup>
            <Label>
              <input
                type="checkbox"
                name="approval"
                id="approval"
                onChange={() => setApproval(!approval)}
                checked={approval || caseDetails.isHold === 1}
                disabled={caseDetails.isHold === 1 || externalApproval || repeat}
              />
              &nbsp; Request Checker Approval
            </Label>
          </FormGroup>
        )}
        {((role === 'maker' && approval) || caseDetails.isHold === 1) && (
          <FormGroup>
            <Label>Checker</Label>
            <FontAwesomeIcon icon={faAsterisk} className="required-star" />
            <Input
              type="select"
              name="checker"
              id="checker"
              onChange={(e) => setChecker(e.target.value)}
              value={checker}
              required>
              <option value="">-- SELECT --</option>
              {checkerOptions}
            </Input>
          </FormGroup>
        )}
        {moduleType === 'acquirer' && hasAcquirerPortals === 1 && (
          <>
            <FormGroup>
              <Label>
                <input
                  type="checkbox"
                  name="externalApproval"
                  id="externalApproval"
                  className="mr-1"
                  onChange={() => setExternalApproval(!externalApproval)}
                  checked={externalApproval}
                  disabled={approval || repeat || !_.isEmpty(bulkCaseIds)}
                />
                &nbsp; Request Partner Checker Approval
              </Label>
            </FormGroup>
            {externalApproval && (
              <FormGroup>
                <Label>Partner Checker</Label>
                <FontAwesomeIcon icon={faAsterisk} className="required-star" />
                <Input
                  type="select"
                  name="partnerChecker"
                  id="partnerChecker"
                  onChange={(e) => setPartnerChecker(e.target.value)}
                  value={partnerChecker}
                  required>
                  <option value="">-- SELECT --</option>
                  {getExternalCheckerOptions()}
                </Input>
              </FormGroup>
            )}
          </>
        )}
        {role !== 'investigator' && (
          <>
            <FormGroup>
              <Label>
                <input
                  type="checkbox"
                  name="repeat"
                  id="repeat"
                  className="mr-1"
                  onChange={() => setRepeat(!repeat)}
                  checked={repeat}
                  disabled={approval || externalApproval || !_.isEmpty(bulkCaseIds)}
                />
                &nbsp; Repeat action for similar transactions
              </Label>
            </FormGroup>
            {repeat && (
              <>
                <FormGroup>
                  <Label>Repeat till</Label>
                  <Datetime
                    name="snooze"
                    dateFormat="YYYY-MM-DD"
                    timeFormat={false}
                    value={repeatUntil}
                    onChange={(dateObj) => setRepeatUntil(dateObj._d)}
                    isValidDate={validDate}
                    inputProps={{ required: true }}
                    closeOnSelect={true}
                  />
                </FormGroup>
                <FormGroup>
                  <Label>Conditions</Label>
                  <Row>
                    <Col md="4" sm="4" xs="12">
                      <FormGroup>
                        <Label>Select Attribute </Label>
                        <Input
                          type="select"
                          name="attribute"
                          value={!_.isEmpty(attribute) ? attribute?.value : ''}
                          onChange={(e) => handleFieldChange(e)}>
                          <option value="">-- Select --</option>
                          {conditionalAttributesOptions}
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col md="4" sm="4" xs="12">
                      <FormGroup>
                        <Label>Select Operator </Label>
                        <Input
                          type="select"
                          name="operator"
                          value={operator}
                          onChange={(e) => {
                            setOperator(e.target.value);
                          }}
                          disabled={_.isEmpty(attribute)}>
                          <option value="">-- Select --</option>
                          {operatorOptions}
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col md="3" sm="3" xs="12">
                      <FormGroup>
                        <Label>Value </Label>
                        <Input
                          placeholder="value"
                          type="text"
                          name="value"
                          value={value}
                          onChange={(e) => {
                            validateValue(attribute?.dataType, e.target.value);
                            validateDuplicateCondition({
                              attribute: attribute.value,
                              operator,
                              value: e.target.value
                            });
                            setValue(e.target.value);
                          }}
                          disabled={_.isEmpty(attribute) || _.isEmpty(operator)}
                          spellCheck={false}
                          invalid={!_.isEmpty(value) && (invalidValue || duplicateCondition)}
                        />
                        <FormFeedback>{invalidValueMessage}</FormFeedback>
                      </FormGroup>
                    </Col>
                    <Col md="1" sm="1" xs="12">
                      <FormGroup className="d-flex justify-content-end">
                        <Button
                          color="primary"
                          size="sm"
                          className="add-condition-btn"
                          disabled={
                            invalidValue ||
                            duplicateCondition ||
                            _.isEmpty(attribute) ||
                            value === '' ||
                            _.isEmpty(operator)
                          }
                          onClick={handleAddConditions}>
                          <FontAwesomeIcon icon={faPlus} />
                        </Button>
                      </FormGroup>
                    </Col>
                  </Row>
                </FormGroup>
              </>
            )}

            {!_.isEmpty(conditionsList) && (
              <ReactTable
                filterable={false}
                defaultFilterMethod={(filter, row) =>
                  row[filter.id] &&
                  _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
                }
                columns={conditionsTableheader}
                data={conditionsList}
                defaultPageSize={20}
                minRows={2}
                showPaginationTop={false}
                showPaginationBottom={false}
                className="-highlight  -striped"
              />
            )}
          </>
        )}
        <FormGroup className="d-flex justify-content-end mt-3">
          <Button
            size="sm"
            type="submit"
            color="primary"
            disabled={repeat && _.isEmpty(conditionsList)}>
            Submit
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
};

VerdictModal.propTypes = {
  caseId: PropTypes.string,
  partnerId: PropTypes.number,
  singleType: PropTypes.string,
  bulkCaseIds: PropTypes.array,
  display: PropTypes.bool.isRequired,
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  violatedRules: PropTypes.array.isRequired,
  liability: PropTypes.object.isRequired,
  fraudTypes: PropTypes.object.isRequired,
  caseDetails: PropTypes.object.isRequired,
  attributeList: PropTypes.object.isRequired,
  externalCheckers: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fraudTypesWithBuckets: PropTypes.object.isRequired,
  hasAcquirerPortals: PropTypes.number.isRequired,
  toggle: PropTypes.func.isRequired,
  closeCase: PropTypes.func.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  requestApproval: PropTypes.func.isRequired,
  fetchLiabilityList: PropTypes.func.isRequired,
  fetchAttributeList: PropTypes.func.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  requestPartnerApproval: PropTypes.func.isRequired,
  addInvestigatorVerdict: PropTypes.func.isRequired,
  fetchExternalCheckerList: PropTypes.func.isRequired,
  fetchFraudTypesWithBuckets: PropTypes.func.isRequired,
  bucket: PropTypes.string,
  txnDetails: PropTypes.object,
  customerInfo: PropTypes.object
};

export default VerdictModal;
