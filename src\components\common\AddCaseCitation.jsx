import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, FormGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';
import ChecklistContainer from 'containers/ruleEngine/ChecklistContainer';

function AddCaseCitation({ theme, channel, caseRefNo, addCaseCitation }) {
  const [display, setDisplay] = useState(false);
  const [citationNames, setCitationNames] = useState([]);

  const onChecklistChange = (newChecklist) => {
    const citations = newChecklist?.map((item) => item.citationName);
    setCitationNames(citations);
  };

  const submitCaseCitations = () => {
    addCaseCitation(caseRefNo, citationNames);
    setDisplay(false);
  };

  return (
    <div>
      <Button outline size="sm" color="primary" onClick={() => setDisplay(true)}>
        <FontAwesomeIcon icon={faPlus} className="me-1" />
        Add Citations
      </Button>
      <ModalContainer
        size="lg"
        header="Add Case Citation"
        theme={theme}
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <ChecklistContainer channel={channel} onChecklistChange={onChecklistChange} />
        <FormGroup className="d-flex justify-content-end mt-2">
          <Button size="sm" color="primary" onClick={submitCaseCitations}>
            Submit
          </Button>
        </FormGroup>
      </ModalContainer>
    </div>
  );
}

AddCaseCitation.propTypes = {
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  addCaseCitation: PropTypes.func.isRequired
};

export default AddCaseCitation;
