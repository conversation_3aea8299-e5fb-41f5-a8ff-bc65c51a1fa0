import _ from 'lodash';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { useHistory, useParams } from 'react-router-dom';

import Log from 'containers/common/LogContainer';
import Loader from 'components/loader/Loader';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import STRReportLogContainer from 'containers/investigation/STRReportLogContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';

const AuditCaseDetails = ({
  txnDetails,
  selectCase,
  selectedCase,
  documentStatus,
  clearSelectedCase
}) => {
  const history = useHistory();
  const { txnId, channel } = useParams();
  useEffect(() => {
    if (txnId) selectCase({ txnId, channel });
  }, [txnId, channel]);

  useEffect(() => {
    selectedCase && (document.title = 'BANKiQ FRC | Audit Case - ' + selectedCase.caseRefNo);

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const cognitiveResponse = !_.isEmpty(txnDetails.details.cognitiveResponse)
    ? JSON.parse(txnDetails.details.cognitiveResponse)
    : {};
  const cognitiveViolations = _.isEmpty(cognitiveResponse.unusualMethods)
    ? []
    : cognitiveResponse.unusualMethods;

  return !selectedCase?.txnId ? (
    <Loader show={true} />
  ) : (
    <div className={'content-wrapper'}>
      <span className="d-flex justify-content-end mb-3">
        <Button
          outline
          size="sm"
          color="secondary"
          onClick={() => {
            clearSelectedCase();
            history.goBack();
          }}>
          Back
        </Button>
      </span>
      <CaseDetailCard
        caseDetails={selectedCase}
        documentStatus={documentStatus}
        channel={channel}
      />
      <TransactionDetailCardContainer channel={channel} />
      {!_.isEmpty(txnDetails.details) && (
        <ViolatedRulesCardContainer
          transactionId={selectedCase.txnId}
          txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
          reViolatedRules={txnDetails?.details?.reViolatedRules || []}
          cognitiveViolations={cognitiveViolations}
          channel={channel}
        />
      )}
      {channel === 'str' && (
        <STRReportLogContainer caseRefNo={selectedCase.caseRefNo} txnId={txnId} />
      )}
      <Log module="Case" id={selectedCase.caseRefNo} />
    </div>
  );
};

AuditCaseDetails.propTypes = {
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  documentStatus: PropTypes.object.isRequired,
  clearSelectedCase: PropTypes.func.isRequired,
  selectCase: PropTypes.func.isRequired
};

export default AuditCaseDetails;
