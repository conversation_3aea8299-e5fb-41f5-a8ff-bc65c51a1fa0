import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import ReactTable from 'react-table';
import { MultiSelect } from 'react-multi-select-component';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Row, Col, Button, FormGroup, Label, Input, FormFeedback } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const RuleSnoozeModal = ({
  theme,
  ruleCode,
  ruleName,
  snoozeRule,
  show,
  onToggle,
  channel,
  snoozeAttributes,
  fetchSnoozeAttributes,
  txnDetails
}) => {
  const [comment, setComment] = useState('');
  const [snooze, setSnooze] = useState('');
  const [conditionsList, setConditionsList] = useState([]);
  const [attribute, setAttribute] = useState([]);
  const [operator, setOperator] = useState('');
  const [value, setValue] = useState('');
  const [invalidValue, setInvalidValue] = useState(false);
  const [invalidValueMessage, setInvalidValueMessage] = useState('');
  const [duplicateCondition, setDuplicateCondition] = useState(false);
  const validDate = (current) => current.isAfter(new Date());

  useEffect(() => {
    _.isEmpty(snoozeAttributes?.list) && fetchSnoozeAttributes(channel);
  }, []);

  useEffect(() => {
    setComment('');
    setSnooze('');
    setConditionsList([]);
    setAttribute([]);
    setOperator('');
    setValue('');
    setInvalidValue(false);
    setInvalidValueMessage('');
    setDuplicateCondition(false);
  }, [ruleCode]);

  const submit = (e) => {
    e.preventDefault();
    let formData = {
      code: ruleCode,
      name: ruleName,
      reason: comment,
      merchantId: txnDetails?.details?.entityId,
      snoozeUntil: moment(snooze).format('YYYY-MM-DD HH:mm:ss'),
      snoozeConditions: conditionsList
    };
    snoozeRule(formData, channel);
    setComment('');
    setSnooze('');
    onToggle();
  };

  const pattern = {
    alphanumeric: /^[\w\-\s]+$/,
    integer: /^[+-]?[\d]+$/,
    decimal: /^[+-]?\d+(\.\d+)?$/,
    default: /^$/
  };

  const operators = [
    { key: '=', value: 'equalTo', dataType: ['text', 'int', 'numeric'] },
    { key: '>', value: 'greaterThan', dataType: ['int', 'numeric'] },
    { key: '<', value: 'lessThan', dataType: ['int', 'numeric'] },
    { key: '>=', value: 'greaterThanEqualTo', dataType: ['int', 'numeric'] },
    { key: '<=', value: 'lessThanEqualTo', dataType: ['int', 'numeric'] },
    { key: '!=', value: 'notEqualTo', dataType: ['text', 'int', 'numeric'] }
  ];

  const handleValueValidationProps = (dataType) => {
    switch (true) {
      case dataType == 'text':
        return {
          pattern: pattern.alphanumeric,
          title: 'Please enter an alphanumeric value'
        };
      case dataType == 'int':
        return {
          pattern: pattern.integer,
          title: 'Please enter an integer'
        };
      case dataType == 'numeric':
        return {
          pattern: pattern.decimal,
          title: 'Please enter a decimal'
        };
      default:
        return {
          pattern: pattern.default,
          title: ''
        };
    }
  };

  const validateValue = (dataType, value) => {
    const applicableRegex = handleValueValidationProps(dataType);
    setInvalidValue(!applicableRegex.pattern.test(value));
    setInvalidValueMessage(applicableRegex.title);
  };

  const validateDuplicateCondition = (condition) => {
    if (_.find(conditionsList, (d) => _.isEqual(d, condition)) !== undefined) {
      setDuplicateCondition(true);
      setInvalidValueMessage('Condition already exists!');
    } else {
      setDuplicateCondition(false);
    }
  };

  const findValueInTxnDetail = (key, txnDetail) => {
    if (_.isObject(txnDetail) && !_.isNil(txnDetail)) {
      return _.reduce(
        txnDetail,
        (result, value, k) => {
          if (result !== undefined) {
            return result; // Return early if key is already found
          }
          if (k === key) {
            // Return 'value' property if it exists and is an object
            return _.isObject(value) && 'value' in value ? value.value : value;
          }
          // Recursively search nested objects
          return _.isObject(value) ? findValueInTxnDetail(key, value) : undefined;
        },
        undefined
      );
    }
    return undefined; // Return undefined if the object is not valid
  };

  const handleFieldChange = (fields) => {
    const latestField = fields[fields.length - 1];
    const selectedField = snoozeAttributes.list.find((attr) => attr.value === latestField?.value);
    if (selectedField) {
      const formattedField = {
        label: selectedField.value,
        value: selectedField.value,
        dataType: selectedField.dataType
      };

      setAttribute([formattedField]);
      setOperator(operators[0].key);

      const selectedValue = findValueInTxnDetail(latestField.value, txnDetails.details) || '';

      if (selectedField.value === 'txnType' || selectedField.value === 'channelName') {
        setValue(selectedValue.toString().split(',')[0]);
      } else {
        setValue(selectedValue.toString());
      }

      validateDuplicateCondition({
        attribute: latestField.value,
        operator: operators[0].key,
        value: selectedValue.toString()
      });
    }
  };

  let operatorOptions = operators
    .filter((operator) => _.includes(operator.dataType, _.toLower(attribute[0]?.dataType)))
    .map((operator) => (
      <option key={operator.value} value={operator.key}>
        {operator.value}
      </option>
    ));

  const handleAddConditions = () => {
    let condition = { attribute: attribute[0]?.value, operator, value };
    setConditionsList((conditionsList) => [...conditionsList, condition]);
    setAttribute([]);
    setOperator('');
    setValue('');
  };

  const handleRemoveConditions = (selectedCondition) => {
    if (confirm('Are you sure you wish to remove this condition ?')) {
      const newConditionsList =
        conditionsList &&
        conditionsList.filter((condition) => !_.isEqual(condition, selectedCondition));
      setConditionsList(newConditionsList);
    }
  };

  let conditionsTableheader = [
    {
      Header: 'attribute',
      accessor: 'attribute',
      // eslint-disable-next-line react/no-multi-comp, react/prop-types
      Cell: ({ value }) => {
        return <span>{value}</span>;
      }
    },
    { Header: 'Operator', accessor: 'operator' },
    { Header: 'Values', accessor: 'value' },
    {
      Header: 'Actions',
      filterable: false,
      sortable: false,
      maxWidth: 80,
      // eslint-disable-next-line react/no-multi-comp
      Cell: (row) => (
        <Button
          size="sm"
          color="danger"
          title="Delete"
          onClick={() => handleRemoveConditions(row.original)}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    }
  ];

  const attributeOptions = snoozeAttributes?.list?.map((attribute) => ({
    label: attribute.key,
    value: attribute.value
  }));

  const itemRendererRule = ({ option, onClick }) => (
    <button
      className="d-flex align-items-center w-100 p-2 bg-transparent border-0 text-start"
      onClick={(e) => {
        e.preventDefault();
        onClick();
      }}
      aria-label={`Select ${option.label}`}>
      <span className="ms-2">{option.label}</span>
    </button>
  );

  return (
    <ModalContainer
      size="lg"
      theme={theme}
      isOpen={show}
      header={`Snooze Rule - ${ruleName}`}
      toggle={onToggle}>
      <form onSubmit={submit}>
        <FormGroup>
          <Label>Snooze till</Label>
          <Datetime
            name="snooze"
            dateFormat="YYYY-MM-DD"
            timeFormat={false}
            value={snooze}
            onChange={(dateObj) => setSnooze(dateObj._d)}
            isValidDate={validDate}
            inputProps={{ required: true }}
            closeOnSelect={true}
          />
        </FormGroup>
        <FormGroup>
          <Label>Reason</Label>
          <Input
            type="textarea"
            name="comment"
            id="comment"
            placeholder="comment"
            onChange={(e) => setComment(e.target.value)}
            value={comment}
            required
          />
        </FormGroup>
        <FormGroup>
          <Label>Conditions</Label>
          <Row>
            <Col md="4" sm="4" xs="12">
              <FormGroup>
                <Label>Select attribute </Label>
                <MultiSelect
                  options={attributeOptions}
                  labelledBy="-- select --"
                  name="attribute"
                  value={!_.isEmpty(attribute) ? attribute : []}
                  onChange={(e) => handleFieldChange(e)}
                  hasSelectAll={false}
                  overrideStrings={{
                    selectSomeItems: '-- select --'
                  }}
                  ItemRenderer={itemRendererRule}
                />
              </FormGroup>
            </Col>
            <Col md="4" sm="4" xs="12">
              <FormGroup>
                <Label>Select Operator </Label>
                <Input
                  type="select"
                  name="operator"
                  value={operator}
                  onChange={(e) => {
                    setOperator(e.target.value);
                  }}
                  disabled={_.isEmpty(attribute)}>
                  <option value="">-- Select --</option>
                  {operatorOptions}
                </Input>
              </FormGroup>
            </Col>
            <Col md="3" sm="3" xs="12">
              <FormGroup>
                <Label>Value </Label>
                <Input
                  placeholder="value"
                  type="text"
                  name="value"
                  value={value}
                  onChange={(e) => {
                    validateValue(attribute[0]?.dataType, e.target.value);
                    validateDuplicateCondition({
                      attribute: attribute[0].value,
                      operator,
                      value: e.target.value
                    });
                    setValue(e.target.value);
                  }}
                  disabled={_.isEmpty(attribute) || _.isEmpty(operator)}
                  spellCheck={false}
                  invalid={!_.isEmpty(value) && (invalidValue || duplicateCondition)}
                />
                <FormFeedback>{invalidValueMessage}</FormFeedback>
              </FormGroup>
            </Col>
            <Col md="1" sm="1" xs="12">
              <FormGroup className="d-flex justify-content-end">
                <Button
                  color="primary"
                  size="sm"
                  className="add-condition-btn"
                  disabled={
                    invalidValue ||
                    duplicateCondition ||
                    _.isEmpty(attribute) ||
                    value === '' ||
                    _.isEmpty(operator)
                  }
                  onClick={handleAddConditions}>
                  <FontAwesomeIcon icon={faPlus} />
                </Button>
              </FormGroup>
            </Col>
          </Row>
        </FormGroup>

        {!_.isEmpty(conditionsList) && (
          <ReactTable
            filterable={false}
            defaultFilterMethod={(filter, row) =>
              row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
            }
            columns={conditionsTableheader}
            data={conditionsList}
            defaultPageSize={20}
            minRows={2}
            showPaginationTop={false}
            showPaginationBottom={false}
            className={'-highlight  -striped'}
          />
        )}
        <FormGroup className="d-flex justify-content-end mt-3">
          <Button type="submit" size="sm" color="primary" disabled={_.isEmpty(conditionsList)}>
            Submit
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
};

RuleSnoozeModal.propTypes = {
  show: PropTypes.bool.isRequired,
  theme: PropTypes.string.isRequired,
  ruleCode: PropTypes.string.isRequired,
  ruleName: PropTypes.string.isRequired,
  snoozeRule: PropTypes.func.isRequired,
  onToggle: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired,
  txnDetails: PropTypes.object.isRequired,
  snoozeAttributes: PropTypes.object.isRequired,
  fetchSnoozeAttributes: PropTypes.func.isRequired
};

export default RuleSnoozeModal;
