FROM ubuntu:18.04
RUN apt-get update 
RUN apt-get install -y wget 
RUN apt-get install -y gnupg
RUN apt-get install -y gnupg1
RUN apt-get install -y gnupg2
ADD nginx.list /etc/apt/sources.list.d/
RUN wget http://nginx.org/keys/nginx_signing.key
RUN apt-key add nginx_signing.key
RUN apt-get update
RUN apt-get install nginx -y
RUN apt-get install -y nano
RUN apt-get install -y httpie
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN service nginx stop
RUN mkdir -p /var/www/ifrm-ui
RUN mkdir -p /var/www/ifrm-ui/backup
RUN mkdir -p /var/www/ifrm-ui/dist
RUN mkdir /var/log/bankiq
RUN mkdir /var/log/bankiq/ifrm
RUN mkdir /var/log/bankiq/ifrm/nginx
RUN touch /var/log/bankiq/ifrm/nginx/ifrm-access.log
RUN touch /var/log/bankiq/ifrm/nginx/ifrm-error.log
RUN rm -rf /etc/nginx/sites-available/default
RUN rm -rf /etc/nginx/sites-enabled/default
RUN rm -rf /etc/nginx/nginx.conf
ADD ifrm-ui /etc/nginx/sites-available/
#ADD ifrm-ui /etc/nginx/sites-enabled/
RUN mkdir /etc/nginx/sites-enabled/
RUN rm /etc/nginx/conf.d/default.conf
ADD dist /var/www/ifrm-ui/dist
ADD nginx.conf /etc/nginx
#ADD 10.140.139.84.cer /etc/ssl/certs/
#ADD 10.140.139.84.key /etc/ssl/private/
#RUN apt-get install -y nginx-extras
RUN ln -s /etc/nginx/sites-available/ifrm-ui /etc/nginx/sites-enabled/
RUN chown -R $USER:www-data /var/www/ifrm-ui/dist/*
CMD service status
RUN service nginx start
CMD ["nginx", "-g", "daemon off;"]

