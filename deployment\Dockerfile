FROM ubuntu:20.04
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Kolkata
RUN apt-get update && \
    apt-get install -y wget gnupg2 nano curl tzdata python3-pip && \
    pip3 install --no-cache-dir httpie && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone
RUN curl -fsSL https://nginx.org/keys/nginx_signing.key | gpg --dearmor -o /usr/share/keyrings/nginx-archive-keyring.gpg
RUN echo "deb [signed-by=/usr/share/keyrings/nginx-archive-keyring.gpg] http://nginx.org/packages/ubuntu focal nginx" \
    > /etc/apt/sources.list.d/nginx.list && \
    echo "deb-src [signed-by=/usr/share/keyrings/nginx-archive-keyring.gpg] http://nginx.org/packages/ubuntu focal nginx" \
    >> /etc/apt/sources.list.d/nginx.list
RUN apt-get update && \
    apt-get install -y nginx && \
    apt-get clean
RUN service nginx stop
RUN mkdir -p /var/www/ifrm-ui/{backup,dist} && \
    mkdir -p /var/log/bankiq/ifrm/nginx && \
    touch /var/log/bankiq/ifrm/nginx/ifrm-access.log && \
    touch /var/log/bankiq/ifrm/nginx/ifrm-error.log && \
    rm -f /etc/nginx/sites-available/default && \
    rm -f /etc/nginx/sites-enabled/default && \
    rm -f /etc/nginx/conf.d/default.conf && \
    mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
ADD ifrm-ui /etc/nginx/sites-available/
ADD nginx.conf /etc/nginx/nginx.conf
RUN ln -s /etc/nginx/sites-available/ifrm-ui /etc/nginx/sites-enabled/
ADD dist /var/www/ifrm-ui/dist
RUN chown -R www-data:www-data /var/www/ifrm-ui/dist
RUN service nginx start
CMD ["nginx", "-g", "daemon off;"]
