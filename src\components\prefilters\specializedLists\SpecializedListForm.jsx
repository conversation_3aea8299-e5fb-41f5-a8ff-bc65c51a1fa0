import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect, useCallback } from 'react';
import Datetime from 'react-datetime';
import { MultiSelect } from 'react-multi-select-component';
import { Button, FormGroup, Label, Input, TabPane } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import Tabs from 'components/common/Tabs';
import { MAX_AMOUNT } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

function SpecializedListForm({
  listType,
  data,
  toggle,
  actions,
  categoryOptions,
  partnerIdOptions,
  currentPrefilterList,
  toggleFilterModal,
  isEditmode = false
}) {
  // State initialization
  const [fileData, setFileData] = useState('');
  const [categoryName, setCategoryName] = useState('');
  const [identifier, setIdentifier] = useState('');
  const [amount, setAmount] = useState('');
  const [count, setCount] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [remark, setRemark] = useState('');
  const [partnerId, setPartnerId] = useState('');
  const [selectedPartnerIds, setSelectedPartnerIds] = useState([]);
  const [status, setStatus] = useState(false);
  const [bulkUploadLoading, setBulkUploadLoading] = useState(false);
  const [singleUploadLoading, setSingleUploadLoading] = useState(false);

  const partnerSelectOptions = partnerIdOptions.map((opt) => ({
    label: opt.partnerName,
    value: opt.id
  }));

  useEffect(() => {
    if (isEditmode) {
      setCategoryName(data?.categoryName);
      setIdentifier(data?.identifier);
      setAmount(data?.amount);
      setCount(data?.count);
      setStartDate(data?.startDate);
      setEndDate(data?.endDate);
      setRemark(data?.remark);
      const preSelected = data?.partnerId || [];
      const selected = partnerSelectOptions.filter((opt) => preSelected.includes(opt.value));
      setSelectedPartnerIds(selected);
      setPartnerId(data?.partnerId);
      setStatus(data?.isActive === 1);
    } else clearUpdatedValue();
  }, [data]);

  // Always use a fresh Moment for 'now' to avoid stale references
  const getNow = useCallback(() => Moment(), []);

  // Helper to clear form values
  const clearUpdatedValue = useCallback(() => {
    setCategoryName('');
    setIdentifier('');
    setAmount('');
    setCount('');
    setStartDate(null);
    setEndDate(null);
    setRemark('');
    setPartnerId('');
    setSelectedPartnerIds([]);
    setStatus(false);
  }, []);

  // Populate form in edit mode
  useEffect(() => {
    if (isEditmode) {
      setCategoryName(data?.categoryName || '');
      setIdentifier(data?.identifier || '');
      setAmount(data?.amount || '');
      setCount(data?.count || '');
      setStartDate(data?.startDate ? Moment(data?.startDate).format('YYYY-MM-DD HH:mm:ss') : null);
      setEndDate(data?.endDate ? Moment(data?.endDate).format('YYYY-MM-DD HH:mm:ss') : null);
      setRemark(data?.remark || '');
      setPartnerId(data?.partnerId || '');
      setStatus(data?.isActive === 1);
    } else clearUpdatedValue();
  }, [data, isEditmode, clearUpdatedValue]);

  // Date validation helpers
  // Validation: startDate must be strictly after now, endDate after startDate
  // Patch: enforce minHour in isValidDate for startDate
  // Validation: start date must be after current hour and before end date
  const validStartDate = useCallback(
    (current) => {
      const now = getNow();
      // Must be after current hour
      if (!current.isAfter(now)) return false;
      if (current.isSame(now, 'day') && current.hour() <= now.hour()) return false;
      // Must be before endDate if set
      if (endDate && !current.isBefore(Moment(endDate))) return false;
      return true;
    },
    [endDate, getNow]
  );

  // Validation: end date must be after current hour and after start date
  const validEndDate = useCallback(
    (current) => {
      const now = getNow();
      // Must be after current hour
      if (!current.isAfter(now)) return false;
      if (current.isSame(now, 'day') && current.hour() <= now.hour()) return false;
      // Must be after startDate if set
      if (startDate && !current.isAfter(Moment(startDate))) return false;
      if (
        startDate &&
        current.isSame(Moment(startDate), 'day') &&
        current.hour() <= Moment(startDate).hour()
      )
        return false;
      return true;
    },
    [startDate, getNow]
  );

  // Ensure minutes/seconds are always zero
  const normalizeDate = useCallback((dateObj) => {
    if (!dateObj) return null;
    const m = Moment(dateObj);
    // If selected date is today, set to current hour if after now, otherwise set to next valid hour
    if (Moment(dateObj).isSame(getNow(), 'day')) {
      const nowHour = getNow().hour();
      if (m.hour() <= nowHour) m.hour(nowHour + 1);
    }

    m.minutes(0);
    m.seconds(0);
    m.milliseconds(0);
    return m.toDate();
  }, []);

  // Handlers
  const handleStartDateChange = useCallback(
    (dateObj) => {
      const normalized = normalizeDate(dateObj);
      setStartDate(normalized);
      // If endDate is before new startDate, clear endDate
      if (endDate && Moment(endDate).isSameOrBefore(normalized)) setEndDate(null);
    },
    [endDate, normalizeDate]
  );

  const handleEndDateChange = useCallback(
    (dateObj) => {
      setEndDate(normalizeDate(dateObj));
    },
    [normalizeDate]
  );

  const addSingleEntity = (e) => {
    e.preventDefault();
    setSingleUploadLoading(true);
    const formData = {
      categoryName,
      identifier,
      ...(amount && { amount: parseFloat(amount) }),
      ...(count && { count: parseInt(count) }),
      ...(startDate &&
        endDate && {
          startDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss'),
          endDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss')
        }),
      ...(remark && { remark }),
      ...(isCooperative &&
        selectedPartnerIds.length > 0 && {
          partnerId: selectedPartnerIds.map((opt) => +opt.value)
        }),
      isActive: status ? 1 : 0
    };

    if (isEditmode)
      actions.onUpdateSpecializedListItem(formData, currentPrefilterList, listType, isEditmode);
    else actions.onAddSingleItemToSpecializedList(formData, currentPrefilterList, listType);

    setSingleUploadLoading(false);
    clearUpdatedValue();
  };

  const addInBulkEntity = (e) => {
    e.preventDefault();
    setBulkUploadLoading(true);
    const formData = {
      [currentPrefilterList.prefilterValue]: fileData
    };
    actions.onAddItemsInBulkToSpecializedList(formData, currentPrefilterList, listType);
    setBulkUploadLoading(false);
    setFileData('');
  };

  return (
    <ModalContainer
      size="md"
      theme={toggle.theme}
      isOpen={toggle.prefiltersListModal[listType]}
      toggle={() => toggleFilterModal('close')}
      header={`Add ${currentPrefilterList.prefilterName}`}>
      <Tabs tabNames={isEditmode ? ['Single'] : ['Single', 'Bulk']}>
        <TabPane tabId={0}>
          <form onSubmit={addSingleEntity}>
            <FormGroup>
              <Label for="categoryName">Category</Label>
              <Input
                type="select"
                id="categoryName"
                name="categoryName"
                value={categoryName}
                disabled={isEditmode}
                onChange={(event) => setCategoryName(event.target.value)}
                required>
                <option value="">-- select --</option>
                {categoryOptions}
              </Input>
            </FormGroup>
            <FormGroup>
              <Label for="identifier">Identifier</Label>
              <Input
                type="text"
                id="identifier"
                name="identifier"
                value={identifier}
                readOnly={isEditmode}
                onChange={(event) => setIdentifier(event.target.value)}
                required
              />
            </FormGroup>
            <FormGroup>
              <Label for="amount">Amount</Label>
              <Input
                type="number"
                id="amount"
                name="amount"
                value={amount}
                onChange={(event) => setAmount(event.target.value)}
                min={0}
                max={MAX_AMOUNT}
                step={0.01}
                pattern="^[0-9]*\.[0-9]{0,2}$"
              />
            </FormGroup>
            <FormGroup>
              <Label for="count">Count</Label>
              <Input
                type="number"
                id="count"
                name="count"
                value={count}
                onChange={(event) => setCount(event.target.value)}
              />
            </FormGroup>
            <FormGroup>
              <Label>Start Date</Label>
              <Datetime
                name="startDate"
                dateFormat="YYYY-MM-DD"
                timeFormat="HH:00:00"
                value={startDate}
                onChange={handleStartDateChange}
                // eslint-disable-next-line react/prop-types
                renderInput={(props) => <input {...props} value={startDate ? props.value : ''} />}
                isValidDate={validStartDate}
                initialViewDate={startDate ? Moment(startDate) : getNow().add(1, 'hour')}
              />
            </FormGroup>
            <FormGroup>
              <Label>End Date</Label>
              <Datetime
                name="endDate"
                dateFormat="YYYY-MM-DD"
                timeFormat="HH:00:00"
                value={endDate}
                onChange={handleEndDateChange}
                // eslint-disable-next-line react/prop-types
                renderInput={(props) => <input {...props} value={endDate ? props.value : ''} />}
                isValidDate={validEndDate}
                initialViewDate={(() => {
                  if (startDate) return Moment(startDate).add(1, 'hour');
                  else if (endDate) return Moment(endDate);
                  else return getNow().add(2, 'hour');
                })()}
              />
            </FormGroup>
            <FormGroup>
              <Label for="remark">Remark</Label>
              <Input
                type="text"
                id="remark"
                name="remark"
                value={remark}
                onChange={(event) => setRemark(event.target.value)}
              />
            </FormGroup>
            {isCooperative && (
              <FormGroup>
                <Label>Select Partner</Label>
                <MultiSelect
                  options={partnerSelectOptions}
                  value={selectedPartnerIds}
                  onChange={(selected) => {
                    // Only allow one selection at a time
                    setSelectedPartnerIds(
                      selected.length > 0 ? [selected[selected.length - 1]] : []
                    );
                  }}
                  hasSelectAll={false}
                  disableSearch={false}
                  overrideStrings={{
                    selectSomeItems: '-- Select --',
                    search: 'Search...',
                    allItemsAreSelected: 'All partners selected',
                    noOptions: 'No partners available',
                    selectAll: 'Select All',
                    clearSearch: '×'
                  }}
                  disabled={isEditmode}
                />
              </FormGroup>
            )}
            <FormGroup className="ms-4">
              <Label for="isActive">
                <Input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  value={status}
                  checked={status}
                  onChange={() => setStatus(!status)}
                />{' '}
                Activate
              </Label>
            </FormGroup>
            <FormGroup className="d-flex justify-content-end">
              <Button size="sm" type="submit" color="success" disabled={singleUploadLoading}>
                {singleUploadLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className="loader fa-spin" />
                ) : (
                  'Save'
                )}
              </Button>
            </FormGroup>
          </form>
        </TabPane>
        <TabPane tabId={1}>
          <form onSubmit={addInBulkEntity}>
            <FormGroup>
              <Input
                name="fileUpload"
                accept="text/csv, .csv"
                type="file"
                files={fileData}
                onChange={(event) => setFileData(event.target.files[0])}
                required
              />
            </FormGroup>
            <FormGroup className="d-flex justify-content-end">
              <Button size="sm" type="submit" color="success" disabled={toggle.uploadLoader}>
                {bulkUploadLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className="loader fa-spin" />
                ) : (
                  'Upload'
                )}
              </Button>
            </FormGroup>
          </form>
        </TabPane>
      </Tabs>
    </ModalContainer>
  );
}

SpecializedListForm.propTypes = {
  isEditmode: PropTypes.bool,
  listType: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  toggle: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  categoryOptions: PropTypes.array.isRequired,
  partnerIdOptions: PropTypes.array.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  toggleFilterModal: PropTypes.func.isRequired
};

export default SpecializedListForm;
