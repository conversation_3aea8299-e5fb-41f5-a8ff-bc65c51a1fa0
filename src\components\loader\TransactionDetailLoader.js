import React from 'react';
import { Row, Col } from 'reactstrap';
import { range } from 'lodash';

const TransactionDetailLoader = () => {
  return (
    <Row>
      <Col lg="3" md="4" sm="12" xs="12">
        <Row className="txn-info">
          {range(6).map((d) => (
            <Col key={d} md="6" sm="6" xs="6">
              <span className="violation-badge-placeholder">{''}</span>
            </Col>
          ))}
        </Row>
      </Col>
      <Col lg="9" md="8" sm="12" xs="12">
        <Row className="txn-info">
          {range(12).map((d) => (
            <Col key={d} md="3" sm="4" xs="6" className="data-columns p-3">
              <span className="detail-placeholder">{''}</span>
            </Col>
          ))}
        </Row>
      </Col>
    </Row>
  );
};

export default TransactionDetailLoader;
