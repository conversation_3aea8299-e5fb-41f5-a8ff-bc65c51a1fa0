import _ from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_SUCCESSFUL_ADD_USER,
  ON_SUCCESSFUL_ADD_SHIFT,
  ON_SUCCESSFUL_FETCH_ROLES,
  ON_SUCCESSFUL_FETCH_SHIFTS,
  ON_SUCCESSFUL_FETCH_STAGES,
  ON_SUCCESSFUL_FETCH_CHANNELS,
  ON_SUCCESSFUL_USER_LIST_FETCH,
  ON_SUCCESSFUL_UPDATE_USER_ROLES,
  ON_SUCCESSFUL_USER_ASSIGN_SHIFTS,
  ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST,
  ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS,
  ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS,
  ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE,
  ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS,
  ON_USER_TOGGLE_AUTO_CASE_SUCCESS,
  ON_FETCH_CONFIGURATIONS_SUCCESS,
  ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS,
  ON_SUCCESSFUL_FETCH_ADMIN_LIST,
  ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH
} from 'constants/actionTypes';

import initialState from './initialState';

const getRoles = (list) => {
  const userslist = list.map((user) => {
    const rolesChannelTuples = user.channelRoles.map((value) => {
      if (value === 'admin') return 'admin';
      const arr = _.split(value, ':');
      return arr;
    });

    const unzipTuples = _.unzip(rolesChannelTuples);

    return objectAssign({}, user, {
      roles: _.uniq(unzipTuples[1]),
      channels: _.uniq(unzipTuples[0])
    });
  });

  const hasMakerChecker = _.map(userslist, (user) => user.roles.includes('maker')).length > 0;

  return { userslist, hasMakerChecker };
};

const updateUserRoles = (userlist, newData) => {
  const updatedList = userlist.map((user) =>
    user.id === newData.userId
      ? objectAssign({}, user, {
          channelRoles: newData.channelRoles
        })
      : user
  );
  return getRoles(updatedList);
};

const updateUserShifts = (userslist, userShifts) =>
  userslist.map((user) =>
    user.id === userShifts.userId ? objectAssign({}, user, { shiftNames: userShifts.shifts }) : user
  );

export default function userReducer(state = initialState.user, action) {
  switch (action.type) {
    case ON_SUCCESSFUL_USER_LIST_FETCH:
      return objectAssign({}, state, getRoles(action.response));
    case ON_SUCCESSFUL_UNAPPROVED_USER_LIST_FETCH:
      return objectAssign({}, state, {
        unapprovedUserslist: action.response
      });
    case ON_SUCCESSFUL_FETCH_ROLES:
      return objectAssign({}, state, {
        roles: action.response
      });
    case ON_SUCCESSFUL_FETCH_SHIFTS:
      return objectAssign({}, state, {
        shifts: action.response
      });
    case ON_SUCCESSFUL_FETCH_CHANNELS:
      return objectAssign({}, state, {
        channels: action.response
      });
    case ON_SUCCESSFUL_ADD_USER:
      return objectAssign({}, state, getRoles([...state.userslist, action.user]));
    case ON_SUCCESSFUL_UPDATE_USER_ROLES:
      return objectAssign({}, state, updateUserRoles(state.userslist, action.user));
    case ON_SUCCESSFUL_ADD_SHIFT:
      return objectAssign({}, state, {
        shifts: [...state.shifts, action.shift]
      });
    case ON_SUCCESSFUL_USER_ASSIGN_SHIFTS:
      return objectAssign({}, state, {
        userslist: updateUserShifts(state.userslist, action.userShifts)
      });
    case ON_SUCCESSFUL_FETCH_PARTNER_ID_LIST:
      return objectAssign({}, state, {
        partnerIdList: action.response
      });
    case ON_SUCCESSFUL_FETCH_ADMIN_LIST:
      return objectAssign({}, state, {
        adminlist: action.response
      });
    case ON_FETCH_EXTERNAL_CHECKER_LIST_SUCCESS:
      return objectAssign({}, state, {
        externalCheckers: {
          list: action.response,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_EXTERNAL_CHECKER_LIST_FAILURE:
      return objectAssign({}, state, {
        externalCheckers: {
          list: [],
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_SUCCESSFUL_FETCH_STAGES:
      return objectAssign({}, state, {
        stages: action.response
      });
    case ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_LOADING:
      return objectAssign({}, state, {
        attributesList: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_SUCCESS:
      return objectAssign({}, state, {
        attributesList: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CASE_CRITERIA_ATTRIBUTE_LIST_FAILURE:
      return objectAssign({}, state, {
        attributesList: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_USER_ASSIGN_CASE_CRITERIA_SUCCESS:
      return objectAssign({}, state, {
        userslist: _.map(state.userslist, (user) =>
          user.id === action.response.userId
            ? { ...user, caseCriteriaInfo: action.response.caseCriteriaInfo }
            : user
        )
      });
    case ON_USER_TOGGLE_AUTO_CASE_SUCCESS:
      return objectAssign({}, state, {
        userslist: _.map(state.userslist, (user) =>
          user.id === action.response.userId
            ? { ...user, pauseAutoCase: action.response.pauseAutoCase }
            : user
        )
      });
    case ON_FETCH_CONFIGURATIONS_SUCCESS:
      return objectAssign({}, state, {
        configurations: objectAssign({}, state.configurations, action.response)
      });
    case ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS:
      return objectAssign({}, state, {
        provisionalFields: action.response?.provisionalFields || []
      });
    default:
      return state;
  }
}
