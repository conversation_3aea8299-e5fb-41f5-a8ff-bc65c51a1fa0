import isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import React, { useState, useCallback } from 'react';
import { FormGroup, Label, Input, Col, Button } from 'reactstrap';

import HelpIcon from 'components/common/HelpIcon';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';
import { MAX_AMOUNT } from 'constants/applicationConstants';

function CPContributingFactors({
  highlightText,
  saveConfigurations,
  configurationsData,
  toggleRow,
  openRows,
  recalculatePropensityScores
}) {
  const [cpContributingFactors, setCPContributingFactors] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        cpContributingFactors,
        setCPContributingFactors,
        saveConfigurations
      ),
    [configurationsData, cpContributingFactors, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, cpContributingFactors, setCPContributingFactors),
    [cpContributingFactors]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setCPContributingFactors),
    [configurationsData]
  );

  const handleRecalculatePropensityScore = () => {
    recalculatePropensityScores();
  };

  return (
    <ConfigFormWrapper
      configTitle="CP Contributing Factors"
      data={cpContributingFactors}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      highlightText={highlightText}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="recency" className="searchable">
          {highlightText(`Recency: `)}
          <span className="sub-label">{highlightText(`Last`)}</span>
          {!isEmpty(cpContributingFactors?.recency?.desc) && (
            <HelpIcon size="lg" id="recency" text={cpContributingFactors?.recency?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="recency"
            id="recency"
            onChange={(event) =>
              inputChangeDataHandler(event, cpContributingFactors, setCPContributingFactors)
            }
            value={cpContributingFactors?.recency?.value}
          />
        </Col>
        <Label sm={2} for="recency" className="searchable">
          {highlightText('Transactions')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="frequency" className="searchable">
          {highlightText(`Frequency: `)}
          <span className="sub-label">{highlightText(`Last`)}</span>
          {!isEmpty(cpContributingFactors?.frequency?.desc) && (
            <HelpIcon size="lg" id="frequency" text={cpContributingFactors?.frequency?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="frequency"
            id="frequency"
            onChange={(event) =>
              inputChangeDataHandler(event, cpContributingFactors, setCPContributingFactors)
            }
            max={12}
            min={1}
            value={cpContributingFactors?.frequency?.value}
          />
        </Col>
        <Label sm={2} for="frequency" className="searchable">
          {highlightText('Months')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="monetaryValue" className="searchable">
          {highlightText(`Monetary: `)}
          <span className="sub-label">{highlightText(`Amount ≥`)}</span>
          {!isEmpty(cpContributingFactors?.monetaryValue?.desc) && (
            <HelpIcon size="lg" id="monetary" text={cpContributingFactors?.monetaryValue?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="monetaryValue"
            id="monetaryValue"
            onChange={(event) =>
              inputChangeDataHandler(event, cpContributingFactors, setCPContributingFactors)
            }
            value={cpContributingFactors?.monetaryValue?.value}
            min={1}
            max={MAX_AMOUNT}
          />
        </Col>
      </FormGroup>
      <FormGroup row>
        <Col sm={6} md={5} lg={4} className="mt-4">
          <Button
            size="sm"
            color="primary"
            title="Recalculate Propensity Scores"
            className="me-2"
            onClick={handleRecalculatePropensityScore}>
            Recalculate Propensity Scores
          </Button>
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

CPContributingFactors.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired,
  recalculatePropensityScores: PropTypes.func.isRequired
};

export default CPContributingFactors;
