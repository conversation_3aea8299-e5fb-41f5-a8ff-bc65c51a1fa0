/* eslint-disable jsx-a11y/no-onchange */
'use strict';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Input, Card, CardBody, CardTitle, Label, FormGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

const CognitiveStatusCard = ({ cognitiveStatusAction, channel, cognitiveStatus }) => {
  useEffect(() => {
    cognitiveStatusAction.onFetchCognitiveStatus(channel);
  }, []);

  const toggleCognitiveStatus = (currentStatus) => {
    cognitiveStatusAction.onToggleCognitiveStatus(channel, currentStatus);
  };

  return (
    <Card className={'d-flex card-main cognitive-status-card'}>
      {cognitiveStatus.loader ? (
        <FontAwesomeIcon icon={faSpinner} className={'loader fa-spin'} />
      ) : cognitiveStatus.error ? (
        <div className="no-data-div">{cognitiveStatus.errorMessage}</div>
      ) : (
        <CardBody className="p-3 card-container">
          <CardTitle className="mb-0">
            <FormGroup switch>
              <Label className="mb-0 ms-2" for={`${channel}-cognitiveStatus`}>
                <Input
                  type="switch"
                  role="switch"
                  id={`${channel}-cognitiveStatus`}
                  name="cognitiveStatus[]"
                  value={'cognitiveStatus'}
                  checked={cognitiveStatus.list[channel]}
                  onChange={() => toggleCognitiveStatus(cognitiveStatus.list[channel])}
                />
                {cognitiveStatus.list[channel]
                  ? 'Deactivate Cognitive System'
                  : 'Activate Cognitive System'}
              </Label>
            </FormGroup>
          </CardTitle>
        </CardBody>
      )}
    </Card>
  );
};

CognitiveStatusCard.propTypes = {
  channel: PropTypes.string.isRequired,
  cognitiveStatus: PropTypes.object.isRequired,
  cognitiveStatusAction: PropTypes.object.isRequired
};

export default CognitiveStatusCard;
