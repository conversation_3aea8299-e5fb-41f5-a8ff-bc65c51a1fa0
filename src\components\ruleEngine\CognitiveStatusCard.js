'use strict';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Input, Card, CardBody, CardTitle, Label, FormGroup } from 'reactstrap';

const CognitiveStatusCard = ({ cognitiveStatusAction, channel, cognitiveStatus }) => {
  useEffect(() => {
    cognitiveStatusAction.onFetchCognitiveStatus(channel);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const toggleCognitiveStatus = (currentStatus) => {
    cognitiveStatusAction.onToggleCognitiveStatus(channel, currentStatus);
  };

  const renderCardContent = () => {
    if (cognitiveStatus.loader)
      return <FontAwesomeIcon icon={faSpinner} className="loader fa-spin" />;

    if (cognitiveStatus.error)
      return <div className="no-data-div">{cognitiveStatus.errorMessage}</div>;

    return (
      <CardBody className="p-3 card-container">
        <CardTitle className="mb-0">
          <FormGroup switch>
            <Label className="mb-0 ms-2" for={`${channel}-cognitiveStatus`}>
              <Input
                type="switch"
                role="switch"
                id={`${channel}-cognitiveStatus`}
                name="cognitiveStatus[]"
                value="cognitiveStatus"
                checked={cognitiveStatus.list[channel]}
                onChange={() => toggleCognitiveStatus(cognitiveStatus.list[channel])}
              />
              {cognitiveStatus.list[channel]
                ? 'Deactivate Cognitive System'
                : 'Activate Cognitive System'}
            </Label>
          </FormGroup>
        </CardTitle>
      </CardBody>
    );
  };

  return <Card className="d-flex card-main cognitive-status-card">{renderCardContent()}</Card>;
};

CognitiveStatusCard.propTypes = {
  channel: PropTypes.string.isRequired,
  cognitiveStatus: PropTypes.object.isRequired,
  cognitiveStatusAction: PropTypes.object.isRequired
};

export default CognitiveStatusCard;
