import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import { onToggleLoader } from 'actions/toggleActions';
import {
  ON_FETCH_SETTINGS_LOADING,
  ON_FETCH_SETTINGS_SUCCESS,
  ON_FETCH_SETTINGS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function submitSettings(settings) {
  return client({
    method: 'POST',
    url: `casereview/str/filing/dictionary/update`,
    data: settings,
    badRequestMessage: 'Invalid settings. Please check input data'
  });
}

function onSubmitSettings(settings) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitSettings(settings)
      .then(
        () => {
          dispatch(onFetchSettings());
          dispatch(onShowSuccessAlert({ message: 'Settings updated successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchSettings() {
  return client({ url: `casereview/str/filing/fetch` });
}

function onFetchSettingsLoading() {
  return { type: ON_FETCH_SETTINGS_LOADING };
}

function onFetchSettingsSuccess(response) {
  return { type: ON_FETCH_SETTINGS_SUCCESS, response };
}

function onFetchSettingsFailure(response) {
  return { type: ON_FETCH_SETTINGS_FAILURE, response };
}

function onFetchSettings() {
  return function (dispatch) {
    dispatch(onFetchSettingsLoading());
    return fetchSettings().then(
      (success) => dispatch(onFetchSettingsSuccess(success)),
      (error) => dispatch(onFetchSettingsFailure(error))
    );
  };
}

export { onSubmitSettings, onFetchSettings };
