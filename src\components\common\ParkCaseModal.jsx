import React, { useState } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import { Button, FormGroup, Label, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolder } from '@fortawesome/free-solid-svg-icons';

import ModalContainer from 'components/common/ModalContainer';

const ParkCaseModal = ({ theme, caseId, stageId, selectedCase, parkCase, channel }) => {
  const [comment, setComment] = useState('');
  const [snooze, setSnooze] = useState(moment().endOf('day'));
  const [display, setDisplay] = useState(false);

  const isDateValid = (current) => {
    const yesterday = moment().subtract(1, 'day');
    return current.isAfter(yesterday, 'day');
  };

  const isFutureDateTime = (dateTime) => {
    const now = moment();
    return moment(dateTime).isAfter(now);
  };

  const submit = (e) => {
    e.preventDefault();

    const formData = {
      stageId,
      reason: comment,
      caseRefNo: caseId,
      ...(moment(snooze).isValid() && {
        snoozeUntil: moment(snooze).format('YYYY-MM-DDTHH:mm:ssZ')
      })
    };
    parkCase(formData, selectedCase, channel);

    setDisplay(false);
    setComment('');
    setSnooze('');
  };

  return (
    <>
      <Button outline className="ms-1" color="warning" size="sm" onClick={() => setDisplay(true)}>
        <FontAwesomeIcon icon={faFolder} /> {' Park Cases'}
      </Button>
      <ModalContainer
        size="sm"
        theme={theme}
        header="Park Case"
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form onSubmit={submit}>
          <FormGroup>
            <Label>Reason</Label>
            <Input
              type="textarea"
              name="comment"
              id="comment"
              placeholder="comment"
              onChange={(e) => setComment(e.target.value)}
              value={comment}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label>Park till</Label>
            <Datetime
              name="snooze"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={snooze}
              onChange={(dateObj) => setSnooze(dateObj._d)}
              isValidDate={isDateValid}
              closeOnSelect={true}
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button disabled={!isFutureDateTime(snooze)} type="submit" size="sm" color="primary">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

ParkCaseModal.propTypes = {
  theme: PropTypes.string.isRequired,
  caseId: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  stageId: PropTypes.number.isRequired,
  parkCase: PropTypes.func.isRequired,
  selectedCase: PropTypes.object.isRequired
};

export default ParkCaseModal;
