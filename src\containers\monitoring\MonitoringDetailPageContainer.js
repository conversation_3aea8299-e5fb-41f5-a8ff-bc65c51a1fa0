import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchUsersList } from 'actions/userManagementActions';
import { onCreateCaseAndAssign } from 'actions/caseAssignmentActions';
import * as transactionDetailsActions from 'actions/transactionDetailsActions';
import MonitoringDetailPage from 'components/monitoring/MonitoringDetailPage';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    authDetails: state.auth.userCreds,
    userslist: state.user.userslist,
    selectedTxn: state.transactionDetails
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchUsersList: bindActionCreators(onFetchUsersList, dispatch),
    createCaseAndAssign: bindActionCreators(onCreateCaseAndAssign, dispatch),
    transactionDetailsActions: bindActionCreators(transactionDetailsActions, dispatch)
  };
};

const MonitoringDetailPageContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(MonitoringDetailPage);

export default MonitoringDetailPageContainer;
