import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import ruleDashboardReducer from 'reducers/ruleDashboardReducer';

describe('ruleDashboard Reducer', () => {
  it('should return the intial state', () => {
    expect(ruleDashboardReducer(undefined, {})).toEqual(initialState.ruleDashboard);
  });

  it('should handle ON_FETCH_RULE_STATS_LOADING', () => {
    expect(
      ruleDashboardReducer(
        {
          stats: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_STATS_LOADING
        }
      )
    ).toEqual({
      stats: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_STATS_SUCCESS', () => {
    expect(
      ruleDashboardReducer(
        {
          stats: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_STATS_SUCCESS,
          response: responses.ruleDashboard.stats
        }
      )
    ).toEqual({
      stats: {
        data: responses.ruleDashboard.stats,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_STATS_FAILURE', () => {
    expect(
      ruleDashboardReducer(
        {
          stats: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_STATS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      stats: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICACY_LOADING', () => {
    expect(
      ruleDashboardReducer(
        {
          efficacy: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICACY_LOADING
        }
      )
    ).toEqual({
      efficacy: {
        data: 0,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICACY_SUCCESS', () => {
    expect(
      ruleDashboardReducer(
        {
          efficacy: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICACY_SUCCESS,
          response: responses.ruleDashboard.efficacy
        }
      )
    ).toEqual({
      efficacy: {
        data: responses.ruleDashboard.efficacy.ruleEfficacy,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICACY_FAILURE', () => {
    expect(
      ruleDashboardReducer(
        {
          efficacy: {
            data: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICACY_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      efficacy: {
        data: 0,
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_RULE_BEHAVIOUR_LOADING', () => {
    expect(
      ruleDashboardReducer(
        {
          behaviour: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_BEHAVIOUR_LOADING
        }
      )
    ).toEqual({
      behaviour: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_BEHAVIOUR_SUCCESS', () => {
    expect(
      ruleDashboardReducer(
        {
          behaviour: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_BEHAVIOUR_SUCCESS,
          response: responses.ruleDashboard.behaviour
        }
      )
    ).toEqual({
      behaviour: {
        data: responses.ruleDashboard.behaviour,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_BEHAVIOUR_FAILURE', () => {
    expect(
      ruleDashboardReducer(
        {
          behaviour: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_BEHAVIOUR_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      behaviour: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICIENCY_LOADING', () => {
    expect(
      ruleDashboardReducer(
        {
          efficiency: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICIENCY_LOADING
        }
      )
    ).toEqual({
      efficiency: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICIENCY_SUCCESS', () => {
    expect(
      ruleDashboardReducer(
        {
          efficiency: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICIENCY_SUCCESS,
          response: responses.ruleDashboard.efficiency
        }
      )
    ).toEqual({
      efficiency: {
        data: responses.ruleDashboard.efficiency.ruleEfficiency,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFICIENCY_FAILURE', () => {
    expect(
      ruleDashboardReducer(
        {
          efficiency: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFICIENCY_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      efficiency: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFECTIVENESS_LOADING', () => {
    expect(
      ruleDashboardReducer(
        {
          effectiveness: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFECTIVENESS_LOADING
        }
      )
    ).toEqual({
      effectiveness: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFECTIVENESS_SUCCESS', () => {
    expect(
      ruleDashboardReducer(
        {
          effectiveness: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFECTIVENESS_SUCCESS,
          response: responses.ruleDashboard.effectiveness
        }
      )
    ).toEqual({
      effectiveness: {
        data: responses.ruleDashboard.effectiveness.alertRates,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_EFFECTIVENESS_FAILURE', () => {
    expect(
      ruleDashboardReducer(
        {
          effectiveness: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_EFFECTIVENESS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      effectiveness: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
