import profilingReducer from 'reducers/profilingReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import responses from 'mocks/responses';

describe('Profiling reducer', () => {
  it('should return the intial state', () => {
    expect(profilingReducer(undefined, {})).toEqual(initialState.profiling);
  });

  it('should handle ON_SELECT_ENTITY', () => {
    const entity = {
      id: 123,
      name: 'neha last-name',
      entityCategory: 'customer',
      mobileNumber: '**********',
      accountNumber: '*************'
    };

    expect(
      profilingReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: '',
          selected: {}
        },
        {
          type: types.ON_SELECT_ENTITY,
          entity
        }
      )
    ).toEqual({
      list: [],
      loader: false,
      error: false,
      errorMessage: '',
      selected: entity
    });
  });

  it('should handle ON_FETCH_ENTITY_LIST_LOADING', () => {
    expect(
      profilingReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: '',
          selected: {}
        },
        {
          type: types.ON_FETCH_ENTITY_LIST_LOADING
        }
      )
    ).toEqual({
      list: [],
      loader: true,
      error: false,
      errorMessage: '',
      selected: {}
    });
  });

  it('should handle ON_FETCH_ENTITY_LIST_SUCCESS', () => {
    expect(
      profilingReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: '',
          selected: {}
        },
        {
          type: types.ON_FETCH_ENTITY_LIST_SUCCESS,
          response: responses.uds.profile
        }
      )
    ).toEqual({
      list: responses.uds.profile,
      loader: false,
      error: false,
      errorMessage: '',
      selected: {}
    });
  });

  it('should handle ON_FETCH_ENTITY_LIST_FAILURE', () => {
    expect(
      profilingReducer(
        {
          list: [],
          loader: false,
          error: false,
          errorMessage: '',
          selected: {}
        },
        {
          type: types.ON_FETCH_ENTITY_LIST_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      list: [],
      loader: false,
      error: true,
      errorMessage: 'error msg',
      selected: {}
    });
  });
});
