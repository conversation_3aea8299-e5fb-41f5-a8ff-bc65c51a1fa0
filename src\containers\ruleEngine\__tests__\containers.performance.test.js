import { render } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { createStore, combineReducers } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import ProductionRuleTableContainer from '../ProductionRuleTableContainer';
import RuleBuilderContainer from '../RuleBuilderContainer';
import RuleEngineContainer from '../RuleEngineContainer';
import RuleFormContainer from '../RuleFormContainer';

// Mock child components
jest.mock('components/ruleEngine/RuleEngine', () =>
  jest.fn((props) => <div data-testid="rule-engine">RuleEngine - {JSON.stringify(props)}</div>)
);

jest.mock('components/ruleEngine/RuleForm', () =>
  jest.fn((props) => (
    <div data-testid="rule-form">RuleForm - {Object.keys(props).length} props</div>
  ))
);

jest.mock('components/ruleEngine/RuleBuilder', () =>
  jest.fn((props) => (
    <div data-testid="rule-builder">RuleBuilder - {Object.keys(props).length} props</div>
  ))
);

jest.mock('components/ruleEngine/ProductionRuleTable', () =>
  jest.fn((props) => (
    <div data-testid="production-table">ProductionTable - {Object.keys(props).length} props</div>
  ))
);

describe('Container Performance Tests', () => {
  let store;

  beforeEach(() => {
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();

    // Create comprehensive mock store
    const initialState = {
      auth: {
        moduleType: 'acquirer',
        userCreds: {
          roles: 'supervisor',
          channels: ['frm', 'str']
        }
      },
      user: {
        hasMakerChecker: true,
        configurations: {
          sandbox: 1,
          cognitive: 1,
          acquirerPortals: 1
        }
      },
      toggle: {
        theme: 'light',
        ruleDuplicateModal: { frm: false }
      },
      ruleCreation: {
        loader: false,
        error: false,
        actionList: Array.from({ length: 50 }, (_, i) => ({
          actionCode: `${i + 1}`.padStart(2, '0'),
          actionName: `ACTION_${i + 1}`
        })),
        alertCategories: Array.from({ length: 20 }, (_, i) => ({
          id: i + 1,
          categoryName: `Category ${i + 1}`
        })),
        ruleChannels: [{ name: 'FRM' }, { name: 'STR' }],
        fraudCategories: Array.from({ length: 30 }, (_, i) => ({
          id: i + 1,
          name: `Fraud Category ${i + 1}`
        })),
        ruleLabels: Array.from({ length: 40 }, (_, i) => ({
          id: i + 1,
          name: `Label ${i + 1}`
        })),
        validation: { status: true },
        helperList: {
          frm: {
            prefix: Array.from({ length: 100 }, (_, i) => ({ name: `PREFIX_${i}` })),
            functions: Array.from({ length: 75 }, (_, i) => ({ name: `FUNCTION_${i}` }))
          }
        },
        nonProductionRules: {
          list: {
            frm: Array.from({ length: 50 }, (_, i) => ({
              code: `NPR_${i}`,
              name: `Non-Production Rule ${i}`
            }))
          }
        }
      },
      ruleConfigurator: {
        productionRules: {
          list: {
            frm: Array.from({ length: 200 }, (_, i) => ({
              code: `PROD_${i}`,
              name: `Production Rule ${i}`,
              order: i + 1
            }))
          }
        }
      },
      prefiltersList: {
        allLists: {
          data: Array.from({ length: 25 }, (_, i) => ({
            categoryName: `Category ${i}`,
            listName: `List ${i}`
          }))
        }
      },
      snoozeRules: {
        list: {
          frm: Array.from({ length: 15 }, (_, i) => ({
            ruleCode: `SNOOZE_${i}`
          }))
        }
      },
      sandboxing: {
        testHistory: Array.from({ length: 35 }, (_, i) => ({
          testId: `test_${i}`
        }))
      }
    };

    const rootReducer = combineReducers({
      auth: (state = initialState.auth) => state,
      user: (state = initialState.user) => state,
      toggle: (state = initialState.toggle) => state,
      ruleCreation: (state = initialState.ruleCreation) => state,
      ruleConfigurator: (state = initialState.ruleConfigurator) => state,
      prefiltersList: (state = initialState.prefiltersList) => state,
      snoozeRules: (state = initialState.snoozeRules) => state,
      sandboxing: (state = initialState.sandboxing) => state
    });

    store = createStore(rootReducer);
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const renderWithStore = (component) => render(<Provider store={store}>{component}</Provider>);

  describe('RuleEngineContainer Performance', () => {
    it('should connect to store efficiently', () => {
      performanceMonitor.startTiming('rule_engine_container_connect');

      renderWithStore(<RuleEngineContainer />);

      const duration = performanceMonitor.endTiming('rule_engine_container_connect');
      expect(duration).toBeLessThan(50);
    });

    it('should handle store updates efficiently', () => {
      const { rerender } = renderWithStore(<RuleEngineContainer />);

      // Update store state
      store.dispatch({ type: 'UPDATE_THEME', payload: 'dark' });

      performanceMonitor.startTiming('rule_engine_container_update');

      rerender(
        <Provider store={store}>
          <RuleEngineContainer />
        </Provider>
      );

      const duration = performanceMonitor.endTiming('rule_engine_container_update');
      expect(duration).toBeLessThan(30);
    });
  });

  describe('RuleFormContainer Performance', () => {
    it('should handle large state efficiently', () => {
      performanceMonitor.startTiming('rule_form_container_large_state');

      renderWithStore(<RuleFormContainer channel="frm" formName="create" />);

      const duration = performanceMonitor.endTiming('rule_form_container_large_state');
      expect(duration).toBeLessThan(100);
    });

    it('should use memoized selectors efficiently', () => {
      const { rerender } = renderWithStore(<RuleFormContainer channel="frm" formName="create" />);

      performanceMonitor.startTiming('rule_form_container_memoized_selectors');

      // Re-render with same props (should use memoized selectors)
      rerender(
        <Provider store={store}>
          <RuleFormContainer channel="frm" formName="create" />
        </Provider>
      );

      const duration = performanceMonitor.endTiming('rule_form_container_memoized_selectors');
      expect(duration).toBeLessThan(25);
    });
  });

  describe('RuleBuilderContainer Performance', () => {
    it('should handle complex helper lists efficiently', () => {
      performanceMonitor.startTiming('rule_builder_container_complex_helpers');

      renderWithStore(<RuleBuilderContainer channel="frm" />);

      const duration = performanceMonitor.endTiming('rule_builder_container_complex_helpers');
      expect(duration).toBeLessThan(80);
    });

    it('should optimize selector performance', () => {
      const { rerender } = renderWithStore(<RuleBuilderContainer channel="frm" />);

      performanceMonitor.startTiming('rule_builder_container_selector_optimization');

      // Multiple re-renders should be optimized by selectors
      for (let i = 0; i < 5; i++)
        rerender(
          <Provider store={store}>
            <RuleBuilderContainer channel="frm" />
          </Provider>
        );

      const duration = performanceMonitor.endTiming('rule_builder_container_selector_optimization');
      expect(duration).toBeLessThan(60);
    });
  });

  describe('ProductionRuleTableContainer Performance', () => {
    it('should handle large rule datasets efficiently', () => {
      performanceMonitor.startTiming('production_table_container_large_dataset');

      renderWithStore(<ProductionRuleTableContainer channel="frm" />);

      const duration = performanceMonitor.endTiming('production_table_container_large_dataset');
      expect(duration).toBeLessThan(120);
    });

    it('should optimize Redux connections', () => {
      const { rerender } = renderWithStore(<ProductionRuleTableContainer channel="frm" />);

      // Update unrelated state
      store.dispatch({ type: 'UPDATE_UNRELATED', payload: 'test' });

      performanceMonitor.startTiming('production_table_container_redux_optimization');

      rerender(
        <Provider store={store}>
          <ProductionRuleTableContainer channel="frm" />
        </Provider>
      );

      const duration = performanceMonitor.endTiming(
        'production_table_container_redux_optimization'
      );
      expect(duration).toBeLessThan(20); // Should be very fast due to selector optimization
    });
  });

  describe('Memory Management in Containers', () => {
    it('should not leak memory during container lifecycle', () => {
      performanceMonitor.takeMemorySnapshot('before_container_lifecycle');

      const containers = [
        <RuleEngineContainer key="engine" />,
        <RuleFormContainer key="form" channel="frm" formName="create" />,
        <RuleBuilderContainer key="builder" channel="frm" />,
        <ProductionRuleTableContainer key="table" channel="frm" />
      ];

      const renders = containers.map((container) => renderWithStore(container));

      performanceMonitor.takeMemorySnapshot('after_container_render');

      // Unmount all containers
      renders.forEach(({ unmount }) => unmount());

      performanceMonitor.takeMemorySnapshot('after_container_unmount');
      performanceMonitor.checkMemoryLeaks();

      expect(true).toBe(true);
    });
  });

  describe('Selector Performance', () => {
    it('should use memoized selectors across containers', () => {
      performanceMonitor.startTiming('memoized_selectors_across_containers');

      // Render multiple containers that use same selectors
      const containers = [
        renderWithStore(<RuleFormContainer channel="frm" formName="create" />),
        renderWithStore(<RuleBuilderContainer channel="frm" />),
        renderWithStore(<ProductionRuleTableContainer channel="frm" />)
      ];

      const duration = performanceMonitor.endTiming('memoized_selectors_across_containers');
      expect(duration).toBeLessThan(200);

      // Cleanup
      containers.forEach(({ unmount }) => unmount());
    });
  });

  describe('Concurrent Container Operations', () => {
    it('should handle multiple container updates efficiently', () => {
      const containers = [
        renderWithStore(<RuleEngineContainer />),
        renderWithStore(<RuleFormContainer channel="frm" formName="create" />),
        renderWithStore(<ProductionRuleTableContainer channel="frm" />)
      ];

      performanceMonitor.startTiming('concurrent_container_updates');

      // Simulate concurrent state updates
      store.dispatch({ type: 'UPDATE_RULES', payload: { channel: 'frm' } });
      store.dispatch({ type: 'UPDATE_VALIDATION', payload: { status: false } });
      store.dispatch({ type: 'UPDATE_THEME', payload: 'dark' });

      const duration = performanceMonitor.endTiming('concurrent_container_updates');
      expect(duration).toBeLessThan(100);

      // Cleanup
      containers.forEach(({ unmount }) => unmount());
    });
  });
});
