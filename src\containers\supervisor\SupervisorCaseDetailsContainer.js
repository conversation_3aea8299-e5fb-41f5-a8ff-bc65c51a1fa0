import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onClearSelectedCase, onSelectCase } from 'actions/caseReviewActions';
import SupervisorCaseDetails from 'components/supervisor/SupervisorCaseDetails';

const mapStateToProps = (state) => {
  return {
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase,
    documentStatus: state.releaseFunds.documentStatus
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    selectCase: bindActionCreators(onSelectCase, dispatch),
    clearSelectedCase: bindActionCreators(onClearSelectedCase, dispatch)
  };
};

const SupervisorCaseDetailsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(SupervisorCaseDetails);

export default SupervisorCaseDetailsContainer;
