import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import rfiReportsReducer from 'reducers/rfiReportsReducer';

describe('compliance Dashboard Reducer', () => {
  it('should return the intial state', () => {
    expect(rfiReportsReducer(undefined, {})).toEqual(initialState.rfiReports);
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS', () => {
    expect(
      rfiReportsReducer(
        {
          highValueClosedAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS,
          response: responses.rfiReports.highValueClosedAccount.count
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: responses.rfiReports.highValueClosedAccount.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE', () => {
    expect(
      rfiReportsReducer(
        {
          highValueClosedAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          highValueClosedAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          highValueClosedAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS,
          response: responses.rfiReports.highValueClosedAccount.data,
          conf: formData
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: responses.rfiReports.highValueClosedAccount.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE', () => {
    expect(
      rfiReportsReducer(
        {
          highValueClosedAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      highValueClosedAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS', () => {
    expect(
      rfiReportsReducer(
        {
          highValueNewAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS,
          response: responses.rfiReports.highValueNewAccount.count
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: responses.rfiReports.highValueNewAccount.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE', () => {
    expect(
      rfiReportsReducer(
        {
          highValueNewAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          highValueNewAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          highValueNewAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS,
          response: responses.rfiReports.highValueNewAccount.data,
          conf: formData
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: responses.rfiReports.highValueNewAccount.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE', () => {
    expect(
      rfiReportsReducer(
        {
          highValueNewAccount: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      highValueNewAccount: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          fraudToSaleRatio: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS,
          response: responses.rfiReports.fraudToSaleRatio.count,
          conf: formData
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: responses.rfiReports.fraudToSaleRatio.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          fraudToSaleRatio: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          fraudToSaleRatio: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          fraudToSaleRatio: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS,
          response: responses.rfiReports.fraudToSaleRatio.data,
          conf: formData
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: responses.rfiReports.fraudToSaleRatio.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          fraudToSaleRatio: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      fraudToSaleRatio: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          unusualDeclineTurnover: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS,
          response: responses.rfiReports.unusualDeclineTurnover.count,
          conf: formData
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: responses.rfiReports.unusualDeclineTurnover.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          unusualDeclineTurnover: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          unusualDeclineTurnover: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          unusualDeclineTurnover: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS,
          response: responses.rfiReports.unusualDeclineTurnover.data,
          conf: formData
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: responses.rfiReports.unusualDeclineTurnover.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          unusualDeclineTurnover: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      unusualDeclineTurnover: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_TOP_MERCHANT_COUNT_LOADING
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          topMerchant: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS,
          response: responses.rfiReports.topMerchant.count,
          conf: formData
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: responses.rfiReports.topMerchant.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_COUNT_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          topMerchant: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TOP_MERCHANT_COUNT_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          topMerchant: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TOP_MERCHANT_DATA_LOADING
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          topMerchant: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TOP_MERCHANT_DATA_SUCCESS,
          response: responses.rfiReports.topMerchant.data,
          conf: formData
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: responses.rfiReports.topMerchant.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TOP_MERCHANT_DATA_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          topMerchant: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TOP_MERCHANT_DATA_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      topMerchant: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_HRC_TRXNS_COUNT_LOADING
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_COUNT_SUCCESS', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          hrcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HRC_TRXNS_COUNT_SUCCESS,
          response: responses.rfiReports.hrcTrxns.count,
          conf: formData
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: responses.rfiReports.hrcTrxns.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_COUNT_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          hrcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HRC_TRXNS_COUNT_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        cummValue: formData.cummValue,
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          hrcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HRC_TRXNS_DATA_LOADING
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10 };
    expect(
      rfiReportsReducer(
        {
          hrcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HRC_TRXNS_DATA_SUCCESS,
          response: responses.rfiReports.hrcTrxns.data,
          conf: formData
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: responses.rfiReports.hrcTrxns.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_HRC_TRXNS_DATA_FAILURE', () => {
    const formData = { cummValue: 40 };
    expect(
      rfiReportsReducer(
        {
          hrcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_HRC_TRXNS_DATA_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      hrcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_COUNT_LOADING', () => {
    expect(
      rfiReportsReducer(
        {},
        {
          type: types.ON_FETCH_NBFC_TRXNS_COUNT_LOADING
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: 0,
          loader: true,
          error: false,
          errorMessage: ''
        },
        cummValue: '',
        period: 'Day',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS', () => {
    const formData = { cummValue: 40, period: 'Day' };
    expect(
      rfiReportsReducer(
        {
          nbfcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            period: 'Day',
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS,
          response: responses.rfiReports.nbfcTrxns.count,
          conf: formData
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: responses.rfiReports.nbfcTrxns.count.count,
          loader: false,
          error: false,
          errorMessage: ''
        },
        cummValue: formData.cummValue,
        period: 'Day',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_COUNT_FAILURE', () => {
    const formData = { cummValue: 40, period: 'Day' };
    expect(
      rfiReportsReducer(
        {
          nbfcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            cummValue: '',
            period: 'Day',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_NBFC_TRXNS_COUNT_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: true,
          errorMessage: 'error message'
        },
        cummValue: formData.cummValue,
        period: 'Day',
        data: {
          records: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_DATA_LOADING', () => {
    expect(
      rfiReportsReducer(
        {
          nbfcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            period: 'Day',
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_NBFC_TRXNS_DATA_LOADING
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        period: 'Day',
        cummValue: '',
        data: {
          records: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_DATA_SUCCESS', () => {
    const formData = { pageNo: 1, pageSize: 10, period: 'Day' };
    expect(
      rfiReportsReducer(
        {
          nbfcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            period: 'Day',
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_NBFC_TRXNS_DATA_SUCCESS,
          response: responses.rfiReports.nbfcTrxns.data,
          conf: formData
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        period: 'Day',
        cummValue: '',
        data: {
          records: responses.rfiReports.nbfcTrxns.data.merchantDetails,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_NBFC_TRXNS_DATA_FAILURE', () => {
    const formData = { cummValue: 40, period: 'Day' };
    expect(
      rfiReportsReducer(
        {
          nbfcTrxns: {
            count: {
              value: 0,
              loader: false,
              error: false,
              errorMessage: ''
            },
            period: 'Day',
            cummValue: '',
            data: {
              records: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_NBFC_TRXNS_DATA_FAILURE,
          response: { message: 'error message' },
          conf: formData
        }
      )
    ).toEqual({
      nbfcTrxns: {
        count: {
          value: 0,
          loader: false,
          error: false,
          errorMessage: ''
        },
        period: 'Day',
        cummValue: '',
        data: {
          records: [],
          loader: false,
          error: true,
          errorMessage: 'error message'
        }
      }
    });
  });
});
