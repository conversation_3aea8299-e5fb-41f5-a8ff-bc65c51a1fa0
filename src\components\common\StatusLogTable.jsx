/* eslint-disable jsx-a11y/no-onchange */
/* eslint-disable react/prop-types */
/* eslint-disable react/display-name */
/* eslint-disable react/no-multi-comp */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import Datetime from 'react-datetime';
import { useHistory } from 'react-router-dom';
import { FormGroup, Label, Input, Button, Row, Col } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown, faCaretRight, faChevronRight } from '@fortawesome/free-solid-svg-icons';

import { getScreen } from 'constants/functions';
import ModalContainer from 'components/common/ModalContainer';
import CombinedViolationDropdownContainer from 'containers/common/CombinedViolationDropdownContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';

const StatusLogTable = ({
  id,
  name,
  theme,
  display,
  logs,
  role,
  ruleNames,
  fetchRuleNamesList,
  toggleStatusLogModal,
  fetchEntityLogs,
  userslist,
  fetchUsersList,
  channel
}) => {
  const history = useHistory();
  const [pageNo, setPageNo] = useState(0);
  const [expanded, setExpanded] = useState({});
  const [pageRecords, setPageRecords] = useState(10);
  const [user, setUser] = useState('');
  const [rule, setRule] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [typeFilter, setTypeFilter] = useState('true');

  useEffect(() => {
    if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
    if (_.isEmpty(userslist)) fetchUsersList();
  }, []);

  const getFilterCondition = () => {
    let filterCondition = [];

    !_.isEmpty(rule) &&
      filterCondition.push({
        key: 'rules',
        condition: 'EQUAL',
        values: [rule]
      });

    !_.isEmpty(user) &&
      filterCondition.push({
        key: 'user',
        condition: 'EQUAL',
        values: [user]
      });

    startDate &&
      endDate &&
      filterCondition.push({
        key: 'activityDateTime',
        condition: 'BETWEEN',
        values: [
          Moment(startDate).format('YYYY-MM-DD HH:mm:ss'),
          Moment(endDate).format('YYYY-MM-DD HH:mm:ss')
        ]
      });

    filterCondition.push({
      key: 'autoCaseAssignment',
      condition: 'EQUAL',
      values: [typeFilter]
    });

    return filterCondition;
  };

  useEffect(() => {
    let formData = {
      pageNo: pageNo + 1,
      pageRecords,
      filterCondition: getFilterCondition()
    };
    if (id) fetchEntityLogs(id, channel, formData);
  }, [id, channel, pageNo, pageRecords]);

  const searchLogs = (e) => {
    e.preventDefault();
    setPageNo(0);
    let formData = {
      pageNo: pageNo + 1,
      pageRecords,
      filterCondition: getFilterCondition()
    };
    if (id) fetchEntityLogs(id, channel, formData);
  };

  const userOptions =
    !_.isEmpty(userslist) &&
    userslist.map((user) => (
      <option key={user.id} value={user.userName}>
        {user.userName}
      </option>
    ));

  const getPage = (role, caseItem) => `${getScreen(role)}/${channel}/${caseItem.txnId}`;

  const handleOnClickCaseId = (role, caseItem) => {
    toggleStatusLogModal();
    history.push(getPage(role, caseItem));
  };

  const tableHeader = [
    {
      expander: true,
      Expander: ({ isExpanded, ...rest }) => {
        if (_.isEmpty(rest.original.violatedRules)) return null;
        else {
          return <FontAwesomeIcon icon={isExpanded ? faCaretDown : faCaretRight} />;
        }
      },
      getProps: (state, rowInfo) => {
        if (rowInfo) {
          if (_.isEmpty(rowInfo.original.violatedRules)) {
            return {
              onClick: (e) => {
                e.preventDefault();
              }
            };
          }
        }
        return { className: 'cursor-pointer' };
      }
    },
    {
      Header: '',
      accessor: 'caseId',
      Cell: (row) => (
        <Button
          size="sm"
          title="view"
          color="primary"
          className="me-1"
          onClick={() => handleOnClickCaseId(role, row.original)}
          onContextMenu={() => window.open(getPage(role, row.original))}>
          <FontAwesomeIcon icon={faChevronRight} />
        </Button>
      )
    },
    { Header: 'Transaction ID', accessor: 'txnId' },
    {
      Header: 'Date Time',
      accessor: 'dateAndTime',
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        Moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'User', accessor: 'userName' },
    { Header: 'Action', accessor: 'action', minWidth: 150 },
    { Header: 'Description', accessor: 'description', minWidth: 400 },
    {
      Header: 'Amount',
      accessor: 'txnAmount'
    },
    {
      Header: 'Transaction Type',
      accessor: 'txnTypeName',
      Cell: (row) => <span>{row.original.txnType + ', ' + row.original.txnTypeName}</span>
    },
    { Header: 'Response', accessor: 'responseCode' },
    {
      Header: 'Rules Violated',
      accessor: 'violatedRules',
      Cell: ({ value }) => (!_.isEmpty(value) ? _.split(value, ',').length : 0)
    }
  ];

  return (
    <div>
      <Button color="primary" onClick={toggleStatusLogModal}>
        Status Log
      </Button>
      <ModalContainer
        size="xl"
        theme={theme}
        header={'Status Log for ' + name}
        isOpen={display}
        toggle={toggleStatusLogModal}>
        {!id ? (
          <div className="no-data-div">No entityId found</div>
        ) : (
          <>
            <form onSubmit={searchLogs}>
              <Row className="align-items-end">
                <Col lg="2" md="3" sm="4">
                  <FormGroup>
                    <Label>Start Date</Label>
                    <Datetime
                      name="startDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat="HH:mm:ss"
                      value={startDate}
                      onChange={(dateObj) => setStartDate(dateObj._d)}
                    />
                  </FormGroup>
                </Col>
                <Col lg="2" md="3" sm="4">
                  <FormGroup>
                    <Label>End Date</Label>
                    <Datetime
                      name="endDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat="HH:mm:ss"
                      value={endDate}
                      onChange={(dateObj) => setEndDate(dateObj._d)}
                    />
                  </FormGroup>
                </Col>
                <Col lg="2" md="3" sm="4">
                  <FormGroup>
                    <Label>User</Label>
                    <Input
                      type="select"
                      name="user"
                      value={user}
                      onChange={(e) => setUser(e.target.value)}>
                      <option value="">All</option>
                      {userOptions}
                    </Input>
                  </FormGroup>
                </Col>
                <Col lg="2" md="3" sm="4">
                  <FormGroup>
                    <Label>Rules</Label>
                    <CombinedViolationDropdownContainer
                      name="rule"
                      value={rule}
                      onChange={(value) => setRule(value)}
                      defaultOption="-- SELECT RULE --"
                    />
                  </FormGroup>
                </Col>
                <Col lg="3" md="4" sm="4">
                  <FormGroup>
                    <Label check>
                      <Input
                        type="checkbox"
                        name="typeFilter"
                        value="true"
                        checked={typeFilter === 'true'}
                        onChange={() => setTypeFilter(typeFilter === 'true' ? 'false' : 'true')}
                      />{' '}
                      Hide auto case assignment logs
                    </Label>
                  </FormGroup>
                </Col>
                <Col lg="1" md="3" sm="4">
                  <FormGroup>
                    <Button size="sm" color="primary">
                      Search
                    </Button>
                  </FormGroup>
                </Col>
              </Row>
            </form>

            <ReactTable
              columns={tableHeader}
              data={logs.list}
              SubComponent={(row) => (
                <ViolatedRuleNameBadgeContainer
                  violatedRulesList={row.original?.reViolatedRules || ''}
                  taggedRulesList={row.original?.taggedRule || ''}
                />
              )}
              loading={logs.loader}
              noDataText={logs.errorMessage || 'No logs found'}
              showPaginationTop={true}
              showPaginationBottom={false}
              pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              defaultPageSize={10}
              minRows={3}
              showPageJump={false}
              page={pageNo}
              expanded={expanded}
              onPageChange={(page) => setPageNo(page)}
              onPageSizeChange={(pageSize, page) => {
                setPageNo(page);
                setPageRecords(pageSize);
              }}
              onExpandedChange={(expanded) => setExpanded(expanded)}
              className={'-highlight -striped'}
              pages={logs.count / pageRecords > 1 ? Math.ceil(logs.count / pageRecords) : 1}
            />
          </>
        )}
      </ModalContainer>
    </div>
  );
};

StatusLogTable.propTypes = {
  id: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  logs: PropTypes.object.isRequired,
  theme: PropTypes.string.isRequired,
  display: PropTypes.bool.isRequired,
  ruleNames: PropTypes.object.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  fetchEntityLogs: PropTypes.func.isRequired,
  toggleStatusLogModal: PropTypes.func.isRequired,
  userslist: PropTypes.array.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired
};

export default StatusLogTable;
