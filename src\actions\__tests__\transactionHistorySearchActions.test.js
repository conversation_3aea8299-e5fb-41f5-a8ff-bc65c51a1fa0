import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import {
  onSearchTxn,
  onClearSearch,
  onAdvanceSearchTxn,
  onRemoveSearchTxn
} from 'actions/transactionHistorySearchActions';
import responses from 'mocks/responses';

describe('transaction history search actions', () => {
  it('should fetch transactions for given criteria', () => {
    let criterion = [
      {
        condition: 'EQUAL',
        key: 'entity_id',
        values: 'Agent10001'
      },
      {
        condition: 'BETWEEN',
        key: 'txn_timestamp',
        values: ['2021-07-05 00:00:00Z', '2021-07-26 00:00:00Z']
      }
    ];
    const formData = {
      criterion,
      pageNo: 1,
      pageSize: 10
    };

    const expectedActions = [
      { type: types.ON_FETCH_TRANSACTION_HISTORY_LOADING, channel: 'frm' },
      {
        type: types.ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY,
        response: {
          isLastPage: responses.caseAssignment.cases.isLastPage,
          count: responses.caseAssignment.cases.count,
          records: responses.caseAssignment.cases.records
        },
        selectedDates: ['2021-07-05 00:00:00Z', '2021-07-26 00:00:00Z'],
        channel: 'frm',
        entityId: 'A'
      }
    ];
    const store = mockStore({ violatedRules: {} });

    return store.dispatch(onSearchTxn('merchant', formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Advance Search Txn', () => {
    const formData = {
      filters: {
        identifiers: {
          channel: 'frm'
        }
      },
      pageNo: 1,
      pageSize: 10
    };

    const expectedActions = [
      { type: types.ON_ADVANCE_SEARCH_TRANSACTION_LOADING },
      {
        type: types.ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS,
        response: {
          isLastPage: responses.caseAssignment.cases.isLastPage,
          count: responses.caseAssignment.cases.count,
          records: responses.caseAssignment.cases.records
        },
        channel: 'frm',
        filterCondition: formData.filters
      }
    ];

    const store = mockStore({ advanceSearchTxns: {} });

    return store.dispatch(onAdvanceSearchTxn(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should clear searched transaction history', () => {
    const expectedActions = { type: types.ON_CLEAR_TRANSACTION_HISTORY_SEARCH };
    expect(onClearSearch()).toEqual(expectedActions);
  });

  it('should Remove Search Txn', () => {
    const expectedActions = { type: types.ON_REMOVE_SEARCH_TRANSACTION, items: {} };
    expect(onRemoveSearchTxn({})).toEqual(expectedActions);
  });
});
