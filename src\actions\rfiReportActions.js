import {
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE,
  ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING,
  ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS,
  ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE,
  ON_FETCH_TOP_MERCHANT_COUNT_LOADING,
  ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS,
  ON_FETCH_TOP_MERCHANT_COUNT_FAILURE,
  ON_FETCH_TOP_MERCHANT_DATA_LOADING,
  ON_FETCH_TOP_MERCHANT_DATA_SUCCESS,
  ON_FETCH_TOP_MERCHANT_DATA_FAILURE,
  ON_FETCH_NBFC_TRXNS_COUNT_LOADING,
  ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS,
  ON_FETCH_NBFC_TRXNS_COUNT_FAILURE,
  ON_FETCH_NBFC_TRXNS_DATA_LOADING,
  ON_FETCH_NBFC_TRXNS_DATA_SUCCESS,
  ON_FETCH_NBFC_TRXNS_DATA_FAILURE,
  ON_FETCH_HRC_TRXNS_COUNT_LOADING,
  ON_FETCH_HRC_TRXNS_COUNT_SUCCESS,
  ON_FETCH_HRC_TRXNS_COUNT_FAILURE,
  ON_FETCH_HRC_TRXNS_DATA_LOADING,
  ON_FETCH_HRC_TRXNS_DATA_SUCCESS,
  ON_FETCH_HRC_TRXNS_DATA_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchHighValueClosedAccountCount() {
  return client({ url: `uds/str/high-value-txn-closed-account/count` });
}

function onFetchHighValueClosedAccountCountLoading() {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING };
}

function onFetchHighValueClosedAccountCountSuccess(response) {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS, response };
}

function onFetchHighValueClosedAccountCountFailure(response) {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE, response };
}

function onFetchHighValueClosedAccountCount() {
  return function (dispatch) {
    dispatch(onFetchHighValueClosedAccountCountLoading());
    return fetchHighValueClosedAccountCount().then(
      (success) => dispatch(onFetchHighValueClosedAccountCountSuccess(success)),
      (error) => dispatch(onFetchHighValueClosedAccountCountFailure(error))
    );
  };
}

function fetchHighValueClosedAccountData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/high-value-txn-closed-account`,
    data: conf,
    badRequestMessage:
      'Curently unable to fetch closed account with high value transactions for given value'
  });
}

function onFetchHighValueClosedAccountDataLoading() {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING };
}

function onFetchHighValueClosedAccountDataSuccess(response, conf) {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS, response, conf };
}

function onFetchHighValueClosedAccountDataFailure(response, conf) {
  return { type: ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE, response, conf };
}

function onFetchHighValueClosedAccountData(conf) {
  return function (dispatch) {
    dispatch(onFetchHighValueClosedAccountDataLoading());
    return fetchHighValueClosedAccountData(conf).then(
      (success) => dispatch(onFetchHighValueClosedAccountDataSuccess(success, conf)),
      (error) => dispatch(onFetchHighValueClosedAccountDataFailure(error, conf))
    );
  };
}

function fetchHighValueNewAccountCount() {
  return client({ url: `uds/str/high-value-txn-new-account/count` });
}

function onFetchHighValueNewAccountCountLoading() {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING };
}

function onFetchHighValueNewAccountCountSuccess(response) {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS, response };
}

function onFetchHighValueNewAccountCountFailure(response) {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE, response };
}

function onFetchHighValueNewAccountCount() {
  return function (dispatch) {
    dispatch(onFetchHighValueNewAccountCountLoading());
    return fetchHighValueNewAccountCount().then(
      (success) => dispatch(onFetchHighValueNewAccountCountSuccess(success)),
      (error) => dispatch(onFetchHighValueNewAccountCountFailure(error))
    );
  };
}

function fetchHighValueNewAccountData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/high-value-txn-new-account`,
    data: conf,
    badRequestMessage:
      'Curently unable to fetch new account with high value transactions for given value'
  });
}

function onFetchHighValueNewAccountDataLoading() {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING };
}

function onFetchHighValueNewAccountDataSuccess(response, conf) {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS, response, conf };
}

function onFetchHighValueNewAccountDataFailure(response, conf) {
  return { type: ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE, response, conf };
}

function onFetchHighValueNewAccountData(conf) {
  return function (dispatch) {
    dispatch(onFetchHighValueNewAccountDataLoading());
    return fetchHighValueNewAccountData(conf).then(
      (success) => dispatch(onFetchHighValueNewAccountDataSuccess(success, conf)),
      (error) => dispatch(onFetchHighValueNewAccountDataFailure(error, conf))
    );
  };
}

function fetchFraudToSaleRatioCount(formData) {
  return client({
    method: 'POST',
    url: `uds/str/f2s-ratio/count`,
    data: formData,
    badRequestMessage:
      'Curently unable to fetch account with high fraud to sales ratio for given value'
  });
}

function onFetchFraudToSaleRatioCountLoading() {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING };
}

function onFetchFraudToSaleRatioCountSuccess(response, conf) {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS, response, conf };
}

function onFetchFraudToSaleRatioCountFailure(response, conf) {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE, response, conf };
}

function onFetchFraudToSaleRatioCount(formData) {
  return function (dispatch) {
    dispatch(onFetchFraudToSaleRatioCountLoading());
    return fetchFraudToSaleRatioCount(formData).then(
      (success) => dispatch(onFetchFraudToSaleRatioCountSuccess(success, formData)),
      (error) => dispatch(onFetchFraudToSaleRatioCountFailure(error, formData))
    );
  };
}

function fetchFraudToSaleRatioData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/f2s-ratio`,
    data: conf,
    badRequestMessage:
      'Curently unable to fetch account with high fraud to sales ratio for given value'
  });
}

function onFetchFraudToSaleRatioDataLoading() {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING };
}

function onFetchFraudToSaleRatioDataSuccess(response, conf) {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS, response, conf };
}

function onFetchFraudToSaleRatioDataFailure(response, conf) {
  return { type: ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE, response, conf };
}

function onFetchFraudToSaleRatioData(conf) {
  return function (dispatch) {
    dispatch(onFetchFraudToSaleRatioDataLoading());
    return fetchFraudToSaleRatioData(conf).then(
      (success) => dispatch(onFetchFraudToSaleRatioDataSuccess(success, conf)),
      (error) => dispatch(onFetchFraudToSaleRatioDataFailure(error, conf))
    );
  };
}

function fetchTopMerchantCount(formData) {
  return client({
    method: 'POST',
    url: `uds/str/top-n-merchant-txns/count`,
    data: formData,
    badRequestMessage: 'Curently unable to fetch top merchants for given value'
  });
}

function onFetchTopMerchantCountLoading() {
  return { type: ON_FETCH_TOP_MERCHANT_COUNT_LOADING };
}

function onFetchTopMerchantCountSuccess(response, conf) {
  return { type: ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS, response, conf };
}

function onFetchTopMerchantCountFailure(response, conf) {
  return { type: ON_FETCH_TOP_MERCHANT_COUNT_FAILURE, response, conf };
}

function onFetchTopMerchantCount(formData) {
  return function (dispatch) {
    dispatch(onFetchTopMerchantCountLoading());
    return fetchTopMerchantCount(formData).then(
      (success) => dispatch(onFetchTopMerchantCountSuccess(success, formData)),
      (error) => dispatch(onFetchTopMerchantCountFailure(error, formData))
    );
  };
}
function fetchTopMerchantData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/top-n-merchant-txns`,
    data: conf,
    badRequestMessage: 'Curently unable to fetch top merchants for given value'
  });
}

function onFetchTopMerchantDataLoading() {
  return { type: ON_FETCH_TOP_MERCHANT_DATA_LOADING };
}

function onFetchTopMerchantDataSuccess(response, conf) {
  return { type: ON_FETCH_TOP_MERCHANT_DATA_SUCCESS, response, conf };
}

function onFetchTopMerchantDataFailure(response, conf) {
  return { type: ON_FETCH_TOP_MERCHANT_DATA_FAILURE, response, conf };
}

function onFetchTopMerchantData(conf) {
  return function (dispatch) {
    dispatch(onFetchTopMerchantDataLoading());
    return fetchTopMerchantData(conf).then(
      (success) => dispatch(onFetchTopMerchantDataSuccess(success, conf)),
      (error) => dispatch(onFetchTopMerchantDataFailure(error, conf))
    );
  };
}

function fetchNBFCTrxnsCount(formData) {
  return client({
    method: 'POST',
    url: `uds/str/nbfc-txns/count`,
    data: formData,
    badRequestMessage: 'Curently unable to fetch NBFC Transactions for given value'
  });
}

function onFetchNBFCTrxnsCountLoading() {
  return { type: ON_FETCH_NBFC_TRXNS_COUNT_LOADING };
}

function onFetchNBFCTrxnsCountSuccess(response, conf) {
  return { type: ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS, response, conf };
}

function onFetchNBFCTrxnsCountFailure(response, conf) {
  return { type: ON_FETCH_NBFC_TRXNS_COUNT_FAILURE, response, conf };
}

function onFetchNBFCTrxnsCount(formData) {
  return function (dispatch) {
    dispatch(onFetchNBFCTrxnsCountLoading());
    return fetchNBFCTrxnsCount(formData).then(
      (success) => dispatch(onFetchNBFCTrxnsCountSuccess(success, formData)),
      (error) => dispatch(onFetchNBFCTrxnsCountFailure(error, formData))
    );
  };
}

function fetchNBFCTrxnsData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/nbfc-txns`,
    data: conf,
    badRequestMessage: 'Curently unable to fetch NBFC Transactions for given value'
  });
}

function onFetchNBFCTrxnsDataLoading() {
  return { type: ON_FETCH_NBFC_TRXNS_DATA_LOADING };
}

function onFetchNBFCTrxnsDataSuccess(response, conf) {
  return { type: ON_FETCH_NBFC_TRXNS_DATA_SUCCESS, response, conf };
}

function onFetchNBFCTrxnsDataFailure(response, conf) {
  return { type: ON_FETCH_NBFC_TRXNS_DATA_FAILURE, response, conf };
}

function onFetchNBFCTrxnsData(conf) {
  return function (dispatch) {
    dispatch(onFetchNBFCTrxnsDataLoading());
    return fetchNBFCTrxnsData(conf).then(
      (success) => dispatch(onFetchNBFCTrxnsDataSuccess(success, conf)),
      (error) => dispatch(onFetchNBFCTrxnsDataFailure(error, conf))
    );
  };
}

function fetchHRCTrxnsCount(formData) {
  return client({
    method: 'POST',
    url: `uds/str/high-risk-country-txns/count`,
    data: formData,
    badRequestMessage: 'Curently unable to fetch High Risk Country Transactions for given value'
  });
}

function onFetchHRCTrxnsCountLoading() {
  return { type: ON_FETCH_HRC_TRXNS_COUNT_LOADING };
}

function onFetchHRCTrxnsCountSuccess(response, conf) {
  return { type: ON_FETCH_HRC_TRXNS_COUNT_SUCCESS, response, conf };
}

function onFetchHRCTrxnsCountFailure(response, conf) {
  return { type: ON_FETCH_HRC_TRXNS_COUNT_FAILURE, response, conf };
}

function onFetchHRCTrxnsCount(formData) {
  return function (dispatch) {
    dispatch(onFetchHRCTrxnsCountLoading());
    return fetchHRCTrxnsCount(formData).then(
      (success) => dispatch(onFetchHRCTrxnsCountSuccess(success, formData)),
      (error) => dispatch(onFetchHRCTrxnsCountFailure(error, formData))
    );
  };
}

function fetchHRCTrxnsData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/high-risk-country-txns`,
    data: conf,
    badRequestMessage: 'Curently unable to fetch High Risk Country Transactions for given value'
  });
}

function onFetchHRCTrxnsDataLoading() {
  return { type: ON_FETCH_HRC_TRXNS_DATA_LOADING };
}

function onFetchHRCTrxnsDataSuccess(response, conf) {
  return { type: ON_FETCH_HRC_TRXNS_DATA_SUCCESS, response, conf };
}

function onFetchHRCTrxnsDataFailure(response, conf) {
  return { type: ON_FETCH_HRC_TRXNS_DATA_FAILURE, response, conf };
}

function onFetchHRCTrxnsData(conf) {
  return function (dispatch) {
    dispatch(onFetchHRCTrxnsDataLoading());
    return fetchHRCTrxnsData(conf).then(
      (success) => dispatch(onFetchHRCTrxnsDataSuccess(success, conf)),
      (error) => dispatch(onFetchHRCTrxnsDataFailure(error, conf))
    );
  };
}

function fetchUnusalDeclineTurnoverCount(formData) {
  return client({
    method: 'POST',
    url: `uds/str/unusual-decline-turnover/count`,
    data: formData,
    badRequestMessage:
      'Curently unable to fetch Unsual Decline Turnover Transactions for given value'
  });
}

function onFetchUnusalDeclineTurnoverCountLoading() {
  return { type: ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING };
}

function onFetchUnusalDeclineTurnoverCountSuccess(response, conf) {
  return { type: ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS, response, conf };
}

function onFetchUnusalDeclineTurnoverCountFailure(response, conf) {
  return { type: ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE, response, conf };
}

function onFetchUnusalDeclineTurnoverCount(formData) {
  return function (dispatch) {
    dispatch(onFetchUnusalDeclineTurnoverCountLoading());
    return fetchUnusalDeclineTurnoverCount(formData).then(
      (success) => dispatch(onFetchUnusalDeclineTurnoverCountSuccess(success, formData)),
      (error) => dispatch(onFetchUnusalDeclineTurnoverCountFailure(error, formData))
    );
  };
}

function fetchUnusalDeclineTurnoverData(conf) {
  return client({
    method: 'POST',
    url: `uds/str/unusual-decline-turnover`,
    data: conf,
    badRequestMessage:
      'Curently unable to fetch Unsual Decline Turnover Transactions for given value'
  });
}

function onFetchUnusalDeclineTurnoverDataLoading() {
  return { type: ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING };
}

function onFetchUnusalDeclineTurnoverDataSuccess(response, conf) {
  return { type: ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS, response, conf };
}

function onFetchUnusalDeclineTurnoverDataFailure(response, conf) {
  return { type: ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE, response, conf };
}

function onFetchUnusalDeclineTurnoverData(conf) {
  return function (dispatch) {
    dispatch(onFetchUnusalDeclineTurnoverDataLoading());
    return fetchUnusalDeclineTurnoverData(conf).then(
      (success) => dispatch(onFetchUnusalDeclineTurnoverDataSuccess(success, conf)),
      (error) => dispatch(onFetchUnusalDeclineTurnoverDataFailure(error, conf))
    );
  };
}

export {
  onFetchHighValueClosedAccountCount,
  onFetchHighValueClosedAccountData,
  onFetchUnusalDeclineTurnoverCount,
  onFetchUnusalDeclineTurnoverData,
  onFetchHighValueNewAccountCount,
  onFetchHighValueNewAccountData,
  onFetchFraudToSaleRatioCount,
  onFetchFraudToSaleRatioData,
  onFetchTopMerchantCount,
  onFetchTopMerchantData,
  onFetchNBFCTrxnsCount,
  onFetchNBFCTrxnsData,
  onFetchHRCTrxnsCount,
  onFetchHRCTrxnsData
};
