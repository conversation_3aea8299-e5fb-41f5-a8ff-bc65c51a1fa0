import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/strReportActions';
import responses from 'mocks/responses';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  caseAssignment: {
    cases: {
      frm: {
        list: [],
        conf: {
          bucket: '',
          role: '',
          pageNo: 1,
          pageRecords: 10,
          sortBy: 'weight',
          sortOrder: 'desc',
          filterCondition: []
        },
        count: 0,
        isLastPage: true,
        loader: false,
        error: false,
        errorMessage: ''
      },
      str: {
        list: [],
        conf: {
          bucket: '',
          role: '',
          pageNo: 1,
          pageRecords: 10,
          sortBy: 'weight',
          sortOrder: 'desc',
          filterCondition: []
        },
        count: 0,
        isLastPage: true,
        loader: false,
        error: false,
        errorMessage: ''
      }
    }
  },
  auth: { userCreds },
  strReport: {}
};

describe('str Report actions', () => {
  it('should Fetch STR Report Masters', () => {
    const expectedActions = [
      { type: types.ON_FETCH_STR_REPORT_MASTERS_LOADING },
      {
        type: types.ON_FETCH_STR_REPORT_MASTERS_SUCCESS,
        response: responses.strReport.masters
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSTRReportMasters()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Submit STR Report', () => {
    const formData = {};

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { channel: 'str', type: 'ON_REMOVE_CASES_FROM_LIST', cases: [undefined] },
      { channel: 'str', type: 'ON_BUCKETS_FETCH_LOADING' },
      {
        channel: 'str',
        conf: {
          bucket: '',
          filterCondition: [],
          pageNo: 1,
          pageRecords: 10,
          role: '',
          sortBy: 'weight',
          sortOrder: 'desc'
        },
        type: 'ON_CASES_FETCH_LOADING'
      },
      { type: types.ON_FETCH_STR_REPORT_DETAILS_LOADING },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Requested STR Report filing' }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSubmitSTRReport(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch STR Report Details', () => {
    const expectedActions = [
      { type: types.ON_FETCH_STR_REPORT_DETAILS_LOADING },
      {
        type: types.ON_FETCH_STR_REPORT_DETAILS_SUCCESS,
        response: responses.strReport.details
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSTRReportDetails('case123')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch STR Report Logs', () => {
    const expectedActions = [
      { type: types.ON_FETCH_STR_REPORT_LOGS_LOADING },
      {
        type: types.ON_FETCH_STR_REPORT_LOGS_SUCCESS,
        response: responses.strReport.history
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSTRReportLogs('case123')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Submit BatchId', () => {
    const formData = { caseRefNo: 'case123', batchId: 'test123' };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_STR_REPORT_LOGS_LOADING },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'BatchID updated successfully' }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSubmitBatchId(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
