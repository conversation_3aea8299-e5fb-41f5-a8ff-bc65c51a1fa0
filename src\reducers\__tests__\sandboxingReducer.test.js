import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import sandboxing from 'reducers/sandboxingReducer';

describe('sandboxing Reducer', () => {
  it('should return the intial state', () => {
    expect(sandboxing(undefined, {})).toEqual(initialState.sandboxing);
  });

  it('should handle ON_FETCH_SANDBOX_DATE_RANGE_LOADING', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_DATE_RANGE_LOADING
        }
      )
    ).toEqual({
      dateRange: {
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS,
          response: []
        }
      )
    ).toEqual({
      dateRange: {
        data: [],
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_DATE_RANGE_FAILURE', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_DATE_RANGE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      dateRange: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_TEST_SANDBOX_RULES_LOADING', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_TEST_SANDBOX_RULES_LOADING
        }
      )
    ).toEqual({
      testing: {
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_TEST_SANDBOX_RULES_SUCCESS', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_TEST_SANDBOX_RULES_SUCCESS,
          response: responses.sandboxing.testingMsg,
          ruleName: 'BenfordLawCheck',
          ruleCode: 'test'
        }
      )
    ).toEqual({
      testing: {
        status: 'STARTED',
        ruleName: 'BenfordLawCheck',
        ruleCode: 'test',
        testId: responses.sandboxing.testingMsg.testId
      }
    });
  });

  it('should handle ON_TEST_SANDBOX_RULES_FAILURE', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_TEST_SANDBOX_RULES_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      testing: {
        loader: false,
        error: true,
        errorMessage: 'error message',
        status: 'FAILURE'
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_STATUS_SUCCESS', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_STATUS_SUCCESS,
          response: responses.sandboxing.testing
        }
      )
    ).toEqual({
      testing: {
        status: 'COMPLETE',
        data: responses.sandboxing.testing,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_STATUS_FAILURE', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_STATUS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      testing: {
        status: 'FAILED',
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING
        }
      )
    ).toEqual({
      violationDetails: {
        loader: true
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS', () => {
    expect(
      sandboxing(
        {
          violationDetails: {
            date: '',
            list: [],
            count: 0,
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS,
          response: responses.sandboxing.violationDetails,
          date: '2023-07-20'
        }
      )
    ).toEqual({
      violationDetails: {
        date: '2023-07-20',
        list: responses.sandboxing.violationDetails.records,
        count: responses.sandboxing.violationDetails.count,
        isLastPage: responses.sandboxing.violationDetails.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE', () => {
    expect(
      sandboxing(
        {
          violationDetails: {
            date: '',
            list: [],
            count: 0,
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE,
          response: { message: 'error message' },
          date: '2023-07-20'
        }
      )
    ).toEqual({
      violationDetails: {
        date: '2023-07-20',
        list: [],
        count: 0,
        isLastPage: true,
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_HISTORY_LOADING', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_HISTORY_LOADING
        }
      )
    ).toEqual({
      testHistory: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_HISTORY_SUCCESS', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_HISTORY_SUCCESS,
          response: responses.sandboxing.testHistory
        }
      )
    ).toEqual({
      testHistory: {
        list: responses.sandboxing.testHistory,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SANDBOX_HISTORY_FAILURE', () => {
    expect(
      sandboxing(
        {},
        {
          type: types.ON_FETCH_SANDBOX_HISTORY_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      testHistory: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
