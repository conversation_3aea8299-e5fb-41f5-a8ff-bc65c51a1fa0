import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchBusinessKpis } from 'actions/businessDashboardActions';
import FraudTypeStats from 'components/dashboards/FraudTypeStats';

const mapStateToProps = (state) => ({
  businessKpis: state.businessDashboard.businessKpis
});

const mapDispatchToProps = (dispatch) => ({
  fetchBusinessKpis: bindActionCreators(onFetchBusinessKpis, dispatch)
});

const FraudTypeStatsContainer = connect(mapStateToProps, mapDispatchToProps)(FraudTypeStats);

export default FraudTypeStatsContainer;
