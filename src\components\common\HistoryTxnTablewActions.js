import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import Datetime from 'react-datetime';
import { Button, Input } from 'reactstrap';
import Moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import objectAssign from 'object-assign';

import CardContainer from 'components/common/CardContainer';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';
import { getCurrentPageCases } from 'constants/functions';

const HistoryTxnTablewActions = ({
  userId,
  createCaseAndAssign,
  entityId,
  entityCategory,
  historyData,
  fetchHistoryTransactions,
  channel,
  searchDate,
  txnId,
  addToCase,
  clearHistory,
  childTxns = [],
  disableAddAction = false
}) => {
  const [endDate, setEndDate] = useState(
    Moment(searchDate).add(2, 'hours').format('YYYY-MM-DD HH:mm:ss')
  );
  const [startDate, setStartDate] = useState(
    Moment(searchDate).subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss')
  );
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [tableFilters, setTableFilters] = useState([]);
  const [filteredHistoryData, setFilteredHistoryData] = useState({});
  const [selectedTxns, setSelectedTxns] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPageData, setCurrentPageData] = useState(historyData[channel].list || []);

  useEffect(() => {
    return () => {
      clearHistory();
    };
  }, []);

  useEffect(() => {
    if (entityId) {
      setPageNo(0);
      const formData = searchTxnHistoryCriterion(entityId, startDate, endDate);
      searchTxnHistory(entityCategory, formData, channel);
    }
  }, [entityId, channel, txnId, searchDate]);

  useEffect(() => {
    if (!_.isEmpty(filteredHistoryData)) {
      const formData = searchTxnHistoryCriterion(entityId, startDate, endDate);
      searchTxnHistory(entityCategory, formData, channel);
    }
  }, [pageNo, pageRecords]);

  useEffect(() => {
    let filteredHistoryData = objectAssign({}, historyData[channel], {
      list: _.filter(historyData[channel].list, (data) => {
        return data?.txnId != txnId;
      })
    });
    setFilteredHistoryData(filteredHistoryData);
  }, [historyData]);

  useEffect(() => {
    setCurrentPageData(
      getCurrentPageCases(pageNo, pageRecords, historyData[channel].list, tableFilters)
    );
  }, [historyData[channel].list, pageNo, pageRecords, tableFilters]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(0);
      }, 500),
    [tableFilters]
  );

  const tablePageCountProp = _.isEmpty(tableFilters)
    ? {
        pages:
          historyData[channel].count / pageRecords > 1
            ? Math.ceil(historyData[channel].count / pageRecords)
            : 1
      }
    : {};

  const resetCheckBox = () => {
    setSelectedTxns([]);
    setSelectAll(false);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      resetCheckBox();
    } else {
      const txnIds = currentPageData.map((d) => d.txnId);
      setSelectedTxns(txnIds);
      setSelectAll(true);
    }
  };

  const handleCheckboxChange = (txnId) => {
    let checkedCases = [...selectedTxns];
    const index = checkedCases.indexOf(txnId);
    if (index > -1) {
      checkedCases.splice(index, 1);
      setSelectedTxns([...checkedCases]);
    } else {
      checkedCases = [...checkedCases, txnId];
      setSelectedTxns([...checkedCases]);
    }

    checkedCases.length == currentPageData.length ? setSelectAll(true) : setSelectAll(false);
  };

  const searchHistoryForDate = (e) => {
    e.preventDefault();
    setPageNo(0);
    const formData = searchTxnHistoryCriterion(entityId, startDate, endDate);
    searchTxnHistory(entityCategory, formData, channel);
  };

  const createCase = (transaction) => {
    let formData = {
      assignedTo: userId,
      assignedBy: userId,
      channel: channel,
      txnId: transaction.txnId,
      partnerId: transaction?.identifiers?.partnerId || 0
    };
    const searchFormData = searchTxnHistoryCriterion(entityId, startDate, endDate);
    const searchRequest = { entityCategory, formData: searchFormData, channel };
    createCaseAndAssign(formData, searchRequest);
  };

  const addTxns = (txnIds) => {
    addToCase({ txnIds, parentTxnId: txnId });
    resetCheckBox();
  };

  const checkboxColumn = {
    Header: <Input type="checkbox" onChange={() => handleSelectAll()} checked={selectAll} />,
    accessor: 'txnId',
    searchable: false,
    sortable: false,
    filterable: false,
    show: !disableAddAction,
    minWidth: 40,
    // eslint-disable-next-line react/prop-types
    Cell: ({ value }) => (
      <Input
        type="checkbox"
        value={value}
        name="tableSelect[]"
        checked={_.includes(selectedTxns, value)}
        onChange={() => handleCheckboxChange(value)}
      />
    )
  };

  const tableActions = {
    Header: '',
    searchable: false,
    filterable: false,
    sortable: false,
    minWidth: 130,
    show: !disableAddAction,
    fixed: true,
    Cell: (row) => {
      return !_.includes(childTxns, row.original.txnId) ? (
        <>
          {!disableAddAction &&
            row.original.currentStatus !== 'Closed' &&
            row.original.currentStage != 2 && (
              <Button
                outline
                size="sm"
                className="me-2"
                title="Add to case"
                color="danger"
                onClick={() => addTxns([row.original.txnId])}>
                Add
              </Button>
            )}
          {row.original.currentStatus == '' && row.original.caseType == 'frm' && (
            <Button
              outline
              size="sm"
              title="Create case"
              color="primary"
              onClick={() => {
                createCase(row.original);
              }}>
              New
            </Button>
          )}
        </>
      ) : (
        <Button
          size="sm"
          className="me-2"
          title="Click to remove from case"
          color="danger"
          onClick={() => addTxns([row.original.txnId])}>
          Added
        </Button>
      );
    }
  };

  const searchTxnHistoryCriterion = (entityId, startDate = null, endDate = null) => {
    let criterion = [
      {
        condition: 'EQUAL',
        key: 'entity_id',
        values: [entityId]
      },
      {
        condition: 'BETWEEN',
        key: 'txn_timestamp',
        values: [
          Moment(startDate).format('YYYY-MM-DD HH:mm:ss') + 'Z',
          Moment(endDate).format('YYYY-MM-DD HH:mm:ss') + 'Z'
        ]
      }
    ];

    return {
      criterion,
      caseStatus: ['New', 'Open'],
      pageNo: pageNo + 1,
      pageSize: pageRecords
    };
  };

  const searchTxnHistory = (entityCategory, formData, channel) =>
    entityCategory && fetchHistoryTransactions(entityCategory, formData, channel);

  return (
    <CardContainer title="Transaction Search">
      <form onSubmit={searchHistoryForDate} className="p-1">
        <div className="d-flex justify-content-between">
          <div className="d-flex gap-3 align-items-end">
            <Datetime
              name="startDate"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={startDate}
              onChange={(dateObj) => setStartDate(dateObj._d)}
              inputProps={{ required: true }}
            />
            To
            <Datetime
              name="endDate"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={endDate}
              onChange={(dateObj) => setEndDate(dateObj._d)}
              inputProps={{ required: true }}
            />
            <Button size="sm" type="submit" color="primary">
              <FontAwesomeIcon icon={faSearch} /> Search
            </Button>
          </div>
          <Button
            outline
            color="danger"
            disabled={_.isEmpty(selectedTxns)}
            onClick={() => addTxns(selectedTxns)}>
            Add Cases
          </Button>
        </div>
      </form>

      <TransactionTableContainer
        page={pageNo}
        pageSize={pageRecords}
        filtered={tableFilters}
        tableActions={tableActions}
        checkboxColumn={checkboxColumn}
        data={filteredHistoryData}
        onPageChange={(page) => setPageNo(page)}
        onPageSizeChange={(pageSize, page) => {
          setPageNo(page);
          setPageRecords(pageSize);
        }}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        displayRedirect={false}
        {...tablePageCountProp}
      />
    </CardContainer>
  );
};

HistoryTxnTablewActions.propTypes = {
  childTxns: PropTypes.array,
  searchDate: PropTypes.string,
  historyData: PropTypes.object,
  disableAddAction: PropTypes.bool,
  txnId: PropTypes.string.isRequired,
  userId: PropTypes.number.isRequired,
  channel: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  entityCategory: PropTypes.string.isRequired,
  createCaseAndAssign: PropTypes.func.isRequired,
  fetchHistoryTransactions: PropTypes.func.isRequired,
  clearHistory: PropTypes.func.isRequired,
  addToCase: PropTypes.func.isRequired
};

export default HistoryTxnTablewActions;
