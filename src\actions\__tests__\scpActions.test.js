import * as types from 'constants/actionTypes';
import * as actions from 'actions/scpActions';
import { mockStore } from 'store/mockStoreConfiguration';
import responses from 'mocks/responses';

describe('scp actions', () => {
  it('should fetch configurations', () => {
    const expectedActions = [
      { type: types.ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING },
      { type: types.ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH, response: responses.scp.config }
    ];

    const store = mockStore({ scp: {} });

    return store.dispatch(actions.onFetchConfigurationsList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should save configurations', () => {
    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Configurations saved successfully' } },
      { type: types.ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING }
    ];

    const store = mockStore({ scp: {} });

    return store.dispatch(actions.onSaveConfigurations()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
