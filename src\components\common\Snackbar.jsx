import PropTypes from 'prop-types';
import React from 'react';
import { Toast, ToastBody, ToastHeader } from 'reactstrap';

const Snackbar = ({ alert, actions }) => {
  const getAlertIcon = (alertType) => {
    switch (alertType) {
      case 'alert-success':
        return 'success';
      case 'alert-danger':
        return 'danger';
      case 'alert-warning':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getAlertTitle = (alertType) => {
    switch (alertType) {
      case 'alert-success':
        return 'Success !';
      case 'alert-danger':
        return 'Error !';
      case 'alert-warning':
        return 'Warning !';
      default:
        return 'Alert !';
    }
  };

  return (
    <Toast isOpen={alert.show}>
      <ToastHeader toggle={() => actions.onClearAlert()} icon={getAlertIcon(alert.type)}>
        {getAlertTitle(alert.type)}
      </ToastHeader>
      <ToastBody>{alert.message}</ToastBody>
    </Toast>
  );
};

Snackbar.propTypes = {
  alert: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired
};

export default Snackbar;
