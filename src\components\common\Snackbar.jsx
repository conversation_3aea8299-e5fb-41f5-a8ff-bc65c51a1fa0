import React from 'react';
import PropTypes from 'prop-types';
import { Toast, ToastBody, ToastHeader } from 'reactstrap';

const Snackbar = ({ alert, actions }) => {
  return (
    <Toast isOpen={alert.show}>
      <ToastHeader
        toggle={() => actions.onClearAlert()}
        icon={
          alert.type == 'alert-success'
            ? 'success'
            : alert.type == 'alert-danger'
            ? 'danger'
            : alert.type == 'alert-warning'
            ? 'warning'
            : 'secondary'
        }>
        {alert.type == 'alert-success'
          ? 'Success !'
          : alert.type == 'alert-danger'
          ? 'Error !'
          : alert.type == 'alert-warning'
          ? 'Warning !'
          : 'Alert !'}
      </ToastHeader>
      <ToastBody>{alert.message}</ToastBody>
    </Toast>
  );
};

Snackbar.propTypes = {
  alert: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired
};

export default Snackbar;
