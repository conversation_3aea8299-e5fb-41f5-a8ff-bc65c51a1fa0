import * as types from 'constants/actionTypes';
import * as actions from 'actions/ruleConfiguratorActions';
import { mockStore } from 'store/mockStoreConfiguration';
import responses from 'mocks/responses';

const { rules } = responses;

describe('rule configurator actions', () => {
  it('should fetch rule list', () => {
    const expectedActions = [
      { type: types.ON_FETCH_RULE_LIST_LOADING },
      { type: types.ON_SUCCESSFUL_FETCH_RULE_LIST, channel: 'frm', response: rules.list }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onFetchRulesList('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should toggle rule status', () => {
    const formData = {
      code: 1,
      channel: 'frm',
      status: 'Activated'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESSFUL_TOGGLE_RULE_STATUS,
        ruleCode: formData.code,
        channel: formData.channel,
        status: formData.status
      },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Rule activated successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onToggleRuleStatus(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should toggle rule activation', () => {
    const formData = {
      code: 1,
      channel: 'frm',
      active: 'Enable'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESSFUL_TOGGLE_RULE_ACTIVATION,
        ruleCode: formData.code,
        channel: formData.channel,
        active: formData.active
      },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Rule enabled successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onToggleRuleActivation(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should make rule explicit', () => {
    const formData = {
      code: 1,
      channel: 'frm',
      explicit: true
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESSFUL_TOGGLE_RULE_EXPLICIT,
        ruleCode: formData.code,
        channel: formData.channel,
        explicit: formData.explicit
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Rule enabled for explicit case creation successfully' }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onToggleRuleExplicit(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch rule names', () => {
    const channel = 'frm';

    const expectedActions = [
      { type: types.ON_FETCH_RULE_NAMES_LIST_LOADING },
      { type: types.ON_FETCH_RULE_NAMES_LIST_SUCCESS, response: rules.ruleNames, channel }
    ];

    const store = mockStore({ ruleConfigurator: {} });

    return store.dispatch(actions.onFetchRuleNamesList(channel)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
