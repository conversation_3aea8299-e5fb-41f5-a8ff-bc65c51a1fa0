import { onShowFailureAlert } from 'actions/alertActions';
import {
  ON_SUCCESSFUL_FETCH_MONITORING_DATA,
  ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT,
  ON_FETCH_MONITORING_DATA_FAILURE,
  ON_FETCH_TRANSACTION_COUNT_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchTransactionCount(channel) {
  return client({
    url: `${channel}/ruleengine/transaction/today/count`
  });
}

function onSuccessfulFetchTransactionCount(channel, response) {
  return {
    type: ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT,
    channel,
    response
  };
}

function onTransactionCountFetchFailure() {
  return { type: ON_FETCH_TRANSACTION_COUNT_FAILURE };
}

function onFetchTransactionCount(channel) {
  return function (dispatch) {
    return fetchTransactionCount(channel).then(
      (success) => dispatch(onSuccessfulFetchTransactionCount(channel, success)),
      (error) => {
        dispatch(onTransactionCountFetchFailure());
        dispatch(onShowFailureAlert(error));
      }
    );
  };
}

function fetchMonitoringData() {
  return client({
    url: `transactionmonitoring/transactions/day/user`
  });
}

function onSuccessfulFetch(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_MONITORING_DATA,
    response
  };
}

function onFetchFailure() {
  return { type: ON_FETCH_MONITORING_DATA_FAILURE };
}

function onFetchMonitoringData() {
  return function (dispatch) {
    return fetchMonitoringData().then(
      (success) => dispatch(onSuccessfulFetch(success)),
      (error) => {
        dispatch(onFetchFailure());
        dispatch(onShowFailureAlert(error));
      }
    );
  };
}

export { onFetchTransactionCount, onFetchMonitoringData };
