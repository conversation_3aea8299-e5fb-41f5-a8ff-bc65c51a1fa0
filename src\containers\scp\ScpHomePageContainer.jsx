import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchConfigurationsList, onSaveConfigurations } from 'actions/scpActions';
import ScpHomePage from 'components/scp/ScpHomePage';

const mapStateToProps = (state) => ({
  configurationsData: state.scp.configurationsData,
  liability: state.caseAssignment.liability,
  fraudTypes: state.caseAssignment.fraudTypes,
  userRoles: state.auth.userCreds.roles,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  fetchConfigurationsList: bindActionCreators(onFetchConfigurationsList, dispatch),
  saveConfigurations: bindActionCreators(onSaveConfigurations, dispatch)
});

const ScpHomePageContainer = connect(mapStateToProps, mapDispatchToProps)(ScpHomePage);

export default ScpHomePageContainer;
