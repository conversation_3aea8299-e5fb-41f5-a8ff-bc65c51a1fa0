import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as releaseFundsActions from 'actions/releaseFundsActions';
import * as toggleActions from 'actions/toggleActions';
import ReleaseFunds from 'components/releaseFunds/ReleaseFunds';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    userCreds: state.auth.userCreds,
    releaseFunds: state.releaseFunds
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    releaseFundsActions: bindActionCreators(releaseFundsActions, dispatch)
  };
};

const ReleaseFundsContainer = connect(mapStateToProps, mapDispatchToProps)(ReleaseFunds);

export default ReleaseFundsContainer;
