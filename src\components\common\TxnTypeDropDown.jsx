import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Input, Label, FormGroup } from 'reactstrap';

function TxnTypeDropDown({
  onChange,
  txnTypeData,
  getTxnTypes,
  value = '',
  isRequired = false,
  isDisabled = false
}) {
  useEffect(() => {
    if (_.isEmpty(txnTypeData?.list) && !txnTypeData.loader) getTxnTypes();
  }, []);

  const txnTypeOptions = _.map(txnTypeData?.list, (txnType) => (
    <option key={txnType.id} value={txnType.name}>
      {txnType.name}
    </option>
  ));

  const inputProps = {
    ...(isRequired && { required: true }),
    ...(isDisabled && { disabled: true })
  };

  return (
    <FormGroup>
      <Label htmlFor="txnType">Txn Type</Label>
      <Input
        type="select"
        id="txnType"
        name="txnType"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        {...inputProps}>
        <option value="" key="nullValue">
          -- SELECT --
        </option>
        {txnTypeOptions}
      </Input>
    </FormGroup>
  );
}

TxnTypeDropDown.propTypes = {
  isRequired: PropTypes.bool,
  isDisabled: PropTypes.bool,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  txnTypeData: PropTypes.object.isRequired,
  getTxnTypes: PropTypes.func.isRequired
};

export default TxnTypeDropDown;
