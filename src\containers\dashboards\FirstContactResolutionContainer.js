import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchFirstContactRate } from 'actions/slaDashboardActions';
import FirstContactResolutionGraph from 'components/dashboards/FirstContactResolutionGraph';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    firstContactRate: state.slaDashboard.firstContactRate
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFirstContactRate: bindActionCreators(onFetchFirstContactRate, dispatch)
  };
};

const FirstContactResolutionContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(FirstContactResolutionGraph);

export default FirstContactResolutionContainer;
