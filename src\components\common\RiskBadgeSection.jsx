import PropTypes from 'prop-types';
import React from 'react';
import {
  Badge,
  UncontrolledPopover,
  PopoverHeader,
  PopoverBody,
  Table,
  Row,
  Col
} from 'reactstrap';

const RiskBadgeSection = ({
  label,
  badgeId,
  badgeText,
  badgeColor,
  popoverTitle,
  popoverRows,
  isRounded = false
}) => {
  if (!badgeText) return null;

  const hasValidValue = (val) => val !== undefined && val !== null && val !== '';
  const validRows = (popoverRows || []).filter((row) => hasValidValue(row?.props?.value));

  return (
    <>
      <Row className="align-items-center">
        {!isRounded && (
          <Col md="8" className="px-0">
            <span className="fw-semibold text-dark">{label}</span>
          </Col>
        )}
        <Col
          md={isRounded ? 12 : 4}
          className={`px-0 ${
            isRounded ? 'd-flex flex-column align-items-center justify-content-center' : ''
          }`}>
          <Badge
            id={badgeId}
            color={badgeColor}
            className={`cursor-pointer text-uppercase d-flex align-items-center justify-content-center ${
              isRounded ? 'rounded-pill risk-lvel-badge' : ''
            }`}>
            {badgeText}
          </Badge>
          {isRounded && <span className="fw-semibold text-dark mt-3">{label}</span>}
        </Col>
      </Row>

      {validRows.length > 0 && (
        <UncontrolledPopover trigger="hover" target={badgeId} placement="right">
          <PopoverHeader>{popoverTitle}</PopoverHeader>
          <PopoverBody>
            <Table className="mb-0">
              <tbody>{validRows}</tbody>
            </Table>
          </PopoverBody>
        </UncontrolledPopover>
      )}
    </>
  );
};

RiskBadgeSection.propTypes = {
  label: PropTypes.string.isRequired,
  badgeId: PropTypes.string.isRequired,
  badgeText: PropTypes.string.isRequired,
  badgeColor: PropTypes.string.isRequired,
  popoverTitle: PropTypes.string.isRequired,
  popoverRows: PropTypes.arrayOf(PropTypes.node),
  isRounded: PropTypes.bool
};

export default RiskBadgeSection;
