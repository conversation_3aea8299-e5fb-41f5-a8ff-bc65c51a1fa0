import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onUnfreezeAccountDebit } from 'actions/caseAssignmentActions';
import UnfreezeAccountButton from 'components/investigation/UnfreezeAccountButton';

const mapStateToProps = (state) => ({
  customerInfo: state.uds.customer.details,
  hasDebitFreeze: state.user.configurations.allowDebitFreeze
});

const mapDispatchToProps = (dispatch) => ({
  unfreezeAccountDebit: bindActionCreators(onUnfreezeAccountDebit, dispatch)
});

const UnfreezeAccountButtonContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(UnfreezeAccountButton);

export default UnfreezeAccountButtonContainer;
