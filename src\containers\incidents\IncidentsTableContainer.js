import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/incidentActions';
import { onToggleCPIFRFormModal } from 'actions/toggleActions';
import IncidentsTable from 'components/incidents/IncidentsTable';

const mapStateToProps = (state) => {
  return {
    data: state.incidents.allIncidents,
    role: state.auth.userCreds.roles,
    channels: state.auth.userCreds.channels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch),
    toggleCPIFRFormModal: bindActionCreators(onToggleCPIFRFormModal, dispatch)
  };
};

const IncidentsTableContainer = connect(mapStateToProps, mapDispatchToProps)(IncidentsTable);

export default IncidentsTableContainer;
