import React, { useState, useEffect } from 'react';
import Moment from 'moment';
import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import { InputGroup, Button, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock, faPaperPlane } from '@fortawesome/free-regular-svg-icons';

import ListLoader from 'components/loader/ListLoader';
import CardContainer from 'components/common/CardContainer';

const Notation = ({
  caseId,
  channel,
  userName,
  notations,
  caseRefNo,
  notationActions,
  showFailureAlert,
  disableComment = false
}) => {
  const [commentOption, setCommentOption] = useState('');
  const [comment, setComment] = useState('');
  const { master, case: cases } = notations;

  useEffect(() => {
    if (master?.list.length === 0 && (!notations.loader || !notations.error))
      notationActions.onFetchNotationsList();
  }, []);

  useEffect(() => {
    caseId && notationActions.onFetchCaseNotationList(caseId, channel);
  }, [caseId, channel]);

  const addComment = (e) => {
    e.preventDefault();
    let notationComment = commentOption == 'Others' ? comment : commentOption;
    if (!notationComment) return showFailureAlert({ message: 'Please provide a proper comment' });
    notationActions.onAddCaseNotation({ caseId, userName, notationComment, caseRefNo });
    setCommentOption('');
    setComment('');
  };

  const getOptionsList = (notations = []) =>
    notations.map((notation) => <option key={notation.id}>{notation.notation}</option>);

  const getCommentList = (notations = []) =>
    notations.map((notation) => (
      <li key={notation.notationTimestamp} className={'comment'}>
        <div className="d-flex justify-content-between flex-wrap comment-header">
          <p>{notation.notationUserName}</p>
          <p className="comment-timestamp">
            <FontAwesomeIcon icon={faClock} />{' '}
            {Moment(notation.notationTimestamp).format('YYYY-MM-DD hh:mm A')}
          </p>
        </div>
        <p>{notation.notationComment}</p>
      </li>
    ));

  return (
    <CardContainer title="Notations">
      {cases.loader ? (
        <ListLoader />
      ) : cases.error ? (
        <div className="no-data-div">{cases.errorMessage}</div>
      ) : (
        <ul className="comment-list">
          {isEmpty(cases.list) ? (
            <p className={'no-comment-text '}>No notations</p>
          ) : (
            getCommentList(cases.list)
          )}
        </ul>
      )}
      {!disableComment && (
        <>
          <hr className={'comment-separator'} />
          <form onSubmit={addComment}>
            <InputGroup className={'notation-action'}>
              <Input
                type="select"
                name="commentOption"
                value={commentOption}
                onChange={(e) => setCommentOption(e.target.value)}
                required>
                <option value="">-- SELECT --</option>
                {master.loader ? (
                  <option value="" disabled>
                    Loading...
                  </option>
                ) : master.error ? (
                  <option value="" disabled>
                    {master.errorMessage}
                  </option>
                ) : (
                  getOptionsList(master.list)
                )}
                <option>Others</option>
              </Input>
              <Button outline size="sm">
                <FontAwesomeIcon icon={faPaperPlane} />
              </Button>
            </InputGroup>
            {commentOption == 'Others' ? (
              <Input
                type="text"
                className={'mt-2'}
                name="comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                required
              />
            ) : null}
          </form>
        </>
      )}
    </CardContainer>
  );
};

Notation.propTypes = {
  disableComment: PropTypes.bool,
  caseId: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  notations: PropTypes.object.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  showFailureAlert: PropTypes.func.isRequired,
  notationActions: PropTypes.object.isRequired
};

export default Notation;
