import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

/**
 * A reusable section header component with icon and optional subtitle
 *
 * @example
 * <SectionHeader
 *   icon={faBuilding}
 *   title="Company Overview"
 *   subtitle="Last updated: 2023"
 *   variant="primary"
 * />
 */
const SectionHeader = ({
  icon,
  title,
  subtitle,
  variant = 'primary',
  children,
  className = ''
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div
        className={`d-flex align-items-center justify-content-center rounded-circle bg-${variant} text-white me-3`}
        style={{ width: 40, height: 40 }}>
        <FontAwesomeIcon icon={icon} fixedWidth />
      </div>
    )}
    <div className="flex-grow-1">
      <h5 className="mb-0">{title}</h5>
      {subtitle && <div className="text-muted small mt-1">{subtitle}</div>}
      {children && <div className="text-muted small mt-1">{children}</div>}
    </div>
  </div>
);

SectionHeader.propTypes = {
  icon: PropTypes.object,
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  variant: PropTypes.string,
  children: PropTypes.node,
  className: PropTypes.string
};

export default SectionHeader;
