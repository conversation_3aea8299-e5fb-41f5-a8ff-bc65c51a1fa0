import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardBody } from 'reactstrap';

/**
 * A reusable metric card component for displaying statistics and metrics
 * with consistent styling and layout.
 *
 * @example
 * <MetricCard
 *   label="Revenue"
 *   value="₹50,000"
 *   subText="FY 2023-24"
 *   variant="success"
 *   icon={faChartLine}
 * />
 */
const MetricCard = ({
  label,
  value,
  subText,
  variant = 'light',
  icon,
  children,
  className = ''
}) => {
  return (
    <Card className={`border-0 bg-${variant} rounded shadow-sm h-100 ${className}`}>
      <CardBody className="p-3">
        {icon && (
          <div className="mb-2 text-muted">
            <FontAwesomeIcon icon={icon} fixedWidth />
          </div>
        )}
        <div className="text-muted small mb-1">{label}</div>
        <div className="fw-bold h5 mb-0">{value !== null && value !== undefined ? value : '—'}</div>
        {subText && <small className="text-muted d-block mt-1">{subText}</small>}
        {children}
      </CardBody>
    </Card>
  );
};

MetricCard.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.node,
  subText: PropTypes.node,
  variant: PropTypes.string,
  icon: PropTypes.object,
  children: PropTypes.node,
  className: PropTypes.string
};

export default MetricCard;
