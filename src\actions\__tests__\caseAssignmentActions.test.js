import mockResponse from 'mocks/responses';

import * as actions from 'actions/caseAssignmentActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const { caseAssignment } = mockResponse;
const { cases, selectedCase } = caseAssignment;

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker'
};

const conf = {
  bucket: 'OpenCases',
  role: 'checker',
  pageNo: 1,
  pageRecords: 10,
  sortBy: 'weight',
  sortOrder: 'desc',
  filterCondition: []
};

const mockedStore = {
  auth: { userCreds },
  caseAssignment: {
    cases: {
      frm: {
        list: cases.cases,
        conf,
        count: cases.count,
        isLastPage: cases.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    selectedCase
  }
};

describe('case assignment actions', () => {
  it('should create case and assign', () => {
    const formData = {
      channel: 'frm',
      txnId: 'Test20230111506',
      assignedTo: 'reviewer',
      assignedBy: 'reviewer',
      stageId: 2,
      caseRefNo: 'a32059c5-59ac-434d-bb75-5a951d8fe1af',
      entityId: 'merchant1'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully opened a new case.' }
      },
      {
        type: types.ON_SUCCESSFUL_CASE_CREATION,
        assignedTo: 'reviewer',
        assignedBy: 'reviewer'
      },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onCreateCaseAndAssign(formData, {}, selectedCase)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should reassign case to another user', () => {
    const formData = {
      caseRefNo: 1,
      assignedTo: 1,
      activityBy: 'user1',
      assignedToName: 'user',
      channel: 'frm'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully reassigned case(s) to user' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onReassignCase(formData, false, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should re-assign case to supervisor', () => {
    const formData = {
      caseRefNo: '1',
      supervisorToBeReassignedTo: '1',
      supervisorCurrentlyAssignedTo: '1',
      activityBy: 'supervisor',
      supervisorToBeReassignedToName: 'supervisor'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully reassigned case to supervisor' }
      },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ caseDetails: {} });

    return store.dispatch(actions.onReassignCaseToSupervisor(formData, selectedCase)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should assign a new case', () => {
    const formData = {
      caseRefNo: 1,
      assignedTo: 1,
      activityBy: 'user1',
      assignedToName: 'user'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully assigned case(s) to user' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onAssignNewCase(formData, false, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should escalate a case', () => {
    const formData = {
      caseRefNo: 1,
      escalatedTo: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully assigned case to maker 1' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onEscalateCase(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should acknowledge a case', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ caseDetails: {} });

    return store.dispatch(actions.onAcknowledgeCase(selectedCase, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch case for closure', () => {
    const formData = {
      ruleId: '15dd9b65-549d-44e1-b922-aa065d98766e',
      from_txn_timeStamp: '2020-05-14T14:18:01.522344',
      to_txn_timeStamp: '2020-06-14T14:18:01.522344'
    };

    const expectedActions = [
      { type: types.ON_FETCH_CLOSURE_CASES_LOADING },
      { type: types.ON_FETCH_CLOSURE_CASES_SUCCESS, response: caseAssignment.closureCases }
    ];
    const store = mockStore({ caseAssignment: { closureCases: {} } });

    return store.dispatch(actions.onFetchCasesForClosure(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should ReOpen Case', () => {
    const formData = {
      caseRefNo: '67887',
      stageId: 2
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Case re-opened successfully' } },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: ['67887'], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onReOpenCase(formData, 'frm', selectedCase)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Park Case', () => {
    const formData = {
      caseRefNo: '67887'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Case parked successfully' } },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: ['67887'], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onParkCase(formData, selectedCase, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should un-Park Case', () => {
    const formData = {
      caseRefNo: '67887'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Case unparked successfully' } },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: ['67887'], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onUnparkCase(formData, selectedCase, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
