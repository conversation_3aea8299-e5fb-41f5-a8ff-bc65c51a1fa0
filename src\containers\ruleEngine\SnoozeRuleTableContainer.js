import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as snoozeActions from 'actions/ruleSnoozeActions';
import SnoozeRuleTable from 'components/ruleEngine/SnoozeRuleTable';

const mapStateToProps = (state) => ({
  role: state.auth.userCreds.roles,
  snoozeList: state.snoozeRules.list
});

const mapDispatchToProps = (dispatch) => ({
  snoozeActions: bindActionCreators(snoozeActions, dispatch)
});
const SnoozeRuleTableContainer = connect(mapStateToProps, mapDispatchToProps)(SnoozeRuleTable);

export default SnoozeRuleTableContainer;
