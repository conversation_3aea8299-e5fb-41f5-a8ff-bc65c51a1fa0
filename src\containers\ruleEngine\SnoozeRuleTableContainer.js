import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SnoozeRuleTable from 'components/ruleEngine/SnoozeRuleTable';
import * as snoozeActions from 'actions/ruleSnoozeActions';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    snoozeList: state.snoozeRules.list
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    snoozeActions: bindActionCreators(snoozeActions, dispatch)
  };
};
const SnoozeRuleTableContainer = connect(mapStateToProps, mapDispatchToProps)(SnoozeRuleTable);

export default SnoozeRuleTableContainer;
