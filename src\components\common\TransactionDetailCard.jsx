import { faCircleUp, faCircleDown } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { <PERSON>, Badge, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TransactionAcquirerInfo from 'components/common/TransactionAcquirerInfo';
import TransactionPayeeInfo from 'components/common/TransactionPayeeInfo';
import TransactionPayerInfo from 'components/common/TransactionPayerInfo';
import TransactionDetailLoader from 'components/loader/TransactionDetailLoader';
import { checkValue } from 'constants/functions';

import TransactionAdditionalInfo from './TransactionAdditionalInfo';
import TransactionApplicationInfo from './TransactionApplicationInfo';
import TransactionDeviceInfo from './TransactionDeviceInfo';
import TransactionHeaderInfo from './TransactionHeaderInfo';
import TransactionInstrumentInfo from './TransactionInstrumentInfo';

const TransactionDetailCard = ({ hasProvisionalFields, data, action, channel }) => {
  const { details, loader, error, errorMessage } = data;

  const [showMore, setShowMore] = useState(false);
  const verdict =
    details?.ifrmVerdict === 'N/A' ? details?.ifrmPostauthVerdictName : details?.ifrmVerdict;

  const alwaysVisibleItems = [
    {
      key: 'instrument',
      component: (
        <TransactionInstrumentInfo details={details} hasProvisionalFields={hasProvisionalFields} />
      )
    },
    {
      key: 'acquirer',
      component: <TransactionAcquirerInfo details={details} />
    },
    {
      key: 'payee',
      component: <TransactionPayeeInfo details={details} />
    },
    {
      key: 'payer',
      component: <TransactionPayerInfo details={details} />
    }
  ];

  const expandedItems = [
    {
      key: 'device',
      component: <TransactionDeviceInfo details={details} />
    },
    {
      key: 'application',
      component: <TransactionApplicationInfo details={details} />
    },
    {
      key: 'header',
      component: <TransactionHeaderInfo details={details} />
    },
    {
      key: 'additional',
      component: <TransactionAdditionalInfo details={details} />
    }
  ];

  return (
    <CardContainer
      withAddToList={true}
      title={
        <span>
          <span>Transaction Detail </span>
          {!_.isEmpty(details) && (
            <>
              <span className="ms-3">
                {checkValue(details.transactionInfo, 'txnId') &&
                  `[${details.transactionInfo.txnId}]`}
              </span>
              {channel !== 'str' && (
                <Badge
                  className="ms-3"
                  color={_.lowerCase(verdict) === 'accepted' ? 'success' : 'danger'}>
                  {verdict}
                </Badge>
              )}
            </>
          )}
        </span>
      }
      action={action}>
      <Card>
        {loader && <TransactionDetailLoader />}

        {!loader && error && <div className="no-data-div">{errorMessage}</div>}

        {!loader && !error && _.isEmpty(details) && (
          <div className="no-data-div">No transaction details available</div>
        )}
        {!loader && !error && !_.isEmpty(details) && (
          <div className="txn-info transaction-detail">
            <div className="transaction-grid">
              {alwaysVisibleItems.map((item) => (
                <React.Fragment key={item.key}>{item.component}</React.Fragment>
              ))}
              {showMore &&
                expandedItems.map((item) => (
                  <React.Fragment key={item.key}>{item.component}</React.Fragment>
                ))}
            </div>
            {!showMore && (
              <div className="d-flex justify-content-center mb-4">
                <Button color="link" onClick={() => setShowMore(true)}>
                  Show More <FontAwesomeIcon icon={faCircleDown} className="ms-2" />
                </Button>
              </div>
            )}
            {showMore && (
              <div className="d-flex justify-content-center">
                <Button color="link" onClick={() => setShowMore(!showMore)}>
                  Show Less <FontAwesomeIcon icon={faCircleUp} className="ms-2" />
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>
    </CardContainer>
  );
};

TransactionDetailCard.propTypes = {
  hasProvisionalFields: PropTypes.number.isRequired,
  data: PropTypes.object.isRequired,
  action: PropTypes.element,
  channel: PropTypes.string.isRequired
};

export default TransactionDetailCard;
