import { isCooperative } from 'constants/publicKey';

//Status codes
export const STATUS_SUCCESS = 200;
export const STATUS_UNAUTHORIZED = 401;
export const INTERNAL_SERVER_ERROR = 500;
export const BAD_REQUEST = 400;
export const FORBIDDEN = 403;
export const NOT_FOUND = 404;

//Other constants
export const RADIX = 10;
export const BASE_URL = 'http://localhost:8002/';
export const URL_CONTEXT = '/api/v1/ifrm/';

export const EMAIL_APPEND_TEXT = '';
export const USERNAME_APPEND_TEXT = '';

export const MODULES_LIST = [
  {
    name: 'Dashboard',
    route: '/dashboard',
    role: ['investigator', 'checker'],
    channel: ['frm'],
    moduleType: ['issuer', 'acquirer'],
    loginType: ['pulse', 'bank']
  },
  {
    name: 'Dashboard',
    route: '/dashboard',
    role: ['supervisor'],
    channel: ['frm'],
    moduleType: ['issuer', 'acquirer'],
    loginType: ['qrt']
  },
  {
    name: 'Dashboard',
    route: '/dashboard',
    role: isCooperative
      ? ['principal-officer', 'maker']
      : ['principal-officer', 'maker', 'checker'],
    channel: ['str'],
    moduleType: ['issuer', 'acquirer'],
    loginType: ['bank']
  },
  {
    name: 'Users',
    route: '/users',
    role: ['super-admin', 'admin'],
    channel: [],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse', 'qrt']
  },
  {
    name: 'Users',
    route: '/employees',
    role: ['supervisor'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse', 'bank', 'qrt']
  },
  {
    name: 'Banks',
    route: '/banks',
    role: ['super-admin'],
    channel: [],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse']
  },
  {
    name: 'Search',
    route: '/search',
    role: isCooperative
      ? ['reviewer', 'maker', 'investigator', 'supervisor']
      : ['reviewer', 'maker', 'checker', 'investigator', 'supervisor'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['bank', 'qrt']
  },
  {
    name: 'Monitoring',
    route: '/monitor',
    role: ['monitoring'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse', 'bank']
  },
  {
    name: 'Review',
    route: '/review',
    role: ['reviewer'],
    channel: ['frm'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['qrt']
  },
  {
    name: 'Cases',
    route: '/cases',
    role: ['principal-officer'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['bank']
  },
  {
    name: 'Cases',
    route: '/cases',
    role: ['supervisor'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['qrt']
  },
  {
    name: 'Investigation',
    route: '/investigation',
    role: isCooperative ? ['maker', 'investigator'] : ['maker', 'checker', 'investigator'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['bank']
  },
  {
    name: 'Release Funds',
    route: '/release-funds',
    role: ['checker'],
    channel: ['frm'],
    moduleType: ['acquirer'],
    loginType: ['pulse']
  },
  {
    name: 'Prefilters',
    route: '/prefilters',
    role: ['supervisor', 'maker', 'checker', 'investigator', 'reviewer'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse', 'bank', 'qrt']
  },
  {
    name: 'Profiling',
    route: '/profiling',
    role: isCooperative
      ? ['supervisor', 'investigator', 'maker']
      : ['supervisor', 'maker', 'checker', 'investigator'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['bank']
  },
  {
    name: 'Reports',
    route: '/reports',
    role: ['supervisor', 'checker'],
    channel: ['frm'],
    moduleType: ['acquirer'],
    loginType: ['pulse']
  },
  {
    name: 'RFI',
    route: '/rfi',
    role: ['maker', 'checker', 'principal-officer', 'supervisor'],
    channel: ['str'],
    moduleType: ['acquirer'],
    loginType: ['pulse', 'bank']
  },
  {
    name: 'Rules',
    route: '/dsl',
    role: ['supervisor', 'checker'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse']
  },
  {
    name: 'Audit',
    route: '/audit',
    role: ['auditor'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse', 'bank']
  },
  {
    name: 'SCP',
    route: '/settings',
    role: ['admin', 'supervisor'],
    channel: ['frm', 'str'],
    moduleType: ['acquirer', 'issuer'],
    loginType: ['pulse']
  }
];

export const USER_LIST_HEADER = [
  { Header: 'User Name', accessor: 'userName' },
  { Header: 'First Name', accessor: 'firstName' },
  { Header: 'Last Name', accessor: 'lastName' },
  { Header: 'Email', accessor: 'email' },
  { Header: 'Partner', accessor: 'partnerName' }
];

export const COGNITIVE_CREDIBILITY_ARRAY = [
  { label: 'High', background: 'success', color: 'color-success' },
  { label: 'Low', background: 'danger', color: 'color-danger' }
];

export const VERDICT_ARRAY = [
  { label: 'Allow', background: 'success', color: 'color-success' },
  { label: 'Stop', background: 'danger', color: 'color-danger' }
];

//UPI
export const UPI_TRANSACTION_HISTORY_HEADER = [
  { Header: 'ID', accessor: 'txnId' },
  { Header: 'RRN', accessor: 'rrn' },
  { Header: 'Transaction Type', accessor: 'txnTypeName', minWidth: 60 },
  { Header: 'App Name', accessor: 'pspName', minWidth: 60 },
  { Header: 'Payee VPA', accessor: 'payeeVirtualAdd', minWidth: 100 },
  { Header: 'Timestamp', accessor: 'txnTime', minWidth: 110 },
  { Header: 'Amount', accessor: 'txnAmount', Cell: ({ value }) => `Rs.${value}`, minWidth: 80 },
  { Header: 'Device Type', accessor: 'deviceType' },
  { Header: 'Sim ID', accessor: 'simId' },
  { Header: 'Device ID', accessor: 'deviceId' },
  { Header: 'Device OS', accessor: 'os' },
  { Header: 'Location', accessor: 'location' },
  { Header: 'Result', accessor: 'ifrmTransactionResult', minWidth: 80 }
];

export const BUCKET_NAMES = {
  masterQueue: { title: 'Master Queue', id: 'MasterQueue' },
  newCases: { title: 'New Cases', id: 'New' },
  openCases: { title: 'Open Cases', id: 'Open' },
  escalateCases: { title: 'Escalate Cases', id: 'Escalate' },
  pendingCases: { title: 'Pending Cases', id: 'Pending' },
  rejectedCases: { title: 'Rejected Cases', id: 'Rejected' },
  closedCases: { title: 'Closed Cases', id: 'Closed' },
  releaseFunds: { title: 'Release Funds', id: 'ReleaseFunds' },
  parkedCases: { title: 'Parked Cases', id: 'Parked' },
  unverified: { title: 'Unverified Cases', id: 'Unverified' },
  priority: { title: 'Priority Cases', id: 'Priority' }
};

export const IDEL_TIMER_VALUES = {
  timeRemaining: 1800000,
  interval: 5000,
  showLogoutModal: 60000,
  debounce: 20000
};

export const MAX_AMOUNT = 1000000000000;

export const ATTRIBUTE_LIST_OPERATORS = [
  { key: '=', value: 'equalTo', dataType: ['text', 'int', 'numeric'] },
  { key: '>', value: 'greaterThan', dataType: ['int', 'numeric'] },
  { key: '<', value: 'lessThan', dataType: ['int', 'numeric'] },
  { key: '>=', value: 'greaterThanEqualTo', dataType: ['int', 'numeric'] },
  { key: '<=', value: 'lessThanEqualTo', dataType: ['int', 'numeric'] },
  { key: '!=', value: 'notEqualTo', dataType: ['text', 'int', 'numeric'] }
];

export const ATTRIBUTES_VALUE_VALIDATION_PATTERNS = {
  alphanumeric: /^[\w\-\d]+$/,
  integer: /^[+-]?[\d]+$/,
  decimal: /^[+-]?\d+(\.\d+)?$/,
  default: /^$/
};

export const PROFILING_TABLE_HEADER = [
  { Header: 'Entity Name', accessor: 'entityName' },
  { Header: 'Total Active Cases', accessor: 'totalCases' },
  { Header: 'Total Amount', accessor: 'totalAmount' },
  { Header: 'Rules Breached', accessor: 'rulesBreached' }
];
