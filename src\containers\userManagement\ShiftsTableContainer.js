import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onToggleShiftModal } from 'actions/toggleActions';
import {
  onAddShift,
  onFetchShifts,
  onDeleteShift,
  onUpdateShift
} from 'actions/userManagementActions';
import ShiftsTable from 'components/userManagement/ShiftsTable';

const mapStateToProps = (state) => ({
  shiftslist: state.user.shifts,
  showShiftModal: state.toggle.shiftModal,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  addShift: bindActionCreators(onAddShift, dispatch),
  fetchShifts: bindActionCreators(onFetchShifts, dispatch),
  toggleShiftModal: bindActionCreators(onToggleShiftModal, dispatch),
  deleteShift: bindActionCreators(onDeleteShift, dispatch),
  updateShift: bindActionCreators(onUpdateShift, dispatch)
});

const ShiftsTableContainer = connect(mapStateToProps, mapDispatchToProps)(ShiftsTable);

export default ShiftsTableContainer;
