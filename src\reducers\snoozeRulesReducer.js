import objectAssign from 'object-assign';

import {
  ON_FETCH_SNOOZE_RULE_LIST_LOADING,
  ON_FETCH_SNOOZE_RULE_LIST_SUCCESS,
  ON_FETCH_SNOOZE_RULE_LIST_FAILURE,
  ON_FETCH_SNOOZE_ATTRIBUTES_LOADING,
  ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS,
  ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function snoozeRulesReducer(state = initialState.snoozeRules, action) {
  switch (action.type) {
    case ON_FETCH_SNOOZE_ATTRIBUTES_LOADING:
      return objectAssign({}, state, {
        attributes: { list: [], loader: true, error: false, errorMessage: '' }
      });
    case ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS:
      return objectAssign({}, state, {
        attributes: objectAssign({}, state.attributes, {
          list: action.response,
          loader: false
        })
      });
    case ON_FETCH_SNOOZE_ATTRIBUTES_FAILURE:
      return objectAssign({}, state, {
        attributes: objectAssign({}, state.attributes, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_SNOOZE_RULE_LIST_LOADING:
      return objectAssign({}, state, {
        list: objectAssign({}, state.list, { loader: true, error: false, errorMessage: '' })
      });
    case ON_FETCH_SNOOZE_RULE_LIST_SUCCESS:
      return objectAssign({}, state, {
        list: objectAssign({}, state.list, {
          [action.channel]: action.response,
          loader: false
        })
      });
    case ON_FETCH_SNOOZE_RULE_LIST_FAILURE:
      return objectAssign({}, state, {
        list: objectAssign({}, state.list, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
