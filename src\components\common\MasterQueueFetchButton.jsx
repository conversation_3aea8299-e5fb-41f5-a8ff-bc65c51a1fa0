import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh } from '@fortawesome/free-solid-svg-icons';

function MasterQueueFetchButton({ onFetchCasesFromMasterQueue }) {
  return (
    <Button
      outline
      size="sm"
      color="secondary"
      className="ms-1"
      title="Fetch cases from master queue"
      onClick={onFetchCasesFromMasterQueue}>
      <FontAwesomeIcon icon={faRefresh} />
    </Button>
  );
}

MasterQueueFetchButton.propTypes = {
  onFetchCasesFromMasterQueue: PropTypes.func.isRequired
};

export default MasterQueueFetchButton;
