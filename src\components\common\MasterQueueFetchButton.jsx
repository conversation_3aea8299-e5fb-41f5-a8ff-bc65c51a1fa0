import { faRefresh } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { Button } from 'reactstrap';

function MasterQueueFetchButton({ onFetchCasesFromMasterQueue }) {
  return (
    <Button
      outline
      size="sm"
      color="secondary"
      className="ms-1"
      title="Fetch cases from master queue"
      onClick={onFetchCasesFromMasterQueue}>
      <FontAwesomeIcon icon={faRefresh} />
    </Button>
  );
}

MasterQueueFetchButton.propTypes = {
  onFetchCasesFromMasterQueue: PropTypes.func.isRequired
};

export default MasterQueueFetchButton;
