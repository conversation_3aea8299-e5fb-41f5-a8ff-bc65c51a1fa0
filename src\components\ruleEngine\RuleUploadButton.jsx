import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUpload } from '@fortawesome/free-solid-svg-icons';

function RuleUploadButton({ channel, uploadRuleList }) {
  const [fileData, setFileData] = useState(null);

  useEffect(() => {
    if (fileData) {
      uploadRuleList({ file: fileData }, channel);
      setFileData(null);
    }
  }, [fileData]);

  return (
    <label
      htmlFor="fileUploader"
      className="btn btn-sm btn-outline-primary me-1"
      title="Supports: CSV">
      <Input
        type="file"
        name="fileUploader"
        id="fileUploader"
        accept="text/csv, .csv"
        files={fileData}
        onChange={(event) => setFileData(event.target.files[0])}
        hidden
      />
      <FontAwesomeIcon icon={faUpload} /> Upload Rules
    </label>
  );
}

RuleUploadButton.propTypes = {
  channel: PropTypes.string.isRequired,
  uploadRuleList: PropTypes.func.isRequired
};

export default RuleUploadButton;
