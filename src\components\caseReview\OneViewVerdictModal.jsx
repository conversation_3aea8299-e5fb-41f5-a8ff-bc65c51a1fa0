import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

function OneViewVerdictModal({
  isOpen,
  theme,
  verdict,
  notations,
  fraudTypes,
  closeCase,
  toggleModal,
  fetchNotationsList,
  fetchFraudTypesList
}) {
  const [fraudType, setFraudType] = useState('');
  const [commentOption, setCommentOption] = useState('');
  const [comment, setComment] = useState('');
  const { master } = notations;

  useEffect(() => {
    const { master, loader: notationLoader, error: notationError } = notations;
    const { loader, error, list } = fraudTypes;
    if (master?.list.length === 0 && (!notationLoader || !notationError)) fetchNotationsList();

    if (list.length === 0 && (!loader || !error)) fetchFraudTypesList();
  }, [notations, fraudTypes, fetchNotationsList, fetchFraudTypesList]);

  const fraudVerdict =
    fraudTypes.list.length > 0 && fraudTypes.list.find((d) => d.verdict.includes('Fraud'));

  const nonFraudVerdict =
    fraudTypes.list.length > 0 && fraudTypes.list.find((d) => d.verdict.includes('Genuine'));

  const fraudTypeOptions = fraudVerdict?.types?.map((fraud) => (
    <option key={fraud.id} value={fraud.id}>
      {fraud.fraudName} ({fraud?.fraudDesc})
    </option>
  ));

  const getOptionsList = (notations = []) =>
    notations
      .filter((notation) => notation.category === verdict.replaceAll(' ', ''))
      .map((notation) => <option key={notation.id}>{notation.notation}</option>);

  const submit = (e) => {
    e.preventDefault();
    closeCase(e);
    setCommentOption('');
    setComment('');
  };

  return (
    <ModalContainer
      header={`Mark as ${verdict}`}
      size="lg"
      theme={theme}
      isOpen={isOpen}
      toggle={toggleModal}>
      <form onSubmit={submit}>
        <Input
          type="hidden"
          name="investigationVerdict"
          id="investigationVerdict"
          defaultValue={verdict === 'Fraud' ? fraudVerdict.id : nonFraudVerdict.id}
        />
        {verdict === 'Fraud' && (
          <FormGroup>
            <Label for="fraudType">Fraud type</Label>
            <Input
              type="select"
              name="fraudType"
              id="fraudType"
              onChange={(e) => setFraudType(e.target.value)}
              value={fraudType || ''}
              required>
              {(() => {
                if (fraudTypes.loader)
                  return (
                    <option key="loading" value="">
                      Loading...
                    </option>
                  );

                if (fraudTypes.error)
                  return (
                    <option key="error" value="">
                      {fraudTypes.errorMessage}
                    </option>
                  );

                return fraudTypeOptions;
              })()}
            </Input>
          </FormGroup>
        )}
        <FormGroup>
          <Label for="commentOption">Notation</Label>
          <Input
            type="select"
            name="commentOption"
            id="commentOption"
            value={commentOption}
            onChange={(e) => setCommentOption(e.target.value)}
            required>
            <option value="">-- SELECT --</option>
            {(() => {
              if (master.loader)
                return (
                  <option value="" disabled>
                    Loading...
                  </option>
                );

              if (master.error)
                return (
                  <option value="" disabled>
                    {master.errorMessage}
                  </option>
                );

              return getOptionsList(master.list);
            })()}
            <option>Others</option>
          </Input>
        </FormGroup>
        {commentOption === 'Others' && (
          <FormGroup>
            <Label for="comment">Comment</Label>
            <Input
              type="text"
              name="comment"
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              required
            />
          </FormGroup>
        )}
        <FormGroup className="d-flex justify-content-end">
          <Button color="primary" size="sm">
            Submit
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
}

OneViewVerdictModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  theme: PropTypes.string.isRequired,
  verdict: PropTypes.string.isRequired,
  notations: PropTypes.object.isRequired,
  fraudTypes: PropTypes.object.isRequired,
  closeCase: PropTypes.func.isRequired,
  toggleModal: PropTypes.func.isRequired,
  fetchNotationsList: PropTypes.func.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired
};

export default OneViewVerdictModal;
