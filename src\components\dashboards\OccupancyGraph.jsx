import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import GraphContainer from 'components/common/GraphContainer';

// Helper function to determine split number for gauge
function getSplitNumber(totalUser) {
  if (totalUser % 10 === 0) return 5;

  if (totalUser % 2 === 0) return 4;

  if (totalUser < 10) return totalUser;

  return 1;
}

function Occupancy({ theme, occupancyRate, fetchOccupancyRate }) {
  useEffect(() => {
    fetchOccupancyRate();
  }, []);

  const config = {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        startAngle: 200,
        endAngle: -20,
        min: 0,
        max: occupancyRate.data.totalUser ?? 0,
        splitNumber: getSplitNumber(occupancyRate.data.totalUser),
        progress: {
          show: true,
          width: 30
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 30
          }
        },
        axisTick: {
          show: false,
          distance: -45,
          splitNumber: 5,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        splitLine: {
          show: false,
          distance: -52,
          length: 14,
          lineStyle: {
            width: 3,
            color: '#999'
          }
        },
        axisLabel: {
          distance: 10,
          color: '#999'
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          fontWeight: 'bolder',
          formatter: '{value}',
          color: 'inherit'
        },
        data: [
          {
            value: occupancyRate.data.logedInUser ?? 0
          }
        ]
      }
    ]
  };

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title={
        <span>
          Active Operators <small>(in current shift)</small>
        </span>
      }
      noData={false}
      loader={occupancyRate.loader}
      error={{ flag: occupancyRate.error, errorMessage: occupancyRate.errorMessage }}
    />
  );
}

Occupancy.propTypes = {
  theme: PropTypes.string.isRequired,
  occupancyRate: PropTypes.object.isRequired,
  fetchOccupancyRate: PropTypes.func.isRequired
};

export default Occupancy;
