import * as types from 'constants/actionTypes';
import alertReducer from 'reducers/alertReducer';
import initialState from 'reducers/initialState';

describe('Alert reducer', () => {
  it('should return the intial state', () => {
    expect(alertReducer(undefined, {})).toEqual(initialState.alert);
  });

  it('should handle ON_SUCCESS_ALERT', () => {
    expect(
      alertReducer(
        {},
        {
          type: types.ON_SUCCESS_ALERT,
          response: { message: 'success message' }
        }
      )
    ).toEqual({
      type: 'alert-success',
      message: 'success message',
      show: true
    });
  });

  it('should handle ON_FAILURE_ALERT', () => {
    expect(
      alertReducer(
        {
          type: 'alert-success',
          message: 'success message',
          show: true
        },
        {
          type: types.ON_FAILURE_ALERT,
          response: { message: 'failure message' }
        }
      )
    ).toEqual({
      type: 'alert-danger',
      message: 'failure message',
      show: true
    });
  });

  it('should handle ON_WARNING_ALERT', () => {
    expect(
      alertReducer(
        {},
        {
          type: types.ON_WARNING_ALERT,
          response: { message: 'warning message' }
        }
      )
    ).toEqual({
      type: 'alert-warning',
      message: 'warning message',
      show: true
    });
  });

  it('should handle ON_CLEAR_ALERT', () => {
    expect(
      alertReducer(
        {
          type: 'alert-success',
          message: 'success message',
          show: true
        },
        {
          type: types.ON_CLEAR_ALERT
        }
      )
    ).toEqual(initialState.alert);
  });
});
