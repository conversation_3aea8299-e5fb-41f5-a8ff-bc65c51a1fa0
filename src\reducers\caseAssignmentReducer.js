import {
  ON_<PERSON><PERSON><PERSON>_SELECTED_CASE,
  ON_<PERSON>ET<PERSON>_CASE_DETAIL_FAILURE,
  ON_SUCCESSFUL_FETCH_CASE_DETAIL,
  ON_LIABILITY_LIST_FETCH_FAILURE,
  ON_LIABILITY_LIST_FETCH_LOADING,
  ON_FRAUD_TYPE_LIST_FETCH_FAILURE,
  ON_FRAUD_TYPE_LIST_FETCH_LOADING,
  ON_SUCCESSFUL_LIABILITY_LIST_FETCH,
  ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH,
  ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING,
  ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE,
  ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS,
  ON_BUCKETS_FETCH_LOADING,
  ON_SUCCESSFUL_BUCKETS_FETCH,
  ON_BUCKETS_FETCH_FAILURE,
  ON_CASES_FETCH_LOADING,
  ON_SUCCESSFUL_CASES_FETCH,
  ON_CASES_FETCH_FAILURE,
  ON_<PERSON>ETCH_CLOSURE_CASES_FAILURE,
  ON_FETCH_CLOSURE_CASES_SUCCESS,
  ON_FETCH_CLOSURE_CASES_LOADING,
  ON_CLEAR_BULK_CLOSURE_SEARCH,
  ON_FETCH_CLOSE_CASE_BUCKETS_LOADING,
  ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS,
  ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS,
  ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE,
  ON_REMOVE_CASES_FROM_LIST,
  ON_FETCH_XCHANNEL_LIST_LOADING,
  ON_FETCH_XCHANNEL_LIST_SUCCESS,
  ON_FETCH_XCHANNEL_LIST_FAILURE,
  ON_FETCH_TXN_TYPE_LOADING,
  ON_FETCH_TXN_TYPE_SUCCESS,
  ON_FETCH_TXN_TYPE_FAILURE,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS,
  ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE,
  ON_SUCCESSFUL_CASE_CREATION,
  ON_ADD_CHILD_TXNS_TO_CASE
} from 'constants/actionTypes';
import _ from 'lodash';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function caseAssignmentReducer(state = initialState.caseAssignment, action) {
  switch (action.type) {
    case ON_LIABILITY_LIST_FETCH_LOADING:
      return objectAssign({}, state, {
        liability: objectAssign({}, initialState.caseAssignment.liability, { loader: true })
      });
    case ON_SUCCESSFUL_LIABILITY_LIST_FETCH:
      return objectAssign({}, state, {
        liability: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_LIABILITY_LIST_FETCH_FAILURE:
      return objectAssign({}, state, {
        liability: objectAssign({}, initialState.caseAssignment.liability, {
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FRAUD_TYPE_LIST_FETCH_LOADING:
      return objectAssign({}, state, {
        fraudTypes: objectAssign({}, initialState.caseAssignment.fraudTypes, { loader: true })
      });
    case ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH:
      return objectAssign({}, state, {
        fraudTypes: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FRAUD_TYPE_LIST_FETCH_FAILURE:
      return objectAssign({}, state, {
        fraudTypes: objectAssign({}, initialState.caseAssignment.fraudTypes, {
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING:
      return objectAssign({}, state, {
        pastInvestigations: objectAssign({}, state.pastInvestigations, {
          [action.channel]: objectAssign({}, state.pastInvestigations[action.channel], {
            loader: true,
            error: false,
            errorMessage: ''
          })
        })
      });
    case ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS:
      return objectAssign({}, state, {
        pastInvestigations: objectAssign({}, state.pastInvestigations, {
          [action.channel]: objectAssign({}, state.pastInvestigations[action.channel], {
            entityId: action.entityId,
            count: action.response.count,
            isLastPage: action.response.isLastPage,
            list:
              action.entityId == state.pastInvestigations[action.channel].entityId &&
              action.calledBy === 'byPagination'
                ? _.unionBy(
                    state.pastInvestigations[action.channel].list,
                    action.response.investigatedTxns,
                    'caseRefNo'
                  )
                : action.response.investigatedTxns,
            loader: false,
            pastInvestigationConfig: action
          })
        })
      });
    case ON_FETCH_PAST_INVESTIGATED_TXNS_FAILURE:
      return objectAssign({}, state, {
        pastInvestigations: objectAssign({}, state.pastInvestigations, {
          [action.channel]: objectAssign({}, state.pastInvestigations[action.channel], {
            count:
              action.entityId == state.pastInvestigations[action.channel].entityId
                ? state.pastInvestigations.count
                : 0,
            entityId: action.entityId,
            list:
              action.entityId == state.pastInvestigations[action.channel].entityId
                ? state.pastInvestigations[action.channel].list
                : [],
            isLastPage:
              action.entityId == state.pastInvestigations[action.channel].entityId
                ? state.pastInvestigations[action.channel].isLastPage
                : true,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_CASE_DETAIL_FAILURE:
      return objectAssign({}, state, {
        selectedCase: action.caseDetails
      });
    case ON_SUCCESSFUL_FETCH_CASE_DETAIL:
      return objectAssign({}, state, {
        selectedCase: action.response
      });
    case ON_CLEAR_SELECTED_CASE:
      return objectAssign({}, state, {
        selectedCase: {}
      });
    case ON_SUCCESSFUL_CASE_CREATION:
      return objectAssign({}, state, {
        selectedCase: objectAssign({}, state.selectedCase, {
          investigationStatus: 'Open',
          investigationAssignedTo: action.assignedTo,
          investigationAssignedBy: action.assignedBy
        })
      });
    case ON_BUCKETS_FETCH_LOADING:
      return objectAssign({}, state, {
        buckets: objectAssign({}, state.buckets, {
          [action.channel]: objectAssign({}, state.buckets[action.channel], {
            loader: true,
            error: false,
            errorMessage: ''
          })
        })
      });
    case ON_SUCCESSFUL_BUCKETS_FETCH:
      return objectAssign({}, state, {
        buckets: objectAssign({}, state.buckets, {
          [action.channel]: {
            stats: action.response,
            loader: false,
            error: false,
            errorMessage: ''
          }
        })
      });
    case ON_BUCKETS_FETCH_FAILURE:
      return objectAssign({}, state, {
        buckets: objectAssign({}, state.buckets, {
          [action.channel]: {
            stats: {},
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          }
        })
      });
    case ON_CASES_FETCH_LOADING:
      return objectAssign({}, state, {
        cases: objectAssign({}, state.cases, {
          [action.channel]: objectAssign({}, state.cases[action.channel], { loader: true })
        })
      });
    case ON_SUCCESSFUL_CASES_FETCH:
      return objectAssign({}, state, {
        cases: objectAssign({}, state.cases, {
          [action.channel]: objectAssign({}, state.cases[action.channel], {
            count: action.response.count,
            isLastPage: action.response.isLastPage,
            list: action.refresh
              ? action.response.cases
              : state.cases[action.channel].conf.bucket !== action.conf.bucket ||
                state.cases[action.channel].conf.sortBy !== action.conf.sortBy ||
                state.cases[action.channel].conf.sortOrder !== action.conf.sortOrder ||
                state.cases[action.channel].conf.filterCondition !== action.conf.filterCondition
              ? action.response.cases
              : _.unionBy(state.cases[action.channel].list, action.response.cases, 'txnId'),
            conf: action.conf,
            loader: false,
            error: false,
            errorMessage: ''
          })
        })
      });
    case ON_CASES_FETCH_FAILURE:
      return objectAssign({}, state, {
        cases: objectAssign({}, state.cases, {
          [action.channel]: objectAssign({}, state.cases[action.channel], {
            count: action.refresh
              ? 0
              : state.cases[action.channel].conf == action.conf
              ? state.cases[action.channel].count
              : 0,
            list: action.refresh
              ? []
              : state.cases[action.channel].conf.bucket !== action.conf.bucket ||
                state.cases[action.channel].conf.sortBy !== action.conf.sortBy ||
                state.cases[action.channel].conf.sortOrder !== action.conf.sortOrder ||
                state.cases[action.channel].conf.filterCondition !== action.conf.filterCondition
              ? []
              : state.cases[action.channel].list,
            conf: action.conf,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_REMOVE_CASES_FROM_LIST:
      return objectAssign({}, state, {
        cases: objectAssign({}, state.cases, {
          [action.channel]: objectAssign({}, state.cases[action.channel], {
            list: _.filter(
              state.cases[action.channel].list,
              (listItem) => !_.includes(action.cases, listItem.caseRefNo)
            )
          })
        })
      });
    case ON_FETCH_CLOSURE_CASES_LOADING:
      return objectAssign({}, state, {
        closureCases: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CLOSURE_CASES_SUCCESS:
      return objectAssign({}, state, {
        closureCases: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CLOSURE_CASES_FAILURE:
      return objectAssign({}, state, {
        closureCases: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_CLEAR_BULK_CLOSURE_SEARCH:
      return objectAssign({}, state, {
        closureCases: initialState.caseAssignment.closureCases
      });
    case ON_FETCH_CLOSE_CASE_BUCKETS_LOADING:
      return objectAssign({}, state, {
        closeCaseBuckets: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS:
      return objectAssign({}, state, {
        closeCaseBuckets: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CLOSE_CASE_BUCKETS_FAILURE:
      return objectAssign({}, state, {
        closeCaseBuckets: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING:
      return objectAssign({}, state, {
        fraudTypesWithBuckets: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS:
      return objectAssign({}, state, {
        fraudTypesWithBuckets: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_FAILURE:
      return objectAssign({}, state, {
        fraudTypesWithBuckets: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_XCHANNEL_LIST_LOADING:
      return objectAssign({}, state, {
        xChannel: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_XCHANNEL_LIST_SUCCESS:
      return objectAssign({}, state, {
        xChannel: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_XCHANNEL_LIST_FAILURE:
      return objectAssign({}, state, {
        xChannel: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_TXN_TYPE_LOADING:
      return objectAssign({}, state, {
        txnType: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_TXN_TYPE_SUCCESS:
      return objectAssign({}, state, {
        txnType: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_TXN_TYPE_FAILURE:
      return objectAssign({}, state, {
        txnType: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING:
      return objectAssign({}, state, {
        snoozeConditions: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS:
      return objectAssign({}, state, {
        snoozeConditions: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SNOOZE_CONDITIONS_LIST_FAILURE:
      return objectAssign({}, state, {
        snoozeConditions: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_ADD_CHILD_TXNS_TO_CASE:
      return objectAssign({}, state, {
        selectedCase: objectAssign({}, state.selectedCase, {
          childTxns: _.xor(state.selectedCase.childTxns || [], action.response)
        })
      });
    default:
      return state;
  }
}
