/* eslint-disable react/prop-types */

import { faCaretDown, faCaretRight, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import ReactTable from 'react-table';
import { Card, Button } from 'reactstrap';

import TableLoader from 'components/loader/TableLoader';
import { getScreen } from 'constants/functions';
import CombinedViolationDropdownContainer from 'containers/common/CombinedViolationDropdownContainer';
import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';

const TransactionTable = ({
  ruleNames,
  data,
  role,
  loginType,
  tableActions,
  page,
  pages,
  filtered,
  onPageChange,
  onPageSizeChange,
  onFilteredChange,
  fetchRuleNamesList,
  closeCaseBuckets,
  fetchCloseCaseBuckets,
  channels,
  hasProvisionalFields,
  minRows = 3,
  pageSize = 10,
  pivotBy = null,
  checkboxColumn = {},
  componentHeaders = [],
  displayCaseHeaders = true,
  displayRedirect = true
}) => {
  const history = useHistory();
  const [expanded, setExpanded] = useState({});

  useEffect(() => {
    channels.map((channel) => {
      if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
    });
    if (_.isEmpty(closeCaseBuckets.list) && !closeCaseBuckets.loader && displayCaseHeaders)
      fetchCloseCaseBuckets();
  }, []);

  const getPage = (role, caseItem) => `${getScreen(role)}/${channels[0]}/${caseItem.txnId}`;

  const bucketOptions =
    !closeCaseBuckets.error &&
    closeCaseBuckets.list.map((bucket) => (
      <option key={bucket.id} value={bucket.id}>
        {bucket.name}
      </option>
    ));

  const tableProps = {
    ...(page && { page }),
    ...(pages && { pages }),
    ...(pivotBy && { pivotBy }),
    ...(filtered && { filtered }),
    ...(onPageChange && { onPageChange }),
    ...(onPageSizeChange && { onPageSizeChange }),
    ...(onFilteredChange && { onFilteredChange })
  };

  const headers = [
    {
      expander: true,
      Expander: ({ isExpanded, ...rest }) => {
        if (_.isEmpty(rest.original?.reViolatedRules)) return null;
        else return <FontAwesomeIcon icon={isExpanded ? faCaretDown : faCaretRight} />;
      },
      getProps: (state, rowInfo) => {
        if (rowInfo)
          if (_.isEmpty(rowInfo?.original?.reViolatedRules))
            return {
              onClick: (e) => {
                e.preventDefault();
              }
            };

        return { className: 'cursor-pointer' };
      }
    }
  ];

  !_.isEmpty(checkboxColumn) && headers.push(checkboxColumn);

  displayRedirect &&
    headers.push({
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      width: 40,
      fixed: true,
      Cell: (row) => (
        <Button
          outline
          title="view"
          size="sm"
          color="primary"
          className="ms-auto"
          disabled={role === 'reviewer' && row.original.currentStatus === ''}
          onClick={() => history.push(getPage(role, row.original))}
          onContextMenu={() => window.open(getPage(role, row.original))}>
          <FontAwesomeIcon icon={faChevronRight} />
        </Button>
      )
    });

  !_.isEmpty(tableActions) && headers.push(tableActions);

  headers.push(
    {
      Header: 'Transaction Timestamp',
      accessor: 'txnTimestamp',
      Aggregated: (row) => {
        const min = _.minBy(row.subRows, 'txnTimestamp')?.txnTimestamp;
        const max = _.maxBy(row.subRows, 'txnTimestamp')?.txnTimestamp;

        if (row.subRows.length > 1) {
          if (min && max)
            return `${moment(min).format('YYYY-MM-DD hh:mm A')} - ${moment(max).format(
              'YYYY-MM-DD hh:mm A'
            )}`;

          return '';
        }

        return moment(row.subRows[0]?.txnTimestamp).format('YYYY-MM-DD hh:mm A');
      },
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row?._aggregated
          ? _.chain(row?._subRows)
              .map(
                (subRow) =>
                  subRow &&
                  moment(subRow[filter.id])
                    .format('YYYY-MM-DD hh:mm A')
                    .match(new RegExp(filter.value, 'ig'))
              )
              .reduce((sum, item) => sum || item)
          : row[filter.id] &&
            moment(row[filter.id])
              .format('YYYY-MM-DD hh:mm A')
              .match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Entity', accessor: 'entityId', width: 140, Aggregated: () => '' },
    {
      Header: 'Payee ID',
      accessor: 'payeeId',
      width: 140,
      Aggregated: () => ''
    },
    { Header: 'Transaction ID', accessor: 'txnId', Aggregated: () => '' },
    ...(hasProvisionalFields === 1
      ? [
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute1" />,
            accessor: 'attribute1'
          },
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute2" />,
            accessor: 'attribute2'
          }
        ]
      : []),
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      aggregate: (vals) => _.sum(vals),
      Aggregated: (row) => <span>{row.value}</span>,
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          value={
            !_.isEmpty(_.find(filtered, ['id', 'txnAmount']))
              ? _.find(filtered, ['id', 'txnAmount']).value
              : ''
          }
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Amount Foreign',
      accessor: 'amountForeign',
      aggregate: (vals) => _.sum(vals),
      Aggregated: (row) => <span>{row.value}</span>,
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          value={
            !_.isEmpty(_.find(filtered, ['id', 'amountForeign']))
              ? _.find(filtered, ['id', 'amountForeign']).value
              : ''
          }
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Currency',
      accessor: 'txnCurrency',
      Aggregated: (row) => {
        const unique = _.chain(row.subRows)
          .map((d) => d.txnCurrency)
          .uniq()
          .join(', ')
          .value();
        return unique;
      }
    },
    {
      Header: 'Response Code',
      accessor: 'responseCodeName',
      Aggregated: (row) => {
        const unique = _.chain(row.subRows)
          .map((d) => d.responseCodeName)
          .uniq()
          .join(', ')
          .value();
        return unique;
      }
    },
    {
      Header: 'MCC',
      accessor: 'payeeMccCodeName',
      width: 140,
      Aggregated: () => ''
    },
    {
      Header: 'Channel',
      accessor: 'channelName',
      Aggregated: (row) => {
        const unique = _.chain(row.subRows)
          .map((d) => d.channelName)
          .uniq()
          .join(', ')
          .value();
        return unique;
      }
    },
    {
      Header: 'Payer ID',
      accessor: 'payerId',
      Aggregated: () => ''
    },
    {
      Header: 'Sender Masked Card',
      accessor: 'payerCardNumberMask',
      show: _.lowerCase(loginType) !== 'qrt',
      Aggregated: () => ''
    },
    {
      Header: 'Sender Hashed Card',
      accessor: 'payerCardNumberHash',
      show: _.lowerCase(loginType) !== 'qrt',
      Aggregated: () => ''
    },
    {
      Header: 'Type',
      accessor: 'txnTypeName',
      width: 140,
      Aggregated: () => ''
    },
    {
      Header: 'Customer Account',
      accessor: 'payerAccountNumber',
      show: false,
      Aggregated: () => ''
    },
    {
      Header: 'Beneficiary Account',
      accessor: 'payeeAccountNumber',
      width: 120,
      show: false,
      Aggregated: () => ''
    },
    { Header: 'Device ID', accessor: 'deviceId', show: false, Aggregated: () => '' },
    {
      Header: 'Violated Rules',
      accessor: 'reViolatedRules',
      Aggregated: () => '',
      Cell: ({ value }) => (!_.isEmpty(value) ? _.split(value, ',').length : 0),
      Filter: ({ onChange }) => (
        <CombinedViolationDropdownContainer
          value={
            !_.isEmpty(_.find(filtered, ['id', 'reViolatedRules']))
              ? _.find(filtered, ['id', 'reViolatedRules']).value
              : ''
          }
          onChange={(value) => onChange(value)}
          defaultOption="All"
        />
      )
    },
    {
      Header: 'FRM Action',
      accessor: 'ifrmVerdict',
      Aggregated: (row) => {
        const unique = _.chain(row.subRows)
          .map((d) => d.ifrmVerdict)
          .uniq()
          .join(', ')
          .value();
        return unique;
      },
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(filtered, ['id', 'ifrmVerdict']))
              ? _.find(filtered, ['id', 'ifrmVerdict']).value
              : ''
          }>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    { Header: 'Terminal ID', accessor: 'terminalId', show: false, Aggregated: () => '' }
  );

  displayCaseHeaders &&
    headers.push(
      { Header: 'Case Type', accessor: 'caseType', show: false, Aggregated: () => '' },
      ...(channels[0] === 'str'
        ? [
            {
              Header: 'Suggested Action',
              accessor: 'makerAction',

              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={
                    !_.isEmpty(_.find(filtered, ['id', 'makerAction']))
                      ? _.find(filtered, ['id', 'makerAction']).value
                      : ''
                  }>
                  <option value="">All</option>
                  <option>File STR</option>
                  <option>Close with false positive</option>
                </select>
              )
            }
          ]
        : [
            {
              Header: 'Case Verdict',
              accessor: 'caseVerdict',
              Aggregated: (row) => {
                const unique = _.chain(row.subRows)
                  .map((d) => d.caseVerdict)
                  .uniq()
                  .join(', ')
                  .value();
                return unique;
              },
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={
                    !_.isEmpty(_.find(filtered, ['id', 'caseVerdict']))
                      ? _.find(filtered, ['id', 'caseVerdict']).value
                      : ''
                  }>
                  <option value="">All</option>
                  <option>Confirmed Fraud</option>
                  <option>Undetermined</option>
                  <option>Confirmed Genuine</option>
                  <option>Assumed Genuine</option>
                </select>
              )
            },
            {
              Header: 'Bucket',
              accessor: 'bucketId',
              show: false,
              Aggregated: () => '',
              Cell: ({ value }) => {
                const closeCaseBucket = _.find(
                  closeCaseBuckets.list,
                  (bucket) => bucket.id === value
                );
                return closeCaseBucket ? (
                  <div
                    className={
                      closeCaseBucket.id === 1
                        ? 'color-danger '
                        : closeCaseBucket.id === 2
                          ? 'color-primary '
                          : closeCaseBucket.id === 3
                            ? 'color-warning '
                            : ''
                    }>
                    {closeCaseBucket.name}
                  </div>
                ) : null;
              },
              filterMethod: (filter, row) => row[filter.id] && row[filter.id] === filter.value,
              Filter: ({ onChange }) => (
                <select
                  onChange={(event) => onChange(event.target.value)}
                  value={
                    !_.isEmpty(_.find(filtered, ['id', 'bucketId']))
                      ? _.find(filtered, ['id', 'bucketId']).value
                      : ''
                  }>
                  <option value="">All</option>
                  {bucketOptions}
                </select>
              )
            }
          ]),
      {
        Header: 'Status',
        accessor: 'currentStatus',
        Aggregated: () => '',
        Filter: ({ onChange }) => (
          <select
            onChange={(event) => onChange(event.target.value)}
            value={
              !_.isEmpty(_.find(filtered, ['id', 'currentStatus']))
                ? _.find(filtered, ['id', 'currentStatus']).value
                : ''
            }>
            <option value="">All</option>
            <option>New</option>
            <option>Open</option>
            <option>Pending</option>
            <option>Rejected</option>
            <option>On hold</option>
            <option>Closed</option>
          </select>
        )
      },
      {
        Header: 'Stage',
        accessor: 'currentStage',
        show: _.lowerCase(loginType) !== 'qrt',
        Aggregated: () => ''
      },
      { Header: 'Assigned To', accessor: 'currentlyAssignedTo', Aggregated: () => '' },
      {
        Header: 'Assignment Timestamp',
        accessor: 'assignmentTimeStamp',
        minWidth: 120,
        Aggregated: () => '',
        Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
        filterMethod: (filter, row) =>
          row?._aggregated
            ? _.chain(row?._subRows)
                .map(
                  (subRow) =>
                    subRow &&
                    moment(subRow[filter.id])
                      .format('YYYY-MM-DD hh:mm A')
                      .match(new RegExp(filter.value, 'ig'))
                )
                .reduce((sum, item) => sum || item)
            : row[filter.id] &&
              moment(row[filter.id])
                .format('YYYY-MM-DD hh:mm A')
                .match(new RegExp(filter.value, 'ig'))
      }
    );

  headers.push(...componentHeaders);

  return (
    <div>
      {data.loader ? (
        <TableLoader />
      ) : data.error ? (
        <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>
      ) : _.isEmpty(data.list) ? (
        <div className="no-data-div no-data-card-padding">No transactions found</div>
      ) : (
        <Card className="mt-3">
          <ReactTable
            defaultFilterMethod={(filter, row) =>
              row?._aggregated
                ? _.chain(row?._subRows)
                    .map(
                      (subRow) =>
                        subRow &&
                        _.includes(_.lowerCase(subRow[filter.id]), _.lowerCase(filter.value))
                    )
                    .reduce((sum, item) => sum || item)
                : row[filter.id] &&
                  _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
            }
            columns={headers}
            data={data.list}
            SubComponent={(row) => (
              <ViolatedRuleNameBadgeContainer
                violatedRulesList={row.original?.reViolatedRules || ''}
                taggedRulesList={row.original?.taggedRule || ''}
              />
            )}
            noDataText="No transaction found"
            filterable
            expanded={expanded}
            showPaginationTop={true}
            showPaginationBottom={false}
            defaultPageSize={pageSize}
            minRows={minRows}
            showPageJump={false}
            pageSizeOptions={[5, 10, 20, 30, 40, 50]}
            onExpandedChange={(expanded) => setExpanded(expanded)}
            className="-highlight -striped"
            {...tableProps}
          />
        </Card>
      )}
    </div>
  );
};

TransactionTable.propTypes = {
  page: PropTypes.number,
  pages: PropTypes.number,
  minRows: PropTypes.number,
  pageSize: PropTypes.number,
  pivotBy: PropTypes.array,
  filtered: PropTypes.array,
  onPageChange: PropTypes.func,
  tableActions: PropTypes.object,
  onPageSizeChange: PropTypes.func,
  onFilteredChange: PropTypes.func,
  componentHeaders: PropTypes.array,
  displayRedirect: PropTypes.bool,
  displayCaseHeaders: PropTypes.bool,
  checkboxColumn: PropTypes.object,
  data: PropTypes.object.isRequired,
  role: PropTypes.string.isRequired,
  loginType: PropTypes.string.isRequired,
  ruleNames: PropTypes.object.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  channels: PropTypes.array.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired
};

export default TransactionTable;
