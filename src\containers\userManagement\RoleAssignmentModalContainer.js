import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onToggleUpdateUserRolesModal } from 'actions/toggleActions';
import { onUpdateUserRoles, onFetchPartnerIdList } from 'actions/userManagementActions';
import RoleAssignmentModal from 'components/userManagement/RoleAssignmentModal';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    roleslist: state.user.roles,
    channelslist: state.user.channels,
    userChannels: state.auth.userCreds.channels,
    showUserModal: state.toggle.updateUserRolesModal,
    partnerList: state.user.partnerIdList,
    loginType: state.auth.loginType,
    hasDualRole: state.user.configurations.dualRole
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    updateUserRoles: bindActionCreators(onUpdateUserRoles, dispatch),
    fetchPartnerList: bindActionCreators(onFetchPartnerIdList, dispatch),
    toggleUpdateUserRolesModal: bindActionCreators(onToggleUpdateUserRolesModal, dispatch)
  };
};

const RoleAssignmentModalContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RoleAssignmentModal);

export default RoleAssignmentModalContainer;
