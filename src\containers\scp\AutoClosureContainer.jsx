import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchFraudTypesList } from 'actions/caseReviewActions';
import AutoClosure from 'components/scp/AutoClosure';

const mapStateToProps = (state) => ({
  fraudTypes: state.caseAssignment.fraudTypes
});

const mapDispatchToProps = (dispatch) => ({
  fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch)
});

const AutoClosureContainer = connect(mapStateToProps, mapDispatchToProps)(AutoClosure);

export default AutoClosureContainer;
