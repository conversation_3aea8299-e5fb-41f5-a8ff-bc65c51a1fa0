import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import GraphContainer from 'components/common/GraphContainer';

function PartnersCasesStats({ theme, formData, partnersCaseStats, fetchPartnersCaseStats }) {
  const partnerNames = Object.keys(partnersCaseStats?.data);

  useEffect(() => {
    if (formData.startDate && formData.endDate) fetchPartnersCaseStats(formData);
  }, [formData]);

  const assignedCountData = partnerNames?.map((d) => ({
    name: d,
    value: partnersCaseStats?.data?.[d].assignedCount || 0
  }));

  const resolvedCountData = partnerNames?.map((d) => ({
    name: d,
    value: partnersCaseStats?.data?.[d].resolvedCount || 0
  }));

  const alertCountData = partnerNames?.map((d) => ({
    name: d,
    value: partnersCaseStats?.data?.[d].alertCount || 0
  }));

  const config = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      data: partnerNames
    },
    series: [
      {
        name: 'Alerts',
        type: 'pie',
        radius: [0, '30%'],
        label: {
          position: 'inner',
          fontSize: 14
        },
        labelLine: {
          show: false
        },
        data: alertCountData
      },
      {
        name: 'Assigned',
        type: 'pie',
        radius: ['35%', '50%'],
        label: {
          formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}: }{c}  {per|{d}%}  ',
          backgroundColor: '#F6F8FC',
          borderColor: '#8C8D8E',
          borderWidth: 1,
          borderRadius: 4,
          rich: {
            a: {
              color: '#6E7079',
              lineHeight: 22,
              align: 'center'
            },
            hr: {
              borderColor: '#8C8D8E',
              width: '100%',
              borderWidth: 1,
              height: 0
            },
            b: {
              color: '#4C5058',
              fontSize: 14,
              fontWeight: 'bold',
              lineHeight: 33
            },
            per: {
              color: '#fff',
              backgroundColor: '#4C5058',
              padding: [3, 4],
              borderRadius: 4
            }
          }
        },
        data: assignedCountData
      },
      {
        name: 'Resolved',
        type: 'pie',
        radius: ['55%', '70%'],
        label: {
          formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}: }{c}  {per|{d}%}  ',
          backgroundColor: '#F6F8FC',
          borderColor: '#8C8D8E',
          borderWidth: 1,
          borderRadius: 4,
          rich: {
            a: {
              color: '#6E7079',
              lineHeight: 22,
              align: 'center'
            },
            hr: {
              borderColor: '#8C8D8E',
              width: '100%',
              borderWidth: 1,
              height: 0
            },
            b: {
              color: '#4C5058',
              fontSize: 14,
              fontWeight: 'bold',
              lineHeight: 33
            },
            per: {
              color: '#fff',
              backgroundColor: '#4C5058',
              padding: [3, 4],
              borderRadius: 4
            }
          }
        },
        data: resolvedCountData
      }
    ]
  };
  return (
    <GraphContainer
      theme={theme}
      config={config}
      className="card-height-500"
      title="Partner-wise Case Stats"
      noData={partnerNames.length === 0}
      loader={partnersCaseStats?.loader}
      error={{ flag: partnersCaseStats?.error, errorMessage: partnersCaseStats?.errorMessage }}
    />
  );
}

PartnersCasesStats.propTypes = {
  theme: PropTypes.string.isRequired,
  formData: PropTypes.object.isRequired,
  partnersCaseStats: PropTypes.object.isRequired,
  fetchPartnersCaseStats: PropTypes.func.isRequired
};

export default PartnersCasesStats;
