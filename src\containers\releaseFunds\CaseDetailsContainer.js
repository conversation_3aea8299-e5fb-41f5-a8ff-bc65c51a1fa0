import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actions from 'actions/caseReviewActions';
import CaseDetails from 'components/releaseFunds/CaseDetails';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    txnDetails: state.transactionDetails,
    selectedCase: state.caseAssignment.selectedCase
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(actions, dispatch)
  };
};

const CaseDetailsContainer = connect(mapStateToProps, mapDispatchToProps)(CaseDetails);

export default CaseDetailsContainer;
