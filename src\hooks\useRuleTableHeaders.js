import keyBy from 'lodash/keyBy';
import React, { useMemo } from 'react';

import {
  getCoreHeaders,
  getFRMHeaders,
  getAssignmentHeader,
  getRuleOrderHeader,
  getLookupHeader,
  getChannelsHeader
} from 'utility/ruleTableHelpers';

export default function useRuleTableHeaders({
  actionHeaders = [],
  typeHeaders = [],
  tableFilters = [],
  channel,
  moduleType,
  hasMakerChecker = false,
  ruleCreation = {}
}) {
  const alertCategoriesMap = useMemo(
    () => keyBy(ruleCreation?.alertCategories, 'id'),
    [ruleCreation?.alertCategories]
  );

  const fraudCategoriesMap = useMemo(
    () => keyBy(ruleCreation?.fraudCategories, 'name'),
    [ruleCreation?.fraudCategories]
  );

  const ruleLabelsMap = useMemo(
    () => keyBy(ruleCreation?.ruleLabels, 'name'),
    [ruleCreation?.ruleLabels]
  );

  const actionsOptions = useMemo(
    () =>
      ruleCreation?.actionList?.map((action) => (
        <option key={action.actionCode} value={action.actionName}>
          {action.actionName}
        </option>
      )),
    [ruleCreation?.actionList]
  );

  const alertCategoryOptions = useMemo(
    () =>
      ruleCreation?.alertCategories
        ?.filter((c) => c.categoryName !== 'NA')
        .map((c) => (
          <option key={c.id} value={c.id}>
            {c.categoryName}
          </option>
        )),
    [ruleCreation?.alertCategories]
  );

  const fraudCategoryOptions = useMemo(
    () =>
      ruleCreation?.fraudCategories
        ?.filter((c) => c.name !== 'NA')
        .map((c) => (
          <option key={c.id} value={c.name}>
            {c.name}
          </option>
        )),
    [ruleCreation?.fraudCategories]
  );

  const labelOptions = useMemo(
    () =>
      ruleCreation?.ruleLabels?.map((label) => (
        <option key={label.id} value={label.name}>
          {label.name}
        </option>
      )),
    [ruleCreation?.ruleLabels]
  );

  const channelOptions = useMemo(
    () =>
      ruleCreation?.ruleChannels
        ?.filter((c) => c.name !== 'NA')
        .map((c) => (
          <option key={c.id} value={c.name}>
            {c.name}
          </option>
        )),
    [ruleCreation?.ruleChannels]
  );

  const headers = useMemo(() => {
    const baseHeaders = [
      ...actionHeaders,
      ...getCoreHeaders(),
      ...typeHeaders,
      ...getFRMHeaders({ channel, moduleType, actionsOptions, tableFilters }),
      ...getAssignmentHeader({ channel, hasMakerChecker, tableFilters }),
      getRuleOrderHeader(tableFilters),
      getLookupHeader({
        Header: 'Category',
        accessor: 'alertCategoryId',
        map: alertCategoriesMap,
        filterList: alertCategoryOptions,
        tableFilters
      }),
      getLookupHeader({
        Header: 'Fraud Category',
        accessor: 'fraudCategory',
        map: fraudCategoriesMap,
        filterList: fraudCategoryOptions,
        tableFilters
      }),
      getChannelsHeader(channelOptions, tableFilters),
      getLookupHeader({
        Header: 'Label',
        accessor: 'label',
        map: ruleLabelsMap,
        filterList: labelOptions,
        tableFilters
      })
    ];

    return baseHeaders;
  }, [
    actionHeaders,
    typeHeaders,
    channel,
    moduleType,
    hasMakerChecker,
    tableFilters,
    actionsOptions,
    alertCategoryOptions,
    fraudCategoryOptions,
    labelOptions,
    channelOptions,
    alertCategoriesMap,
    fraudCategoriesMap,
    ruleLabelsMap
  ]);

  return headers;
}
