import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Row, Col, Input, FormGroup, Label } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';
import AnalystTATTrendContainer from 'containers/dashboards/AnalystTATTrendContainer';
import CaseStatusStats from 'containers/dashboards/CaseStatusStatsContainer';
import EmployeeList from 'containers/dashboards/EmployeeListContainer';
import FirstContactResolutionGraph from 'containers/dashboards/FirstContactResolutionContainer';
import OccupancyGraph from 'containers/dashboards/OccupancyGraphContainer';
import PartnersCasesStatsContainer from 'containers/dashboards/PartnersCasesStatsContainer';
import SLACasesTable from 'containers/dashboards/SLACasesTableContainer';

function ShiftDashboard({ period, theme, userslist, shiftslist, shiftDetails }) {
  const [selectedShift, setSelectedShift] = useState({});
  const [requestData, setRequestData] = useState({});
  const [displayModal, setDisplayModal] = useState(false);
  const [selectedOperator, setSelectedOperator] = useState({});

  useEffect(() => {
    if (shiftDetails?.data?.id) setSelectedShift(shiftDetails.data);
  }, [shiftDetails.data]);

  useEffect(() => {
    const shiftusers = userslist.filter(
      (d) =>
        d.channelRoles?.includes('frm:reviewer') &&
        (selectedShift === undefined ? true : d?.shiftNames?.includes(selectedShift?.shiftName))
    );

    const newRequestData = {
      startDate: period.startDate,
      endDate: period.endDate,
      ...(!isEmpty(selectedShift) && { shift: selectedShift }),
      users: shiftusers
    };

    setRequestData(newRequestData);
  }, [selectedShift, period.startDate, period.endDate, userslist]);

  const updateShift = (id) => setSelectedShift(shiftslist?.find((d) => d?.id === id));

  const shiftDropdown = (
    <FormGroup>
      <Label>Select shift </Label>
      <Input
        type="select"
        id="shift"
        value={selectedShift?.id ?? ''}
        onChange={(e) => updateShift(e.target.value)}>
        <option value="">All shifts</option>
        {shiftslist?.map((d) => (
          <option key={d.id} value={d.id}>{`${d.shiftName} (${d.fromTime} - ${d.toTime})`}</option>
        ))}
      </Input>
    </FormGroup>
  );

  if (userslist.length === 0)
    <div className="no-data-div">
      <p>Unable to fetch user details.</p>
    </div>;

  const onSelectOperation = (id, name) => {
    setSelectedOperator({ id, name });
    setDisplayModal(true);
  };

  return (
    <div className="content-wrapper">
      <Row className="align-items-end">
        <Col md="4" sm="5">
          {shiftDropdown}
        </Col>
      </Row>
      <Row>
        <Col md="12" lg="6">
          <CaseStatusStats formData={requestData} />
        </Col>
        <Col md="4" lg="3">
          <OccupancyGraph />
        </Col>
        <Col md="4" lg="3">
          <FirstContactResolutionGraph formData={requestData} />
        </Col>
        <Col md="4" lg="4">
          <EmployeeList formData={requestData} action={onSelectOperation} />
        </Col>
        <Col md="12" lg="8">
          <SLACasesTable formData={requestData} />
          {isCooperative && <PartnersCasesStatsContainer formData={requestData} />}
        </Col>
      </Row>
      <ModalContainer
        size="lg"
        theme={theme}
        isOpen={displayModal}
        toggle={() => setDisplayModal(!displayModal)}
        header={`${selectedOperator?.name} TAT over period`}>
        <AnalystTATTrendContainer operatorId={selectedOperator.id} />
      </ModalContainer>
    </div>
  );
}

ShiftDashboard.propTypes = {
  period: PropTypes.object.isRequired,
  theme: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  shiftslist: PropTypes.array.isRequired,
  shiftDetails: PropTypes.object.isRequired
};

export default ShiftDashboard;
