'use strict';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { DropdownItem, Button } from 'reactstrap';

import DropdownButton from 'components/common/DropdownButton';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';

const MonitoringDetailPage = ({
  selectedTxn,
  transactionDetailsActions,
  userslist,
  fetchUsersList,
  authDetails,
  createCaseAndAssign
}) => {
  const history = useHistory();
  const { txnId, channel } = useParams();

  useEffect(() => {
    if (txnId !== selectedTxn.transactionId)
      transactionDetailsActions.onFetchTransactionDetails(txnId, channel);

    if (_.isEmpty(userslist)) fetchUsersList();
  }, [userslist, selectedTxn, txnId, transactionDetailsActions, channel, fetchUsersList]);

  const createCase = (userId) => {
    const formData = {
      assignedTo: userId,
      assignedBy: authDetails.userId,
      channel: this.props.match?.params?.channel,
      txnId: selectedTxn.transactionId,
      partnerId: selectedTxn?.identifiers?.partnerId || 0
    };
    createCaseAndAssign(formData);
  };

  const cognitiveResponse = !_.isEmpty(selectedTxn?.details?.cognitiveResponse)
    ? JSON.parse(selectedTxn.details.cognitiveResponse)
    : {};
  const cognitiveViolations = _.isEmpty(cognitiveResponse?.unusualMethods)
    ? []
    : cognitiveResponse.unusualMethods;

  const reviewers = userslist
    .filter((user) => _.includes(user.channelRoles, `${channel}:maker`))
    .map((user) =>
      user.id === authDetails.userId ? (
        <DropdownItem key={user.id} onClick={() => createCase(user.id)}>
          Self
        </DropdownItem>
      ) : (
        <DropdownItem key={user.id} onClick={() => createCase(user.id)}>
          {user.userName}
        </DropdownItem>
      )
    );

  const assignmentAction = <DropdownButton name="Assign to"> {reviewers} </DropdownButton>;

  const displayAssignmentAction = _.isEmpty(selectedTxn?.details?.investigationStatus)
    ? assignmentAction
    : null;

  return (
    <div className="content-wrapper ">
      <div className="d-flex p-2 justify-content-between">
        <Button outline size="sm" onClick={() => history.push('/monitor')}>
          <FontAwesomeIcon icon={faChevronLeft} />
          <span className="ms-4">Back</span>
        </Button>
        {displayAssignmentAction}
      </div>
      <TransactionDetailCardContainer channel={channel} />

      <ViolatedRulesCardContainer
        transactionId={txnId}
        cognitiveViolations={cognitiveViolations}
        channel={channel}
      />
    </div>
  );
};

MonitoringDetailPage.propTypes = {
  userslist: PropTypes.array.isRequired,
  authDetails: PropTypes.object.isRequired,
  selectedTxn: PropTypes.object.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  createCaseAndAssign: PropTypes.func.isRequired,
  transactionDetailsActions: PropTypes.object.isRequired
};

export default MonitoringDetailPage;
