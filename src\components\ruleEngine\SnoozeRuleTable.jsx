import { faCheck, faTimes, faBellSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React from 'react';
import ReactTable from 'react-table';
import { Button, ButtonGroup } from 'reactstrap';

import TableLoader from 'components/loader/TableLoader';

const SnoozeRuleTable = ({ role, channel, snoozeList, snoozeActions }) => {
  const headers = [
    ...(role === 'supervisor'
      ? [
          {
            Header: 'Actions',
            filterable: false,
            sortable: false,
            Cell: (row) => {
              const { isApproved } = row.original;

              if (isApproved === -1)
                return (
                  <ButtonGroup>
                    <Button
                      outline
                      size="sm"
                      color="success"
                      className="me-1"
                      title="Approve request"
                      onClick={() =>
                        snoozeActions.onSnoozeRuleResponse(
                          {
                            code: row.original.code,
                            name: row.original.name,
                            isApproved: 1,
                            snoozeUntil: row.original.snoozeUntil,
                            attributeList: row.original.attributeList,
                            merchantId: row.original.merchantId
                          },
                          channel
                        )
                      }>
                      <FontAwesomeIcon icon={faCheck} />
                    </Button>
                    <Button
                      outline
                      size="sm"
                      color="danger"
                      title="Reject request"
                      onClick={() =>
                        snoozeActions.onSnoozeRuleResponse(
                          {
                            code: row.original.code,
                            name: row.original.name,
                            isApproved: 0,
                            snoozeUntil: row.original.snoozeUntil,
                            attributeList: row.original.attributeList,
                            merchantId: row.original.merchantId
                          },
                          channel
                        )
                      }>
                      <FontAwesomeIcon icon={faTimes} />
                    </Button>
                  </ButtonGroup>
                );

              if (isApproved === 1)
                return (
                  <Button
                    outline
                    size="sm"
                    color="primary"
                    title="Unsnooze rule"
                    onClick={() =>
                      snoozeActions.onUnsnoozeRule(
                        {
                          code: row.original.code,
                          name: row.original.name,
                          attributeList: row.original.attributeList,
                          merchantId: row.original.merchantId
                        },
                        channel
                      )
                    }>
                    <FontAwesomeIcon icon={faBellSlash} />
                  </Button>
                );

              return null;
            }
          }
        ]
      : []),
    { Header: 'Name', accessor: 'name', minWidth: 200 },
    {
      Header: 'Attributes',
      accessor: 'attributeList',
      minWidth: 250,
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => {
        const list = _.split(value, ',');
        return (
          <ul>
            {_.map(list, (d, i) => (
              <li key={i}>{_.replace(d, new RegExp('~', 'g'), ' ')}</li>
            ))}
          </ul>
        );
      }
    },
    {
      Header: 'Status',
      accessor: 'isApproved',
      minWidth: 100,
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => {
        if (value === 1) return <span className="text-success">Approved</span>;
        if (value === 0) return <span className="text-danger">Rejected</span>;
        return <span className="text-warning">Pending</span>;
      }
    },
    { Header: 'Comments', accessor: 'comments', minWidth: 250 },
    {
      Header: 'Snooze till',
      accessor: 'snoozeUntil',
      minWidth: 130,
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A')
    },
    { Header: 'Requested by', accessor: 'userName', minWidth: 130 },
    {
      Header: 'Requested at',
      accessor: 'createdAt',
      minWidth: 130,
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A')
    }
  ];

  if (snoozeList.loader) return <TableLoader />;

  if (snoozeList.error && _.isEmpty(snoozeList[channel]))
    return <div className="no-data-div">{snoozeList.errorMessage}</div>;

  return (
    <ReactTable
      filterable
      defaultFilterMethod={(filter, row) =>
        row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
      }
      columns={headers}
      data={snoozeList[channel]}
      pageSizeOptions={[5, 10, 20, 30, 40, 50]}
      defaultPageSize={10}
      minRows={6}
      showPaginationTop={true}
      showPaginationBottom={false}
      className="-highlight  -striped"
      defaultSorted={[
        {
          id: 'createdAt',
          asc: true
        }
      ]}
    />
  );
};

SnoozeRuleTable.propTypes = {
  role: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  snoozeList: PropTypes.object.isRequired,
  snoozeActions: PropTypes.object.isRequired
};

export default SnoozeRuleTable;
