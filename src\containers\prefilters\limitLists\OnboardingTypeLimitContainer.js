import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import OnboardingTypeLimit from 'components/prefilters/limitLists/OnboardingTypeLimit';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  prefiltersList: state.prefiltersList
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const OnboardingTypeLimitContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(OnboardingTypeLimit);

export default OnboardingTypeLimitContainer;
