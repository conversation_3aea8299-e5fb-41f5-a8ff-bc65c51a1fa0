import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import OnboardingTypeLimit from 'components/prefilters/limitLists/OnboardingTypeLimit';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    prefiltersList: state.prefiltersList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const OnboardingTypeLimitContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(OnboardingTypeLimit);

export default OnboardingTypeLimitContainer;
