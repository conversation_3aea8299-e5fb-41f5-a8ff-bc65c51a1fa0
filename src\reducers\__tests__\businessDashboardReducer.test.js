import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import businessDashboardReducer from 'reducers/businessDashboardReducer';
import initialState from 'reducers/initialState';

describe('business Dashboard Reducer', () => {
  it('should return the intial state', () => {
    expect(businessDashboardReducer(undefined, {})).toEqual(initialState.businessDashboard);
  });

  it('should handle ON_FETCH_BUSINESS_KPI_LOADING', () => {
    expect(
      businessDashboardReducer(
        {
          businessKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_BUSINESS_KPI_LOADING
        }
      )
    ).toEqual({
      businessKpis: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_BUSINESS_KPI_SUCCESS', () => {
    expect(
      businessDashboardReducer(
        {
          businessKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_BUSINESS_KPI_SUCCESS,
          response: responses.businessDashboard.businessKpis
        }
      )
    ).toEqual({
      businessKpis: {
        data: responses.businessDashboard.businessKpis,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_BUSINESS_KPI_FAILURE', () => {
    expect(
      businessDashboardReducer(
        {
          businessKpis: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_BUSINESS_KPI_FAILURE,
          response: { errorMessage: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      businessKpis: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_ACTIONS_SHARE_LOADING', () => {
    expect(
      businessDashboardReducer(
        {
          actionShare: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ACTIONS_SHARE_LOADING
        }
      )
    ).toEqual({
      actionShare: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ACTIONS_SHARE_SUCCESS', () => {
    expect(
      businessDashboardReducer(
        {
          actionShare: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ACTIONS_SHARE_SUCCESS,
          response: responses.businessDashboard.actionShare
        }
      )
    ).toEqual({
      actionShare: {
        data: responses.businessDashboard.actionShare,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ACTIONS_SHARE_FAILURE', () => {
    expect(
      businessDashboardReducer(
        {
          actionShare: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ACTIONS_SHARE_FAILURE,
          response: { errorMessage: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      actionShare: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_NO_VIOLATION_FRAUD_LOADING', () => {
    expect(
      businessDashboardReducer(
        {
          noViolationFraud: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NO_VIOLATION_FRAUD_LOADING
        }
      )
    ).toEqual({
      noViolationFraud: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS', () => {
    expect(
      businessDashboardReducer(
        {
          noViolationFraud: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NO_VIOLATION_FRAUD_SUCCESS,
          response: responses.businessDashboard.actionShare
        }
      )
    ).toEqual({
      noViolationFraud: {
        data: responses.businessDashboard.actionShare,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_NO_VIOLATION_FRAUD_FAILURE', () => {
    expect(
      businessDashboardReducer(
        {
          noViolationFraud: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_NO_VIOLATION_FRAUD_FAILURE,
          response: { errorMessage: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      noViolationFraud: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_RULE_CATEGORY_TREND_LOADING', () => {
    expect(
      businessDashboardReducer(
        {
          ruleCategoryTrend: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_CATEGORY_TREND_LOADING
        }
      )
    ).toEqual({
      ruleCategoryTrend: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_CATEGORY_TREND_SUCCESS', () => {
    expect(
      businessDashboardReducer(
        {
          ruleCategoryTrend: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_CATEGORY_TREND_SUCCESS,
          response: responses.businessDashboard.ruleCategoryTrend
        }
      )
    ).toEqual({
      ruleCategoryTrend: {
        data: responses.businessDashboard.ruleCategoryTrend,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_RULE_CATEGORY_TREND_FAILURE', () => {
    expect(
      businessDashboardReducer(
        {
          ruleCategoryTrend: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_RULE_CATEGORY_TREND_FAILURE,
          response: { errorMessage: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      ruleCategoryTrend: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });

  it('should handle ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING', () => {
    expect(
      businessDashboardReducer(
        {
          highAlertCustomers: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_HIGH_ALERT_CUSTOMERS_LOADING
        }
      )
    ).toEqual({
      highAlertCustomers: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS', () => {
    expect(
      businessDashboardReducer(
        {
          highAlertCustomers: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_HIGH_ALERT_CUSTOMERS_SUCCESS,
          response: responses.businessDashboard.highAlertCustomers
        }
      )
    ).toEqual({
      highAlertCustomers: {
        data: responses.businessDashboard.highAlertCustomers.customers,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE', () => {
    expect(
      businessDashboardReducer(
        {
          highAlertCustomers: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_HIGH_ALERT_CUSTOMERS_FAILURE,
          response: { errorMessage: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      highAlertCustomers: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'Insufficient rights to access data'
      }
    });
  });
});
