import {
  ON_FETCH_STR_REPORT_MASTERS_LOADING,
  ON_FETCH_STR_REPORT_MASTERS_SUCCESS,
  ON_FETCH_STR_REPORT_MASTERS_FAILURE,
  ON_FETCH_STR_REPORT_DETAILS_LOADING,
  ON_FETCH_STR_REPORT_DETAILS_SUCCESS,
  ON_FETCH_STR_REPORT_DETAILS_FAILURE,
  ON_FETCH_STR_REPORT_LOGS_LOADING,
  ON_FETCH_STR_REPORT_LOGS_SUCCESS,
  ON_FETCH_STR_REPORT_LOGS_FAILURE,
  ON_FETCH_STR_REPORTS_LIST_LOADING,
  ON_FETCH_STR_REPORTS_LIST_SUCCESS,
  ON_FETCH_STR_REPORTS_LIST_FAILURE
} from 'constants/actionTypes';
import { onToggleLoader } from 'actions/toggleActions';
import { onShowFailure<PERSON>lert, onShowSuc<PERSON><PERSON><PERSON>t } from 'actions/alertActions';
import { onUpdateCaseDetails } from './caseDetailsActions';
import client from 'utility/apiClient';

function fetchSTRReportMasters() {
  return client({ url: `casereview/str/case/suspicion/master/all` });
}

function onFetchSTRReportMastersLoading() {
  return { type: ON_FETCH_STR_REPORT_MASTERS_LOADING };
}

function onFetchSTRReportMastersSuccess(response) {
  return { type: ON_FETCH_STR_REPORT_MASTERS_SUCCESS, response };
}

function onFetchSTRReportMastersFailure(response) {
  return { type: ON_FETCH_STR_REPORT_MASTERS_FAILURE, response };
}

function onFetchSTRReportMasters() {
  return function (dispatch) {
    dispatch(onFetchSTRReportMastersLoading());
    return fetchSTRReportMasters().then(
      (success) => dispatch(onFetchSTRReportMastersSuccess(success)),
      (error) => dispatch(onFetchSTRReportMastersFailure(error))
    );
  };
}

function submitSTRReport(formData) {
  return client({
    method: 'POST',
    url: `casereview/str/case/suspicion/details`,
    data: formData,
    badRequestMessage: 'Unable to submit STR report request. Please check input data'
  });
}

function onSubmitSTRReport(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitSTRReport(formData)
      .then(
        () => {
          dispatch(onUpdateCaseDetails(formData.caseRefNo, undefined, 'str'));
          dispatch(onFetchSTRReportDetails(formData.caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'Requested STR Report filing' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchSTRReportDetails(caseRefNo) {
  return client({ url: `casereview/fetch/str/case/${caseRefNo}/gos/details` });
}

function onFetchSTRReportDetailsLoading() {
  return { type: ON_FETCH_STR_REPORT_DETAILS_LOADING };
}

function onFetchSTRReportDetailsSuccess(response) {
  return { type: ON_FETCH_STR_REPORT_DETAILS_SUCCESS, response };
}

function onFetchSTRReportDetailsFailure(response) {
  return { type: ON_FETCH_STR_REPORT_DETAILS_FAILURE, response };
}

function onFetchSTRReportDetails(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchSTRReportDetailsLoading());
    return fetchSTRReportDetails(caseRefNo).then(
      (success) => dispatch(onFetchSTRReportDetailsSuccess(success)),
      (error) => dispatch(onFetchSTRReportDetailsFailure(error))
    );
  };
}

function fetchSTRReportLogs(caseRefNo) {
  return client({ url: `casereview/case/fetch/${caseRefNo}/str/download/history` });
}

function onFetchSTRReportLogsLoading() {
  return { type: ON_FETCH_STR_REPORT_LOGS_LOADING };
}

function onFetchSTRReportLogsSuccess(response) {
  return { type: ON_FETCH_STR_REPORT_LOGS_SUCCESS, response };
}

function onFetchSTRReportLogsFailure(response) {
  return { type: ON_FETCH_STR_REPORT_LOGS_FAILURE, response };
}

function onFetchSTRReportLogs(caseRefNo) {
  return function (dispatch) {
    dispatch(onFetchSTRReportLogsLoading());
    return fetchSTRReportLogs(caseRefNo).then(
      (success) => dispatch(onFetchSTRReportLogsSuccess(success)),
      (error) => dispatch(onFetchSTRReportLogsFailure(error))
    );
  };
}

function submitBatchId(formData) {
  return client({
    method: 'POST',
    url: `casereview/case/fetch/str/upload/update`,
    data: formData,
    badRequestMessage: 'Unable to submit batch ID'
  });
}

function onSubmitBatchId(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitBatchId(formData)
      .then(
        () => {
          dispatch(onFetchSTRReportLogs(formData.caseRefNo));
          dispatch(onShowSuccessAlert({ message: 'BatchID updated successfully' }));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchSTRReportsList(formData) {
  return client({ method: 'POST', url: `casereview/case/str/str/history/list`, data: formData });
}

function onFetchSTRReportsListLoading() {
  return { type: ON_FETCH_STR_REPORTS_LIST_LOADING };
}

function onFetchSTRReportsListSuccess(response, conf) {
  return { type: ON_FETCH_STR_REPORTS_LIST_SUCCESS, response, conf };
}

function onFetchSTRReportsListFailure(response, conf) {
  return { type: ON_FETCH_STR_REPORTS_LIST_FAILURE, response, conf };
}

function onFetchSTRReportsList(formData) {
  return function (dispatch) {
    dispatch(onFetchSTRReportsListLoading());
    return fetchSTRReportsList(formData).then(
      (success) => dispatch(onFetchSTRReportsListSuccess(success, formData)),
      (error) => dispatch(onFetchSTRReportsListFailure(error))
    );
  };
}

export {
  onFetchSTRReportMasters,
  onSubmitSTRReport,
  onFetchSTRReportDetails,
  onFetchSTRReportLogs,
  onSubmitBatchId,
  onFetchSTRReportsList
};
