import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchUnverifiedCases } from 'actions/incidentActions';
import UnverifiedCasesTable from 'components/incidents/UnverifiedCasesTable';

const mapStateToProps = (state) => {
  return {
    role: state.auth.userCreds.roles,
    data: state.incidents.unverifiedList,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};
const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchUnverifiedCases, dispatch)
  };
};

const UnverifiedCasesTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(UnverifiedCasesTable);

export default UnverifiedCasesTableContainer;
