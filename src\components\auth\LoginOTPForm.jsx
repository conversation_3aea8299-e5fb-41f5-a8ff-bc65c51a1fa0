import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { Form, FormGroup, Label, InputGroup, Input, Button } from 'reactstrap';

function LoginOTPForm({ userName, submitOTP, backClick }) {
  const [otp, setOtp] = useState('');

  return (
    <Form onSubmit={submitOTP}>
      <Label className="mb-4">Welcome {userName},</Label>
      <FormGroup>
        <Label>Enter OTP</Label>
        <InputGroup>
          <Input
            type="number"
            name="otp"
            id="otp"
            placeholder="Enter OTP"
            onChange={(e) => setOtp(e.target.value)}
            value={otp}
            required
            autoComplete="off"
            pattern="^[0-9]{0,10}$"
            min="0"
          />
        </InputGroup>
      </FormGroup>
      <FormGroup className="d-flex justify-content-center">
        <Button outline color="link" className="me-4" type="button" onClick={backClick}>
          Back
        </Button>
        <Button color="primary">Login</Button>
      </FormGroup>
    </Form>
  );
}

LoginOTPForm.propTypes = {
  userName: PropTypes.string.isRequired,
  submitOTP: PropTypes.func.isRequired,
  backClick: PropTypes.func.isRequired
};

export default LoginOTPForm;
