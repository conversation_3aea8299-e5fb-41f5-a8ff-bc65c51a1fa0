import {
  ON_<PERSON><PERSON><PERSON>_TRANSACTION_DETAIL_LOADING,
  ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL,
  ON_FETCH_TRANSACTION_DETAIL_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function transactionDetailsReducer(state = initialState.transactionDetails, action) {
  switch (action.type) {
    case ON_FETCH_TRANSACTION_DETAIL_LOADING:
      return {
        details: {},
        loader: true,
        error: false,
        errorMessage: ''
      };
    case ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL:
      return {
        details: action.response,
        loader: false,
        error: false,
        errorMessage: ''
      };
    case ON_FETCH_TRANSACTION_DETAIL_FAILURE:
      return {
        details: {},
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      };
    default:
      return state;
  }
}
