import { createStore, compose, applyMiddleware } from 'redux';
import reduxImmutableStateInvariant from 'redux-immutable-state-invariant';
import { persistStore, persistReducer } from 'redux-persist';
import autoMergeLevel2 from 'redux-persist/lib/stateReconciler/autoMergeLevel2';
import storage from 'redux-persist/lib/storage';
import thunk from 'redux-thunk';

import rootReducer from 'reducers';
import crossBrowserListener from 'utility/reduxpersist-listener';

function configureStoreProd() {
  const persistConfig = {
    key: 'ifrm',
    stateReconciler: autoMergeLevel2,
    whitelist: [
      'auth',
      'user',
      'ruleCreation',
      'ruleConfigurator',
      'cognitiveStatus',
      'prefilter',
      'scp',
      'caseAssignment',
      'slaDashboard'
    ],
    storage
  };

  const persistedReducer = persistReducer(persistConfig, rootReducer);

  const middlewares = [thunk];

  const store = createStore(persistedReducer, compose(applyMiddleware(...middlewares)));

  window.addEventListener('storage', crossBrowserListener(store, persistConfig));

  return store;
}

function configureStoreDev() {
  const persistConfig = {
    key: 'ifrm-dev',
    debug: true,
    stateReconciler: autoMergeLevel2,
    whitelist: [
      'auth',
      'user',
      'ruleCreation',
      'ruleConfigurator',
      'cognitiveStatus',
      'prefilter',
      'scp'
    ],
    storage
  };

  const persistedReducer = persistReducer(persistConfig, rootReducer);

  const middlewares = [reduxImmutableStateInvariant(), thunk];

  const composeEnhancers =
    window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({ trace: true, traceLimit: 25 }) || compose;
  const store = createStore(persistedReducer, composeEnhancers(applyMiddleware(...middlewares)));

  window.addEventListener('storage', crossBrowserListener(store, persistConfig));

  if (module.hot)
    module.hot.accept('reducers', () => {
      const nextReducer = require('reducers').default;
      store.replaceReducer(persistReducer(persistConfig, nextReducer));
    });

  return store;
}

const store = process.env.NODE_ENV === 'production' ? configureStoreProd() : configureStoreDev();

export const persistor = persistStore(store);
export default store;
