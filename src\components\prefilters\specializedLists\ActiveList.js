'use strict';
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import Moment from 'moment';
import { Button, FormGroup, Input, ButtonGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencil, faTrash, faSearch } from '@fortawesome/free-solid-svg-icons';

import ConfirmAlert from 'components/common/ConfirmAlert';
import CardContainer from 'components/common/CardContainer';
import ListExcelDownload from 'components/prefilters/ListExcelDownload';
import { isCooperative } from 'constants/publicKey';
import SpecializedListFormContainer from 'containers/prefilters/specializedLists/SpecializedListFormContainer';
import { useInterval } from 'utility/customHooks';

const ActiveList = ({
  toggle,
  toggleActions,
  actions,
  prefiltersList,
  currentPrefilterList,
  listType,
  role,
  partnerIdList,
  userActions
}) => {
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [selectedEntry, setSelectedEntry] = useState({});
  const [isEditmode, setIsEditmode] = useState(false);
  const [tableFilters, setTableFilters] = useState([]);
  const [searchCategoryName, setSearchCategoryName] = useState('');
  const [searchIdentifier, setSearchIdentifier] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [selectedLists, setSelectedLists] = useState([]);
  const [isBulkDelete, setIsBulkDelete] = useState(false);

  const getListData = (searchCategoryName, searchIdentifier, pageNumber, pageRecords) =>
    actions.onFetchSpecializedList({
      listName: currentPrefilterList.prefilterValue,
      categoryName: searchCategoryName,
      identifier: searchIdentifier,
      pageNumber,
      pageRecords
    });

  useEffect(() => {
    document.title = 'BANKiQ FRC | Prefilters - ' + currentPrefilterList.prefilterValue;
    getListData(searchCategoryName, searchIdentifier, pageNo + 1, pageRecords);
    actions.onFetchCategories();
    if (_.isEmpty(partnerIdList) && isCooperative) userActions.onFetchPartnerIdList();
    setTableFilters([]);

    setSearchIdentifier('');
    setSearchCategoryName('');

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [currentPrefilterList.prefilterValue]);

  useInterval(() => {
    if (prefiltersList.specializedList.fileUpload.fileId !== '') {
      actions.onFetchBulkListUploadStatus(
        prefiltersList.specializedList.fileUpload,
        prefiltersList.specializedList.fileUpload.listType
      );
    }
  }, 5000);

  const searchFilterValues = (e) => {
    e.preventDefault();
    setPageNo(() => 0);
    getListData(searchCategoryName, searchIdentifier, 1, pageRecords);
  };

  const handlePageChange = (page) => {
    if (pageNo < page) getListData(searchCategoryName, searchIdentifier, page + 1, pageRecords);

    setPageNo(page);
  };

  const handlePageSizeChange = (page, pageSize) => {
    setPageNo(page);
    setPageRecords(pageSize);
    if (pageRecords < pageSize)
      getListData(searchCategoryName, searchIdentifier, page + 1, pageSize);
  };

  const setUpdatedValue = (filterData) => {
    setSelectedEntry(filterData);
    setIsEditmode(true);
    setSearchIdentifier('');
    setSearchCategoryName('');
    setPageNo(0);
    setPageRecords(10);
  };

  const clearUpdatedValue = () => {
    setSelectedEntry({});
    setIsEditmode(false);
    setSearchIdentifier('');
    setSearchCategoryName('');
    setPageNo(0);
    setPageRecords(10);
  };

  const toggleFilterModal = (type, filterData) => {
    type == 'edit' ? setUpdatedValue(filterData) : clearUpdatedValue();
    toggleActions.onTogglePrefiltersListModal(listType);
  };

  const toggleConfirmAlertModal = (selectedItem) => {
    !isBulkDelete && setSelectedEntry(selectedItem);
    toggleActions.onToggleConfirmAlertModal(listType);
  };

  const toggleStatus = ({
    categoryName,
    identifier,
    amount,
    count,
    startDate,
    endDate,
    isActive,
    partnerId,
    remark
  }) => {
    let formData = {
      categoryName,
      identifier,
      ...(amount && { amount: parseFloat(amount) }),
      ...(count && { count: parseInt(count) }),
      ...(startDate && endDate && { startDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss') }),
      ...(startDate && endDate && { endDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss') }),
      ...(remark && { remark }),
      ...(isCooperative &&
        partnerId && {
          partnerId: Array.isArray(partnerId)
            ? partnerId.map((id) => parseInt(id))
            : [parseInt(partnerId)]
        }),
      isActive: isActive == 0 ? 1 : 0
    };
    actions.onUpdateSpecializedListItem(formData, currentPrefilterList, listType, false);
  };

  let categoryOptions = prefiltersList.category.data.map((category, i) => (
    <option key={category.id + category.categoryName + i} value={category.categoryName}>
      {category.categoryName}
    </option>
  ));

  const partnerIdOptions = partnerIdList.map((partner) => (
    <option key={partner.id} value={partner.id}>
      {partner.partnerName}
    </option>
  ));

  useEffect(() => {
    !_.isEmpty(tableFilters) &&
      !_.isEmpty(selectedLists) &&
      setSelectedLists(filteredPrefilterList(selectedLists));
    selectAll && setSelectAll(false);
  }, [tableFilters]);

  const handleSelectAll = () => {
    if (!selectAll)
      setSelectedLists(
        _.isEmpty(tableFilters)
          ? prefiltersList.specializedList.data.listInfo
          : filteredPrefilterList(prefiltersList.specializedList.data.listInfo)
      );
    else setSelectedLists([]);
    setSelectAll(!selectAll);
  };

  const filteredPrefilterList = (prefiltersList) => {
    const filteredArray = _.filter(prefiltersList, (list) =>
      _.every(
        tableFilters,
        (filter) =>
          _.has(list, filter.id) &&
          _.includes(_.lowerCase(list[filter.id]), _.lowerCase(filter.value))
      )
    );

    return filteredArray;
  };

  const handleCheckboxChange = (list) => {
    if (_.includes(selectedLists, list)) {
      setSelectedLists((prev) =>
        _.filter(prev, (prevList) => prevList.identifier !== list.identifier)
      );
      setSelectAll(false);
    } else {
      const newList = [...selectedLists, list];
      setSelectedLists(newList);
      setSelectAll(newList.length === prefiltersList.specializedList.data.listInfo.length);
    }
  };

  const onDeleteHandler = () => {
    isBulkDelete
      ? actions.onDeleteSpecializedListItemInBulk(selectedLists, currentPrefilterList, listType)
      : actions.onDeleteSpecializedListItem(selectedEntry, currentPrefilterList, '', listType);

    setIsBulkDelete(false);
    setSelectedLists([]);
    setSelectAll(false);

    setSelectedEntry({});
    setTableFilters([]);
  };

  const header = [
    {
      Header: (
        <Input
          bsSize="md"
          type="checkbox"
          id="selectAll"
          onChange={handleSelectAll}
          checked={selectAll}
          disabled={prefiltersList.specializedList.data.listInfo.length === 0}
        />
      ),
      accessor: '',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 50,
      show: role == 'checker',

      Cell: (row) => (
        <Input
          type="checkbox"
          value={row.original.identifier}
          id={row.original.identifier}
          name="tableSelect[]"
          checked={_.includes(selectedLists, row.original)}
          onChange={() => handleCheckboxChange(row.original)}
        />
      )
    },
    {
      Header: 'Actions',
      minWidth: 80,
      filterable: false,
      sortable: false,
      show: isCooperative
        ? _.includes(['checker'], role)
        : _.includes(['checker', 'investigator'], role),
      Cell: (row) => (
        <span className="d-flex justify-content-start">
          <Button
            outline
            size="sm"
            color="warning"
            title="edit"
            className="me-2"
            onClick={() => toggleFilterModal('edit', row.original)}>
            <FontAwesomeIcon icon={faPencil} />
          </Button>
          <Button
            outline
            size="sm"
            color="danger"
            title="Delete"
            onClick={() => toggleConfirmAlertModal(row.original)}>
            <FontAwesomeIcon icon={faTrash} />
          </Button>
        </span>
      )
    },
    {
      Header: 'Category',
      accessor: 'categoryName',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'categoryName']))
              ? _.find(tableFilters, ['id', 'categoryName']).value
              : ''
          }>
          <option value="">All</option>
          {categoryOptions}
        </select>
      )
    },
    { Header: 'Identifier', accessor: 'identifier' },
    { Header: 'Amount', accessor: 'amount' },
    { Header: 'Count', accessor: 'count' },
    {
      Header: 'Start Date',
      accessor: 'startDate',
      minWidth: 110,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] && Moment(row[filter.id]).format('YYYY-MM-DD') === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="date"
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'startDate']))
              ? _.find(tableFilters, ['id', 'startDate']).value
              : ''
          }
        />
      )
    },
    {
      Header: 'End Date',
      accessor: 'endDate',
      minWidth: 110,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] && Moment(row[filter.id]).format('YYYY-MM-DD') === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="date"
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'endDate']))
              ? _.find(tableFilters, ['id', 'endDate']).value
              : ''
          }
        />
      )
    },
    { Header: 'Remark', accessor: 'remark' },
    ...(isCooperative && role == 'checker'
      ? [
          {
            Header: 'Partner Name',
            accessor: 'partnerId',
            // eslint-disable-next-line react/prop-types
            Cell: ({ value }) => {
              const partner = partnerIdList.find((d) => d.id == value);
              return !_.isEmpty(partner) ? <span>{partner.partnerName}</span> : null;
            },
            filterMethod: (filter, row) => row[filter.id] == filter.value,
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'partnerId']))
                    ? _.find(tableFilters, ['id', 'partnerId']).value
                    : ''
                }>
                <option value="">All</option>
                {partnerIdOptions}
              </select>
            )
          }
        ]
      : []),
    { Header: 'Added by', accessor: 'userName' },
    {
      Header: 'Added on',
      accessor: 'updatedTimeStamp',
      minWidth: 110,
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] && Moment(row[filter.id]).format('YYYY-MM-DD') === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="date"
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'updatedTimeStamp']))
              ? _.find(tableFilters, ['id', 'updatedTimeStamp']).value
              : ''
          }
        />
      )
    },
    {
      Header: 'Status',
      accessor: 'isActive',
      minWidth: 50,
      Cell: (row) => (
        <FormGroup switch>
          <Input
            type="switch"
            role="switch"
            id={'statusSwitch' + row.original.identifier + row.original.channel}
            name="statusSwitch[]"
            value={row.original.isActive}
            checked={row.original.isActive === 1}
            onChange={() => toggleStatus(row.original)}
            disabled={
              isCooperative
                ? !_.includes(['checker'], role)
                : !_.includes(['checker', 'investigator'], role)
            }
          />
        </FormGroup>
      ),
      filterMethod: (filter, row) => row[filter.id] == filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'isActive']))
              ? _.find(tableFilters, ['id', 'isActive']).value
              : ''
          }>
          <option value="">All</option>
          <option value={1}>Activated</option>
          <option value={0}>Suspended</option>
        </select>
      )
    }
  ];

  const action = (
    <ButtonGroup>
      <ListExcelDownload
        theme={toggle.theme}
        categoryOptions={categoryOptions}
        listName={currentPrefilterList.prefilterName}
        data={prefiltersList.specializedList.data.listInfo}
        partnerIdList={partnerIdList}
      />
      {(isCooperative
        ? _.includes(['checker'], role)
        : _.includes(['checker', 'investigator'], role)) && (
        <Button className="ms-1" size="sm" color="primary" onClick={() => toggleFilterModal('add')}>
          {`Add ${currentPrefilterList.prefilterName}`}
        </Button>
      )}
    </ButtonGroup>
  );

  return (
    <div>
      <CardContainer title={currentPrefilterList.prefilterName} action={action}>
        <form onSubmit={searchFilterValues}>
          <div className="prefilter-search d-flex flex-row justify-content-end">
            <FormGroup className="select-box">
              <Input
                type="select"
                name="searchCategoryName"
                value={searchCategoryName}
                onChange={(e) => setSearchCategoryName(e.target.value)}
                required>
                <option value="">-- Category --</option>
                {categoryOptions}
              </Input>
            </FormGroup>
            <FormGroup className="input-box">
              <Input
                type="text"
                id="searchIdentifier"
                name="searchIdentifier"
                value={searchIdentifier}
                placeholder="Enter Identifier"
                onChange={(event) => setSearchIdentifier(event.target.value)}
                required
              />
            </FormGroup>
            <FormGroup>
              <Button size="sm" type="submit" color="primary">
                <FontAwesomeIcon icon={faSearch} /> Search
              </Button>
            </FormGroup>
            {role == 'checker' && (
              <div className="d-flex justify-content-end">
                <Button
                  size="sm"
                  color="danger"
                  className="ms-3 mb-3"
                  disabled={_.isEmpty(selectedLists)}
                  onClick={() => {
                    toggleConfirmAlertModal();
                    setIsBulkDelete(true);
                  }}>
                  Delete All
                </Button>
              </div>
            )}
          </div>
        </form>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] &&
            row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          columns={header}
          data={prefiltersList.specializedList.data.listInfo}
          noDataText="No data found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={3}
          page={pageNo}
          pageSize={pageRecords}
          className={'-highlight  -striped'}
          filtered={tableFilters}
          onFilteredChange={(filtered) => setTableFilters(filtered)}
          onPageChange={(page) => handlePageChange(page)}
          onPageSizeChange={(pageSize, page) => handlePageSizeChange(page, pageSize)}
          showPageJump={false}
          pages={
            prefiltersList.specializedList.data.count / pageRecords > 1
              ? Math.ceil(prefiltersList.specializedList.data.count / pageRecords)
              : 1
          }
        />
      </CardContainer>

      <SpecializedListFormContainer
        isEditmode={isEditmode}
        listType={listType}
        data={selectedEntry}
        categoryOptions={categoryOptions}
        partnerIdOptions={partnerIdList}
        currentPrefilterList={currentPrefilterList}
        toggleFilterModal={toggleFilterModal}
      />

      <ConfirmAlert
        theme={toggle.theme}
        confirmAlertModal={toggle.confirmAlertModal[listType]}
        toggleConfirmAlertModal={toggleConfirmAlertModal}
        confirmationAction={onDeleteHandler}
        confirmAlertTitle={
          isBulkDelete
            ? 'Are you sure you want to delete selected lists ?'
            : 'Are you sure you want to delete it ?'
        }
      />
    </div>
  );
};

ActiveList.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  listType: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired,
  userActions: PropTypes.object.isRequired,
  partnerIdList: PropTypes.array.isRequired
};

export default ActiveList;
