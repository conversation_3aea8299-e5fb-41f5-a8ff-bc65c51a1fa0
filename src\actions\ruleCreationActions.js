import {
  ON_DSL_VERIFY_FAILURE,
  ON_<PERSON><PERSON>AR_DSL_VALIDATION,
  ON_SUCCESSFUL_DSL_VERIFY,
  ON_FETCH_DSL_HELPERS_LOADING,
  ON_FETCH_DSL_HELPERS_FAILURE,
  ON_SUCCESSFUL_FETCH_DSL_HELPERS,
  ON_SUCCESSFUL_FETCH_ACTION_LIST,
  ON_SUCCESSFUL_UPDATE_ACTION_LIST,
  ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS,
  ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE,
  ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS,
  ON_SUCCESSFUL_FETCH_CHECKLIST,
  ON_FETCH_CHECKLIST_OPTIONS_FAILURE,
  ON_FETCH_CHECKLIST_FAILURE,
  ON_FETCH_RULE_CHANNELS_LIST_SUCCESS,
  ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS,
  ON_FETCH_RULE_LABELS_SUCCESS,
  ON_UPDATE_LABELS_ORDER_SUCCESS,
  ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS,
  ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE,
  ON_FETCH_SIMILAR_RULE_SUCCESS,
  ON_FETCH_SIMILAR_RULE_FAILURE
} from 'constants/actionTypes';
import {
  onToggleLoader,
  onToggleRuleEditModal,
  onToggleRuleCreateModal,
  onToggleRuleDuplicateModal,
  onToggleCreateLabelModal
} from 'actions/toggleActions';
import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import client from 'utility/apiClient';

function fetchDSLHelpers(channel) {
  return client({
    url: `${channel}/ruleengine/rule/dsl/components`
  });
}

function onFetchDSLHelpersLoading() {
  return { type: ON_FETCH_DSL_HELPERS_LOADING };
}

function onSuccessfulFetchDSLHelpers(channel, response) {
  return {
    type: ON_SUCCESSFUL_FETCH_DSL_HELPERS,
    response,
    channel
  };
}

function onFetchDSLHelpersFailure(response) {
  return {
    type: ON_FETCH_DSL_HELPERS_FAILURE,
    response
  };
}

function onFetchDSLHelpers(channel) {
  return function (dispatch) {
    dispatch(onFetchDSLHelpersLoading());
    return fetchDSLHelpers(channel).then(
      (success) => dispatch(onSuccessfulFetchDSLHelpers(channel, success)),
      (error) => dispatch(onFetchDSLHelpersFailure(error))
    );
  };
}

function fetchActionList(channel) {
  return client({ url: `${channel}/ruleengine/action` });
}

function onSuccessfulFetchActionList(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_ACTION_LIST,
    response
  };
}

function onFetchActionList(channel) {
  return function (dispatch) {
    return fetchActionList(channel).then((success) =>
      dispatch(onSuccessfulFetchActionList(success))
    );
  };
}

function fetchAlertCategories(channel) {
  return client({ url: `${channel}/ruleengine/fetch/alert/categories` });
}

function onSuccessfulFetchAlertCategories(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_ALERT_CATEGORIES,
    response
  };
}

function onFetchAlertCategories(channel) {
  return function (dispatch) {
    return fetchAlertCategories(channel).then((success) =>
      dispatch(onSuccessfulFetchAlertCategories(success))
    );
  };
}

function onClearValidation() {
  return { type: ON_CLEAR_DSL_VALIDATION };
}

function fetchPredictFalsePositive(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/case/rule/predictFalsePositive`,
    data: formData
  });
}

function onSuccessfulFetchPredictFalsePositive(response) {
  return {
    type: ON_FETCH_PREDICT_FALSE_POSITIVE_SUCCESS,
    response
  };
}

function onfetchPredictFalsePositiveFailure(response) {
  return {
    type: ON_FETCH_PREDICT_FALSE_POSITIVE_FAILURE,
    response
  };
}

function onFetchPredictFalsePositive(formData) {
  return function (dispatch) {
    return fetchPredictFalsePositive(formData).then(
      (success) => dispatch(onSuccessfulFetchPredictFalsePositive(success)),
      (error) => dispatch(onfetchPredictFalsePositiveFailure(error))
    );
  };
}

function fetchSimilarRule(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/case/rule/fetchSimilarRule`,
    data: formData
  });
}

function onSuccessfulFetchSimilarRule(response) {
  return {
    type: ON_FETCH_SIMILAR_RULE_SUCCESS,
    response
  };
}

function onFetchSimilarRuleFailure(response) {
  return {
    type: ON_FETCH_SIMILAR_RULE_FAILURE,
    response
  };
}

function onFetchSimilarRule(formData) {
  return function (dispatch) {
    return fetchSimilarRule(formData).then(
      (success) => dispatch(onSuccessfulFetchSimilarRule(success)),
      (error) => dispatch(onFetchSimilarRuleFailure(error))
    );
  };
}

function verifyDSL(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/ruleengine/validate`,
    data: formData,
    badRequestMessage: 'Unable to verify DSL'
  });
}

function onSuccessfulVerifyDSL(response) {
  return {
    type: ON_SUCCESSFUL_DSL_VERIFY,
    response
  };
}

function onVerifyDSLFailure(response) {
  return {
    type: ON_DSL_VERIFY_FAILURE,
    response
  };
}

function onVerifyDSL(formData) {
  return function (dispatch) {
    return verifyDSL(formData).then(
      (success) => {
        if (formData.channel == 'frm') {
          dispatch(onFetchPredictFalsePositive(formData));
          dispatch(onFetchSimilarRule(formData));
        }
        success == 'OK'
          ? dispatch(onSuccessfulVerifyDSL({ message: 'DSL string validated successfully' }))
          : dispatch(onVerifyDSLFailure({ message: success }));
      },
      (error) => dispatch(onVerifyDSLFailure(error))
    );
  };
}

function createRule(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/ruleengine/rule/approval/supervisor`,
    data: formData.rule,
    badRequestMessage: 'Unable to create rule'
  });
}

function onCreateRule(formData, type) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return createRule(formData)
      .then(
        (success) => {
          dispatch(onShowSuccessAlert({ message: success }));
          dispatch(onFetchRulesList(formData.channel));
          dispatch(onFetchNonProductionRulesList(formData.channel, 'checker'));
          type == 'create'
            ? dispatch(onToggleRuleCreateModal(formData.channel))
            : dispatch(onToggleRuleDuplicateModal(formData.channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateRule(formData) {
  return client({
    method: 'POST',
    url: `${formData.channel}/ruleengine/rule/approval/${formData.editUrl}`,
    data: formData.rule,
    badRequestMessage: 'Unable to update rule'
  });
}

function onUpdateRule(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateRule(formData)
      .then(
        (success) => {
          dispatch(onShowSuccessAlert({ message: success }));
          dispatch(onFetchRulesList(formData.channel));
          dispatch(onFetchNonProductionRulesList(formData.channel, 'checker'));
          dispatch(onToggleRuleEditModal(formData.channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchNonProductionRulesList(channel, role) {
  return client({
    url: `${channel}/ruleengine/rule/approval/${role}/fetch`,
    badRequestMessage: 'Currently unable to fetch non production rules'
  });
}

function onFetchRulesListLoading() {
  return { type: ON_FETCH_NON_PRODUCTION_RULE_LIST_LOADING };
}

function onSuccessfulFetchRulesList(channel, response) {
  return {
    type: ON_FETCH_NON_PRODUCTION_RULE_LIST_SUCCESS,
    channel,
    response
  };
}

function onFetchRulesListFailure(response) {
  return {
    type: ON_FETCH_NON_PRODUCTION_RULE_LIST_FAILURE,
    response
  };
}

function onFetchNonProductionRulesList(channel, role) {
  return function (dispatch) {
    dispatch(onFetchRulesListLoading());
    return fetchNonProductionRulesList(channel, role).then(
      (success) => dispatch(onSuccessfulFetchRulesList(channel, success)),
      (error) => dispatch(onFetchRulesListFailure(error))
    );
  };
}

function ruleApproval(channel, role, formData) {
  return client({
    method: 'PUT',
    url: `${channel}/ruleengine/rule/approval/${role}/response`,
    data: formData,
    badRequestMessage: 'Check submitted information'
  });
}

function onRuleApproval(channel, role, formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return ruleApproval(channel, role, formData)
      .then(
        () => {
          dispatch(
            onShowSuccessAlert({
              message: 'Rule ' + (formData.isApproved ? 'approved' : 'rejected') + ' successfully!'
            })
          );
          formData.isApproved && dispatch(onFetchRulesList(channel));
          dispatch(onFetchNonProductionRulesList(channel, role));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchCheckListOptions(channel) {
  return client({ url: `${channel}/ruleengine/fetch/citations/all` });
}

function onSuccessfulFetchCheckListOptions(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CHECKLIST_OPTIONS,
    response
  };
}

function onFetchCheckListOptionsFailure(response) {
  return {
    type: ON_FETCH_CHECKLIST_OPTIONS_FAILURE,
    response
  };
}

function onFetchCheckListOptions(channel) {
  return function (dispatch) {
    return fetchCheckListOptions(channel).then(
      (success) => dispatch(onSuccessfulFetchCheckListOptions(success)),
      (error) => dispatch(onFetchCheckListOptionsFailure(error))
    );
  };
}

function fetchCheckList(channel, code) {
  return client({ url: `${channel}/ruleengine/fetch/citations/rule/${code}` });
}

function onSuccessfulFetchCheckList(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CHECKLIST,
    response
  };
}

function onFetchCheckListFailure(response) {
  return {
    type: ON_FETCH_CHECKLIST_FAILURE,
    response
  };
}

function onFetchCheckList(channel, code) {
  return function (dispatch) {
    return fetchCheckList(channel, code).then(
      (success) => dispatch(onSuccessfulFetchCheckList(success)),
      (error) => dispatch(onFetchCheckListFailure(error))
    );
  };
}

function updateActionList(channel, data) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/action/order/update`,
    data,
    notFoundMessage: 'Action order service not available'
  });
}

function onSuccessfulUpdateActionList(response) {
  return {
    type: ON_SUCCESSFUL_UPDATE_ACTION_LIST,
    response
  };
}

function onUpdateActionList(channel, data) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateActionList(channel, data)
      .then(
        () => dispatch(onSuccessfulUpdateActionList(data.updatedRuleOrder)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchRuleChannelsList(channel) {
  return client({ url: `${channel}/ruleengine/rule-channels` });
}

function onSuccessfulFetchRuleChannelsList(response) {
  return {
    type: ON_FETCH_RULE_CHANNELS_LIST_SUCCESS,
    response
  };
}

function onFetchRuleChannelsList(channel) {
  return function (dispatch) {
    return fetchRuleChannelsList(channel).then(
      (success) => dispatch(onSuccessfulFetchRuleChannelsList(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchRuleFraudCategoriesList(channel) {
  return client({ url: `${channel}/ruleengine/fraud-categories` });
}

function onSuccessfulfetchRuleFraudCategoriesList(response) {
  return {
    type: ON_FETCH_RULE_FRAUD_CATEGORIES_LIST_SUCCESS,
    response
  };
}

function onFetchRuleFraudCategoriesList(channel) {
  return function (dispatch) {
    return fetchRuleFraudCategoriesList(channel).then(
      (success) => dispatch(onSuccessfulfetchRuleFraudCategoriesList(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchRuleLabels(channel) {
  return client({ url: `${channel}/ruleengine/rule-labels` });
}

function onSuccessfulFetchRuleLabels(response) {
  return {
    type: ON_FETCH_RULE_LABELS_SUCCESS,
    response
  };
}

function onFetchRuleLabels(channel) {
  return function (dispatch) {
    return fetchRuleLabels(channel).then(
      (success) => dispatch(onSuccessfulFetchRuleLabels(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function addRuleLabels(channel, data) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/update/rule-label`,
    data
  });
}

function onCreateRuleLabel(channel, data) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return addRuleLabels(channel, data)
      .then(
        (success) => {
          dispatch(onShowSuccessAlert({ message: success || 'Rule label added successfully' }));
          dispatch(onToggleCreateLabelModal());
          dispatch(onFetchRuleLabels(channel));
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updateLabelOrder(channel, data) {
  return client({
    method: 'POST',
    url: `${channel}/ruleengine/label/order/update`,
    data,
    notFoundMessage: 'Label order service not available'
  });
}

function onSuccessfulUpdateLabelOrder(response) {
  return {
    type: ON_UPDATE_LABELS_ORDER_SUCCESS,
    response
  };
}

function onUpdateLabelOrder(channel, data) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updateLabelOrder(channel, data)
      .then(
        () => dispatch(onSuccessfulUpdateLabelOrder(data.updatedRuleLabelOrder)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

export {
  onFetchNonProductionRulesList,
  onFetchDSLHelpers,
  onFetchActionList,
  onFetchAlertCategories,
  onClearValidation,
  onVerifyDSL,
  onCreateRule,
  onUpdateRule,
  onRuleApproval,
  onFetchCheckListOptions,
  onFetchCheckList,
  onUpdateActionList,
  onFetchRuleChannelsList,
  onFetchRuleFraudCategoriesList,
  onFetchRuleLabels,
  onCreateRuleLabel,
  onUpdateLabelOrder
};
