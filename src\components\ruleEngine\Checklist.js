'use strict';
import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Button, FormGroup, Input, Label, InputGroup } from 'reactstrap';

const Checklist = ({
  channel,
  fetchCheckListOptions,
  fetchCheckList,
  code,
  checkListOptions,
  checkList,
  onChecklistChange
}) => {
  const [localCheckList, setLocalCheckList] = useState([{ id: '', citationName: '' }]);
  const [localCheckListOptions, setLocalCheckListOptions] = useState(checkListOptions?.list || []);

  // Fetch data on mount
  useEffect(() => {
    fetchCheckListOptions(channel);
    if (!_.isEmpty(code)) fetchCheckList(channel, code);
  }, [channel, fetchCheckListOptions, fetchCheckList, code]);

  // Update local state when props change, but avoid infinite loops
  useEffect(() => {
    if (checkList?.list && checkList.list.length > 0) {
      const newCheckListOptions = _.differenceBy(
        checkListOptions?.list || [],
        checkList.list,
        'citationName'
      );

      setLocalCheckList(checkList.list);
      setLocalCheckListOptions(newCheckListOptions);

      // Only call onChecklistChange if the list actually changed
      if (!_.isEqual(localCheckList, checkList.list)) onChecklistChange(checkList.list);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkList?.list, checkListOptions?.list]);

  // Update options when checkListOptions change
  useEffect(() => {
    if (checkListOptions?.list) setLocalCheckListOptions(checkListOptions.list);
  }, [checkListOptions?.list]);

  const handleCheckListChange = useCallback(
    (index, event) => {
      const newChecklist = [...localCheckList];

      const newCheckListOptions = _.filter(
        localCheckListOptions,
        (option) => option.citationName !== event.target.value
      );

      const option = _.find(localCheckListOptions, { citationName: event.target.value });
      newChecklist[index] = !_.isEmpty(option)
        ? option
        : { id: '', citationName: event.target.value };

      setLocalCheckList(newChecklist);
      setLocalCheckListOptions(newCheckListOptions);
      onChecklistChange(newChecklist);
    },
    [localCheckList, localCheckListOptions, onChecklistChange]
  );

  const addChecklist = useCallback(() => {
    const newCheckList = [...localCheckList, { id: '', citationName: '' }];

    const newCheckListOptions = _.differenceBy(
      checkListOptions?.list || [],
      newCheckList,
      'citationName'
    );

    setLocalCheckList(newCheckList);
    setLocalCheckListOptions(newCheckListOptions);
  }, [localCheckList, checkListOptions?.list]);

  const removeChecklist = useCallback(
    (index) => {
      const currentOption = localCheckList[index];

      // Add back to options if it was a valid option
      if (
        _.some(checkListOptions?.list || [], currentOption) &&
        !_.some(localCheckListOptions, currentOption)
      )
        setLocalCheckListOptions([...localCheckListOptions, currentOption]);

      const newChecklist = localCheckList.filter((item, i) => i !== index);
      setLocalCheckList(newChecklist);
      onChecklistChange(newChecklist);
    },
    [localCheckList, localCheckListOptions, checkListOptions?.list, onChecklistChange]
  );

  return (
    <Row>
      <Col>
        {_.map(localCheckList, (d, i) => (
          <FormGroup key={i}>
            <Label>Question #{i + 1}</Label>
            <InputGroup>
              {i !== 0 && (
                <Button size="sm" type="button" color="danger" onClick={() => removeChecklist(i)}>
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              )}
              <Input
                type="text"
                name="checklist"
                onChange={(e) => {
                  handleCheckListChange(i, e);
                }}
                value={d.citationName}
                list="optionsList"
                autoComplete="off"
              />
              <datalist id="optionsList">
                {_.map(localCheckListOptions, (o) => (
                  <option key={o.citationName}>{o.citationName}</option>
                ))}
              </datalist>
              {i === localCheckList.length - 1 && (
                <Button size="sm" type="button" color="success" onClick={() => addChecklist()}>
                  <FontAwesomeIcon icon={faPlus} />
                </Button>
              )}
            </InputGroup>
          </FormGroup>
        ))}
      </Col>
    </Row>
  );
};

Checklist.propTypes = {
  onChecklistChange: PropTypes.func.isRequired,
  checkListOptions: PropTypes.object.isRequired,
  fetchCheckListOptions: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired,
  fetchCheckList: PropTypes.func.isRequired,
  checkList: PropTypes.object.isRequired,
  code: PropTypes.string
};

export default React.memo(Checklist);
