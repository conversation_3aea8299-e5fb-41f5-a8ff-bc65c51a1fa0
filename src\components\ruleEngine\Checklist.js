'use strict';
import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Button, FormGroup, Input, Label, InputGroup } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';

class Checklist extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      checkList: [{ id: '', citationName: '' }],
      checkListOptions: this.props.checkListOptions.list
    };

    this._removeChecklist = this._removeChecklist.bind(this);
    this._addChecklist = this._addChecklist.bind(this);
  }

  componentDidMount() {
    let { channel, fetchCheckListOptions, fetchCheckList, code } = this.props;
    fetchCheckListOptions(channel);
    !_.isEmpty(code) && fetchCheckList(channel, code);
  }

  componentDidUpdate(prevProps) {
    if (this.props.checkList?.list !== prevProps.checkList?.list) {
      let { checkListOptions, checkList } = this.props;

      let newCheckListOptions = _.differenceBy(
        checkListOptions?.list,
        checkList?.list,
        'citationName'
      );

      this.setState({
        checkList: _.isEmpty(checkList?.list) ? this.state.checkList : checkList.list,
        checkListOptions: newCheckListOptions
      });
      this.props.onChecklistChange(
        _.isEmpty(checkList?.list) ? this.state.checkList : checkList.list
      );
    }
  }

  _handleCheckListChange = (index, event) => {
    let { checkList, checkListOptions } = this.state;
    let newChecklist = checkList;

    let newCheckListOptions = _.filter(
      checkListOptions,
      (option) => option.citationName !== event.target.value
    );

    let option = _.find(checkListOptions, { citationName: event.target.value });
    newChecklist[index] = !_.isEmpty(option)
      ? option
      : { id: '', citationName: event.target.value };

    this.setState({ checkList: newChecklist, checkListOptions: newCheckListOptions });
    this.props.onChecklistChange(newChecklist);
  };

  _addChecklist = () => {
    let { checkList } = this.state;

    let newCheckListOptions = _.differenceBy(
      this.props.checkListOptions.list,
      checkList,
      'citationName'
    );

    this.setState({
      checkList: [...checkList, { id: '', citationName: '' }],
      checkListOptions: newCheckListOptions
    });
  };

  _removeChecklist = (index) => {
    let { checkList, checkListOptions } = this.state;

    let currentOption = checkList[index];

    _.some(this.props.checkListOptions.list, currentOption) &&
      !_.some(checkListOptions, currentOption) &&
      this.setState({ checkListOptions: [...checkListOptions, currentOption] });

    let newChecklist = checkList.filter((item, i) => i != index);
    this.setState({ checkList: newChecklist });

    this.props.onChecklistChange(newChecklist);
  };

  render() {
    const { checkList, checkListOptions } = this.state;

    return (
      <Row>
        <Col>
          {_.map(checkList, (d, i) => (
            <FormGroup key={i}>
              <Label>Question #{i + 1}</Label>
              <InputGroup>
                {i != 0 && (
                  <Button
                    size="sm"
                    type="button"
                    color="danger"
                    onClick={() => this._removeChecklist(i)}>
                    <FontAwesomeIcon icon={faTrash} />
                  </Button>
                )}
                <Input
                  type="text"
                  name="checklist"
                  onChange={(e) => {
                    this._handleCheckListChange(i, e);
                  }}
                  value={d.citationName}
                  list="optionsList"
                  autoComplete="off"
                />
                <datalist id="optionsList">
                  {_.map(checkListOptions, (o) => (
                    <option key={o.citationName}>{o.citationName}</option>
                  ))}
                </datalist>
                {i == checkList.length - 1 && (
                  <Button
                    size="sm"
                    type="button"
                    color="success"
                    onClick={() => this._addChecklist()}>
                    <FontAwesomeIcon icon={faPlus} />
                  </Button>
                )}
              </InputGroup>
            </FormGroup>
          ))}
        </Col>
      </Row>
    );
  }
}

Checklist.propTypes = {
  onChecklistChange: PropTypes.func.isRequired,
  checkListOptions: PropTypes.object.isRequired,
  fetchCheckListOptions: PropTypes.func.isRequired,
  channel: PropTypes.string.isRequired,
  fetchCheckList: PropTypes.func.isRequired,
  checkList: PropTypes.object.isRequired,
  code: PropTypes.string
};

export default Checklist;
