import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { graphic } from 'echarts/core';
import Moment from 'moment';

import GraphContainer from 'components/common/GraphContainer';
import HelpIcon from 'components/common/HelpIcon';

function RuleEfficiencyGraph({ theme, rule, period, ruleEfficiency, fetchRuleEfficiency }) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleEfficiency({
        ruleId: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const datediff = Moment.duration(Moment(period.endDate).diff(Moment(period.startDate))).days();
  const alertsData = ruleEfficiency.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    d.alert
  ]);
  const fraudData = ruleEfficiency.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    d.confirmedFraud
  ]);
  const manualData = ruleEfficiency.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    d.manualCases
  ]);
  const efficiencyData = ruleEfficiency.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    d.efficiency
  ]);

  const config = {
    grid: {
      left: 30,
      containLabel: true,
      bottom: 20,
      top: 40,
      right: 30
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['Alerts', 'Confirm Fraud', 'Reported Fraud', 'Efficiency']
    },
    xAxis: [
      {
        type: 'category',
        axisPointer: { type: 'shadow' }
      }
    ],
    yAxis: [
      { type: 'value', name: 'Alerts' },
      {
        type: 'value',
        name: 'Rate',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0]
      }
    ],
    series: [
      {
        name: 'Efficiency',
        type: 'line',
        smooth: true,
        showAllSymbol: true,
        symbol: 'emptyCircle',
        symbolSize: 8,
        yAxisIndex: 1,
        lineStyle: {
          width: 2
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        data: efficiencyData,
        markLine: {
          data: [{ type: 'average', name: 'Average Efficiency' }]
        }
      },
      {
        name: 'Alerts',
        type: 'bar',
        barMaxWidth: '30px',
        barMinWidth: 10,
        itemStyle: {
          borderRadius: [30, 30, 0, 0],
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: '#273a72' },
            { offset: 0, color: '#2ca8de' }
          ])
        },
        data: alertsData
      },
      {
        name: 'Confirm Fraud',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: '30px',
        barMinWidth: 10,
        data: fraudData
      },
      {
        name: 'Reported Fraud',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: '30px',
        barMinWidth: 10,
        itemStyle: {
          borderRadius: [30, 30, 0, 0]
        },
        data: manualData
      }
    ]
  };

  const subtitle = (
    <HelpIcon
      size="lg"
      id="efficiencyHelp"
      key="efficiencyHelp"
      text={
        <small>
          <b>Alerts</b> - No of alerts for the rule.
          <br />
          <b>Confimed Fraud</b> - No of fraud cases.
          <br />
          <b>Reported Fraud</b> - No of manual or reported fraud cases.
          <br />
          <b>Efficiency</b> - Rate of total fraud cases vs total alerts for the rule
        </small>
      }
    />
  );

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title="Rule Efficiency"
      subtitle={subtitle}
      noData={ruleEfficiency.data?.length === 0}
      loader={ruleEfficiency.loader}
      error={{ flag: ruleEfficiency.error, errorMessage: ruleEfficiency.errorMessage }}
    />
  );
}

RuleEfficiencyGraph.propTypes = {
  rule: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  ruleEfficiency: PropTypes.object.isRequired,
  fetchRuleEfficiency: PropTypes.func.isRequired
};

export default RuleEfficiencyGraph;
