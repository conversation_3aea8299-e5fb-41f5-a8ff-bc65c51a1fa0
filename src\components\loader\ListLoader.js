import { range } from 'lodash';
import React from 'react';

const ListLoader = () => (
  <ul className="comment-list">
    {range(4).map((d) => (
      <li key={d} className="comment">
        <div className="d-flex justify-content-between flex-wrap comment-header">
          <span className="comment-title-placeholder" />
          <span className="comment-timestamp-placeholder" />
        </div>
        <span className="comment-text-placeholder" />
      </li>
    ))}
  </ul>
);

export default ListLoader;
