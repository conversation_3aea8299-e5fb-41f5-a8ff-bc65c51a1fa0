import React from 'react';
import { range } from 'lodash';

const ListLoader = () => {
  return (
    <ul className="comment-list">
      {range(4).map((d) => (
        <li key={d} className="comment">
          <div className="d-flex justify-content-between flex-wrap comment-header">
            <span className="comment-title-placeholder">{''}</span>
            <span className="comment-timestamp-placeholder">{''}</span>
          </div>
          <span className="comment-text-placeholder">{''}</span>
        </li>
      ))}
    </ul>
  );
};

export default ListLoader;
