import {
  ON_FETCH_RELEASE_FUNDS_LIST_LOADING,
  ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST,
  ON_FETCH_RELEASE_FUNDS_LIST_FAILURE,
  ON_FETCH_DOCUMENT_STATUS_LOADING,
  ON_FETCH_DOCUMENT_STATUS_SUCCESS,
  ON_FETCH_DOCUMENT_STATUS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function releaseFundsReducer(state = initialState.releaseFunds, action) {
  switch (action.type) {
    case ON_FETCH_RELEASE_FUNDS_LIST_LOADING:
      return objectAssign({}, state, {
        fundsToBeRelease: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_SUCCESSFUL_FETCH_RELEASE_FUNDS_LIST:
      return objectAssign({}, state, {
        fundsToBeRelease: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_RELEASE_FUNDS_LIST_FAILURE:
      return objectAssign({}, state, {
        fundsToBeRelease: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    case ON_FETCH_DOCUMENT_STATUS_LOADING:
      return objectAssign({}, state, {
        documentStatus: {
          details: {},
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_DOCUMENT_STATUS_SUCCESS:
      return objectAssign({}, state, {
        documentStatus: {
          details: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_DOCUMENT_STATUS_FAILURE:
      return objectAssign({}, state, {
        documentStatus: {
          details: {},
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        }
      });
    default:
      return state;
  }
}
