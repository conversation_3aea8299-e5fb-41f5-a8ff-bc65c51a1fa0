import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchAnalystTAT } from 'actions/slaDashboardActions';
import AnalystTATTrend from 'components/dashboards/AnalystTATTrend';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    analystTAT: state.slaDashboard.analystTAT
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchAnalystTAT: bindActionCreators(onFetchAnalystTAT, dispatch)
  };
};

const AnalystTATTrendContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)((props) => (
  <DateRangeProvider contextKey="analystTATTrend">
    <AnalystTATTrend {...props} contextKey="analystTATTrend" />
  </DateRangeProvider>
));

export default AnalystTATTrendContainer;
