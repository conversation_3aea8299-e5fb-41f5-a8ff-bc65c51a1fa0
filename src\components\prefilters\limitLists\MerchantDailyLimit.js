/* eslint-disable jsx-a11y/no-onchange */
/* eslint-disable react/display-name */
/* eslint-disable react/no-multi-comp */
/* eslint-disable react/prop-types */
'use strict';
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button } from 'reactstrap';
import CardContainer from 'components/common/CardContainer';

const MerchantDailyLimit = ({ actions, prefiltersList, currentPrefilterList }) => {
  const [pageNo, setPageNo] = useState(0);
  const [pageRecords, setPageRecords] = useState(10);
  const [searchMerchantIdText, setSearchMerchantIdText] = useState('');

  useEffect(() => {
    let formData = {
      pageNo: pageNo + 1,
      pageRecords: pageRecords
    };
    actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
  }, []);

  const handlePageChange = (page) => {
    if (pageNo < page) {
      let formData = {
        pageNo: page + 1,
        pageRecords: pageRecords,
        ...(searchMerchantIdText && {
          filterCondition: {
            key: 'merchantId',
            value: searchMerchantIdText
          }
        })
      };
      actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
    }

    setPageNo(page);
  };

  const handlePageSizeChange = (page, pageSize) => {
    setPageNo(page);
    setPageRecords(pageSize);
    if (pageRecords < pageSize) {
      let formData = {
        pageNo: page + 1,
        pageRecords: pageSize,
        ...(searchMerchantIdText && {
          filterCondition: {
            key: 'merchantId',
            value: searchMerchantIdText
          }
        })
      };
      actions.onFetchLimitListWithPagination(currentPrefilterList, formData);
    }
  };

  const header = [
    {
      Header: 'Merchant ID',
      accessor: 'merchantId',
      Filter: () => (
        <input
          type="text"
          value={searchMerchantIdText}
          onChange={(event) => setSearchMerchantIdText(event.target.value)}
          onKeyPress={(e) => {
            if (e.which === 13 || e.keyCode === 13) {
              let formData = {
                pageNo: 1,
                pageRecords: pageRecords,
                ...(searchMerchantIdText && {
                  filterCondition: {
                    key: 'merchantId',
                    value: searchMerchantIdText
                  }
                })
              };
              actions.onFetchLimitListWithPagination(currentPrefilterList, formData, 'search');
              setPageNo(0);
            }
          }}
        />
      )
    },
    {
      Header: 'Open Loop Exit Amount Limit',
      accessor: 'openLoopExitAmountLimit',
      filterable: false
    },
    { Header: 'Open Loop Amount Limit', accessor: 'openLoopAmountLimit', filterable: false },
    {
      Header: 'Closed Loop Exit Amount Limit',
      accessor: 'closedLoopExitAmountLimit',
      filterable: false
    },
    { Header: 'Closed Loop Amount Limit', accessor: 'closedLoopAmountLimit', filterable: false },
    {
      Header: 'Open Loop Exit Refund Amount Limit',
      accessor: 'openLoopExitRefundAmountLimit',
      filterable: false
    },
    {
      Header: 'Open Loop Refund Amount Limit',
      accessor: 'openLoopRefundAmountLimit',
      filterable: false
    },
    {
      Header: 'Closed Loop Exit Refund Amount Limit',
      accessor: 'closedLoopExitRefundAmountLimit',
      filterable: false
    },
    {
      Header: 'Closed Loop Refund Amount Limit',
      accessor: 'closedLoopRefundAmountLimit',
      filterable: false
    },
    { Header: 'Open Loop Exit Count Limit', accessor: 'openLoopExitCountLimit', filterable: false },
    { Header: 'Open Loop Count Limit', accessor: 'openLoopCountLimit', filterable: false },
    {
      Header: 'Closed Loop Exit Count Limit',
      accessor: 'closedLoopExitCountLimit',
      filterable: false
    },
    { Header: 'Closed Loop Count Limit', accessor: 'closedLoopCountLimit', filterable: false },
    {
      Header: 'Open Loop Exit Refund Count Limit',
      accessor: 'openLoopExitRefundCountLimit',
      filterable: false
    },
    {
      Header: 'Open Loop Refund Count Limit',
      accessor: 'openLoopRefundCountLimit',
      filterable: false
    },
    {
      Header: 'Closed Loop Exit Refund Count Limit',
      accessor: 'closedLoopExitRefundCountLimit',
      filterable: false
    },
    {
      Header: 'Closed Loop Refund Count Limit',
      accessor: 'closedLoopRefundCountLimit',
      filterable: false
    }
  ];

  return (
    <div>
      <CardContainer
        title={currentPrefilterList.prefilterName}
        action={
          <Button
            outline
            color="primary"
            size="sm"
            onClick={() =>
              window.open(`/api/v1/ifrm/listsandlimitsrdbms/limit/download-merchant-daily`)
            }>
            Download
          </Button>
        }>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] &&
            row[filter.id].toString().toLowerCase().includes(filter.value.toLowerCase())
          }
          columns={header}
          data={prefiltersList.limitListWithPagination.data.merchantdaily}
          noDataText="No data found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={pageRecords}
          minRows={3}
          page={pageNo}
          pages={
            prefiltersList.limitListWithPagination.data.count / pageRecords > 1
              ? Math.ceil(prefiltersList.limitListWithPagination.data.count / pageRecords)
              : 1
          }
          className={'-highlight  -striped'}
          onPageChange={(page) => handlePageChange(page)}
          onPageSizeChange={(pageSize, page) => handlePageSizeChange(page, pageSize)}
          showPageJump={false}
        />
      </CardContainer>
    </div>
  );
};

MerchantDailyLimit.propTypes = {
  actions: PropTypes.object.isRequired,
  prefiltersList: PropTypes.object.isRequired,
  currentPrefilterList: PropTypes.object.isRequired,
  listType: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired
};

export default MerchantDailyLimit;
