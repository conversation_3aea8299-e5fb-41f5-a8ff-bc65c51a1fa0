import { faCopy } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { Card, Row, Col } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

function FMRReport({ fmrDetails }) {
  return (
    <CardContainer title="FMR Report Data">
      <Card>
        <Row>
          <Col>Branch code</Col>
          <Col>
            {fmrDetails?.branchCode}
            {fmrDetails?.branchCode && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.branchCode)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Branch name</Col>
          <Col>
            {fmrDetails?.branchName}
            {fmrDetails?.branchName && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.branchName)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Name of account</Col>
          <Col>
            {fmrDetails?.accountName}
            {fmrDetails?.accountName && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.accountName)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Area of operation where the fraud has occured</Col>
          <Col>
            {fmrDetails?.areaOfOperation}
            {fmrDetails?.areaOfOperation && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.areaOfOperation)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Total amount involved</Col>
          <Col>
            {fmrDetails?.amount}
            {fmrDetails?.amount && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.amount)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Date of occurance</Col>
          <Col>
            {fmrDetails?.transactionDate}
            {fmrDetails?.transactionDate && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.transactionDate)}
              />
            )}
          </Col>
        </Row>
        <Row>
          <Col>Date of detection</Col>
          <Col>
            {fmrDetails?.detectionDate}
            {fmrDetails?.detectionDate && (
              <FontAwesomeIcon
                icon={faCopy}
                className="ms-2"
                title="Copy to clipboard"
                onClick={() => navigator.clipboard.write(fmrDetails?.detectionDate)}
              />
            )}
          </Col>
        </Row>
      </Card>
    </CardContainer>
  );
}

FMRReport.propTypes = {
  fmrDetails: PropTypes.object.isRequired
};

export default FMRReport;
