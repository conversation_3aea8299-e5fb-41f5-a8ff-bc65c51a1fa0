import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Card, Badge } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import CustomerInfoLoader from 'components/loader/CustomerInfoLoader';
import { checkValue, formatMobile } from 'constants/functions';
import StatusLogTableContainer from 'containers/common/StatusLogTableContainer';
import { dataColumn } from 'utility/customRenders';

const MerchantInfoCard = ({
  data,
  isFacctum,
  merchantId,
  fetchDetails,
  demographicDetails,
  channel,
  facctumData,
  fetchFacctumDetails
}) => {
  useEffect(() => {
    merchantId && fetchDetails('merchant', merchantId);
  }, [fetchDetails, merchantId]);

  useEffect(() => {
    if (
      isFacctum === 1 &&
      (!_.isEmpty(data?.details?.personalDetails?.merchantName) ||
        !_.isEmpty(data?.details?.personalDetails?.aadhar))
    ) {
      const formData = {
        entity_type: 'U',
        ...(!_.isEmpty(data.details?.personalDetails?.merchantName) && {
          name: data.details?.personalDetails?.merchantName
        }),
        ...(!_.isEmpty(data.details?.personalDetails?.aadhar) && {
          national_id: data.details?.personalDetails?.aadhar
        })
      };

      channel === 'str' && fetchFacctumDetails(formData);
    }
  }, [channel, data.details, fetchFacctumDetails, isFacctum]);

  const { details, loader, error, errorMessage, riskScore } = data;
  const { merchant } = demographicDetails;
  const facctumDetails = facctumData.details;

  const scoreBadge = (scoreItem) => {
    const scoreColorMap = {
      low: 'success',
      medium: 'warning',
      high: 'danger'
    };

    const normalizedScore = _.toLower(scoreItem.scoreValue);
    const color = scoreColorMap[normalizedScore] || 'primary';
    const badge = (
      <Badge color={color} className="ms-2" key={_.camelCase(scoreItem.scoreName)}>
        {_.capitalize(scoreItem.scoreValue)}
      </Badge>
    );

    return dataColumn(scoreItem.scoreName, badge);
  };

  const facctumDetailsBadge = (facctumItem) => {
    const badge = (
      <div className="data-columns no-break p-1">
        <span>
          <Badge color="danger">{facctumItem} list</Badge>
        </span>
      </div>
    );

    return badge;
  };

  const renderRiskScoreDetails = () => {
    if (riskScore.loader) return null;
    if (riskScore.error) return <p>{riskScore.errorMessage}</p>;
    return (
      <div>
        <b>Risk Score</b>
        {riskScore.details.map(scoreBadge)}
        {checkValue(details.personalDetails, 'risk') &&
          scoreBadge({
            scoreName: 'Merchant Risk Level',
            scoreValue: details.personalDetails.risk
          })}
        {checkValue(details.personalDetails, 'score') &&
          scoreBadge({
            scoreName: 'Merchant Score',
            scoreValue: details.personalDetails.score
          })}
      </div>
    );
  };

  const renderFacctumDetails = () => {
    if (
      channel === 'str' &&
      !_.isEmpty(facctumDetails) &&
      (facctumDetails?.is_sanctioned ||
        facctumDetails?.is_PEP ||
        facctumDetails?.is_ECR ||
        facctumDetails?.is_SI ||
        facctumDetails?.is_SCO ||
        facctumDetails?.is_RCA)
    )
      return (
        <div>
          <b>Facctum Details</b>
          {facctumDetails?.is_sanctioned && facctumDetailsBadge('Sanctioned')}
          {facctumDetails?.is_PEP && facctumDetailsBadge('PEP')}
          {facctumDetails?.is_ECR && facctumDetailsBadge('ECR')}
          {facctumDetails?.is_SI && facctumDetailsBadge('SI')}
          {facctumDetails?.is_SCO && facctumDetailsBadge('SCO')}
          {facctumDetails?.is_RCA && facctumDetailsBadge('RCA')}
        </div>
      );
  };

  const renderBusinessDetails = () => (
    <div>
      <b>Business</b>
      {checkValue(details.id, 'value') &&
        dataColumn('ID', details.id.value, {
          entityDetails: details.id,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
      {checkValue(details.personalDetails, 'merchantName') &&
        dataColumn('Name', details.personalDetails.merchantName)}
      {checkValue(details, 'mccData') &&
        checkValue(details.mccData, 'mcc') &&
        details.mccData.mcc.value &&
        checkValue(details.mccData, 'mccName') &&
        dataColumn('MCC', details.mccData.mccName, {
          entityDetails: details.mccData.mcc,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
      {checkValue(details, 'businessType') && dataColumn('Business Type', details.businessType)}
      {checkValue(details, 'settlementType') &&
        dataColumn('Settlement Type', details.settlementType)}
      {checkValue(details, 'channelName') &&
        dataColumn('Merchant Onboarding Channel', details.channelName)}
      {details.loopType && dataColumn('Loop Type', details.loopType)}
      {details.onboardingType && dataColumn('Onboarding Type', details.onboardingType)}
    </div>
  );

  const renderAccountDetails = () => (
    <div>
      <b>Account</b>
      {checkValue(details.account, 'accountNo') &&
        details.account.accountNo.value &&
        dataColumn('Account No', details.account.accountNo.value, {
          entityDetails: details.account.accountNo,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
      {checkValue(details.account, 'ifscCode') &&
        details.account.ifscCode.value &&
        dataColumn('Bank IFSC', details.account.ifscCode.value, {
          entityDetails: details.account.ifscCode,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
      {checkValue(details.account, 'accountOpeningDate') &&
        dataColumn(
          'Account Opening Date',
          Moment(details.account.accountOpeningDate).format('YYYY-MM-DD')
        )}
      {checkValue(details.account, 'branch') && dataColumn('Branch', details.account.branch)}
      {checkValue(details.personalDetails, 'kycType') &&
        dataColumn('KYC Type', details.personalDetails.kycType)}
      {checkValue(details.personalDetails, 'pan') && dataColumn('PAN', details.personalDetails.pan)}
      {checkValue(details.personalDetails, 'aadhar') &&
        dataColumn('Aadhar', details.personalDetails.aadhar)}
    </div>
  );

  const renderLocationDetails = () => (
    <div>
      <b>Location</b>
      {checkValue(details.address, 'city') && dataColumn('City', details.address.city)}
      {checkValue(details.address, 'provinceState') &&
        dataColumn(
          'State',
          checkValue(details.address, 'country')
            ? `${details.address.provinceState}, ${details.address.country}`
            : details.address.provinceState
        )}
      {checkValue(details.address, 'postalZip') && dataColumn('PinCode', details.address.postalZip)}
      {checkValue(details.personalDetails, 'contact') &&
        dataColumn('Contact', formatMobile(details.personalDetails.contact))}
      {checkValue(details.lat, 'value') &&
        dataColumn('Latitude', details.lat.value, {
          entityDetails: details.lat,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
      {checkValue(details.lng, 'value') &&
        dataColumn('Longitude', details.lng.value, {
          entityDetails: details.lng,
          partnerId: details?.txnCounterResponse?.partnerId || ''
        })}
    </div>
  );

  const renderActivityDetails = () => (
    <div>
      <b>Activity</b>
      {dataColumn('Activity', details.isActive ? 'Active' : 'Inactive')}
      {dataColumn('On Hold', details.isOnHold ? 'Yes' : 'No')}
      {_.has(details, 'txnCounterResponse') && (
        <div>
          {_.has(details.txnCounterResponse, 'txnCount') &&
            dataColumn('Transaction Count', details.txnCounterResponse.txnCount)}
          {_.has(details.txnCounterResponse, 'txnAmount') &&
            dataColumn('Transaction Amount', details.txnCounterResponse.txnAmount)}
          {_.has(details.txnCounterResponse, 'averageTransaction') &&
            dataColumn('Average Transaction', details.txnCounterResponse.averageTransaction)}
          {_.has(details.txnCounterResponse, 'fraudCount') &&
            dataColumn('Fraud Count', details.txnCounterResponse.fraudCount)}
          {_.has(details.txnCounterResponse, 'fraudAmount') &&
            dataColumn('Fraud Amount', details.txnCounterResponse.fraudAmount)}
          {_.has(details.txnCounterResponse, 'averageFraud') &&
            dataColumn('Average Fraud', details.txnCounterResponse.averageFraud)}
        </div>
      )}
    </div>
  );

  const renderAdditionalDetails = () => {
    !_.isEmpty(merchant.details) && (
      <div>
        {_.has(merchant.details, 'name') &&
          merchant.details.name &&
          dataColumn('Name', merchant.details.name)}
        {_.has(merchant.details, 'address') &&
          merchant.details.address &&
          dataColumn('Address', merchant.details.address)}
        {_.has(merchant.details, 'account_creation_date') &&
          merchant.details.account_creation_date &&
          dataColumn('Account Opening Date', merchant.details.account_creation_date)}
        {_.has(merchant.details, 'jm_exit_limit_daily_amount') &&
          merchant.details.jm_exit_limit_daily_amount &&
          dataColumn('Close Loop Daily Exit Limit', merchant.details.jm_exit_limit_daily_amount)}
        {_.has(merchant.details, 'ol_exit_limit_daily_amount') &&
          merchant.details.ol_exit_limit_daily_amount &&
          dataColumn('Open Loop Daily Exit Limit', merchant.details.ol_exit_limit_daily_amount)}
        {_.has(merchant.details, 'mcc_desc') &&
          merchant.details.mcc_desc &&
          dataColumn('MCC Desc', merchant.details.mcc_desc)}
        {_.has(merchant.details, 'is_api_based_merchant') &&
          merchant.details.is_api_based_merchant &&
          dataColumn('Is Api Based Merchant', merchant.details.is_api_based_merchant)}
        {_.has(merchant.details, 'is_express_maf') &&
          merchant.details.is_express_maf &&
          dataColumn('Is Express maf', merchant.details.is_express_maf)}
      </div>
    );
  };

  const MerchantDemographicDetails =
    _.has(merchant.details, 'add_on') &&
    merchant.details.add_on.map((item) => dataColumn(item.key, item.val));

  // Helper function to render the main content
  const renderMainContent = () => {
    if (loader) return <CustomerInfoLoader />;

    if (error && _.isEmpty(merchant?.details))
      return <div className="no-data-div">{errorMessage}</div>;

    if (_.isEmpty(details) && _.isEmpty(merchant?.details))
      return <div className="no-data-div">No merchant details available</div>;

    return (
      <div className="txn-info">
        {renderRiskScoreDetails()}
        {renderFacctumDetails()}
        {renderBusinessDetails()}
        {renderAccountDetails()}
        {renderLocationDetails()}
        {renderActivityDetails()}
        {renderAdditionalDetails()}
        {MerchantDemographicDetails && <div>{MerchantDemographicDetails}</div>}
      </div>
    );
  };

  return (
    <CardContainer
      withAddToList={true}
      title="Merchant Information"
      action={
        <StatusLogTableContainer
          id={merchantId}
          channel={channel}
          name={
            checkValue(details.personalDetail, 'merchantName')
              ? details.personalDetail.merchantName
              : merchantId
          }
        />
      }>
      <Card className="merchant-info-card">{renderMainContent()}</Card>
    </CardContainer>
  );
};

MerchantInfoCard.propTypes = {
  data: PropTypes.object.isRequired,
  isFacctum: PropTypes.number.isRequired,
  fetchDetails: PropTypes.func.isRequired,
  merchantId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  demographicDetails: PropTypes.object,
  channel: PropTypes.string.isRequired,
  facctumData: PropTypes.object.isRequired,
  fetchFacctumDetails: PropTypes.func.isRequired
};

export default MerchantInfoCard;
