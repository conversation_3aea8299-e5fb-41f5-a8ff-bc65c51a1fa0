/* eslint-disable react/no-multi-comp */
import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { useHistory } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faChevronRight,
  faCaretDown,
  faCaretRight,
  faFolderOpen
} from '@fortawesome/free-solid-svg-icons';
import { Input, Button, ButtonGroup, Badge } from 'reactstrap';
import objectAssign from 'object-assign';

import TableLoader from 'components/loader/TableLoader';
import VerdictModalContainer from 'containers/common/VerdictModalContainer';
import ApprovalModalContainer from 'containers/common/ApprovalModalContainer';
import VerdictModalSTRContainer from 'containers/common/VerdictModalSTRContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import CombinedViolationDropdownContainer from 'containers/common/CombinedViolationDropdownContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';
import STRCaseReopenModalContainer from 'containers/common/STRCaseReopenModalContainer';
import SnoozeCaseModalContainer from 'containers/common/SnoozeCaseModalContainer';
import CaseSettingsContainer from 'containers/supervisor/CaseSettingsContainer';
import MasterQueueFetchButtonContainer from 'containers/common/MasterQueueFetchButtonContainer';
import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';
import { getScreen, getCurrentPageCases } from 'constants/functions';
import { isCooperative } from 'constants/publicKey';

// TODO: Re factored code
const BucketCasesTable = (props) => {
  const {
    data,
    stages,
    userRole,
    loginType,
    fraudTypes,
    hasProvisionalFields,
    fetchCases,
    toggleActions,
    caseActions,
    fetchStages,
    closeCaseBuckets,
    fetchFraudTypesList,
    fetchCloseCaseBuckets,
    channel,
    currentConf,
    openSTRCase
  } = props;
  const history = useHistory();
  const [pageNo, setPageNo] = useState(1);
  const [pageRecords, setPageRecords] = useState(10);
  const [selectedCase, setSelectedCase] = useState({});
  const [bulkCaseIds, setBulkCaseIds] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [expanded, setExpanded] = useState({});
  const [tableFilters, setTableFilters] = useState([]);
  const [currentPageCases, setCurrentPageCases] = useState(data?.list || []);
  const { bucket } = currentConf;
  const isCooperativeSTRSupervisor = isCooperative && channel == 'str' && userRole == 'supervisor';
  const [isBulkOpenStrCaseDisabled, setIsBulkOpenStrCaseDisabled] = useState(true);
  const [pendingCasesInBulkCount, setPendingCasesInBulkCount] = useState(0);

  useEffect(() => {
    if (channel === 'frm' && _.isEmpty(closeCaseBuckets.list) && !closeCaseBuckets.loader)
      fetchCloseCaseBuckets();
    if (channel === 'frm' && _.isEmpty(fraudTypes.list) && !fraudTypes.loader)
      fetchFraudTypesList();
    if (_.isEmpty(stages)) fetchStages();
  }, []);

  useEffect(() => {
    currentConf?.pageNo !== pageNo && setPageNo(currentConf?.pageNo);
    currentConf?.pageRecords !== pageRecords && setPageNo(currentConf?.pageRecords);
  }, [currentConf]);

  useEffect(() => {
    setBulkCaseIds([]);
    setSelectAll(false);
  }, [data]);

  useEffect(() => setPageNo(1), [bucket]);

  useEffect(
    () =>
      _.debounce(() => {
        setPageNo(1);
      }, 500),
    [tableFilters]
  );

  useEffect(() => {
    setCurrentPageCases(getCurrentPageCases(pageNo, pageRecords, data?.list, tableFilters));
  }, [data?.list, pageNo, pageRecords, tableFilters]);

  useEffect(() => {
    bucket && fetchCases(objectAssign({}, currentConf, { bucket, pageRecords }), channel);
  }, [pageRecords]);

  const nextPage = () => fetchCases(objectAssign({}, currentConf, { pageNo: pageNo + 1 }), channel);

  const prevPage = () => fetchCases(objectAssign({}, currentConf, { pageNo: pageNo - 1 }), channel);

  const handlePageChange = (page) => {
    pageNo < page ? nextPage() : prevPage();
    setPageNo(page);
    setCurrentPageCases(getCurrentPageCases(page, pageRecords, data?.list));
  };

  const handlePageSizeChange = (page, pageSize) => {
    setPageNo(1);
    setPageRecords(pageSize);
    setCurrentPageCases(getCurrentPageCases(page, pageSize, data?.list));
  };

  const handleOpenSTRCase = (caseItem = {}) => {
    let formData = {
      caseRefNos: bulkCaseIds.length > 0 ? bulkCaseIds : [caseItem?.caseRefNo]
    };

    openSTRCase(formData);

    setBulkCaseIds([]);
    setSelectAll(false);
    !_.isEmpty(caseItem) && setSelectedCase(caseItem);
    setIsBulkOpenStrCaseDisabled(true);
  };

  const getPage = (role, caseItem) => `${getScreen(role)}/${channel}/${caseItem.txnId}`;

  const getActions = (caseItem = null) => {
    switch (true) {
      case userRole === 'auditor':
      case channel === 'str' && !_.isEmpty(caseItem.parentTxn):
        return null;
      case bucket == 'Open' && ['maker', 'checker'].includes(userRole):
      case bucket == 'Rejected' && userRole == 'maker':
        return (
          <>
            {channel === 'frm' && userRole !== 'supervisor' && (
              <Button
                outline
                size="sm"
                color="danger"
                className="ms-1"
                title="Close Case"
                onClick={() => {
                  setBulkCaseIds([]);
                  setSelectAll(false);
                  setSelectedCase(caseItem);
                  toggleActions.onToggleVerdictModal(channel);
                }}>
                <FontAwesomeIcon icon={faTimes} />
              </Button>
            )}
            {channel === 'frm' && !caseItem?.isStrCaseCreated && (
              <Button
                outline
                size="sm"
                color="success"
                className="ms-1 hide"
                title="Open STR Case"
                onClick={() => handleOpenSTRCase(caseItem)}>
                <FontAwesomeIcon icon={faFolderOpen} />
              </Button>
            )}
            {channel === 'str' && userRole !== 'supervisor' && (
              <VerdictModalSTRContainer
                key={caseItem?.caseRefNo}
                caseRefNo={caseItem?.caseRefNo}
                singleType="/singleFromTable"
                onClick={() => {
                  setBulkCaseIds([]);
                  setSelectAll(false);
                  setSelectedCase(caseItem);
                }}
              />
            )}
            <UserListDropdownContainer
              bucket={bucket}
              channel={channel}
              caseId={caseItem.caseRefNo}
              onClick={() => {
                setBulkCaseIds([]);
                setSelectAll(false);
                setSelectedCase(caseItem);
              }}
            />
          </>
        );
      case bucket == 'Pending' && userRole == 'checker' && caseItem.stage == 'Checker':
        return (
          <ApprovalModalContainer
            key={caseItem.caseRefNo}
            caseId={caseItem.caseRefNo || ''}
            status={bucket || ''}
            caseDetails={selectedCase}
            channel={channel}
            singleType="/singleFromTable"
            makerAction={caseItem?.makerAction || ''}
          />
        );
      case bucket == 'Closed' && channel === 'frm' && ['maker', 'checker'].includes(userRole):
        return (
          <Button
            outline
            size="sm"
            color="primary"
            className="ms-1"
            onClick={() => {
              setBulkCaseIds([]);
              setSelectAll(false);
              setSelectedCase(caseItem);
              caseActions.onReOpenCase(
                {
                  caseRefNo: caseItem.caseRefNo,
                  stageId:
                    _.find(stages, (stage) => _.lowerCase(stage.stageName) === userRole)?.id || 0
                },
                'frm',
                caseItem
              );
            }}>
            Re-open
          </Button>
        );
      case bucket == 'Closed' && channel === 'str' && ['maker', 'checker'].includes(userRole):
        return (
          <STRCaseReopenModalContainer caseRefNo={caseItem.caseRefNo} selectedCase={caseItem} />
        );
      case (bucket == 'New' || bucket == 'MasterQueue') &&
        userRole == 'supervisor' &&
        ((channel === 'str' && !isCooperative) || channel === 'frm'):
      case (!isCooperative || channel !== 'frm') &&
        bucket == 'Parked' &&
        ['maker', 'checker', 'supervisor'].includes(userRole):
        return (
          <UserListDropdownContainer
            className="ms-1"
            bucket={bucket === 'MasterQueue' ? caseItem.investigationStatus : bucket}
            channel={channel}
            caseId={caseItem.caseRefNo}
            onClick={() => {
              setBulkCaseIds([]);
              setSelectAll(false);
              setSelectedCase(caseItem);
            }}
          />
        );
      case (bucket == 'New' || bucket == 'MasterQueue') && ['maker', 'checker'].includes(userRole):
        return (
          <UserListDropdownContainer
            className={
              'ms-1' + userRole == 'maker' && caseItem.investigationStatus === 'Pending'
                ? ' hide'
                : ''
            }
            bucket={bucket === 'MasterQueue' ? caseItem.investigationStatus : bucket}
            channel={channel}
            caseId={caseItem.caseRefNo}
            onClick={() => {
              setBulkCaseIds([]);
              setSelectAll(false);
              setSelectedCase(caseItem);
            }}
            selfAssign
          />
        );
      case bucket == 'Unverified' && channel == 'frm' && userRole == 'investigator':
      case bucket == 'Priority' && channel == 'frm' && userRole == 'investigator':
        return (
          <Button
            size="sm"
            color="danger"
            className="ms-1"
            title="Update Investigation"
            onClick={() => {
              setSelectAll(false);
              setSelectedCase(caseItem);
              setBulkCaseIds([caseItem?.caseRefNo]);
              toggleActions.onToggleVerdictModal('frm');
            }}>
            <FontAwesomeIcon icon={faTimes} />
          </Button>
        );
      default:
        return null;
    }
  };

  const getBulkActions = () => {
    switch (true) {
      case bucket == 'Open' && ['maker', 'checker'].includes(userRole):
      case bucket == 'Rejected' && userRole === 'maker':
        return (
          <ButtonGroup>
            {channel == 'frm' && (
              <>
                <MasterQueueFetchButtonContainer />
                <Button
                  outline
                  size="sm"
                  color="danger"
                  className="ms-1"
                  title="Close Case"
                  disabled={_.isEmpty(bulkCaseIds)}
                  onClick={() => toggleActions.onToggleVerdictModal('frm')}>
                  <FontAwesomeIcon icon={faTimes} /> {' Close Cases'}
                </Button>

                <Button
                  outline
                  size="sm"
                  color="success"
                  className="ms-1 hide"
                  title="Open STR Cases"
                  disabled={isBulkOpenStrCaseDisabled}
                  onClick={() => handleOpenSTRCase()}>
                  Open STR Cases
                </Button>
              </>
            )}
            {channel === 'str' && (
              <VerdictModalSTRContainer
                key={'bulkSTR'}
                showText={true}
                disabled={_.isEmpty(bulkCaseIds)}
                bulkCaseIds={bulkCaseIds}
              />
            )}
            <UserListDropdownContainer
              bucket={bucket}
              bulkCaseIds={bulkCaseIds}
              channel={channel}
              showText
            />
          </ButtonGroup>
        );
      case channel == 'frm' && bucket == 'Pending' && userRole == 'checker':
        return (
          <ButtonGroup>
            <MasterQueueFetchButtonContainer />
            <ApprovalModalContainer
              key={channel}
              caseId={''}
              bulkCaseIds={bulkCaseIds}
              status={''}
              channel={channel}
            />
          </ButtonGroup>
        );
      case (['MasterQueue', 'New', 'Open'].includes(bucket) &&
        userRole == 'supervisor' &&
        ((channel === 'str' && !isCooperative) || channel === 'frm')) ||
        ((!isCooperative || channel !== 'frm') &&
          bucket == 'Parked' &&
          ['maker', 'checker'].includes(userRole)):
        return (
          <UserListDropdownContainer
            bucket={bucket}
            bulkCaseIds={bulkCaseIds}
            channel={channel}
            showText
            showMaker={
              channel === 'frm' && bucket === 'MasterQueue' ? pendingCasesInBulkCount < 1 : true
            }
          />
        );
      case (bucket === 'New' || bucket === 'MasterQueue') &&
        ['maker', 'checker'].includes(userRole):
        return (
          <UserListDropdownContainer
            bucket={bucket}
            bulkCaseIds={bulkCaseIds}
            channel={channel}
            showText
            selfAssign
          />
        );
      case bucket == 'Unverified' && channel == 'frm' && userRole == 'investigator':
      case bucket == 'Priority' && channel == 'frm' && userRole == 'investigator':
        return (
          <Button
            size="sm"
            color="danger"
            className="ms-1"
            disabled={_.isEmpty(bulkCaseIds)}
            onClick={() => toggleActions.onToggleVerdictModal('frm')}>
            <FontAwesomeIcon icon={faTimes} /> {' Update Investigation'}
          </Button>
        );
      default:
        return null;
    }
  };

  const bulkActions = (
    <>
      <span className="me-1">{getBulkActions()}</span>
      {!isCooperative &&
        ['maker', 'checker', 'supervisor'].includes(userRole) &&
        channel === 'frm' && <SnoozeCaseModalContainer />}
      {userRole === 'supervisor' && channel === 'frm' && <CaseSettingsContainer />}
    </>
  );

  const handleSelectAll = () => {
    if (selectAll) {
      setBulkCaseIds([]);
      setSelectAll(false);
      setPendingCasesInBulkCount(0);
    } else {
      const caseRefNos = currentPageCases
        .filter((d) => channel !== 'str' || !d?.parentTxn)
        .map((d) => d.caseRefNo);
      const pendingCases = currentPageCases.filter((d) => d?.investigationStatus == 'Pending');
      setPendingCasesInBulkCount(pendingCases.length);
      setBulkCaseIds(caseRefNos);
      setSelectAll(true);
    }
  };

  const handleCheckboxChange = (caseRefNo, status) => {
    let checkedCases = [...bulkCaseIds];
    const index = checkedCases.indexOf(caseRefNo);
    if (index > -1) {
      checkedCases.splice(index, 1);
      setBulkCaseIds([...checkedCases]);
      status === 'Pending' && setPendingCasesInBulkCount((prev) => prev - 1);
    } else {
      checkedCases = [...checkedCases, caseRefNo];
      setBulkCaseIds([...checkedCases]);
      status === 'Pending' && setPendingCasesInBulkCount((prev) => prev + 1);
    }

    const isBulkOpenStrCaseBtnDisabled = _.chain(currentPageCases)
      .filter((item) => _.includes(checkedCases, item.caseRefNo))
      .some((item) => item?.isStrCaseCreated)
      .value();

    setIsBulkOpenStrCaseDisabled(isBulkOpenStrCaseBtnDisabled);
    setSelectAll(checkedCases.length == currentPageCases.length);
  };

  const bucketOptions =
    !closeCaseBuckets.error &&
    closeCaseBuckets.list.map((bucket) => (
      <option key={bucket.id} value={bucket.id}>
        {bucket.name}
      </option>
    ));

  const verdictOptions =
    !fraudTypes.error &&
    fraudTypes.list.map((verdict) => <option key={verdict.id}>{verdict.verdict}</option>);

  const headers = [
    {
      expander: true,
      // eslint-disable-next-line react/prop-types
      Expander: ({ isExpanded, ...rest }) => {
        if (rest.original.reViolatedRules.length == 0) return null;
        else {
          return <FontAwesomeIcon icon={isExpanded ? faCaretDown : faCaretRight} />;
        }
      },
      getProps: (state, rowInfo) => {
        if (rowInfo) {
          if (rowInfo.original.reViolatedRules.length == 0) {
            return {
              onClick: (e) => {
                e.preventDefault();
              }
            };
          }
        }
        return { className: 'cursor-pointer' };
      }
    }
  ];

  ((bucket == 'Open' && !isCooperativeSTRSupervisor && userRole !== 'investigator') ||
    ((!isCooperative || channel !== 'frm') &&
      bucket == 'Parked' &&
      ['maker', 'checker'].includes(userRole)) ||
    ((bucket == 'New' || bucket == 'MasterQueue') && !isCooperativeSTRSupervisor) ||
    (bucket == 'Rejected' && userRole === 'maker') ||
    (bucket == 'Unverified' && channel == 'frm' && userRole == 'investigator') ||
    (bucket == 'Priority' && channel == 'frm' && userRole == 'investigator') ||
    (bucket == 'Pending' && userRole == 'checker' && channel == 'frm')) &&
    headers.push({
      Header: (
        <Input
          bsSize="md"
          type="checkbox"
          id="selectAll"
          onChange={() => handleSelectAll()}
          checked={selectAll}
          disabled={currentPageCases.length === 0}
        />
      ),
      accessor: 'caseRefNo',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 50,
      // eslint-disable-next-line react/prop-types
      Cell: (row) =>
        (channel === 'str' && row?.original?.parentTxn) ||
        (channel === 'frm' &&
          userRole === 'maker' &&
          bucket === 'MasterQueue' &&
          row?.original?.investigationStatus === 'Pending') ? null : (
          <Input
            type="checkbox"
            value={row.value}
            id={row.value}
            name="tableSelect[]"
            checked={_.includes(bulkCaseIds, row.value)}
            onChange={() => handleCheckboxChange(row.value, row.original?.investigationStatus)}
          />
        )
    });

  headers.push(
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      fixed: true,
      minWidth: 190,
      style: { overflow: 'visible' },
      Cell: (row) => (
        <ButtonGroup>
          <Button
            outline
            size="sm"
            title="view"
            color="primary"
            onClick={() => history.push(getPage(userRole, row.original))}
            onContextMenu={() => window.open(getPage(userRole, row.original))}>
            <FontAwesomeIcon icon={faChevronRight} />
          </Button>
          {getActions(row.original)}
        </ButtonGroup>
      )
    },
    {
      Header: 'Transaction Timestamp',
      accessor: 'txnTimestamp',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Entity', accessor: 'entityIdOpt', width: 140 },
    { Header: 'Transaction ID', accessor: 'txnId' },
    {
      Header: 'TRS',
      accessor: 'txnScore',
      Cell: ({ value }) => {
        const colorMap = {
          high: 'danger',
          low: 'success'
        };

        return (
          <Badge
            pill
            color={colorMap[value?.toLowerCase()] || 'secondary'}
            className="ms-4 me-1 badge-sm">
            {value || 'NONE'}
          </Badge>
        );
      }
    },
    { Header: 'Bank', accessor: 'partnerName', show: _.lowerCase(loginType) === 'qrt' },
    ...(hasProvisionalFields === 1
      ? [
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute1" />,
            accessor: 'attribute1'
          },
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute2" />,
            accessor: 'attribute2'
          }
        ]
      : []),
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      style: { textAlign: 'right' },
      headerStyle: { textAlign: 'right' },
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'txnAmount']))
              ? _.find(tableFilters, ['id', 'txnAmount']).value
              : ''
          }
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Amount Foreign',
      accessor: 'amountForeign',
      aggregate: (vals) => _.sum(vals),
      Aggregated: (row) => {
        return <span>{row.value}</span>;
      },
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'amountForeign']))
              ? _.find(tableFilters, ['id', 'amountForeign']).value
              : ''
          }
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Currency',
      accessor: 'txnCurrency',
      Aggregated: (row) => {
        const unique = _.chain(row.subRows)
          .map((d) => d.txnCurrency)
          .uniq()
          .join(', ')
          .value();
        return unique;
      }
    },
    {
      Header: 'Status',
      accessor: 'investigationStatus',
      show: bucket == 'MasterQueue',
      Aggregated: () => '',
      // eslint-disable-next-line jsx-a11y/no-onchange, react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'investigationStatus']))
              ? _.find(tableFilters, ['id', 'investigationStatus']).value
              : ''
          }>
          <option value="">All</option>
          <option>New</option>
          <option>Open</option>
          <option>Pending</option>
          <option>Rejected</option>
          <option>On hold</option>
          <option>Closed</option>
        </select>
      )
    },
    { Header: 'Stage', accessor: 'caseStage' },
    { Header: 'Assigned To', accessor: 'assignedTo' },
    {
      Header: 'Assignment Timestamp',
      accessor: 'assignmentTimeStamp',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Response Code', accessor: 'responseCode' },
    { Header: 'MCC', accessor: 'payeeMcc', width: 140 },
    { Header: 'Channel', accessor: 'channel' },
    { Header: 'Payee ID', accessor: 'payeeId' },
    { Header: 'Payer ID', accessor: 'payerId' },
    { Header: 'Sender Masked Card', accessor: 'senderMaskedCard' },
    { Header: 'Sender Hashed Card', accessor: 'senderHashedCard' },
    { Header: 'Type', accessor: 'txnType', width: 140 },
    {
      Header: 'Violated Rules',
      accessor: 'reViolatedRules',
      Cell: ({ value }) => (!_.isEmpty(value) ? _.split(value, ',').length : 0),
      filterMethod: (filter, row) => row[filter.id] && _.includes(row[filter.id], filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <CombinedViolationDropdownContainer
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'reViolatedRules']))
              ? _.find(tableFilters, ['id', 'reViolatedRules']).value
              : ''
          }
          onChange={(value) => onChange(value)}
          defaultOption="All"
        />
      )
    },
    {
      Header: 'IFRM Action',
      accessor: 'ifrmVerdict',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'ifrmVerdict']))
              ? _.find(tableFilters, ['id', 'ifrmVerdict']).value
              : ''
          }>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    ...(channel === 'str'
      ? [
          {
            Header: 'Suggested Action',
            accessor: 'makerAction',
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'makerAction']))
                    ? _.find(tableFilters, ['id', 'makerAction']).value
                    : ''
                }>
                <option value="">All</option>
                <option>File STR</option>
                <option>Close with false positive</option>
              </select>
            )
          }
        ]
      : [
          {
            Header: 'Case Verdict',
            accessor: 'investigationVerdict',
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'investigationVerdict']))
                    ? _.find(tableFilters, ['id', 'investigationVerdict']).value
                    : ''
                }>
                <option value="">All</option>
                {verdictOptions}
              </select>
            )
          },
          {
            Header: 'Bucket ID',
            accessor: 'bucketId',
            show: false,
            // eslint-disable-next-line react/prop-types
            Cell: ({ value }) => {
              const bucket = closeCaseBuckets.list.find((d) => d.id == value);
              return !_.isEmpty(bucket) ? <span>{bucket.name}</span> : null;
            },
            filterMethod: (filter, row) => row[filter.id] == filter.value,
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              // eslint-disable-next-line jsx-a11y/no-onchange
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'bucketId']))
                    ? _.find(tableFilters, ['id', 'bucketId']).value
                    : ''
                }>
                <option value="">All</option>
                {bucketOptions}
              </select>
            )
          }
        ])
  );

  const tablePageCountProp = _.isEmpty(tableFilters)
    ? {
        pages: data?.count / pageRecords > 1 ? Math.ceil(data?.count / pageRecords) : 1
      }
    : {};

  if (data.loader) return <TableLoader />;

  if (data.error) return <div className="no-data-div">{data?.errorMessage}</div>;

  return (
    <>
      <div className="d-flex justify-content-end mt-2 me-2">{bulkActions}</div>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        columns={headers}
        data={data?.list}
        SubComponent={(row) => (
          <ViolatedRuleNameBadgeContainer
            violatedRulesList={row.original?.reViolatedRules || ''}
            taggedRulesList={row.original?.taggedRule || ''}
          />
        )}
        noDataText={data?.errorMessage || 'No cases found'}
        filterable
        filtered={tableFilters}
        expanded={expanded}
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        pageSize={pageRecords}
        minRows={5}
        page={pageNo - 1}
        onPageChange={(page) => handlePageChange(page + 1)}
        onPageSizeChange={(pageSize, page) => handlePageSizeChange(page + 1, pageSize)}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        onExpandedChange={(expanded) => setExpanded(expanded)}
        showPageJump={false}
        className={'-highlight -striped'}
        {...tablePageCountProp}
      />
      {channel === 'frm' && (
        <VerdictModalContainer
          bucket={bucket}
          caseId={selectedCase.caseRefNo}
          bulkCaseIds={bulkCaseIds}
          channel={channel}
          caseDetails={selectedCase}
          singleType="/singleFromTable"
          partnerId={selectedCase?.partnerId || bulkCaseIds[0]?.partnerId}
          violatedRules={selectedCase?.reViolatedRules}
        />
      )}
    </>
  );
};

BucketCasesTable.propTypes = {
  data: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  userRole: PropTypes.string.isRequired,
  loginType: PropTypes.string.isRequired,
  stages: PropTypes.array.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  fetchCases: PropTypes.func.isRequired,
  fetchStages: PropTypes.func.isRequired,
  currentConf: PropTypes.object.isRequired,
  caseActions: PropTypes.object.isRequired,
  fraudTypes: PropTypes.object.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired,
  toggleActions: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  openSTRCase: PropTypes.func.isRequired
};

export default BucketCasesTable;
