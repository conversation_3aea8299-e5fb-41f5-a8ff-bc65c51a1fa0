import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCustomerEvents } from 'actions/customerEventsActions';
import CustomerEventsTable from 'components/investigation/CustomerEventsTable';

const mapStateToProps = (state) => ({
  customerEvents: state.customerEvents
});

const mapDispatchToProps = (dispatch) => ({
  fetchCustomerEvents: bindActionCreators(onFetchCustomerEvents, dispatch)
});

const CustomerEventsTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(CustomerEventsTable);

export default CustomerEventsTableContainer;
