import { sortBy } from 'lodash';
import { createSelector } from 'reselect';

// Base Selectors - Directly access parts of the Redux state
export const getRuleCreationState = (state) => state.ruleCreation;
export const getRuleConfiguratorState = (state) => state.ruleConfigurator;
export const getSnoozeRulesState = (state) => state.snoozeRules;
export const getToggleState = (state) => state.toggle;
export const getAuthState = (state) => state.auth;
export const getUserState = (state) => state.user;
export const getSandboxingState = (state) => state.sandboxing;

// Rule Creation Selectors
export const getActionList = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.actionList
);

export const getAlertCategories = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.alertCategories
);

export const getRuleChannels = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.ruleChannels
);

export const getFraudCategories = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.fraudCategories
);

export const getRuleLabels = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.ruleLabels
);

export const getHelperList = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.helperList
);

export const getValidation = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.validation
);

export const getNonProductionRules = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.nonProductionRules
);

export const getCheckListOptions = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.checkListOptions
);

export const getCheckList = createSelector(
  [getRuleCreationState],
  (ruleCreation) => ruleCreation.checkList
);

// Rule Configurator Selectors
export const getProductionRules = createSelector(
  [getRuleConfiguratorState],
  (ruleConfigurator) => ruleConfigurator.productionRules
);

export const getArchievedRules = createSelector(
  [getRuleConfiguratorState],
  (ruleConfigurator) => ruleConfigurator.archievedRules
);

export const getRuleNames = createSelector(
  [getRuleConfiguratorState],
  (ruleConfigurator) => ruleConfigurator.ruleNames
);

// Production Rules by Channel Selectors
export const getProductionRulesByChannel = createSelector(
  [getProductionRules, (state, channel) => channel],
  (productionRules, channel) => productionRules.list[channel] || []
);

export const getSortedProductionRulesByChannel = createSelector(
  [getProductionRulesByChannel],
  (rules) => sortBy(rules, ['order', 'name'])
);

// Non-Production Rules by Channel Selectors
export const getNonProductionRulesByChannel = createSelector(
  [getNonProductionRules, (state, channel) => channel],
  (nonProductionRules, channel) => nonProductionRules.list[channel] || []
);

export const getSortedNonProductionRulesByChannel = createSelector(
  [getNonProductionRulesByChannel],
  (rules) => sortBy(rules, ['name'])
);

// Snooze Rules Selectors
export const getSnoozeRulesList = createSelector(
  [getSnoozeRulesState],
  (snoozeRules) => snoozeRules.list
);

export const getSnoozeAttributes = createSelector(
  [getSnoozeRulesState],
  (snoozeRules) => snoozeRules.attributes
);

// Auth & User Selectors
export const getUserRoles = createSelector([getAuthState], (auth) => auth.userCreds.roles);

export const getUserChannels = createSelector([getAuthState], (auth) => auth.userCreds.channels);

export const getModuleType = createSelector([getAuthState], (auth) => auth.moduleType);

export const getHasMakerChecker = createSelector([getUserState], (user) => user.hasMakerChecker);

export const getHasSandbox = createSelector([getUserState], (user) => user.configurations.sandbox);

export const getHasCognitive = createSelector(
  [getUserState],
  (user) => user.configurations.cognitive
);

export const getHasAcquirerPortals = createSelector(
  [getUserState],
  (user) => user.configurations.acquirerPortals
);

// Toggle Selectors
export const getTheme = createSelector([getToggleState], (toggle) => toggle.theme);

// Sandboxing Selectors
export const getSandboxing = createSelector([getSandboxingState], (sandboxing) => sandboxing);

export const getSandboxHistory = createSelector(
  [getSandboxingState],
  (sandboxing) => sandboxing.testHistory
);

// Combined/Complex Selectors
export const getCombinedRuleList = createSelector(
  [getProductionRulesByChannel, getNonProductionRulesByChannel],
  (productionRules, nonProductionRules) => [...productionRules, ...nonProductionRules]
);

export const getRuleCreationFormState = createSelector(
  [
    getActionList,
    getAlertCategories,
    getRuleChannels,
    getFraudCategories,
    getRuleLabels,
    getValidation,
    getHelperList
  ],
  (
    actionList,
    alertCategories,
    ruleChannels,
    fraudCategories,
    ruleLabels,
    validation,
    helperList
  ) => ({
    actionList,
    alertCategories,
    ruleChannels,
    fraudCategories,
    ruleLabels,
    validation,
    helperList
  })
);

export const getUserPermissions = createSelector(
  [getUserRoles, getHasMakerChecker, getHasSandbox, getHasCognitive, getHasAcquirerPortals],
  (roles, hasMakerChecker, hasSandbox, hasCognitive, hasAcquirerPortals) => ({
    roles,
    hasMakerChecker,
    hasSandbox,
    hasCognitive,
    hasAcquirerPortals
  })
);
