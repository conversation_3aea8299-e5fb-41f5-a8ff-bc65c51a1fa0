import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label } from 'reactstrap';
import { MultiSelect } from 'react-multi-select-component';

function CategoryBasedRulesDropdown({
  ruleList,
  ruleState,
  fetchRules,
  ruleLabel = 'Possible Rule violation',
  categoryList,
  fetchCategory,
  categoryState,
  categoryLabel = 'Possible rule classification'
}) {
  useEffect(() => {
    if (categoryList.length === 0) fetchCategory('frm');
    if (ruleList.length === 0) fetchRules('frm');
  }, []);

  const selectedCategoryIds = categoryState?.value?.map((d) => d.value);

  const categoryOptionsData = categoryList?.map((d) => ({
    value: d?.id,
    label: d?.categoryName,
    description: d?.desc
  }));

  const ruleOptionsData = ruleList?.map((d) => ({
    value: d?.code,
    label: d?.name,
    description: d?.description,
    alertCategoryId: d?.alertCategoryId,
    methodType: d?.methodType
  }));

  const filteredRules = ruleOptionsData.filter((d) =>
    selectedCategoryIds?.includes(d.alertCategoryId)
  );

  const itemRenderer = ({ checked, option, onClick, disabled }) => (
    <div className={`item-renderer ${disabled ? 'disabled' : ''}`}>
      <input
        type="checkbox"
        onChange={onClick}
        checked={checked}
        tabIndex={-1}
        disabled={disabled}
      />
      <span>{option.label}</span>
      {option.label !== 'Select All' && <small className="ms-1">({option.description})</small>}
    </div>
  );

  const itemRendererRule = ({ checked, option, onClick, disabled }) => {
    return (
      <div className={`d-flex flex-column item-renderer ${disabled ? 'disabled' : ''}`}>
        <div>
          <input
            type="checkbox"
            onChange={onClick}
            checked={checked}
            tabIndex={-1}
            disabled={disabled}
          />
          {option.label}
        </div>
        {option.label !== 'Select All' && <small className="ms-1">({option.description})</small>}
      </div>
    );
  };

  return (
    <>
      <FormGroup>
        <Label>{categoryLabel}</Label>
        <MultiSelect
          options={categoryOptionsData}
          labelledBy="select category"
          name="txnCategory"
          value={categoryState.value}
          onChange={categoryState.onChange}
          ItemRenderer={itemRenderer}
        />
      </FormGroup>
      <FormGroup>
        <Label>{ruleLabel}</Label>
        <MultiSelect
          options={filteredRules}
          labelledBy="select rule"
          name="rule"
          value={ruleState.value}
          onChange={ruleState.onChange}
          disabled={categoryState.value.length == 0}
          ItemRenderer={itemRendererRule}
        />
      </FormGroup>
    </>
  );
}

CategoryBasedRulesDropdown.propTypes = {
  ruleLabel: PropTypes.string,
  categoryLabel: PropTypes.string,
  ruleList: PropTypes.array.isRequired,
  ruleState: PropTypes.object.isRequired,
  categoryList: PropTypes.array.isRequired,
  categoryState: PropTypes.object.isRequired,
  fetchCategory: PropTypes.func.isRequired,
  fetchRules: PropTypes.func.isRequired
};

export default CategoryBasedRulesDropdown;
