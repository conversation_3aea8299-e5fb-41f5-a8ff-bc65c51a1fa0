import {
  ON_FETCH_UDS_ENTITY_DETAILS_LOADING,
  ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
  ON_FETCH_UDS_ENTITY_DETAILS_FAILURE,
  ON_FETCH_UDS_ENTITY_SCORES_LOADING,
  ON_FETCH_UDS_ENTITY_SCORES_SUCCESS,
  ON_FETCH_UDS_ENTITY_SCORES_FAILURE,
  ON_FETCH_FACCTUM_DETAILS_LOADING,
  ON_FETCH_FACCTUM_DETAILS_SUCCESS,
  ON_FETCH_FACCTUM_DETAILS_FAILURE,
  ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE,
  ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS
} from 'constants/actionTypes';
import client from 'utility/apiClient';

export function fetchUDSEntityScores(entity, entityId) {
  return client({
    url: `uds/${entity}/${entityId}/score`,
    badRequestMessage: `Unable to find risk score for selected ${entity}`,
    notFoundMessage: `No risk score found for selected ${entity}`
  });
}

function onFetchUDSEntityScoresLoading(entity) {
  return { type: ON_FETCH_UDS_ENTITY_SCORES_LOADING, entity };
}

export function onFetchUDSEntityScoresSuccess(entity, response) {
  return {
    type: ON_FETCH_UDS_ENTITY_SCORES_SUCCESS,
    entity,
    response
  };
}

export function onFetchUDSEntityScoresFailure(response) {
  return {
    type: ON_FETCH_UDS_ENTITY_SCORES_FAILURE,
    response
  };
}

export function onFetchUDSEntityScores(entity, entityId) {
  return function (dispatch) {
    dispatch(onFetchUDSEntityScoresLoading(entity, entityId));
    return fetchUDSEntityScores(entity, entityId).then(
      (success) => dispatch(onFetchUDSEntityScoresSuccess(entity, success)),
      (error) => dispatch(onFetchUDSEntityScoresFailure({ entity, ...error }))
    );
  };
}

function fetchUDSEntityDetails(entity, entityId) {
  return client({
    url: `uds/${entity}/${entityId}`,
    badRequestMessage: `Unable to get ${entity} details for current transaction`,
    notFoundMessage: `No details found for selected ${entity}`
  });
}

function onFetchUDSEntityDetailsLoading(entity) {
  return { type: ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity };
}

function onSuccessfulFetchUDSEntityDetails(response, entity) {
  return {
    type: ON_FETCH_UDS_ENTITY_DETAILS_SUCCESS,
    response,
    entity
  };
}

function onFetchUDSEntityDetailsFailure(response) {
  return {
    type: ON_FETCH_UDS_ENTITY_DETAILS_FAILURE,
    response
  };
}

function onFetchUDSEntityDetails(entity, entityId) {
  return function (dispatch) {
    dispatch(onFetchUDSEntityDetailsLoading(entity));
    return fetchUDSEntityDetails(entity, entityId).then(
      (success) => dispatch(onSuccessfulFetchUDSEntityDetails(success, entity)),
      (error) => dispatch(onFetchUDSEntityDetailsFailure({ entity, ...error }))
    );
  };
}

function fetchFacctumDetails(formData) {
  return client({
    method: 'POST',
    url: `uds/str/facctum/match`,
    data: formData,
    badRequestMessage: 'Unable to get facctum details',
    notFoundMessage: 'Unable to connec to Facctum services'
  });
}

function onFetchFacctumDetailsLoading() {
  return { type: ON_FETCH_FACCTUM_DETAILS_LOADING };
}

function onSuccessfulFetchFacctumDetails(response) {
  return {
    type: ON_FETCH_FACCTUM_DETAILS_SUCCESS,
    response
  };
}

function onFetchFacctumDetailsFailure(response) {
  return {
    type: ON_FETCH_FACCTUM_DETAILS_FAILURE,
    response
  };
}

function onFetchFacctumDetails(formData) {
  return function (dispatch) {
    dispatch(onFetchFacctumDetailsLoading());
    return fetchFacctumDetails(formData).then(
      (success) => dispatch(onSuccessfulFetchFacctumDetails(success)),
      (error) => dispatch(onFetchFacctumDetailsFailure(error))
    );
  };
}

function fetchVulnerabilityDetails(formData) {
  return client({
    url:
      `customerservice/channel/${formData.channel}/customer-id/${formData.customerId}/` +
      `account-number/${formData.accountNo}/risk`
  });
}

function onSuccessfulFetchVulnerabilityDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_CUSTOMER_VULNERABILITY_DETAILS,
    response
  };
}

function onFetchVulnerabilityDetailsFailure(response) {
  return {
    type: ON_FETCH_CUSTOMER_VULNERABILITY_DETAILS_FAILURE,
    response
  };
}

function onFetchVulnerabilityDetails(formData) {
  return function (dispatch) {
    return fetchVulnerabilityDetails(formData).then(
      (success) => dispatch(onSuccessfulFetchVulnerabilityDetails(success)),
      (error) => dispatch(onFetchVulnerabilityDetailsFailure(error))
    );
  };
}

function fetchCustomerDetailsWithAllAccounts(customerId) {
  return client({ url: `uds/frm/customerwithallaccounts/${customerId}` });
}

function onFetchCustomerDetailsWithAllAccounts(customerId) {
  return function (dispatch) {
    dispatch(onFetchUDSEntityDetailsLoading('customer'));
    return fetchCustomerDetailsWithAllAccounts(customerId).then(
      (success) => dispatch(onSuccessfulFetchUDSEntityDetails(success, 'customer')),
      (error) => dispatch(onFetchUDSEntityDetailsFailure({ ...error, entity: 'customer' }))
    );
  };
}

export {
  onFetchUDSEntityDetails,
  onFetchFacctumDetails,
  onFetchVulnerabilityDetails,
  onFetchCustomerDetailsWithAllAccounts
};
