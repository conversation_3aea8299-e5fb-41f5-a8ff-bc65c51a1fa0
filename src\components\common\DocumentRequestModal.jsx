import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Form, FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const HoldCaseModal = ({ toggle, toggleRequestDocumentModal, handleRequestDocumentSubmit }) => {
  const [comment, setComment] = useState('');

  useEffect(() => {
    return setComment('');
  });

  const onSubmit = (e) => {
    e.preventDefault();
    handleRequestDocumentSubmit(comment);
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="warning"
        className="ms-1"
        title="Request Documents For Verification"
        onClick={() => toggleRequestDocumentModal()}>
        Request Documents
      </Button>
      <ModalContainer
        theme={toggle.theme}
        isOpen={toggle.requestDocumentModal}
        header={'Request Verification Documents'}
        size="md"
        toggle={toggleRequestDocumentModal}>
        <Form onSubmit={onSubmit}>
          <FormGroup>
            <Label>Description</Label>
            <Input
              type="textarea"
              name="description"
              rows="5"
              value={comment}
              onChange={(event) => setComment(event.target.value)}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button size="sm" type="submit" color="success">
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </>
  );
};

HoldCaseModal.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleRequestDocumentModal: PropTypes.func.isRequired,
  handleRequestDocumentSubmit: PropTypes.func.isRequired
};

export default HoldCaseModal;
