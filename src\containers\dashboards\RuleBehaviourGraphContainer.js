import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRuleBehaviour } from 'actions/ruleDashboardActions';
import RuleBehaviourGraph from 'components/dashboards/RuleBehaviourGraph';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  ruleBehaviour: state.ruleDashboard.behaviour
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleBehaviour: bindActionCreators(onFetchRuleBehaviour, dispatch)
});

const RuleBehaviourGraphContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleBehaviourGraph);

export default RuleBehaviourGraphContainer;
