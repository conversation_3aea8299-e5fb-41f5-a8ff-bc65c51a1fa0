import PropTypes from 'prop-types';
import React from 'react';
import { FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const RuleApprovalModal = ({
  theme,
  setComment,
  submitAction,
  ruleApproval,
  toggleApprovalModal
}) => (
  <ModalContainer
    size="ml"
    theme={theme}
    header={`Rule ${ruleApproval.isApproved ? 'Approval' : 'Rejection'} -  ${
      ruleApproval.ruleApprovalId
    }`}
    isOpen={ruleApproval.showModal}
    toggle={() => toggleApprovalModal()}>
    <form onSubmit={submitAction}>
      <FormGroup>
        <Label for="comment">Comment</Label>
        <Input
          type="textarea"
          name="comment"
          id="comment"
          placeholder="comment"
          onChange={(e) => setComment(e.target.value)}
          value={ruleApproval.comments}
          required
        />
      </FormGroup>
      <FormGroup className="d-flex justify-content-end">
        <Button type="submit" size="sm" color="primary">
          Submit
        </Button>
      </FormGroup>
    </form>
  </ModalContainer>
);

RuleApprovalModal.propTypes = {
  theme: PropTypes.string.isRequired,
  ruleApproval: PropTypes.object.isRequired,
  setComment: PropTypes.func.isRequired,
  submitAction: PropTypes.func.isRequired,
  toggleApprovalModal: PropTypes.func.isRequired
};

export default RuleApprovalModal;
