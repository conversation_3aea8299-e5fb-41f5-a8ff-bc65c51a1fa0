import { faStopwatch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import CollapsiblePill from 'components/common/CollapsiblePill';
import Loader from 'components/loader/Loader';
import ViolationCardLoader from 'components/loader/ViolationCardLoader';
import { isCooperative } from 'constants/publicKey';
import RuleSnoozeModalContainer from 'containers/common/SnoozeRuleModalContainer';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';

const ViolatedRulesCard = ({
  transactionId,
  txnTimestamp,
  actions,
  reViolatedRules,
  violations,
  cognitiveViolations,
  channel,
  role
}) => {
  const [displayModal, setDisplayModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState({ ruleCode: '', ruleName: '' });
  const [tableFilters, setTableFilters] = useState([]);

  useEffect(() => {
    transactionId &&
      txnTimestamp &&
      reViolatedRules?.length > 0 &&
      actions.onFetchViolatedRules(transactionId, channel, { code: reViolatedRules, txnTimestamp });
  }, [transactionId, channel, reViolatedRules, txnTimestamp]);

  const snoozeRule = ({ ruleCode, ruleName }) => {
    setSelectedRule({ ruleCode, ruleName });
    setDisplayModal(true);
  };

  const { list, loader, transactionLoader, error, errorMessage } = violations;

  const reRuleSnooze = (ruleCode, ruleName, snoozeStatus) => (
    <Button
      size="sm"
      color="primary"
      disabled={snoozeStatus === 1 || role !== 'checker'}
      title={snoozeStatus !== 1 ? 'Snooze Rule' : 'Rule is Snoozed!'}
      onClick={() => snoozeRule({ ruleCode, ruleName })}
      {...(snoozeStatus !== 1 ? { outline: true } : {})}>
      <FontAwesomeIcon icon={faStopwatch} />
    </Button>
  );

  const fetchRuleViolationTransactions = (transactionId, channel, rule) => {
    if (channel === 'str' && !_.has(rule, 'transactions'))
      actions.onFetchViolatedRulesTransactions(transactionId, channel, { code: rule.code });
  };

  // Helper function to render transaction content for a rule
  const renderRuleTransactionContent = (rule) => {
    if (!_.isEmpty(rule?.transactions))
      return (
        <TransactionTableContainer
          minRows={3}
          pageSize={5}
          filtered={tableFilters}
          data={{ list: rule.transactions }}
          onFilteredChange={(filtered) => setTableFilters(filtered)}
        />
      );

    if (transactionLoader && !_.has(rule, 'transactions')) return <Loader show={true} />;

    return null;
  };

  // Helper function to render the main content
  const renderCardContent = () => {
    if (_.isEmpty(transactionId)) return <div className="no-data-div">No transactionId found</div>;

    if (loader) return <ViolationCardLoader />;

    if (error) return <div className="no-data-div">{errorMessage}</div>;

    if (_.isEmpty(list.rules) && _.isEmpty(cognitiveViolations))
      return <div className="no-data-div">No violations for current transaction</div>;

    return (
      <Row className="txn-info">
        {!_.isEmpty(list.rules) && (
          <Col md={_.isEmpty(cognitiveViolations) ? 12 : 6} sm="12" xs="12">
            <p className="card-subtitle">Rule Engine Violations</p>
            <div className="d-flex flex-wrap">
              {list.rules.map((rule, index) => (
                <CollapsiblePill
                  key={index}
                  name={rule.name}
                  onClick={() => fetchRuleViolationTransactions(transactionId, channel, rule)}
                  addOn={
                    !isCooperative &&
                    channel !== 'str' &&
                    reRuleSnooze(rule.code, rule.name, rule.isSnooze)
                  }>
                  <p className="description-text">
                    <b>Description: </b> {rule.description}
                  </p>
                  {!_.isEmpty(rule?.citationNames) && channel === 'frm' && (
                    <div className="description-text">
                      <b>Line of Enquiry: </b>
                      <span>
                        <ul>
                          {_.chain(rule.citationNames)
                            .split('~')
                            .map((d) => <li key={_.kebabCase(d)}>{d}</li>)
                            .value()}
                        </ul>
                      </span>
                    </div>
                  )}
                  {renderRuleTransactionContent(rule)}
                </CollapsiblePill>
              ))}
            </div>
          </Col>
        )}
        {!_.isEmpty(cognitiveViolations) && (
          <Col md={_.isEmpty(violations.list) ? 12 : 6} sm="12" xs="12">
            <p className="card-subtitle">Cognitive Violations</p>
            <Row>
              {cognitiveViolations.map((rule, index) => (
                <Col key={index} md={_.isEmpty(violations.list) ? 6 : 12} sm="12" xs="12">
                  <CollapsiblePill key={index} name={rule.methodName}>
                    <p className="description-text">
                      <b>Description: </b> {rule.description}
                    </p>
                  </CollapsiblePill>
                </Col>
              ))}
            </Row>
          </Col>
        )}
      </Row>
    );
  };

  return (
    <CardContainer icon="times" color="bg-warning" title="Violated Rules">
      <Card>{renderCardContent()}</Card>
      {!isCooperative && channel !== 'str' && (
        <RuleSnoozeModalContainer
          show={displayModal}
          onToggle={() => setDisplayModal(!displayModal)}
          ruleCode={selectedRule.ruleCode}
          ruleName={selectedRule.ruleName}
          channel={channel}
        />
      )}
    </CardContainer>
  );
};

ViolatedRulesCard.propTypes = {
  violations: PropTypes.object.isRequired,
  txnTimestamp: PropTypes.string.isRequired,
  transactionId: PropTypes.string.isRequired,
  reViolatedRules: PropTypes.array.isRequired,
  cognitiveViolations: PropTypes.array,
  actions: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  role: PropTypes.string.isRequired
};

export default ViolatedRulesCard;
