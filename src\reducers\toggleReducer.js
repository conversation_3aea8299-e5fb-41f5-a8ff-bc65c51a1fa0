import {
  ON_TOG<PERSON><PERSON>_THEME,
  ON_TOGGLE_LOADER,
  ON_TOGGLE_UPLOAD_LOADER,
  ON_TOGGLE_SHIFT_MOD<PERSON>,
  ON_TOGGLE_VERDICT_MODAL,
  ON_TOGGLE_DOWNLOAD_STR_MODAL,
  ON_TOGGLE_APPROVAL_MODAL,
  ON_TOGGLE_CREATE_CASE_MODAL,
  ON_TOGGLE_ADD_USER_MODAL,
  ON_TOGGLE_ADD_BANK_MOD<PERSON>,
  ON_TOGGLE_WATCHLIST_MOD<PERSON>,
  ON_TOGGLE_BLACKLIST_MODAL,
  ON_TOGGLE_RULE_EDIT_<PERSON>OD<PERSON>,
  ON_TOGGLE_PREFILTER_<PERSON>OD<PERSON>,
  ON_TOG<PERSON><PERSON>_ESCALATION_MODAL,
  ON_TOGGLE_RULE_CREATE_MODAL,
  ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL,
  ON_TOGGLE_ASSIGN_SHIFT_MOD<PERSON>,
  ON_TOGGLE_RULE_DUPLIC<PERSON><PERSON>_MODAL,
  ON_TOGGLE_UPDATE_USER_ROLES_MODAL,
  ON_TOGGLE_PREFILTERS_LIST_MODAL,
  ON_TOGGLE_CREATE_LIST_MODAL,
  ON_TOGGLE_RELEASE_FUNDS_MODAL,
  ON_TOGGLE_HOLD_CASE_MODAL,
  ON_TOGGLE_REQUEST_DOCUMENT_MODAL,
  ON_TOGGLE_CONFIRM_ALERT_MODAL,
  ON_TOGGLE_STATUS_LOG_MODAL,
  ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL,
  ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL,
  ON_TOGGLE_CPIFR_FORM_MODAL,
  ON_TOGGLE_USER_CASE_CRITERIA_MODAL,
  ON_TOGGLE_CREATE_LABEL_MODAL,
  ON_TOGGLE_RULE_FEEDBACK_MODAL,
  ON_TOGGLE_RESET_PASSWORD_MODAL
} from 'constants/actionTypes';
import { isCooperative } from 'constants/publicKey';

import initialState from './initialState';

export default function toggleReducer(state = initialState.toggle, action) {
  switch (action.type) {
    case ON_TOGGLE_THEME:
      return {
        ...state,
        theme: state.theme === 'dark' ? 'light' : 'dark'
      };
    case ON_TOGGLE_LOADER:
      return {
        ...state,
        loader: action.state
      };
    case ON_TOGGLE_UPLOAD_LOADER:
      return {
        ...state,
        uploadLoader: action.state
      };
    case ON_TOGGLE_VERDICT_MODAL:
      return {
        ...state,
        verdictModal: {
          ...state.verdictModal,
          [action.channel]: !state.verdictModal[action.channel]
        }
      };
    case ON_TOGGLE_APPROVAL_MODAL:
      return {
        ...state,
        approvalModal: !state.approvalModal
      };
    case ON_TOGGLE_DOWNLOAD_STR_MODAL:
      return {
        ...state,
        downloadSTRModal: !state.downloadSTRModal
      };
    case ON_TOGGLE_CREATE_CASE_MODAL:
      return {
        ...state,
        createCaseModal: !state.createCaseModal
      };
    case ON_TOGGLE_ADD_USER_MODAL:
      return {
        ...state,
        addUserModal: !state.addUserModal
      };
    case ON_TOGGLE_ADD_BANK_MODAL:
      return {
        ...state,
        addBankModal: !state.addBankModal
      };
    case ON_TOGGLE_UPDATE_USER_ROLES_MODAL:
      return {
        ...state,
        updateUserRolesModal: !state.updateUserRolesModal
      };
    case ON_TOGGLE_SHIFT_MODAL:
      return {
        ...state,
        shiftModal: !state.shiftModal
      };
    case ON_TOGGLE_ESCALATION_MODAL:
      return {
        ...state,
        escalationModal: !state.escalationModal
      };
    case ON_TOGGLE_BLACKLIST_MODAL:
      return {
        ...state,
        blacklistModal: !state.blacklistModal
      };
    case ON_TOGGLE_WATCHLIST_MODAL:
      return {
        ...state,
        watchlistModal: !state.watchlistModal
      };
    case ON_TOGGLE_PREFILTERS_LIST_MODAL:
      return {
        ...state,
        prefiltersListModal: {
          ...state.prefiltersListModal,
          [action.listType]: !state.prefiltersListModal[action.listType]
        }
      };
    case ON_TOGGLE_RELEASE_FUNDS_MODAL:
      return {
        ...state,
        releaseFundsModal: !state.releaseFundsModal
      };
    case ON_TOGGLE_HOLD_CASE_MODAL:
      return {
        ...state,
        holdCaseModal: !state.holdCaseModal
      };
    case ON_TOGGLE_REQUEST_DOCUMENT_MODAL:
      return {
        ...state,
        requestDocumentModal: !state.requestDocumentModal
      };
    case ON_TOGGLE_CREATE_LIST_MODAL:
      return {
        ...state,
        prefiltersListModal: {
          ...state.prefiltersListModal,
          createListModal: !state.prefiltersListModal.createListModal
        }
      };
    case ON_TOGGLE_CONFIRM_ALERT_MODAL:
      return {
        ...state,
        confirmAlertModal: {
          ...state.confirmAlertModal,
          [action.listType]: !state.confirmAlertModal[action.listType]
        }
      };
    case ON_TOGGLE_RULE_CREATE_MODAL:
      return {
        ...state,
        ruleCreateModal: {
          ...state.ruleCreateModal,
          [action.channel]: !state.ruleCreateModal[action.channel]
        }
      };
    case ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL:
      return {
        ...state,
        dynamicCountersCreateModal: !state.dynamicCountersCreateModal
      };
    case ON_TOGGLE_RULE_EDIT_MODAL:
      return {
        ...state,
        ruleEditModal: {
          ...state.ruleEditModal,
          [action.channel]: !state.ruleEditModal[action.channel]
        }
      };
    case ON_TOGGLE_RULE_DUPLICATE_MODAL:
      return {
        ...state,
        ruleDuplicateModal: {
          ...state.ruleDuplicateModal,
          [action.channel]: !state.ruleDuplicateModal[action.channel]
        }
      };
    case ON_TOGGLE_ASSIGN_SHIFT_MODAL:
      return {
        ...state,
        assignShiftModal: !state.assignShiftModal
      };
    case ON_TOGGLE_PREFILTER_MODAL:
      return {
        ...state,
        prefilterModal: {
          ...state.prefilterModal,
          [action.channel]: !state.prefilterModal[action.channel]
        }
      };
    case ON_TOGGLE_STATUS_LOG_MODAL:
      return {
        ...state,
        statusLogModal: !state.statusLogModal
      };
    case ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL:
      return {
        ...state,
        addToListConfirmAlertModal: !state.addToListConfirmAlertModal
      };
    case ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL:
      return {
        ...state,
        requestForInformationModal: !state.requestForInformationModal
      };
    case ON_TOGGLE_CPIFR_FORM_MODAL:
      return {
        ...state,
        cpifrFormModal: !state.cpifrFormModal
      };
    case ON_TOGGLE_USER_CASE_CRITERIA_MODAL:
      return {
        ...state,
        userCaseCriteriaModal: !state.userCaseCriteriaModal
      };
    case ON_TOGGLE_CREATE_LABEL_MODAL:
      return {
        ...state,
        createLabelModal: !state.createLabelModal
      };
    case ON_TOGGLE_RULE_FEEDBACK_MODAL:
      return {
        ...state,
        ruleFeedbackModal: !isCooperative && !state.ruleFeedbackModal
      };
    case ON_TOGGLE_RESET_PASSWORD_MODAL:
      return {
        ...state,
        resetPasswordModal: !state.resetPasswordModal
      };
    default:
      return state;
  }
}
