import advanceSearchTxnsReducer from 'reducers/advanceSearchTxnsReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';
import _ from 'lodash';
import responses from 'mocks/responses';

describe('Advance Search Txns Reducer', () => {
  it('should return the intial state', () => {
    expect(advanceSearchTxnsReducer(undefined, {})).toEqual(initialState.advanceSearchTxns);
  });

  it('should handle ON_ADVANCE_SEARCH_TRANSACTION_LOADING', () => {
    expect(
      advanceSearchTxnsReducer(
        {},
        {
          type: types.ON_ADVANCE_SEARCH_TRANSACTION_LOADING
        }
      )
    ).toEqual({
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS', () => {
    expect(
      advanceSearchTxnsReducer(
        {},
        {
          type: types.ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS,
          response: responses.caseAssignment.cases,
          channel: 'rpsl',
          filterCondition: {
            startTimestamp: '2021-10-05T00:00:00',
            endTimestamp: '2021-10-07T00:00:00',
            identifiers: {}
          }
        }
      )
    ).toEqual({
      filterCondition: {
        startTimestamp: '2021-10-05T00:00:00',
        endTimestamp: '2021-10-07T00:00:00',
        identifiers: {}
      },
      list: responses.caseAssignment.cases.records,
      count: responses.caseAssignment.cases.count,
      isLastPage: responses.caseAssignment.cases.isLastPage,
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_ADVANCE_SEARCH_TRANSACTION_FAILURE', () => {
    expect(
      advanceSearchTxnsReducer(
        {},
        {
          type: types.ON_ADVANCE_SEARCH_TRANSACTION_FAILURE,
          response: { message: 'not found' },
          filterCondition: {
            startTimestamp: '2021-10-05T00:00:00',
            endTimestamp: '2021-10-07T00:00:00',
            identifiers: {}
          }
        }
      )
    ).toEqual({
      filterCondition: {
        startTimestamp: '2021-10-05T00:00:00',
        endTimestamp: '2021-10-07T00:00:00',
        identifiers: {}
      },
      list: [],
      count: 0,
      isLastPage: true,
      loader: false,
      error: true,
      errorMessage: 'not found'
    });
  });

  it('should handle ON_REMOVE_SEARCH_TRANSACTION', () => {
    expect(
      advanceSearchTxnsReducer(
        {
          list: []
        },
        {
          type: types.ON_REMOVE_SEARCH_TRANSACTION,
          items: ['Tes114']
        }
      )
    ).toEqual({
      list: _.filter(
        initialState.advanceSearchTxns.list,
        (listItem) =>
          !_.includes(['Tes114'], listItem.caseRefNo) && !_.includes(['Tes114'], listItem.txnId)
      )
    });
  });
});
