import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function DualRole({ highlightText, saveConfigurations, configurationsData, toggleRow, openRows }) {
  const [dualRole, setDualRole] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        dualRole,
        setDualRole,
        saveConfigurations
      ),
    [configurationsData, dualRole, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, dualRole, setDualRole),
    [dualRole]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setDualRole),
    [configurationsData]
  );

  return (
    <ConfigFormWrapper
      configTitle="Dual Role"
      activationId="activationDualRole"
      data={dualRole}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      highlightText={highlightText}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}
    />
  );
}

DualRole.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired
};

export default DualRole;
