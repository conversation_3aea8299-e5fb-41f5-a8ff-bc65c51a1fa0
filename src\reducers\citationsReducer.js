import {
  ON_<PERSON>ETCH_CASE_CITATION_LOADING,
  ON_FETCH_CASE_CITATION_SUCCESS,
  ON_FETCH_CASE_CITATION_FAILURE,
  ON_ADD_CITATION_COMMENT_LOADING,
  ON_ADD_CITATION_COMMENT_SUCCESS,
  ON_ADD_CITATION_COMMENT_FAILURE
} from 'constants/actionTypes';
import initialState from './initialState';
import objectAssign from 'object-assign';

export default function citationsReducer(state = initialState.citations, action) {
  switch (action.type) {
    case ON_FETCH_CASE_CITATION_LOADING:
      return { ...initialState.citations, loader: true };
    case ON_FETCH_CASE_CITATION_SUCCESS:
      return objectAssign({}, state, {
        loader: false,
        caseRefNo: action.response.caseRefNo,
        data: objectAssign({}, state.data, {
          list: action.response.ruleCitationResponses
        })
      });
    case ON_FETCH_CASE_CITATION_FAILURE:
      return objectAssign({}, state, {
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      });
    case ON_ADD_CITATION_COMMENT_LOADING:
      return objectAssign({}, state, {
        data: objectAssign({}, state.data, { loader: true, error: false, errorMessage: '' })
      });
    case ON_ADD_CITATION_COMMENT_SUCCESS:
      return objectAssign({}, state, {
        data: objectAssign({}, state.data, {
          loader: false,
          list: state.data.list.map((citation) => {
            const ruleCitationResponse = {
              userId: action.response.userId,
              userName: action.response.userName,
              userRole: 'str:' + action.response.userRole,
              response: action.response.response,
              creationTimestamp: new Date()
            };
            return citation.citationId === action.response.citationId
              ? objectAssign({}, citation, {
                  citationResponses: [...citation.citationResponses, { ruleCitationResponse }]
                })
              : citation;
          })
        })
      });
    case ON_ADD_CITATION_COMMENT_FAILURE:
      return objectAssign({}, state, {
        data: objectAssign({}, state.data, {
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
