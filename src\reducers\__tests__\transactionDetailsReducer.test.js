import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import transactionDetailsReducer from 'reducers/transactionDetailsReducer';

describe('Transaction Details reducer', () => {
  it('should return the intial state', () => {
    expect(transactionDetailsReducer(undefined, {})).toEqual(initialState.transactionDetails);
  });

  it('should handle ON_FETCH_TRANSACTION_DETAIL_LOADING', () => {
    expect(
      transactionDetailsReducer(
        {},
        {
          type: types.ON_FETCH_TRANSACTION_DETAIL_LOADING
        }
      )
    ).toEqual({
      details: {},
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL', () => {
    const response = {
      entityId: {
        value: 'Agent10036',
        categoryName: 'Agent ID',
        listTypeData: ['TestOTP', 'Negative']
      },
      entityCategory: 'agent',
      transactionInfo: {
        txnTimestamp: '2020-12-20T11:00:15',
        txnId: 'Test20210120715',
        txnAmount: 2000,
        txnCurrency: '356',
        txnType: '42',
        txnCategoryName: '',
        txnMessageId: 'rpsl010',
        txnCategoryId: '',
        referenceTxnId: ''
      },
      masterFields: {
        txnTypeName: '42, Bill Payment Close Loop',
        payerAccountTypeName: '12, acctype',
        payeeAccountTypeName: '12, acctype',
        channelName: '',
        sourceInstitutionName: '7172, sample siname',
        paymentMethodName: '11',
        acquirerName: 'acquirerId, acquirer name',
        responseCodeName: '00, Accepted',
        payerMccCodeName: '5555, Provision Store',
        payeeMccCodeName: '5411, Kirana'
      },
      isMerchantApiBased: '',
      deviceInfo: {
        isCardJio: '1',
        customerIp: {
          value: '',
          categoryName: 'Customer IP',
          listTypeData: []
        },
        deviceId: {
          value: 'IMEI12345',
          categoryName: 'Device ID',
          listTypeData: []
        },
        deviceOs: {
          value: '',
          categoryName: 'Device OS',
          listTypeData: []
        },
        terminalId: {
          value: '12340',
          categoryName: 'Terminal ID',
          listTypeData: []
        }
      },
      payeeDeviceOf: 'A',
      identifiers: {
        agentId: {
          value: 'Agent10036',
          categoryName: 'Agent ID',
          listTypeData: ['TestOTP', 'Negative']
        },
        acquirerId: {
          value: 'Agent10036',
          categoryName: 'Acquirer ID',
          listTypeData: []
        },
        initiatorId: {
          value: '2102',
          categoryName: 'Initiator ID',
          listTypeData: ['OTP']
        },
        sourceInstitutionId: '7172',
        subAgentId: {
          value: '',
          categoryName: 'Sub Agent ID',
          listTypeData: ['Blocked', 'Suspicious']
        },
        initiatorMobile: {
          value: '',
          categoryName: 'Initiator Mobile',
          listTypeData: []
        },
        initiatorType: '',
        initiatedBy: '123',
        retrievalRefNo: '123',
        originalPaymentTxnId: '123',
        virtualPan: '123',
        isCardPresent: '123'
      },
      isLien: '',
      locationCoordinates: {
        latitude: {
          value: '23.885838',
          categoryName: 'Latitude',
          listTypeData: ['Testblue', 'VIP', 'Green List', 'OTP', 'Suspicious']
        },
        longitude: {
          value: '79.923935',
          categoryName: 'Longitude',
          listTypeData: ['Blocked', 'Suspicious']
        }
      },
      mti: '0200',
      payeeAccount: {
        payeeAccountNumber: {
          value: 'rpsl123',
          categoryName: 'Account Number',
          listTypeData: ['VIP', 'Blocked', 'Blue', 'Suspicious', 'OTP']
        },
        payeeAccountType: '12',
        payeeBankIfsc: {
          value: '',
          categoryName: 'Bank Ifsc',
          listTypeData: []
        },
        payeeId: {
          value: '10005',
          categoryName: 'Payee ID',
          listTypeData: []
        },
        payeeMcc: {
          value: '5411',
          categoryName: 'Mcc',
          listTypeData: ['specializedList', 'OTP']
        },
        payeeMmid: '',
        payeeType: 'ENTITY',
        payeeCardNumberMask: '',
        payeeCardNumberHash: {
          value: '',
          categoryName: 'Hash Card Number',
          listTypeData: []
        },
        payeeVPA: '123'
      },
      payerAccount: {
        payerAccountNumber: {
          value: 'rpsl456',
          categoryName: 'Account Number',
          listTypeData: ['Blocked', 'OTP']
        },
        payerAccountType: '12',
        payerBankIfsc: {
          value: '',
          categoryName: 'Bank Ifsc',
          listTypeData: []
        },
        payerId: {
          value: '992233',
          categoryName: 'Payer ID',
          listTypeData: ['TestBlack']
        },
        payerMcc: {
          value: '5555',
          categoryName: 'Mcc',
          listTypeData: ['Testblue', 'specializedList', 'Negative']
        },
        payerMmid: '',
        payerType: 'PERSON',
        payerCardNumberMask: '',
        payerCardNumberHash: {
          value: '************',
          categoryName: 'Hash Card Number',
          listTypeData: []
        },
        payerVPA: '123'
      },
      paymentMethod: '11',
      responseCode: '00',
      ifrmVerdict: 'REJECTED',
      cognitiveResponse: '{"cognitiveCredibilityLevel":"High","cognitiveIsSuspicious":0}',
      isCognitiveActive: 0,
      reViolatedRules: []
    };
    expect(
      transactionDetailsReducer(
        {},
        {
          type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL,
          response
        }
      )
    ).toEqual({
      details: response,
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_TRANSACTION_DETAIL_FAILURE', () => {
    expect(
      transactionDetailsReducer(
        {},
        {
          type: types.ON_FETCH_TRANSACTION_DETAIL_FAILURE,
          response: { message: 'Transaction not found.' }
        }
      )
    ).toEqual({
      details: {},
      loader: false,
      error: true,
      errorMessage: 'Transaction not found.'
    });
  });
});
