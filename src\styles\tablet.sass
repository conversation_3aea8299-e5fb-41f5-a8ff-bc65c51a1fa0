@use 'mixins' as *;
@use 'variables' as *;

/*sidebar responsive style*/
.sidebar-container.open
  +media($tabletLand)
    width: 100%
    z-index: 2
    position: absolute

  .primary-menu-list li,
  .secondary-menu-list li
    width: 100%

  .sidebar-primary.open
    +media($tabletLand)
      width: 40%

  .sidebar-secondary.open
    +media($tabletLand)
      width: 60%

/* investigation style */
.content-wrapper
  #searchform
    .form-group:not(:last-child)
      width: 16.8%
      margin-right: 2%
    &.advance-search
      .form-group:not(:last-child)
        width: auto
.transaction-detail
  .txn-info.row
    +media($tabletLand)
      display: inline-block
      text-align: center
  .detail-cols
    &>div
      +media($tabletLand)
        width: 30%

.ReactTable .-pagination .-center
  +media($tabletLand)
    -ms-flex: 2.5
    flex: 2.5

.rule-modal.modal-xl
  +media($laptop)
    max-width: 940px
  +media($tablet)
    max-width: 730px
