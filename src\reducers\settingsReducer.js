import {
  ON_<PERSON>ET<PERSON>_SETTINGS_LOADING,
  ON_FETCH_SETTINGS_SUCCESS,
  ON_FETCH_SETTINGS_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function settingsReducer(state = initialState.settings, action) {
  switch (action.type) {
    case ON_FETCH_SETTINGS_LOADING:
      return { data: [], loader: true, error: false, errorMessage: '' };
    case ON_FETCH_SETTINGS_SUCCESS:
      return { data: action.response, loader: false };
    case ON_FETCH_SETTINGS_FAILURE:
      return {
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      };
    default:
      return state;
  }
}
