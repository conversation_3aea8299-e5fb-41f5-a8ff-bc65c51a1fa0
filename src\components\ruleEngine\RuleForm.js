'use strict';
import { isEmpty, assign, map } from 'lodash';
import PropTypes from 'prop-types';
import React, { useCallback, useEffect, useMemo, useRef, useState, lazy, Suspense } from 'react';
import { FormGroup, Button, TabPane } from 'reactstrap';

import FormStepper from 'components/common/FormStepper';
import Loader from 'components/loader/Loader';

const RuleDetailsFormContainer = lazy(
  () => import('containers/ruleEngine/RuleDetailsFormContainer')
);
const RuleBuilderContainer = lazy(() => import('containers/ruleEngine/RuleBuilderContainer'));
const Checklist = lazy(() => import('containers/ruleEngine/ChecklistContainer'));
const Sandbox = lazy(() => import('containers/ruleEngine/SandboxContainer'));
const FormNavigationButtons = lazy(() => import('components/common/FormNavigationButtons'));

const RuleForm = ({
  channel,
  formName,
  formData,
  ruleList,
  ruleCreation,
  fetchPrefilterLists,
  ruleCreationActions,
  submit,
  hasSandbox,
  moduleType,
  isSandbox = false
}) => {
  const initialRuleData = useMemo(
    () => ({
      code: null,
      ruleType: channel === 'str' ? 'Str' : 'Dsl',
      logic: '',
      name: '',
      description: '',
      order: 0,
      assignmentPriority: 0,
      explicit: channel === 'str' ? true : false,
      actionCode: '01',
      actionName: 'ACCEPTED',
      methodType: channel === 'str' ? 'Post' : 'Pre',
      comments: '',
      isMerchantSpecific: false,
      lowLevelOutcome: null,
      medLevelOutcome: null,
      highLevelOutcome: null,
      alertCategoryId: -1,
      fraudCategory: '',
      label: '',
      citationNames: [],
      channels: '',
      isDelete: 0,
      ...formData
    }),
    [formData, channel]
  );
  const ruleForm = useRef(null);
  const [ruleData, setRuleData] = useState(initialRuleData);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [invalidLogic, setInvalidLogic] = useState(false);
  const [active, setActive] = useState(0);
  const [disableDetailsNext, setDisableDetailsNext] = useState(true);

  useEffect(() => {
    if (isEmpty(ruleCreation.actionList)) ruleCreationActions.onFetchActionList(channel);
    if (isEmpty(ruleCreation.alertCategories)) ruleCreationActions.onFetchAlertCategories(channel);
    if (isEmpty(ruleCreation.ruleChannels)) ruleCreationActions.onFetchRuleChannelsList(channel);
    if (isEmpty(ruleCreation.fraudCategories))
      ruleCreationActions.onFetchRuleFraudCategoriesList(channel);
    if (isEmpty(ruleCreation.ruleLabels)) ruleCreationActions.onFetchRuleLabels(channel);
    fetchPrefilterLists();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Memoize expensive computations to prevent unnecessary re-renders
  const steppers = useMemo(() => {
    const steps = ['Details', 'Logic', 'Checklist'];
    if (channel === 'frm' && hasSandbox === 1) steps.push('Sandbox');

    return steps;
  }, [channel, hasSandbox]);

  const currentChannel = useMemo(
    () =>
      moduleType === 'issuer' && ruleData.methodType === 'Post' && channel === 'frm'
        ? 'nrt'
        : channel,
    [moduleType, ruleData.methodType, channel]
  );

  const nonProductionRules = useMemo(
    () => ruleCreation.nonProductionRules.list[channel] || [],
    [ruleCreation.nonProductionRules.list, channel]
  );

  const combinedRuleList = useMemo(
    () => [...ruleList[channel], ...nonProductionRules],
    [ruleList, channel, nonProductionRules]
  );

  // Memoize callback functions to prevent child component re-renders
  const updateRuleData = useCallback((id, value) => {
    setRuleData((prev) =>
      assign({}, prev, {
        [id]:
          id === 'alertCategoryId' || id === 'order' || id === 'assignmentPriority'
            ? parseInt(value)
            : value
      })
    );
  }, []);

  const onChecklistChange = useCallback((newChecklist) => {
    const citations = map(newChecklist, (item) => item.citationName);
    setRuleData((prev) => assign({}, prev, { citationNames: citations }));
  }, []);

  const onSubmit = useCallback(
    (e) => {
      e.preventDefault();
      setSubmitLoading(true);
      if (disableDetailsNext || invalidLogic) {
        setSubmitLoading(false);
        return false;
      }
      submit({ rule: ruleData, channel });
      setSubmitLoading(false);
    },
    [disableDetailsNext, invalidLogic, ruleData, channel, submit]
  );
  const goToStep = useCallback((step) => () => setActive(step), []);

  if (ruleCreation.loader) return <Loader show={true} />;

  if (ruleCreation.error) return <div className="no-data-div">{ruleCreation.errorMessage}</div>;

  if (isSandbox) {
    if (channel === 'frm' && hasSandbox === 1)
      return <Sandbox ruleList={ruleList[channel]} selectedRule={ruleData} />;

    return null;
  }

  return (
    <form name={formName} onSubmit={onSubmit} ref={ruleForm}>
      <FormStepper steps={steppers} active={active}>
        <TabPane tabId={0}>
          <Suspense fallback={<Loader show={true} />}>
            <RuleDetailsFormContainer
              channel={channel}
              formName={formName}
              ruleData={ruleData}
              combinedRuleList={combinedRuleList}
              updateRuleData={updateRuleData}
              updateNextDisable={setDisableDetailsNext}
            />
            <FormNavigationButtons
              prev={null}
              next={() => goToStep(1)()}
              disableNext={disableDetailsNext}
            />
          </Suspense>
        </TabPane>
        <TabPane tabId={1}>
          <Suspense fallback={<Loader show={true} />}>
            <RuleBuilderContainer
              channel={currentChannel}
              formName={formName}
              ruleData={ruleData}
              combinedRuleList={combinedRuleList}
              updateRuleData={updateRuleData}
              invalidateLogic={setInvalidLogic}
            />
            <FormNavigationButtons
              prev={() => goToStep(0)()}
              next={() => goToStep(2)()}
              disableNext={!ruleCreation.validation.status || disableDetailsNext || invalidLogic}
            />
          </Suspense>
        </TabPane>
        <TabPane tabId={2}>
          <Suspense fallback={<Loader show={true} />}>
            <Checklist
              onChecklistChange={onChecklistChange}
              code={formData?.code || ''}
              channel={channel}
            />
            <FormGroup className="d-flex justify-content-end mt-4">
              <Button
                color="primary"
                type="submit"
                size="sm"
                disabled={!ruleCreation.validation.status || invalidLogic || disableDetailsNext}>
                {submitLoading ? 'Creating...' : 'Create'}
              </Button>
            </FormGroup>
            <FormNavigationButtons
              prev={() => goToStep(1)()}
              next={() => hasSandbox === 1 && channel === 'frm' && goToStep(3)()}
              nextLabel="Test and Create"
              disableNext={disableDetailsNext || invalidLogic}
            />
          </Suspense>
        </TabPane>
        {channel === 'frm' && hasSandbox === 1 && (
          <TabPane tabId={3}>
            <Suspense fallback={<Loader show={true} />}>
              <Sandbox ruleList={ruleList[channel]} selectedRule={ruleData} />
              <FormNavigationButtons
                prev={() => goToStep(2)()}
                nextLabel={submitLoading ? 'Creating...' : 'Create'}
                disableNext={false}
                submit
              />
            </Suspense>
          </TabPane>
        )}
      </FormStepper>
    </form>
  );
};

RuleForm.propTypes = {
  isSandbox: PropTypes.bool,
  formData: PropTypes.object,
  channel: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  hasSandbox: PropTypes.number.isRequired,
  ruleList: PropTypes.object.isRequired,
  ruleCreation: PropTypes.object.isRequired,
  ruleCreationActions: PropTypes.object.isRequired,
  fetchPrefilterLists: PropTypes.func.isRequired,
  submit: PropTypes.func.isRequired
};

export default React.memo(RuleForm);
