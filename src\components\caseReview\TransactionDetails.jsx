import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Card, Badge, Row, Col, Label } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import CollapsiblePill from 'components/common/CollapsiblePill';
import { checkValue } from 'constants/functions';
import CallButtonContainer from 'containers/caseReview/CallButtonContainer';
import CallEndButtonContainer from 'containers/caseReview/CallEndButtonContainer';
import { useInterval } from 'utility/customHooks';
import { dataColumn } from 'utility/customRenders';

function TransactionDetails({
  userName,
  agentInfo,
  oneViewCase,
  callDetails,
  merchantInfo,
  customerInfo,
  violationDetails,
  transactionDetails,
  fetchEntityDetails,
  fetchViolationDetails,
  fetchTransactionDetails
}) {
  const { details: txnDetails } = transactionDetails;
  const [timeSpent, setTimeSpent] = useState(
    Moment().diff(Moment(oneViewCase?.assignmentTimeStamp), 'minutes')
  );

  useEffect(() => {
    if (oneViewCase?.txnId) {
      fetchTransactionDetails(oneViewCase?.txnId, 'frm', oneViewCase?.data);
      fetchEntityDetails(_.lowerCase(oneViewCase?.entityCategory), oneViewCase?.entityId);
    }
  }, [fetchEntityDetails, fetchTransactionDetails, oneViewCase]);

  useEffect(() => {
    if (txnDetails?.transactionInfo?.txnTimestamp && txnDetails?.reViolatedRules?.length > 0)
      fetchViolationDetails(oneViewCase?.txnId, 'frm', {
        txnTimestamp: txnDetails?.transactionInfo?.txnTimestamp,
        code: txnDetails?.reViolatedRules || []
      });
  }, [fetchViolationDetails, oneViewCase, txnDetails]);

  useInterval(() => {
    if (oneViewCase?.txnId && oneViewCase?.investigationStatus === 'Open')
      setTimeSpent(Moment().diff(Moment(oneViewCase?.assignmentTimeStamp), 'minutes'));
  }, 60000);

  const getEntityDetails = () => {
    if (oneViewCase?.entityCategory === 'Merchant') return merchantInfo?.details;
    if (oneViewCase?.entityCategory === 'Customer') return customerInfo?.details;
    return agentInfo.details;
  };
  const entityDetails = getEntityDetails();

  const frmVerdict =
    oneViewCase?.ifrmVerdict === 'N/A'
      ? oneViewCase?.ifrmPostauthVerdictName
      : oneViewCase?.ifrmVerdict;

  const { list: ruleList } = violationDetails;

  const getCallAction = () => {
    const hasRequiredData =
      !_.isEmpty(oneViewCase?.channel) &&
      !_.isEmpty(oneViewCase?.caseRefNo) &&
      !_.isEmpty(txnDetails);

    if (!hasRequiredData) return null;

    if (callDetails.status === 'PLACED')
      return <CallEndButtonContainer channel={oneViewCase?.channel} />;

    return (
      <CallButtonContainer
        channel={oneViewCase?.channel}
        caseRefNo={oneViewCase?.caseRefNo}
        entityId={oneViewCase?.entityId}
        disabled={oneViewCase?.assignedTo !== userName}
      />
    );
  };

  const getBadgeColor = (verdict) => {
    const lowerVerdict = _.lowerCase(verdict);
    if (lowerVerdict === 'accepted') return 'success';
    if (lowerVerdict === 'rejected') return 'danger';
    return 'warning';
  };

  return (
    <CardContainer
      title={
        <span>
          <span>Transaction Detail </span>
          {!_.isEmpty(txnDetails) && (
            <>
              <span className="ms-3">
                {txnDetails?.transactionInfo?.txnId && `[${txnDetails.transactionInfo.txnId}]`}
              </span>
              <Badge className="ms-3" color={getBadgeColor(frmVerdict)}>
                {frmVerdict}
              </Badge>
            </>
          )}
        </span>
      }
      action={getCallAction()}
      withAddToList>
      <Card className="mb-0">
        <Row>
          <Col>
            <Label>Case Details</Label>
            {checkValue(oneViewCase, 'partnerName') && dataColumn('Bank', oneViewCase.partnerName)}
            {checkValue(oneViewCase, 'investigationStatus') &&
              dataColumn('Status', oneViewCase.investigationStatus)}
            {checkValue(oneViewCase, 'assignmentTimeStamp') &&
              dataColumn('Assigned at', Moment(oneViewCase?.assignmentTimeStamp).format('HH:mm'))}
            {checkValue(oneViewCase, 'closeTimestamp') &&
              dataColumn('Closed at', Moment(oneViewCase?.closeTimestamp).format('HH:mm'))}
            {checkValue(oneViewCase, 'assignedTo') &&
              oneViewCase?.assignedTo !== userName &&
              dataColumn('Assigned to', oneViewCase?.assignedTo)}
            {oneViewCase?.investigationStatus === 'Open' &&
              oneViewCase?.assignedTo === userName &&
              dataColumn('Time on case', `${timeSpent} mins`)}
            {checkValue(oneViewCase, 'closeTimestamp') &&
              oneViewCase?.assignedTo === userName &&
              dataColumn(
                'Time on case',
                `${Moment(oneViewCase?.closeTimestamp).diff(
                  Moment(oneViewCase?.assignmentTimeStamp),
                  'minutes'
                )} mins`
              )}
            {checkValue(oneViewCase, 'childTxns') &&
              dataColumn(
                'Related Transactions',
                <ul>
                  {oneViewCase.childTxns.map((d) => (
                    <li key={d}>{d}</li>
                  ))}
                </ul>
              )}
            {checkValue(oneViewCase, 'bucketName') && dataColumn('Verdict', oneViewCase.bucketName)}
            {checkValue(oneViewCase, 'remark') && dataColumn('Remark', oneViewCase.remark)}
          </Col>
          <Col>
            <Label>Transaction Details</Label>
            {checkValue(txnDetails, 'masterFields') &&
              checkValue(txnDetails.masterFields, 'channelName') &&
              txnDetails.masterFields.channelName &&
              dataColumn('Channel', txnDetails.masterFields.channelName)}
            {checkValue(txnDetails.transactionInfo, 'txnCategoryName') &&
              dataColumn('Category', txnDetails.transactionInfo.txnCategoryName)}
            {checkValue(txnDetails, 'masterFields') &&
              checkValue(txnDetails.masterFields, 'txnTypeName') &&
              txnDetails.masterFields.txnTypeName &&
              dataColumn('Type', txnDetails.masterFields.txnTypeName)}
            {checkValue(txnDetails, 'masterFields') &&
              checkValue(txnDetails.masterFields, 'paymentMethodName') &&
              txnDetails.masterFields.paymentMethodName &&
              dataColumn('Method', txnDetails.masterFields.paymentMethodName)}
          </Col>
          <Col>
            <Label>Payment Details</Label>
            {checkValue(txnDetails.transactionInfo, 'txnAmount', true) &&
              dataColumn(
                'Amount',
                txnDetails.transactionInfo.txnCurrency === '356'
                  ? `${txnDetails.transactionInfo.txnAmount} (INR)`
                  : `${txnDetails.transactionInfo.txnAmount} (${txnDetails.transactionInfo.txnCurrency})`
              )}
            {checkValue(txnDetails.transactionInfo, 'txnTimestamp') &&
              dataColumn(
                'Timestamp',
                Moment(txnDetails.transactionInfo.txnTimestamp).format('YYYY-MM-DD hh:mm A')
              )}
            {checkValue(txnDetails, 'masterFields') &&
              checkValue(txnDetails.masterFields, 'responseCodeName') &&
              dataColumn('Response', txnDetails.masterFields.responseCodeName)}
          </Col>
          <Col>
            <Label>Payer / Sender Details</Label>
            {checkValue(txnDetails, 'entityId') &&
              txnDetails.entityId.value &&
              dataColumn('Payer ID', txnDetails.entityId.value, {
                entityDetails: txnDetails.entityId,
                partnerId: txnDetails?.identifiers?.partnerId || ''
              })}
            {checkValue(entityDetails.personalDetails, 'merchantName') &&
              dataColumn('Name', entityDetails.personalDetails.merchantName)}
            {(entityDetails?.firstName || entityDetails?.lastName) &&
              dataColumn(
                'Name',
                `${entityDetails?.firstName} ${entityDetails?.middleName} ${entityDetails?.lastName}`
              )}
            {checkValue(entityDetails, 'mccData') &&
              checkValue(entityDetails.mccData, 'mcc') &&
              entityDetails.mccData.mcc.value &&
              checkValue(entityDetails.mccData, 'mccName') &&
              dataColumn('Category', entityDetails.mccData.mccName, {
                entityDetails: entityDetails.mccData.mcc,
                partnerId: entityDetails?.txnCounterResponse?.partnerId || ''
              })}
            {checkValue(txnDetails?.payerAccount, 'payerVpa') &&
              checkValue(txnDetails.payerAccount.payerVpa, 'value') &&
              dataColumn('VPA', txnDetails.payerAccount.payerVpa.value, {
                entityDetails: txnDetails.payerAccount.payerVpa,
                partnerId: txnDetails?.identifiers?.partnerId || ''
              })}
          </Col>
          <Col>
            <Label>Beneficiary / Receiver Details</Label>
            {checkValue(txnDetails.payeeAccount, 'payeeId') &&
              txnDetails.payeeAccount.payeeId.value &&
              dataColumn('Beneficiary ID', txnDetails.payeeAccount.payeeId.value, {
                entityDetails: txnDetails.payeeAccount.payeeId,
                partnerId: txnDetails?.identifiers?.partnerId || ''
              })}
            {checkValue(txnDetails.payeeAccount, 'payeeType') &&
              dataColumn('Beneficiary Type', txnDetails.payeeAccount.payeeType)}
            {checkValue(txnDetails.payeeAccount, 'payeeMcc') &&
              txnDetails.payeeAccount.payeeMcc.value &&
              checkValue(txnDetails, 'masterFields') &&
              checkValue(txnDetails.masterFields, 'payeeMccCodeName') &&
              dataColumn('Category', txnDetails.masterFields.payeeMccCodeName, {
                entityDetails: txnDetails.payeeAccount.payeeMcc,
                partnerId: txnDetails?.identifiers?.partnerId || ''
              })}
            {checkValue(txnDetails?.payeeAccount, 'payeeVpa') &&
              checkValue(txnDetails.payeeAccount.payeeVpa, 'value') &&
              dataColumn('VPA', txnDetails.payeeAccount.payeeVpa.value, {
                entityDetails: txnDetails.payeeAccount.payeeVpa,
                partnerId: txnDetails?.identifiers?.partnerId || ''
              })}
          </Col>
          <Col md="12" lg="12" className="d-flex flex-row flex-wrap justify-content-start">
            {ruleList?.rules?.map((rule, index) => (
              <CollapsiblePill key={index} name={rule.name}>
                <div className="description-text">
                  <p>
                    <b>Description: </b> {rule.description}
                  </p>
                </div>

                {!_.isEmpty(rule?.citationNames) && (
                  <div className="description-text">
                    <p>
                      <b>Line of Enquiry: </b>
                    </p>
                    <ul>
                      {_.chain(rule.citationNames)
                        .split('~')
                        .map((d) => <li key={_.kebabCase(d)}>{d}</li>)
                        .value()}
                    </ul>
                  </div>
                )}
              </CollapsiblePill>
            ))}
          </Col>
        </Row>
      </Card>
    </CardContainer>
  );
}

TransactionDetails.propTypes = {
  userName: PropTypes.string.isRequired,
  agentInfo: PropTypes.object.isRequired,
  oneViewCase: PropTypes.object.isRequired,
  callDetails: PropTypes.object.isRequired,
  customerInfo: PropTypes.object.isRequired,
  merchantInfo: PropTypes.object.isRequired,
  violationDetails: PropTypes.object.isRequired,
  transactionDetails: PropTypes.object.isRequired,
  fetchEntityDetails: PropTypes.func.isRequired,
  fetchViolationDetails: PropTypes.func.isRequired,
  fetchTransactionDetails: PropTypes.func.isRequired
};

export default TransactionDetails;
