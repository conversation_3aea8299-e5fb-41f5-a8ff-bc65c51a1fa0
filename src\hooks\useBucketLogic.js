import { isEmpty, findKey } from 'lodash';
import { useCallback, useEffect, useMemo, useRef } from 'react';

import { BUCKET_NAMES } from 'constants/applicationConstants';

/**
 * Custom hook for managing bucket-related logic in the BucketListDropdown component.
 * Encapsulates fetching buckets, handling bucket changes, and deriving bucket keys.
 *
 * @param {object} buckets - The buckets data, including stats, loader, and error.
 * @param {object} conf - The configuration object, including the currently selected bucket ID.
 * @param {function} fetchBuckets - Function to fetch bucket data.
 * @param {function} fetchCases - Function to fetch cases based on bucket selection.
 * @param {string} channel - The current channel.
 * @param {string} userRole - The current user role.
 * @returns {{bucketKey: string, handleBucketChange: function, bucketKeys: string[]}}
 */
export const useBucketLogic = ({ buckets, conf, fetchBuckets, fetchCases, channel, userRole }) => {
  const hasAutoSelectedRef = useRef(false);

  const bucketKeys = useMemo(
    () => (!isEmpty(buckets.stats) ? Object.keys(buckets.stats) : []),
    [buckets.stats]
  );

  const { bucket } = conf;

  // Correctly find the bucket key based on the bucket ID from conf
  const bucketKey = useMemo(() => {
    if (isEmpty(bucket)) return '';
    // findKey iterates over the values of BUCKET_NAMES, so 'bucketValue' is the object { id: ..., title: ... }
    return findKey(BUCKET_NAMES, (bucketValue) => bucketValue?.id === bucket);
  }, [bucket]);

  const handleBucketChange = useCallback(
    (selectedBucketId) => {
      if (!isEmpty(selectedBucketId))
        fetchCases(
          {
            ...conf,
            bucket: selectedBucketId,
            pageNo: 1,
            role: userRole,
            sortBy: 'updatedTimestamp',
            sortOrder: 'desc',
            filterCondition: []
          },
          channel
        );
    },
    [fetchCases, conf, userRole, channel]
  );

  // Memoize the fetch condition to prevent unnecessary effect runs
  const shouldFetchBuckets = useMemo(
    () =>
      (!buckets.loader && !buckets.error && isEmpty(buckets.stats)) || buckets.loader === undefined,
    [buckets.loader, buckets.error, buckets.stats]
  );

  // Effect to fetch buckets on initial load or when userRole/channel changes
  useEffect(() => {
    if (shouldFetchBuckets) {
      hasAutoSelectedRef.current = false; // Reset auto-selection flag when fetching new buckets
      fetchBuckets(userRole, channel);
    }
  }, [userRole, channel, fetchBuckets, shouldFetchBuckets]);

  // Effect to handle initial bucket selection when buckets data becomes available
  useEffect(() => {
    const { loader, error } = buckets;

    // Only auto-select if no bucket is currently selected, data is ready, and we haven't auto-selected before
    if (
      isEmpty(bucket) &&
      !loader &&
      !error &&
      !isEmpty(bucketKeys) &&
      !hasAutoSelectedRef.current
    ) {
      const firstBucketId = BUCKET_NAMES[bucketKeys[0]]?.id;

      if (firstBucketId) {
        hasAutoSelectedRef.current = true; // Mark that we've performed auto-selection
        fetchCases(
          {
            ...conf,
            bucket: firstBucketId,
            pageNo: 1,
            role: userRole,
            sortBy: 'updatedTimestamp',
            sortOrder: 'desc',
            filterCondition: []
          },
          channel
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [buckets.loader, buckets.error, bucket, bucketKeys]);

  return {
    bucketKey,
    handleBucketChange,
    bucketKeys
  };
};
