import React from 'react';
import { Row, Col, Card } from 'reactstrap';
import PropTypes from 'prop-types';
import CardContainer from 'components/common/CardContainer';

import RiskBadgeSection from './RiskBadgeSection';
import RiskBadgeTableRow from './RiskBadgeTableRow';
import { getRiskLevelColor, formatRiskScore } from 'utility/transactionHelper';

const TransactionRiskScoreInfo = ({ details }) => {
  const riskDetails = details?.riskDetails || {};
  const contributing = riskDetails?.contributingFactors || {};
  const anomaly = contributing?.anomalyDetection || {};

  return (
    <CardContainer
      title={
        <h1 className="h5">
          Transaction Risk Details
          <small className="text-muted ml-4"> (Hover on score for details)</small>
        </h1>
      }>
      <Card>
        <Row className="align-items-center">
          {/* Risk Level */}
          <Col lg="4" className="mb-4 mt-md-3 mb-lg-0">
            <RiskBadgeSection
              label="Transaction Risk Level"
              badgeId="overallRiskLevel"
              badgeText={riskDetails.overallRiskLevel || 'Safe'}
              badgeColor={getRiskLevelColor(riskDetails.overallRiskLevel, false, true)}
              popoverTitle="Risk Score Details"
              popoverRows={[
                <RiskBadgeTableRow
                  key="confidence"
                  label="Confidence Level"
                  value={riskDetails.overallConfidenceLevel}
                  badge={getRiskLevelColor(riskDetails.overallConfidenceLevel, true)}
                />,
                <RiskBadgeTableRow
                  key="riskScore"
                  label="Risk Score"
                  value={riskDetails.overallRiskScore}
                  formatter={formatRiskScore}
                />
              ]}
              isRounded={true}
            />
          </Col>

          {/* Risk Factors */}
          <Col lg="8" className="border-left p-0">
            <Row className="d-flex flex-column align-items-center justify-content-center mt-3">
              <Col md="12" className="mb-3">
                <RiskBadgeSection
                  label="Customer Propensity"
                  badgeId="custPropensity"
                  badgeText={contributing.custPropensity || 'N/A'}
                  badgeColor={getRiskLevelColor(contributing.custPropensity, true)}
                />
              </Col>
              <Col md="12" className="mb-3">
                <RiskBadgeSection
                  label="Customer Vulnerability"
                  badgeId="vulnerability"
                  badgeText={contributing.custVulnerability || 'N/A'}
                  badgeColor={getRiskLevelColor(contributing.custVulnerability)}
                />
              </Col>
              <Col md="12" className="mb-3">
                <RiskBadgeSection
                  label="MCC Risk"
                  badgeId="mccRisk"
                  badgeText={contributing.mccRisk || 'N/A'}
                  badgeColor={getRiskLevelColor(contributing.mccRisk)}
                />
              </Col>
              <Col md="12" className="mb-3">
                <RiskBadgeSection
                  label="Anomaly Risk Level"
                  badgeId="anomalyLevel"
                  badgeText={anomaly.outlierRiskLevel || 'N/A'}
                  badgeColor={getRiskLevelColor(anomaly.outlierRiskLevel)}
                  popoverTitle="Anomaly Detection"
                  popoverRows={[
                    <RiskBadgeTableRow
                      key="anomalyScore"
                      label="Score"
                      value={anomaly.outlierScore}
                      formatter={formatRiskScore}
                    />,
                    <RiskBadgeTableRow
                      key="confidenceRiskLevel"
                      label="Confidence"
                      value={anomaly.confidenceRiskLevel}
                      badge={getRiskLevelColor(anomaly.confidenceRiskLevel, true)}
                    />,
                    <RiskBadgeTableRow key="remarks" label="Remarks" value={anomaly.remarks} />,
                    Array.isArray(anomaly.parameters) && anomaly.parameters.length > 0 && (
                      <RiskBadgeTableRow
                        key="params"
                        label="Parameters"
                        value={anomaly.parameters.join(', ')}
                      />
                    )
                  ]}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>
    </CardContainer>
  );
};

TransactionRiskScoreInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionRiskScoreInfo;
