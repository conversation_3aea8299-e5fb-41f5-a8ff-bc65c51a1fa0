import PropTypes from 'prop-types';
import React from 'react';
import { Badge } from 'reactstrap';

const riskScoreColors = {
  low: 'success',
  medium: 'warning',
  high: 'danger'
};

function EntityRiskScoreBadge({ vulnerability }) {
  return (
    <Badge color={riskScoreColors[vulnerability.riskScore] || 'secondary'} className="ms-2">
      {vulnerability.riskScore} risk
    </Badge>
  );
}

EntityRiskScoreBadge.propTypes = {
  vulnerability: PropTypes.object.isRequired
};

export default EntityRiskScoreBadge;
