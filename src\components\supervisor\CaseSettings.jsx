import { faCog } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { DropdownItem } from 'reactstrap';

import DropdownButton from 'components/common/DropdownButton';
import ModalContainer from 'components/common/ModalContainer';
import NotationsManagerContainer from 'containers/common/NotationsManagerContainer';

function CaseSettings({ theme }) {
  const [displayNotations, setDisplayNotations] = useState(false);

  return (
    <>
      <DropdownButton
        color="secondary"
        size="sm"
        title="settings"
        className="ms-1"
        name={<FontAwesomeIcon icon={faCog} />}>
        <DropdownItem onClick={() => setDisplayNotations(true)}>Notations Settings</DropdownItem>
      </DropdownButton>
      <ModalContainer
        header="Notations Settings"
        size="lg"
        theme={theme}
        isOpen={displayNotations}
        toggle={() => setDisplayNotations(!displayNotations)}>
        <NotationsManagerContainer />
      </ModalContainer>
    </>
  );
}

CaseSettings.propTypes = {
  theme: PropTypes.string.isRequired
};

export default CaseSettings;
