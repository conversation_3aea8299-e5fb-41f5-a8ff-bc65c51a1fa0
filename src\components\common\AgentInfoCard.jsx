import _ from 'lodash';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Card } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import CustomerInfoLoader from 'components/loader/CustomerInfoLoader';
import StatusLogTableContainer from 'containers/common/StatusLogTableContainer';
import { formatMobile } from 'constants/functions';
import { dataColumn } from 'utility/customRenders';

const AgentInfoCard = ({ data, channel, agentId, fetchDetails, demographicDetails }) => {
  useEffect(() => {
    agentId && fetchDetails('agent', agentId);
  }, [agentId]);

  const { details, loader, error, errorMessage } = data;
  let AgentDemographicDetails =
    _.has(demographicDetails.agent.details, 'add_on') &&
    demographicDetails.agent.details.add_on.map((item) => dataColumn(item.key, item.val));

  return (
    <CardContainer
      withAddToList={true}
      title={<span>Agent Information</span>}
      action={
        <StatusLogTableContainer
          id={agentId}
          channel={channel}
          name={(details.firstName || '') + ' ' + (details.lastName || '') || agentId}
        />
      }>
      <Card className="agent-info-card">
        {_.isEmpty(agentId) ? (
          <div className="no-data-div">No agentId found</div>
        ) : loader ? (
          <CustomerInfoLoader />
        ) : error && _.isEmpty(demographicDetails.agent.details) ? (
          <div className="no-data-div">{errorMessage}</div>
        ) : _.isEmpty(details) && _.isEmpty(demographicDetails.agent.details) ? (
          <div className="no-data-div">No agent details available</div>
        ) : !_.isEmpty(details) ? (
          <div className="txn-info">
            <div>
              {details.id &&
                details.id.value &&
                dataColumn('ID', details.id.value, {
                  entityDetails: details.id,
                  partnerId: details?.txnCounterResponse?.partnerId || ''
                })}
              {details.firstName && dataColumn('Name', details.firstName + ' ' + details.lastName)}
              {details.contact && dataColumn('Contact', formatMobile(details.contact))}
              {details.email && dataColumn('Email', details.email)}
              {details.city && dataColumn('City', details.city)}
              {details.provinceState &&
                dataColumn('State', details.provinceState + ', ' + details.country)}
              {details.lat &&
                details.lat.value &&
                dataColumn('Latitude', details.lat.value, {
                  entityDetails: details.lat,
                  partnerId: details?.txnCounterResponse?.partnerId || ''
                })}
              {details.lng &&
                details.lng.value &&
                dataColumn('Longitude', details.lng.value, {
                  entityDetails: details.lng,
                  partnerId: details?.txnCounterResponse?.partnerId || ''
                })}
            </div>
            <div>
              {details.kycType && dataColumn('KYC Type', details.kycType)}
              {details.pan && dataColumn('PAN', details.pan)}
              {details.aadhar && dataColumn('Aadhar', details.aadhar)}
              {dataColumn('Activity', details.isActive ? 'Active' : 'Inactive')}
              {_.has(details, 'txnCounterResponse') && (
                <div>
                  {_.has(details.txnCounterResponse, 'txnCount') &&
                    dataColumn('Transaction Count', details.txnCounterResponse.txnCount)}
                  {_.has(details.txnCounterResponse, 'txnAmount') &&
                    dataColumn('Transaction Amount', details.txnCounterResponse.txnAmount)}
                  {_.has(details.txnCounterResponse, 'averageTransaction') &&
                    dataColumn(
                      'Average Transaction',
                      details.txnCounterResponse.averageTransaction
                    )}
                  {_.has(details.txnCounterResponse, 'fraudCount') &&
                    dataColumn('Fraud Count', details.txnCounterResponse.fraudCount)}
                  {_.has(details.txnCounterResponse, 'fraudAmount') &&
                    dataColumn('Fraud Amount', details.txnCounterResponse.fraudAmount)}
                  {_.has(details.txnCounterResponse, 'averageFraud') &&
                    dataColumn('Average Fraud', details.txnCounterResponse.averageFraud)}
                </div>
              )}
            </div>
          </div>
        ) : null}
        <div className="txn-info">
          <div> {AgentDemographicDetails && <div>{AgentDemographicDetails}</div>}</div>
        </div>
      </Card>
    </CardContainer>
  );
};

AgentInfoCard.propTypes = {
  data: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  fetchDetails: PropTypes.func.isRequired,
  agentId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  demographicDetails: PropTypes.object
};

export default AgentInfoCard;
