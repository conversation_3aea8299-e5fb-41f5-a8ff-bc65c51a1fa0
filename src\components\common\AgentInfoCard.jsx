import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Card } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import CustomerInfoLoader from 'components/loader/CustomerInfoLoader';
import { formatMobile } from 'constants/functions';
import StatusLogTableContainer from 'containers/common/StatusLogTableContainer';
import { dataColumn } from 'utility/customRenders';

const AgentInfoCard = ({ data, channel, agentId, fetchDetails, demographicDetails }) => {
  useEffect(() => {
    agentId && fetchDetails('agent', agentId);
  }, [agentId, fetchDetails]);

  const { details, loader, error, errorMessage } = data;
  const AgentDemographicDetails =
    _.has(demographicDetails.agent.details, 'add_on') &&
    demographicDetails.agent.details.add_on.map((item) => dataColumn(item.key, item.val));

  const name =
    _.isEmpty(details.firstName) && _.isEmpty(details.lastName)
      ? agentId
      : `${details.firstName} ${details.lastName}`;

  // Helper function to render the main content
  const renderMainContent = () => {
    if (_.isEmpty(agentId)) return <div className="no-data-div">No agentId found</div>;

    if (loader) return <CustomerInfoLoader />;

    if (error && _.isEmpty(demographicDetails.agent.details))
      return <div className="no-data-div">{errorMessage}</div>;

    if (_.isEmpty(details) && _.isEmpty(demographicDetails.agent.details))
      return <div className="no-data-div">No agent details available</div>;

    if (!_.isEmpty(details))
      return (
        <div className="txn-info">
          <div>
            {details.id &&
              details.id.value &&
              dataColumn('ID', details.id.value, {
                entityDetails: details.id,
                partnerId: details?.txnCounterResponse?.partnerId || ''
              })}
            {details.firstName && dataColumn('Name', `${details.firstName} ${details.lastName}`)}
            {details.contact && dataColumn('Contact', formatMobile(details.contact))}
            {details.email && dataColumn('Email', details.email)}
            {details.city && dataColumn('City', details.city)}
            {details.provinceState &&
              dataColumn('State', `${details.provinceState}, ${details.country}`)}
            {details.lat &&
              details.lat.value &&
              dataColumn('Latitude', details.lat.value, {
                entityDetails: details.lat,
                partnerId: details?.txnCounterResponse?.partnerId || ''
              })}
            {details.lng &&
              details.lng.value &&
              dataColumn('Longitude', details.lng.value, {
                entityDetails: details.lng,
                partnerId: details?.txnCounterResponse?.partnerId || ''
              })}
          </div>
          <div>
            {details.kycType && dataColumn('KYC Type', details.kycType)}
            {details.pan && dataColumn('PAN', details.pan)}
            {details.aadhar && dataColumn('Aadhar', details.aadhar)}
            {dataColumn('Activity', details.isActive ? 'Active' : 'Inactive')}
            {_.has(details, 'txnCounterResponse') && (
              <div>
                {_.has(details.txnCounterResponse, 'txnCount') &&
                  dataColumn('Transaction Count', details.txnCounterResponse.txnCount)}
                {_.has(details.txnCounterResponse, 'txnAmount') &&
                  dataColumn('Transaction Amount', details.txnCounterResponse.txnAmount)}
                {_.has(details.txnCounterResponse, 'averageTransaction') &&
                  dataColumn('Average Transaction', details.txnCounterResponse.averageTransaction)}
                {_.has(details.txnCounterResponse, 'fraudCount') &&
                  dataColumn('Fraud Count', details.txnCounterResponse.fraudCount)}
                {_.has(details.txnCounterResponse, 'fraudAmount') &&
                  dataColumn('Fraud Amount', details.txnCounterResponse.fraudAmount)}
                {_.has(details.txnCounterResponse, 'averageFraud') &&
                  dataColumn('Average Fraud', details.txnCounterResponse.averageFraud)}
              </div>
            )}
          </div>
        </div>
      );

    return null;
  };

  return (
    <CardContainer
      withAddToList={true}
      title={<span>Agent Information</span>}
      action={<StatusLogTableContainer id={agentId} channel={channel} name={name} />}>
      <Card className="agent-info-card">
        {renderMainContent()}
        <div className="txn-info">
          {AgentDemographicDetails && <div>{AgentDemographicDetails}</div>}
        </div>
      </Card>
    </CardContainer>
  );
};

AgentInfoCard.propTypes = {
  data: PropTypes.object.isRequired,
  channel: PropTypes.string.isRequired,
  fetchDetails: PropTypes.func.isRequired,
  agentId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  demographicDetails: PropTypes.object
};

export default AgentInfoCard;
