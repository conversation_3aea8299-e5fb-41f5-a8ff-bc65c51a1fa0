import { <PERSON><PERSON><PERSON> } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  DatasetComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import ReactEcharts from 'echarts-for-react/lib/core';
import { find, map, isEmpty } from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React from 'react';
import { Label } from 'reactstrap';

import darkTheme from 'constants/chartDarkTheme';
import lightTheme from 'constants/chartLightTheme';
import SandboxResultTable from 'containers/ruleEngine/SandboxResultTableContainer';

echarts.registerTheme('chart-theme-light', lightTheme);
echarts.registerTheme('chart-theme-dark', darkTheme);

const chartTheme = {
  light: 'chart-theme-light',
  dark: 'chart-theme-dark'
};

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  DatasetComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  MarkPointComponent
]);

const SandboxResult = ({ theme, result, rules, violationDetails, fetchViolationDetails }) => {
  const series = map(rules, (d) => ({
    type: 'line',
    smooth: false,
    showSymbol: true,
    symbol: 'circle',
    symbolSize: 8,
    name: d,
    emphasis: {
      focus: 'series'
    },
    label: {
      show: true,
      position: 'top'
    },
    encode: {
      x: 'Date',
      y: 'Alerts',
      label: ['Rule', 'Alerts'],
      itemName: 'Date',
      tooltip: ['Alerts']
    },
    data: map(result, (obj) => {
      const date = Moment(obj.date).format('YYYY/MM/DD');
      return {
        name: `${d}\n${date}`,
        value: [date, find(obj.alerts, ['rule', d])?.noOfTxns || 0]
      };
    })
  }));

  const config = {
    animationDuration: 10000,
    title: {
      text: 'Alerts per Rule over a period'
    },
    tooltip: {
      trigger: 'axis',
      order: 'valueDesc'
    },
    xAxis: {
      type: 'time',
      triggerEvent: true
    },
    yAxis: {
      type: 'value'
    },
    grid: {
      right: 40,
      left: 40
    },
    legend: {
      bottom: 10,
      type: 'scroll'
    },
    series
  };

  const chartPointClick = (params) => {
    let value = '';

    if (params.componentType === 'xAxis') value = params.value;
    else if (params.componentType === 'series') value = params.value[0];

    if (value) {
      const date = Moment(value).format('YYYY-MM-DD');
      fetchViolationDetails({ date, pageNo: 1, pageSize: 5 });
    }
  };

  const chartEvents = {
    click: chartPointClick
  };

  return (
    <>
      <ReactEcharts
        echarts={echarts}
        option={config}
        notMerge={true}
        lazyUpdate={true}
        theme={chartTheme[theme]}
        onEvents={chartEvents}
      />
      {!isEmpty(violationDetails.date) && <Label>{violationDetails.date}</Label>}
      {!isEmpty(violationDetails.list) && <SandboxResultTable />}
    </>
  );
};

SandboxResult.propTypes = {
  rules: PropTypes.array.isRequired,
  theme: PropTypes.string.isRequired,
  result: PropTypes.array.isRequired,
  violationDetails: PropTypes.object.isRequired,
  fetchViolationDetails: PropTypes.func.isRequired
};

export default SandboxResult;
