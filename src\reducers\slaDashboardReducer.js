import {
  ON_<PERSON>ET<PERSON>_<PERSON>A_KPI_LOADING,
  ON_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_KPI_SUCCESS,
  ON_<PERSON>ET<PERSON>_SLA_KPI_FAILURE,
  ON_FETCH_EMPLOYEE_SLA_LOADING,
  ON_FETCH_EMPLOYEE_SLA_SUCCESS,
  ON_FETCH_EMPLOYEE_SLA_FAILURE,
  ON_FETCH_OCCUPANCY_RATE_LOADING,
  ON_FETCH_OCCUPANCY_RATE_SUCCESS,
  ON_FETCH_OCCUPANCY_RATE_FAILURE,
  ON_<PERSON>ET<PERSON>_SLA_BREACH_CASES_LOADING,
  ON_<PERSON>ET<PERSON>_SLA_BREACH_CASES_SUCCESS,
  ON_FETCH_SLA_BREACH_CASES_FAILURE,
  ON_FETCH_FIRST_CONTACT_RATE_LOADING,
  ON_FETCH_FIRST_CONTACT_RATE_SUCCESS,
  ON_FETCH_FIRST_CONTACT_RATE_FAILURE,
  ON_FETCH_SHIFT_DETAILS_LOADING,
  ON_FETCH_SHIFT_DETAILS_SUCCESS,
  ON_FETCH_SHIFT_DETAILS_FAILURE,
  ON_FETCH_ANALYST_TAT_LOADING,
  ON_FETCH_ANALYST_TAT_SUCCESS,
  ON_FETCH_ANALYST_TAT_FAILURE,
  ON_FETCH_PARTNERS_CASE_STATS_LOADING,
  ON_FETCH_PARTNERS_CASE_STATS_SUCCESS,
  ON_FETCH_PARTNERS_CASE_STATS_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function slaDashboardReducer(state = initialState.slaDashboard, action) {
  switch (action.type) {
    case ON_FETCH_SLA_KPI_LOADING:
      return objectAssign({}, state, {
        slaKpis: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.slaKpis.data
        }
      });
    case ON_FETCH_SLA_KPI_SUCCESS:
      return objectAssign({}, state, {
        slaKpis: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response
        }
      });
    case ON_FETCH_SLA_KPI_FAILURE:
      return objectAssign({}, state, {
        slaKpis: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: {}
        }
      });
    case ON_FETCH_EMPLOYEE_SLA_LOADING:
      return objectAssign({}, state, {
        employeeSla: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.employeeSla.data
        }
      });
    case ON_FETCH_EMPLOYEE_SLA_SUCCESS:
      return objectAssign({}, state, {
        employeeSla: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response
        }
      });
    case ON_FETCH_EMPLOYEE_SLA_FAILURE:
      return objectAssign({}, state, {
        employeeSla: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: {}
        }
      });
    case ON_FETCH_OCCUPANCY_RATE_LOADING:
      return objectAssign({}, state, {
        occupancyRate: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.occupancyRate.data
        }
      });
    case ON_FETCH_OCCUPANCY_RATE_SUCCESS:
      return objectAssign({}, state, {
        occupancyRate: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response
        }
      });
    case ON_FETCH_OCCUPANCY_RATE_FAILURE:
      return objectAssign({}, state, {
        occupancyRate: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: 0
        }
      });
    case ON_FETCH_SLA_BREACH_CASES_LOADING:
      return objectAssign({}, state, {
        slaBreachCases: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.slaBreachCases.data
        }
      });
    case ON_FETCH_SLA_BREACH_CASES_SUCCESS:
      return objectAssign({}, state, {
        slaBreachCases: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response.transactions
        }
      });
    case ON_FETCH_SLA_BREACH_CASES_FAILURE:
      return objectAssign({}, state, {
        slaBreachCases: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: []
        }
      });
    case ON_FETCH_FIRST_CONTACT_RATE_LOADING:
      return objectAssign({}, state, {
        firstContactRate: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.firstContactRate.data
        }
      });
    case ON_FETCH_FIRST_CONTACT_RATE_SUCCESS:
      return objectAssign({}, state, {
        firstContactRate: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response.resolutionRate
        }
      });
    case ON_FETCH_FIRST_CONTACT_RATE_FAILURE:
      return objectAssign({}, state, {
        firstContactRate: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: 0
        }
      });
    case ON_FETCH_SHIFT_DETAILS_LOADING:
      return objectAssign({}, state, {
        shiftDetails: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.shiftDetails.data
        }
      });
    case ON_FETCH_SHIFT_DETAILS_SUCCESS:
      return objectAssign({}, state, {
        shiftDetails: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response
        }
      });
    case ON_FETCH_SHIFT_DETAILS_FAILURE:
      return objectAssign({}, state, {
        shiftDetails: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: {}
        }
      });
    case ON_FETCH_ANALYST_TAT_LOADING:
      return objectAssign({}, state, {
        analystTAT: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.analystTAT.data
        }
      });
    case ON_FETCH_ANALYST_TAT_SUCCESS:
      return objectAssign({}, state, {
        analystTAT: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response.slaTimeTaken
        }
      });
    case ON_FETCH_ANALYST_TAT_FAILURE:
      return objectAssign({}, state, {
        analystTAT: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: []
        }
      });
    case ON_FETCH_PARTNERS_CASE_STATS_LOADING:
      return objectAssign({}, state, {
        partnersCaseStats: {
          loader: true,
          error: false,
          errorMessage: '',
          data: state.partnersCaseStats.data
        }
      });
    case ON_FETCH_PARTNERS_CASE_STATS_SUCCESS:
      return objectAssign({}, state, {
        partnersCaseStats: {
          loader: false,
          error: false,
          errorMessage: '',
          data: action.response
        }
      });
    case ON_FETCH_PARTNERS_CASE_STATS_FAILURE:
      return objectAssign({}, state, {
        partnersCaseStats: {
          loader: false,
          error: true,
          errorMessage: action.response.message,
          data: state.partnersCaseStats.data
        }
      });
    default:
      return state;
  }
}
