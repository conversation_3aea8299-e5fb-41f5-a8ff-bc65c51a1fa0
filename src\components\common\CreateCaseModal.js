import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { Button, FormGroup, Label, Input } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const CreateCaseModal = ({ theme, display, toggle, handleExternalCreateCase }) => {
  const [type, setType] = useState('fiu');
  const [detail, setDetail] = useState('');

  const clearModalValue = () => {
    setType('fiu');
    setDetail('');
  };

  const createCase = (e) => {
    e.preventDefault();
    const formData = {
      agencyType: type,
      enquiryDetails: detail
    };
    handleExternalCreateCase(formData);

    clearModalValue();
  };

  return (
    <ModalContainer header="Create case" toggle={toggle} isOpen={display} theme={theme} size="md">
      <form onSubmit={createCase}>
        <FormGroup>
          <Label>Type</Label>
          <Input
            type="select"
            name="type"
            value={type}
            onChange={(e) => setType(e.target.value)}
            required>
            <option value="fiu">FIU-IND</option>
            <option value="police">Police</option>
            <option value="ed">ED</option>
          </Input>
        </FormGroup>
        <FormGroup>
          <Label>Enquiry Details</Label>
          <Input
            type="textarea"
            name="detail"
            value={detail}
            onChange={(e) => setDetail(e.target.value)}
            required
          />
        </FormGroup>
        <FormGroup className="d-flex justify-content-end">
          <Button type="submit" size="sm" color="primary">
            Submit
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
};

CreateCaseModal.propTypes = {
  toggle: PropTypes.func.isRequired,
  theme: PropTypes.string.isRequired,
  display: PropTypes.bool.isRequired,
  handleExternalCreateCase: PropTypes.func.isRequired
};

export default CreateCaseModal;
