import { faFilePdf, faImage } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';
import { Button } from 'reactstrap';

import useDownloadableDashboard from '../../utility/useDownloadableDashboard';

const DownloadReportsButtons = ({ elementId, fileNamePrefix, isDisabled, dashboardRef }) => {
  const { downloadScreenshot, downloadPDF } = useDownloadableDashboard(
    elementId,
    fileNamePrefix,
    dashboardRef
  );

  return (
    <>
      <Button
        size="sm"
        color="secondary"
        className="m-3"
        disabled={isDisabled}
        onClick={downloadScreenshot}>
        <FontAwesomeIcon icon={faImage} title="Download Report as Image" />
      </Button>

      <Button
        size="sm"
        color="secondary"
        className="m-3"
        disabled={isDisabled}
        onClick={downloadPDF}>
        <FontAwesomeIcon icon={faFilePdf} title="Download Report as PDF" />
      </Button>
    </>
  );
};

DownloadReportsButtons.propTypes = {
  elementId: PropTypes.string.isRequired,
  fileNamePrefix: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool,
  dashboardRef: PropTypes.shape({ current: PropTypes.instanceOf(Element) })
};

DownloadReportsButtons.defaultProps = {
  isDisabled: false
};

export default DownloadReportsButtons;
