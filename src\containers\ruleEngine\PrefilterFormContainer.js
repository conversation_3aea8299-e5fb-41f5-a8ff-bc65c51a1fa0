import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as prefilterActions from 'actions/prefilterActions';
import { onTogglePrefilterModal } from 'actions/toggleActions';
import PrefilterForm from 'components/ruleEngine/PrefilterForm';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    display: state.toggle.prefilterModal,
    category: state.prefilter.category
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(prefilterActions, dispatch),
    toggle: bindActionCreators(onTogglePrefilterModal, dispatch)
  };
};

const PrefilterFormContainer = connect(mapStateToProps, mapDispatchToProps)(PrefilterForm);

export default PrefilterFormContainer;
