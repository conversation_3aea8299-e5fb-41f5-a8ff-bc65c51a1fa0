import {
  ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING,
  ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
  ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE,
  ON_DECISION_MATRIX_ACTIONS_FETCH_LOADING,
  ON_SUCCESSFUL_DECISION_MATRIX_ACTIONS_FETCH,
  ON_DECISION_MATRIX_ACTIONS_FETCH_FAILURE
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import client from 'utility/apiClient';

function fetchConfigurationsList() {
  return client({ url: `serviceconfigportal/config` });
}

function onFetchConfigurationsListLoading() {
  return { type: ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING };
}

function onSuccessfullFetchConfigurationsList(response) {
  return {
    type: ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
    response
  };
}

function onFetchConfigurationsListFailure(response) {
  return {
    type: ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE,
    response
  };
}

function onFetchConfigurationsList() {
  return function (dispatch) {
    dispatch(onFetchConfigurationsListLoading());
    return fetchConfigurationsList().then(
      (success) => dispatch(onSuccessfullFetchConfigurationsList(success)),
      (error) => dispatch(onFetchConfigurationsListFailure(error))
    );
  };
}

// save configurations
function saveConfigurations(formData) {
  return client({
    method: 'PUT',
    url: `serviceconfigportal/config`,
    data: formData,
    badRequestMessage: 'Unable to save configurations. Please check input data'
  });
}

function onSaveConfigurations(formData) {
  return function (dispatch) {
    return saveConfigurations(formData).then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Configurations saved successfully' }));
        dispatch(onFetchConfigurationsList());
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function fetchDecisionMatrixActions() {
  return client({ url: `serviceconfigportal/config/trs/configactions` });
}

function onFetchDecisionMatrixActionsLoading() {
  return { type: ON_DECISION_MATRIX_ACTIONS_FETCH_LOADING };
}

function onSuccessfullFetchDecisionMatrixActions(response) {
  return {
    type: ON_SUCCESSFUL_DECISION_MATRIX_ACTIONS_FETCH,
    response
  };
}

function onFetchDecisionMatrixActionsFailure(response) {
  return {
    type: ON_DECISION_MATRIX_ACTIONS_FETCH_FAILURE,
    response
  };
}

function onFetchDecisionMatrixActions() {
  return function (dispatch) {
    dispatch(onFetchDecisionMatrixActionsLoading());
    return fetchDecisionMatrixActions().then(
      (success) => dispatch(onSuccessfullFetchDecisionMatrixActions(success)),
      (error) => dispatch(onFetchDecisionMatrixActionsFailure(error))
    );
  };
}

function recalculatePropensityScores() {
  return client({
    url: `serviceconfigportal/config`,
    badRequestMessage: 'Unable to recalculate propensity scores'
  });
}

function onRecalculatePropensityScores() {
  return function (dispatch) {
    return recalculatePropensityScores().then(
      () => {
        dispatch(onShowSuccessAlert({ message: 'Recalculate Propensity Scores successfully' }));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export {
  onFetchConfigurationsList,
  onSaveConfigurations,
  onFetchDecisionMatrixActions,
  onRecalculatePropensityScores
};
