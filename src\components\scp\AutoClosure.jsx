import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useCallback } from 'react';
import { FormGroup, Label, Input, Col } from 'reactstrap';

import HelpIcon from 'components/common/HelpIcon';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function AutoClosure({
  fraudTypes,
  highlightText,
  saveConfigurations,
  configurationsData,
  fetchFraudTypesList,
  toggleRow,
  openRows
}) {
  const [autoClosure, setAutoClosure] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  useEffect(() => {
    if (isEmpty(fraudTypes.list)) fetchFraudTypesList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const verdictOptions = fraudTypes.error
    ? null
    : fraudTypes.list.map((verdict) => (
        <option key={verdict.id} value={verdict.id}>
          {verdict.verdict}
        </option>
      ));

  const selectedVerdict =
    fraudTypes.error || autoClosure?.verdictTypeId?.value === ''
      ? null
      : fraudTypes.list.find((verdictItem) => verdictItem.id === autoClosure?.verdictTypeId?.value);

  const fraudTypeOptions =
    fraudTypes.error || isEmpty(autoClosure?.verdictTypeId?.value) || isEmpty(selectedVerdict)
      ? null
      : selectedVerdict.types.map((fraud) => (
          <option key={fraud.id} value={fraud.id}>
            {fraud.fraudName}
          </option>
        ));

  const isDisabled = autoClosure?.activation?.value === 'disabled';

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        autoClosure,
        setAutoClosure,
        saveConfigurations
      ),
    [configurationsData, autoClosure, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, autoClosure, setAutoClosure),
    [autoClosure]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setAutoClosure),
    [configurationsData]
  );

  return (
    <ConfigFormWrapper
      configTitle="Auto Closure"
      activationId="activationAutoClosure"
      highlightText={highlightText}
      data={autoClosure}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="autoClosureDays" className="searchable">
          {highlightText('Auto closure in:')}
          {!isEmpty(autoClosure?.autoClosureDays?.desc) && (
            <HelpIcon size="lg" id="autoClosureIn" text={autoClosure?.autoClosureDays?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="autoClosureDays"
            id="autoClosureDays"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            max={100}
            min={0}
            value={autoClosure?.autoClosureDays?.value}
            disabled={isDisabled}
          />
        </Col>
        <Label sm={2} for="autoClosureDays" className="searchable">
          {highlightText('days')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="jobInterval" className="searchable">
          {highlightText('Auto closure interval:')}
          {!isEmpty(autoClosure?.jobInterval?.desc) && (
            <HelpIcon size="lg" id="autoClosureInterval" text={autoClosure?.jobInterval?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="jobInterval"
            id="jobInterval"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            max={24}
            min={0}
            value={autoClosure?.jobInterval?.value}
            step="any"
            disabled={isDisabled}
          />
        </Col>
        <Label sm={2} for="jobInterval" className="searchable">
          {highlightText('hrs')}
        </Label>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="timeOfDay" className="searchable">
          {highlightText('Execution time:')}
          {!isEmpty(autoClosure?.timeOfDay?.desc) && (
            <HelpIcon size="lg" id="executionTime" text={autoClosure?.timeOfDay?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="time"
            name="timeOfDay"
            id="timeOfDay"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure?.timeOfDay?.value}
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>
      <b className="pt-3 my-1 row default-head">
        <span className="searchable">{highlightText('Defaults:')}</span>
      </b>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="verdictTypeId" className="searchable default-option">
          {highlightText('Verdict:')}
          {!isEmpty(autoClosure?.verdictTypeId?.desc) && (
            <HelpIcon size="lg" id="verdict" text={autoClosure?.verdictTypeId?.desc} />
          )}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="select"
            name="verdictTypeId"
            id="verdictTypeId"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure?.verdictTypeId?.value}
            disabled={isDisabled}>
            <option value=""> -- select -- </option>
            {verdictOptions}
          </Input>
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="fraudTypeId" className="searchable default-option">
          {highlightText('Fraud type:')}
          {!isEmpty(autoClosure?.fraudTypeId?.desc) && (
            <HelpIcon size="lg" id="fraudType" text={autoClosure?.fraudTypeId?.desc} />
          )}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="select"
            name="fraudTypeId"
            id="fraudTypeId"
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure?.fraudTypeId?.value}
            disabled={autoClosure?.verdictTypeId?.value === '' || isDisabled}>
            <option value=""> -- select -- </option>
            {fraudTypeOptions}
          </Input>
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="explanation" className="searchable default-option">
          {highlightText('Explanation:')}
          {!isEmpty(autoClosure?.explanation?.desc) && (
            <HelpIcon size="lg" id="explanation" text={autoClosure?.explanation?.desc} />
          )}
        </Label>
        <Col sm={6} md={5} lg={4}>
          <Input
            type="textarea"
            name="explanation"
            id="explanation"
            maxLength={256}
            onChange={(event) => inputChangeDataHandler(event, autoClosure, setAutoClosure)}
            value={autoClosure?.explanation?.value}
            rows={4}
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AutoClosure.propTypes = {
  fraudTypes: PropTypes.object.isRequired,
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired
};

export default AutoClosure;
