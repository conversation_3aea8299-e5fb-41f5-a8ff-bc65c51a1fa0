import { faEye } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import ReactTable from 'react-table';
import { Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import TableLoader from 'components/loader/TableLoader';

const SnoozedCaseModal = ({
  theme,
  liability,
  fraudTypes,
  caseSnoozeList,
  closeCaseBuckets,
  fetchLiabilityList,
  fetchFraudTypesList,
  fetchCloseCaseBuckets,
  fetchSnoozeConditionsList
}) => {
  const [display, setDisplay] = useState(false);

  useEffect(() => {
    if (liability?.list.length === 0) fetchLiabilityList();
    if (fraudTypes?.list.length === 0) fetchFraudTypesList();
    if (closeCaseBuckets?.list.length === 0) fetchCloseCaseBuckets();
    fetchSnoozeConditionsList();
  }, []);

  const headers = [
    { Header: 'Rule Name', accessor: 'ruleName', minWidth: 200 },
    {
      Header: 'Conditions',
      accessor: 'conditionList',
      minWidth: 250,
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => (
        <ul>
          {_.map(value, (d) => (
            <li>
              {d.attribute} {d.operator} {d.value}
            </li>
          ))}
        </ul>
      )
    },
    {
      Header: 'Verdict',
      accessor: 'investigationVerdictId',
      minWidth: 100,

      Cell: ({ value }) => {
        fraudTypes?.list?.find((d) => d.id === value)?.verdict || value;
      }
    },
    {
      Header: 'Fraud Type',
      accessor: 'fraudTypeId',
      minWidth: 100,
      Cell: (row) => {
        const fraudType = fraudTypes?.list?.find(
          (d) => d.id === row.original.investigationVerdictId
        );
        return (
          fraudType?.types?.find((d) => d.id === row.original.fraudTypeId)?.fraudName ||
          row.original.fraudTypeId
        );
      }
    },
    {
      Header: 'Bucket',
      accessor: 'bucketId',
      minWidth: 100,
      Cell: ({ value }) => {
        closeCaseBuckets?.list?.find((d) => d.id === value)?.name || value;
      }
    },
    { Header: 'Remarks', accessor: 'remark', minWidth: 100 },
    {
      Header: 'Snooze till',
      accessor: 'snoozeUntil',
      minWidth: 130,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null)
    },
    { Header: 'Requested by', accessor: 'userName', minWidth: 130 },
    {
      Header: 'Requested at',
      accessor: 'createdAt',
      minWidth: 130,
      Cell: ({ value }) => Moment(value).format('YYYY-MM-DD hh:mm A')
    }
  ];

  return (
    <span>
      <Button outline size="sm" color="primary" onClick={() => setDisplay(!display)}>
        <FontAwesomeIcon icon={faEye} className="me-2" />
        Snooze Conditions
      </Button>
      <ModalContainer
        size="xl"
        header="Case Snooze Conditions"
        theme={theme}
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        {(() => {
          if (caseSnoozeList.loader) return <TableLoader />;

          if (caseSnoozeList.error && _.isEmpty(caseSnoozeList.list))
            return <div className="no-data-div">{caseSnoozeList.errorMessage}</div>;

          return (
            <ReactTable
              filterable
              defaultFilterMethod={(filter, row) =>
                row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
              }
              columns={headers}
              data={caseSnoozeList.list}
              pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              defaultPageSize={10}
              minRows={3}
              showPaginationTop={true}
              showPaginationBottom={false}
              className="-highlight  -striped"
              defaultSorted={[
                {
                  id: 'snoozeUntil',
                  asc: true
                }
              ]}
            />
          );
        })()}
      </ModalContainer>
    </span>
  );
};

SnoozedCaseModal.propTypes = {
  theme: PropTypes.string.isRequired,
  liability: PropTypes.object.isRequired,
  fraudTypes: PropTypes.object.isRequired,
  caseSnoozeList: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  fetchLiabilityList: PropTypes.func.isRequired,
  fetchFraudTypesList: PropTypes.func.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  fetchSnoozeConditionsList: PropTypes.func.isRequired
};

export default SnoozedCaseModal;
