import {
  ON_SUCCESSFUL_FETCH_MONITORING_DATA,
  ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT,
  ON_FETCH_MONITORING_DATA_FAILURE,
  ON_FETCH_TRANSACTION_COUNT_FAILURE
} from 'constants/actionTypes';
import initialState from './initialState';

const getLevels = (txns) => {
  return Object.keys(txns);
};

const flatten = (arr) => {
  return arr.reduce((flat, toFlatten) => {
    return flat.concat(Array.isArray(toFlatten) ? flatten(toFlatten) : toFlatten);
  }, []);
};

const combineArrays = (transactions) => {
  return getLevels(transactions).map((level) => {
    return transactions[level].map((data) => {
      return {
        ...data,
        violatedRules: JSON.parse(data.reViolatedRules),
        priority: level
      };
    });
  });
};

export default function monitoringReducer(state = initialState.monitor, action) {
  switch (action.type) {
    case ON_SUCCESSFUL_FETCH_MONITORING_DATA:
      return {
        ...state,
        flaggedCount: action.response.flaggedTransactions,
        fraudCount: action.response.identifiedFrauds,
        transactions: flatten(combineArrays(action.response.transactions))
      };
    case ON_SUCCESSFUL_FETCH_TRANSACTION_COUNT:
      return {
        ...state,
        totalCount: {
          ...state.totalCount,
          [action.channel]: action.response
        }
      };
    case ON_FETCH_MONITORING_DATA_FAILURE:
      return {
        ...state,
        flaggedCount: initialState.monitor.flaggedCount,
        fraudCount: initialState.monitor.fraudCount,
        transactions: initialState.monitor.transactions
      };
    case ON_FETCH_TRANSACTION_COUNT_FAILURE:
      return {
        ...state,
        totalCount: initialState.monitor.totalCount
      };
    default:
      return state;
  }
}
