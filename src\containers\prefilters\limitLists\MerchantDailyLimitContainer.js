import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import MerchantDailyLimit from 'components/prefilters/limitLists/MerchantDailyLimit';
import * as prefiltersListAction from 'actions/prefiltersListAction';

const mapStateToProps = (state) => {
  return {
    prefiltersList: state.prefiltersList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const MerchantDailyLimitContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(MerchantDailyLimit);

export default MerchantDailyLimitContainer;
