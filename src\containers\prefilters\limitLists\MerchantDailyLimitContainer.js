import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import MerchantDailyLimit from 'components/prefilters/limitLists/MerchantDailyLimit';

const mapStateToProps = (state) => ({
  prefiltersList: state.prefiltersList
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const MerchantDailyLimitContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(MerchantDailyLimit);

export default MerchantDailyLimitContainer;
