import responses from 'mocks/responses';

import * as actions from 'actions/incidentActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const mockedStore = {
  incidents: {
    closedList: {
      conf: {
        sortBy: 'weight',
        sortOrder: 'desc',
        filterCondition: []
      }
    }
  }
};

describe('incident actions', () => {
  it('should Create Incidents', () => {
    const formData = ['issuerTxn20231016207'];

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_FETCH_INCIDENT_BY_ID_SUCCESS,
        response: responses.incidents.incident
      },
      { type: types.ON_TOGGLE_CPIFR_FORM_MODAL },
      { type: types.ON_FETCH_CASE_CLOSED_CPIFR_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onCreateIncidents(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch All Incidents', () => {
    const expectedActions = [
      { type: types.ON_FETCH_ALL_INCIDENTS_LOADING },
      {
        type: types.ON_FETCH_ALL_INCIDENTS_SUCCESS,
        response: responses.incidents.allIncidents
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchAllIncidents(4)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Incident By Id', () => {
    const expectedActions = [
      { type: types.ON_FETCH_INCIDENT_BY_ID_LOADING },
      {
        type: types.ON_FETCH_INCIDENT_BY_ID_SUCCESS,
        response: responses.incidents.incident
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchIncidentById(4)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Submit CPFIR Report', () => {
    const formData = { incidentId: 2, incidents: responses.incidents.allIncidents };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'CPFIR File has been downloaded successfully!' }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSubmitCPFIRReport(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch CPFIR Masters', () => {
    const expectedActions = [
      { type: types.ON_FETCH_CPIFR_MASTERS_LOADING },
      {
        type: types.ON_FETCH_CPIFR_MASTERS_SUCCESS,
        response: responses.incidents.masters
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCPFIRMasters()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Closed Cases CPIFR', () => {
    const formData = {};
    const expectedActions = [
      { type: types.ON_FETCH_CASE_CLOSED_CPIFR_LOADING },
      {
        type: types.ON_FETCH_CASE_CLOSED_CPIFR_SUCCESS,
        response: responses.incidents.closedList,
        conf: formData
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchClosedCasesCPIFR(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Incident By Id Success', () => {
    const expectedAction = {
      type: types.ON_FETCH_INCIDENT_BY_ID_SUCCESS,
      response: responses.incidents.incident
    };
    expect(actions.onFetchIncidentByIdSuccess(responses.incidents.incident)).toEqual(
      expectedAction
    );
  });

  it('should Fetch CPIFR Report History', () => {
    const formData = { internalIdList: [4] };

    const expectedActions = [
      { type: types.ON_FETCH_CPIFR_REPORT_HISTORY_LOADING },
      {
        type: types.ON_FETCH_CPIFR_REPORT_HISTORY_SUCCESS,
        response: responses.incidents.history
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCPIFRReportHistory(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Update FRN', () => {
    const formData = {
      internalId: 4,
      frn: ''
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: 'FRN updated successfully'
      },
      { type: types.ON_FETCH_ALL_INCIDENTS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onUpdateFRN(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Clear Selected Incident', () => {
    const expectedAction = {
      type: types.ON_CLEAR_SELECTED_INCIDENT
    };
    expect(actions.onClearSelectedIncident()).toEqual(expectedAction);
  });
});
