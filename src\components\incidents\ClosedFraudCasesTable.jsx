import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';

import CardContainer from 'components/common/CardContainer';
import TableFilterForm from 'components/common/TableFilterForm';
import TransactionTableContainer from 'containers/common/TransactionTableContainer';

function ClosedFraudCasesTable({ channels, hasProvisionalFields, data, actions }) {
  const [tableFilters, setTableFilters] = useState();
  useEffect(() => {
    actions.onFetchClosedCasesCPIFR(data?.conf);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContainer title="Closed Confirmed Fraud Cases">
      <TableFilterForm
        currentConf={data?.conf}
        fetchCases={actions.onFetchClosedCasesCPIFR}
        channel={channels[0]}
        hasProvisionalFields={hasProvisionalFields}
        dateRange={{ startDate: Moment().subtract(7, 'days'), endDate: Moment() }}
      />
      <TransactionTableContainer
        data={data}
        pivotBy={['entityId']}
        filtered={tableFilters}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
      />
    </CardContainer>
  );
}

ClosedFraudCasesTable.propTypes = {
  channels: PropTypes.array.isRequired,
  data: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired
};

export default ClosedFraudCasesTable;
