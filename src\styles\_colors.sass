// -----------------------------------------------------------------------------
// Dark Theme Colors
// -----------------------------------------------------------------------------
$background-dark: #121212                              // Pure dark background for better contrast
$surface-dark: #1e1e1e                                 // Card/surface background
$surface-variant-dark: #2d2d2d                         // Alternative surface

$text-high-emphasis-dark: rgba(255, 255, 255, 0.87)    // High contrast text
$text-medium-emphasis-dark: rgba(255, 255, 255, 0.6)   // Medium contrast text
$text-disabled-dark: rgba(255, 255, 255, 0.38)         // Disabled text

$color-primary-dark: #2196f3                           // Material Blue 500 - better accessibility
$color-secondary-dark: rgba(255, 255, 255, 0.75)       // Material Grey 300 - improved contrast
$color-error-dark: #f44336                             // Material Red 500 - improved contrast
$color-warning-dark: #ff9800                           // Material Orange 500 - better visibility
$color-info-dark: #00bcd4                              // Material Cyan 500 - distinct from primary
$color-success-dark: #4caf50                           // Material Green 500 - better contrast

$scroll-thumb-dark: rgba(255, 255, 255, 0.3)           // More visible scrollbar
$scroll-track-dark: rgba(255, 255, 255, 0.1)           // Lighter track for better visibility
$sidebar-background-dark: rgba(18, 18, 18, 0.95)       // Better sidebar contrast
$shadow-dark: rgba(0, 0, 0, 0.5)                       // Proper shadow with transparency
$hover-dark: rgba(255, 255, 255, 0.08)                 // Subtle hover state
$border-dark: rgba(255, 255, 255, 0.12)                // Subtle borders in dark theme
$divider-dark: rgba(255, 255, 255, 0.08)               // Divider lines in dark theme
$focus-ring-dark: rgba(33, 150, 243, 0.5)              // Blue focus ring for dark theme
$disabled-dark: rgba(255, 255, 255, 0.3)               // Disabled state for dark theme

// -----------------------------------------------------------------------------
// Light Theme Colors
// -----------------------------------------------------------------------------
$background-light: #f5f5f5                             // Softer background, less harsh than pure white
$surface-light: #ffffff                                // Card/surface background
$surface-variant-light: #f8f9fa                        // Alternative surface

$text-high-emphasis-light: rgba(0, 0, 0, 0.87)         // High contrast text
$text-medium-emphasis-light: rgba(0, 0, 0, 0.6)        // Medium contrast text
$text-disabled-light: rgba(0, 0, 0, 0.38)              // Disabled text

$color-primary-light: #1976d2                          // Material Blue 700 - excellent contrast
$color-secondary-light: #757575                        // Material Grey 600 - better contrast
$color-error-light: #d32f2f                            // Material Red 700 - improved accessibility
$color-warning-light: #f57c00                          // Material Orange 600 - better contrast
$color-info-light: #0288d1                             // Material Light Blue 700 - distinct from primary
$color-success-light: #388e3c                          // Material Green 700 - excellent contrast

$scroll-thumb-light: rgba(0, 0, 0, 0.4)                // More visible scrollbar
$scroll-track-light: rgba(0, 0, 0, 0.08)               // Lighter track for better visibility
$sidebar-background-light: rgba(255, 255, 255, 0.95)   // Better sidebar contrast
$shadow-light: rgba(0, 0, 0, 0.12)                     // Proper shadow with transparency
$hover-light: rgba(0, 0, 0, 0.04)                      // Subtle hover state
$border-light: rgba(0, 0, 0, 0.12)                     // Subtle borders in light theme
$divider-light: rgba(0, 0, 0, 0.08)                    // Divider lines in light theme
$focus-ring-light: rgba(25, 118, 210, 0.3)             // Blue focus ring for light theme
$disabled-light: rgba(0, 0, 0, 0.26)                   // Disabled state for light theme
