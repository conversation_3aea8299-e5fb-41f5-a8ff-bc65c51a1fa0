import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as notationActions from 'actions/notationActions';
import { onShowFailureAlert } from 'actions/alertActions';
import Notation from 'components/common/Notation';

const mapStateToProps = (state) => {
  return {
    userName: state.auth.userCreds.userName,
    notations: state.notations
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    notationActions: bindActionCreators(notationActions, dispatch),
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch)
  };
};

const NotationContainer = connect(mapStateToProps, mapDispatchToProps)(Notation);

export default NotationContainer;
