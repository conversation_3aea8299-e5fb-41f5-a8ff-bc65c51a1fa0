import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onShowFailureAlert } from 'actions/alertActions';
import * as notationActions from 'actions/notationActions';
import Notation from 'components/common/Notation';

const mapStateToProps = (state) => ({
  userName: state.auth.userCreds.userName,
  notations: state.notations
});

const mapDispatchToProps = (dispatch) => ({
  notationActions: bindActionCreators(notationActions, dispatch),
  showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch)
});

const NotationContainer = connect(mapStateToProps, mapDispatchToProps)(Notation);

export default NotationContainer;
