import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as dynamicCountersAction from 'actions/dynamicCountersAction';
import * as toggleActions from 'actions/toggleActions';
import DynamicCountersListTable from 'components/ruleEngine/DynamicCountersListTable';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  dynamicCounters: state.dynamicCounters,
  role: state.auth.userCreds.roles
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  dynamicCountersAction: bindActionCreators(dynamicCountersAction, dispatch)
});

const DynamicCountersListTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(DynamicCountersListTable);

export default DynamicCountersListTableContainer;
