import { onShowF<PERSON>ure<PERSON>lert, onShowSuccessAlert } from 'actions/alertActions';
import { onFetchShiftDetails } from 'actions/slaDashboardActions';
import { onToggleLoader, onToggleResetPasswordModal } from 'actions/toggleActions';
import { onFetchStages, onFetchShifts, onFetchConfigurations } from 'actions/userManagementActions';
import {
  ON_SUCCESSFUL_LOGIN,
  ON_SUCCESSFUL_LOGOUT,
  ON_TOGGLE_LOGOUT_MODAL,
  ON_TOGGLE_SESSION_IDLE,
  ON_RESET_SESSION_TIMEOUT,
  ON_SUCCESSFUL_FETCH_ALL_ROLES,
  ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS,
  ON_SUCCESSFUL_FETCH_LOGIN_TYPES,
  ON_UPDATE_PASSWORD_SUCCESS,
  ON_2FA_AUTHENTICATE_USER_SUCCESS,
  ON_2FA_AUTHENTICATE_OTP_SUCCESS
} from 'constants/actionTypes';
import { isCooperative } from 'constants/publicKey';
import client from 'utility/apiClient';

function fetchProvisionalFields() {
  return client({
    url: `uds/provisionalFields`
  });
}

function onSuccessfulFetchProvisionalFields(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_PROVISIONAL_FIELDS,
    response
  };
}

function onFetchProvisionalFields() {
  return function (dispatch) {
    return fetchProvisionalFields().then(
      (success) => dispatch(onSuccessfulFetchProvisionalFields(success)),
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

function login(userDetails, loginInfo) {
  const loginURL = userDetails.effectiveRole === 'super-admin' ? '/superadmin' : '';
  return client({
    url: `useraccessmanagement/login${loginURL}`,
    method: 'POST',
    data: { loginInfo },
    badRequestMessage: 'Invalid credentials',
    notFoundMessage: 'No user found'
  });
}

function onSuccessfulLogin(response, userDetails) {
  return {
    type: ON_SUCCESSFUL_LOGIN,
    response,
    userDetails
  };
}

function onLogin(userDetails, loginInfo) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return login(userDetails, loginInfo)
      .then(
        (success) => onLoginSuccessActions(success, dispatch),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then((data) => data && dispatch(onSuccessfulLogin(data, userDetails)))
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function logout(formData) {
  return client({
    url: `useraccessmanagement/logout`,
    method: 'POST',
    data: formData,
    badRequestMessage: 'Unable to logout'
  });
}

function onSuccessfulLogout() {
  return { type: ON_SUCCESSFUL_LOGOUT };
}

function onLogout(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return logout(formData).then(
      () => dispatch(onSuccessfulLogout()),
      () => dispatch(onSuccessfulLogout())
    );
  };
}

function onToggleLogoutModal(newState) {
  return { type: ON_TOGGLE_LOGOUT_MODAL, newState };
}

function onToggleSessionIdle(newState) {
  return { type: ON_TOGGLE_SESSION_IDLE, newState };
}

function onResetSessionTimeout() {
  return { type: ON_RESET_SESSION_TIMEOUT };
}

function fetchAllRoles() {
  return client({
    url: `useraccessmanagement/role`,
    badRequestMessage: 'Unable to fetch all roles',
    notFoundMessage: 'No roles available'
  });
}

function onSuccessfulfetchAllRoles(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_ALL_ROLES,
    response
  };
}

function onFetchAllRoles() {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return fetchAllRoles()
      .then(
        (success) => dispatch(onSuccessfulfetchAllRoles(success)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function fetchLoginTypes() {
  return client({
    url: `useraccessmanagement/logintype`,
    badRequestMessage: 'Unable to fetch login type',
    notFoundMessage: 'No login type available'
  });
}

function onSuccessfulFetchLoginTypes(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_LOGIN_TYPES,
    response
  };
}

function onFetchLoginTypes() {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return fetchLoginTypes()
      .then(
        (success) => dispatch(onSuccessfulFetchLoginTypes(success)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function updatePassword(formData) {
  return client({
    method: 'POST',
    url: `useraccessmanagement/updatePassword`,
    data: formData,
    badRequestMessage: 'Unable to update password',
    notFoundMessage: 'Service unavailable. Contact administrator'
  });
}

function onUpdatePasswordSuccess() {
  return { type: ON_UPDATE_PASSWORD_SUCCESS };
}

function onUpdatePassword(formData) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return updatePassword(formData)
      .then(
        () => {
          isCooperative && dispatch(onUpdatePasswordSuccess());
          dispatch(onToggleResetPasswordModal());
          dispatch(
            onShowSuccessAlert({
              message: 'Password updated successfully'
            })
          );
        },
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function refreshToken() {
  return client({ url: `useraccessmanagement/login/refreshtoken` });
}

function onRefreshToken() {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return refreshToken()
      .then(
        () => dispatch(onShowSuccessAlert({ message: 'Active session restored successfully!' })),
        () =>
          dispatch(
            onShowFailureAlert({ message: 'Failed to restore active session. Please login again.' })
          )
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function validateUser2FA(loginInfo) {
  return client({
    url: `useraccessmanagement/2fa/login`,
    method: 'POST',
    data: { loginInfo },
    badRequestMessage: 'Invalid credentials',
    notFoundMessage: 'No user found'
  });
}

function onValidateUser2FASuccess(response, userDetails) {
  return {
    type: ON_2FA_AUTHENTICATE_USER_SUCCESS,
    response,
    userDetails
  };
}

function onValidateUser2FA(userDetails, loginInfo) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return validateUser2FA(loginInfo)
      .then(
        (success) => dispatch(onValidateUser2FASuccess(success, userDetails)),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function submitOTP2FA(loginInfo) {
  return client({
    url: `useraccessmanagement/2fa/otp/validation`,
    method: 'POST',
    data: loginInfo,
    badRequestMessage: 'Invalid credentials',
    notFoundMessage: 'No user found'
  });
}

function onSubmitOTP2FASuccess(response) {
  return {
    type: ON_2FA_AUTHENTICATE_OTP_SUCCESS,
    response
  };
}

function onSubmitOTP2FA(loginInfo) {
  return function (dispatch) {
    dispatch(onToggleLoader(true));
    return submitOTP2FA(loginInfo)
      .then(
        (success) => onLoginSuccessActions(success, dispatch),
        (error) => dispatch(onShowFailureAlert(error))
      )
      .then((data) => data && dispatch(onSubmitOTP2FASuccess(data)))
      .then(() => dispatch(onToggleLoader(false)));
  };
}

function onLoginSuccessActions(loginData, dispatch) {
  if (loginData.channelRoles.length !== 0) {
    dispatch(onFetchConfigurations()).then(
      (data) =>
        data?.response?.provisionalFields === 1 &&
        !['super-admin', 'admin'].includes(loginData.channelRoles) &&
        dispatch(onFetchProvisionalFields())
    );
    dispatch(onFetchStages());
    if (loginData.channelRoles.includes('frm:supervisor')) {
      dispatch(onFetchShifts());
      isCooperative && dispatch(onFetchShiftDetails());
    }

    return loginData;
  } else dispatch(onShowFailureAlert({ message: 'No compatible roles found.' }));
}

export {
  onLogin,
  onLogout,
  onToggleLogoutModal,
  onToggleSessionIdle,
  onResetSessionTimeout,
  onFetchAllRoles,
  onUpdatePassword,
  onRefreshToken,
  onFetchLoginTypes,
  onValidateUser2FA,
  onSubmitOTP2FA
};
