import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const Escalation = ({
  toggle,
  display,
  theme,
  authDetails,
  transactionDetails,
  escalateByMail
}) => {
  const [escalateTo, setEscalateTo] = useState('');
  const [escalateSubject, setEscalateSubject] = useState('');
  const [escalateMessage, setEscalateMessage] = useState('');

  const escalate = (e) => {
    e.preventDefault();
    const formData = {
      to: escalateTo,
      subject: escalateSubject,
      message: escalateMessage,
      from: authDetails.email,
      mobileNo: transactionDetails.mobileNo,
      id: transactionDetails.txnId
    };
    escalateByMail(formData);
  };

  return (
    <ModalContainer theme={theme} header="Escalate Case" isOpen={display} toggle={toggle}>
      <form onSubmit={escalate}>
        <FormGroup>
          <Label>Escalate To</Label>
          <Input
            type="email"
            name="escalateTo"
            placeholder="Escalation to"
            onChange={(e) => setEscalateTo(e.target.value)}
            value={escalateTo}
            required
          />
        </FormGroup>
        <FormGroup>
          <Label>Subject</Label>
          <Input
            type="text"
            name="escalateSubject"
            id="escalateSubject"
            placeholder="Escalation subject"
            onChange={(e) => setEscalateSubject(e.target.value)}
            value={escalateSubject}
            required
          />
        </FormGroup>
        <FormGroup>
          <Label>Message</Label>
          <Input
            type="textarea"
            name="escalateMessage"
            id="escalateMessage"
            placeholder="Message"
            onChange={(e) => setEscalateMessage(e.target.value)}
            value={escalateMessage}
            required
          />
        </FormGroup>
        <p>
          <b>1</b> transaction(s) selected for escalation.
        </p>
        <FormGroup className="d-flex justify-content-end">
          <Button color="primary">Send</Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
};

Escalation.propTypes = {
  theme: PropTypes.string.isRequired,
  display: PropTypes.bool.isRequired,
  authDetails: PropTypes.object.isRequired,
  transactionDetails: PropTypes.object.isRequired,
  escalateByMail: PropTypes.func.isRequired,
  toggle: PropTypes.func.isRequired
};

export default Escalation;
