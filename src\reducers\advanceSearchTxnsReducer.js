import _ from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_ADVANCE_SEARCH_TRANSACTION_LOADING,
  ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS,
  ON_ADVANCE_SEARCH_TRANSACTION_FAILURE,
  ON_REMOVE_SEARCH_TRANSACTION
} from 'constants/actionTypes';

import initialState from './initialState';

export default function advanceSearchTxnsReducer(state = initialState.advanceSearchTxns, action) {
  switch (action.type) {
    case ON_ADVANCE_SEARCH_TRANSACTION_LOADING:
      return objectAssign({}, state, {
        loader: true,
        error: false,
        errorMessage: ''
      });
    case ON_ADVANCE_SEARCH_TRANSACTION_SUCCESS:
      return objectAssign({}, state, {
        filterCondition: action.filterCondition,
        list: _.isEqual(action.filterCondition, state.filterCondition)
          ? _.unionBy(state.list, action.response.records, 'txnId')
          : action.response.records,
        count: action.response.count,
        isLastPage: action.response.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      });
    case ON_ADVANCE_SEARCH_TRANSACTION_FAILURE:
      return objectAssign({}, state, {
        filterCondition: action.filterCondition,
        list: _.isEqual(action.filterCondition, state.filterCondition) ? state.list : [],
        count: _.isEqual(action.filterCondition, state.filterCondition) ? state.count : 0,
        isLastPage: _.isEqual(action.filterCondition, state.filterCondition)
          ? state.isLastPage
          : true,
        loader: false,
        error: true,
        errorMessage: action.response?.message || 'Unknown error'
      });
    case ON_REMOVE_SEARCH_TRANSACTION:
      return objectAssign({}, state, {
        list: _.filter(
          state.list,
          (listItem) =>
            !_.includes(action.items, listItem.caseRefNo) &&
            !_.includes(action.items, listItem.txnId)
        )
      });
    default:
      return state;
  }
}
