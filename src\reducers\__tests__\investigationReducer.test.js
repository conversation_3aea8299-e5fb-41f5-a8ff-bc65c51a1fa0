import investigationReducer from 'reducers/investigationReducer';
import initialState from 'reducers/initialState';
import * as types from 'constants/actionTypes';

describe('Investigation reducer', () => {
  it('should return the intial state', () => {
    expect(investigationReducer(undefined, {})).toEqual(initialState.investigation);
  });

  it('should handle ON_FETCH_SIMILAR_TXNS_LOADING', () => {
    expect(
      investigationReducer(
        {
          similar: {
            list: [],
            count: 0,
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SIMILAR_TXNS_LOADING
        }
      )
    ).toEqual({
      similar: {
        list: [],
        count: 0,
        isLastPage: true,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_FETCH_SIMILAR_TXNS', () => {
    const response = {
      similarTxns: [
        {
          cognitiveResponse: {
            cognitiveCredibilityLevel: 'High',
            cognitiveIsSuspicious: 0
          },
          currentStage: 'Investigation',
          currentStatus: 'New',
          deviceId: '',
          ifrmVerdict: 'REJECTED',
          payeeAccountNumber: 'rpsl123',
          payeeId: '1113',
          payeeMcc: '5411,Kirana',
          payerAccountNumber: 'rpsl456',
          payerCardNumberHash: '',
          payerCardNumberMask: '',
          payerId: '992233',
          responseCode: '00,Accepted',
          similarity: 'Terminal Id',
          terminalId: '12345',
          txnAmount: 2000,
          txnId: 'Test20210107510',
          txnTimestamp: '2021-01-07T11:00:10',
          txnTypeName: '00, Purchase'
        }
      ],
      count: 70,
      isLastPage: false
    };

    expect(
      investigationReducer(
        {
          similar: {
            similarTxnCategory: [],
            list: [],
            count: 0,
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_FETCH_SIMILAR_TXNS,
          response: response,
          similarTxnCategory: 'terminalId'
        }
      )
    ).toEqual({
      similar: {
        similarTxnCategory: 'terminalId',
        list: response.similarTxns,
        count: response.count,
        isLastPage: response.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SIMILAR_TXNS_FAILURE', () => {
    expect(
      investigationReducer(
        {
          similar: {
            similarTxnCategory: [],
            list: [],
            count: 0,
            isLastPage: true,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SIMILAR_TXNS_FAILURE,
          response: { message: 'No similar transactions found' },
          similarTxnCategory: 'terminalId'
        }
      )
    ).toEqual({
      similar: {
        similarTxnCategory: 'terminalId',
        list: [],
        count: 0,
        isLastPage: true,
        loader: false,
        error: true,
        errorMessage: 'No similar transactions found'
      }
    });
  });

  it('should handle ON_CUSTOMER_TRENDS_FETCH_LOADING', () => {
    expect(
      investigationReducer(
        {
          trends: {
            timeTrend: [],
            payeeTrend: [],
            timeRange: {},
            averageAmount: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_CUSTOMER_TRENDS_FETCH_LOADING
        }
      )
    ).toEqual({
      trends: {
        timeTrend: [],
        payeeTrend: [],
        timeRange: {},
        averageAmount: 0,
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH', () => {
    const response = {
      trendTimeGraph: {
        data: [
          {
            dateTime: '19/02/2017 17:40:35',
            transactionAmount: 100.123,
            transactionType: 'COLLECT'
          },
          {
            dateTime: '09/02/2017 17:40:35',
            transactionAmount: 100.123,
            transactionType: 'COLLECT'
          },
          {
            dateTime: '03/02/2017 17:40:35',
            transactionAmount: 100.123,
            transactionType: 'COLLECT'
          }
        ],
        usualTxnTimeRange: {
          startTime: 'x',
          endTime: 'y'
        },
        isEmpty: false
      },
      perPayeeTrend: {
        data: [
          {
            payeeVirtualAdd: 't@okhdfcbank',
            totalCount: 3,
            totalAmount: 300.369
          }
        ],
        averageTransactionAmount: 100.123,
        isEmpty: false
      }
    };

    expect(
      investigationReducer(
        {
          trends: {
            timeTrend: [],
            payeeTrend: [],
            timeRange: {},
            averageAmount: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH,
          response
        }
      )
    ).toEqual({
      trends: {
        timeTrend: response.trendTimeGraph.data,
        payeeTrend: response.perPayeeTrend.data,
        timeRange: response.trendTimeGraph.usualTxnTimeRange,
        averageAmount: response.perPayeeTrend.averageTransactionAmount,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_CUSTOMER_TRENDS_FETCH_FAILURE', () => {
    expect(
      investigationReducer(
        {
          trends: {
            timeTrend: [],
            payeeTrend: [],
            timeRange: {},
            averageAmount: 0,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_CUSTOMER_TRENDS_FETCH_FAILURE,
          response: { message: 'Transaction trend for customer unavailable' }
        }
      )
    ).toEqual({
      trends: {
        timeTrend: [],
        payeeTrend: [],
        timeRange: {},
        averageAmount: 0,
        loader: false,
        error: true,
        errorMessage: 'Transaction trend for customer unavailable'
      }
    });
  });

  it('should handle ON_FETCH_TRANSACTION_HISTORY_LOADING', () => {
    expect(
      investigationReducer(
        {
          transactionHistorySearch: {
            frm: {
              entityId: '',
              selectedDates: [],
              count: 0,
              isLastPage: true,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TRANSACTION_HISTORY_LOADING,
          channel: 'frm'
        }
      )
    ).toEqual({
      transactionHistorySearch: {
        frm: {
          entityId: '',
          selectedDates: [],
          count: 0,
          isLastPage: true,
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY', () => {
    const response = {
      isLastPage: true,
      count: 90,
      records: [
        {
          payeeMmid: 'gyj',
          payeeAccountType: '12',
          payerMcc: '5555',
          txnCurrency: '356',
          payeeType: 'ENTITY',
          payerAccountTypeName: '12',
          txnType: '14',
          deviceId: 'IMEI12345',
          responseCode: '00',
          txnTypeName: '14, Bill Payment Open Loop',
          isLien: 'gyj',
          entityCategory: 'agent',
          payeeId: '10006',
          acquirerId: 'acquirerId',
          payeeDeviceOf: 'A',
          longitude: 79.923935,
          payerMmidMobileNumber: 'ygj',
          isMerchantApiBased: 'yt',
          isCardJio: '1',
          mti: '0200',
          payeeCardNumberMask: 'tyjg',
          reViolatedRules: [],
          payeeMobileNumber: '**********',
          initiatorId: '2102',
          sourceInstitutionId: '7172',
          txnTimestamp: '2020-12-19T11:01:18',
          payerCardNumberHash: '************',
          ifrmVerdict: 'ACCEPTED',
          ifrmPostauthVerdictName: 'ACCEPTED',
          initiatorMobile: 'yjg',
          paymentMethod: '12',
          referenceTxnId: 'yjg',
          txnId: 'Test20210119518',
          initiatorType: 'ty',
          txnCategoryId: 'jygj',
          agentId: 'Agent10001',
          cognitiveResponse: '{"cognitiveCredibilityLevel":"High","cognitiveIsSuspicious":0}',
          deviceOs: 'yiy',
          latitude: 23.885838,
          isCognitiveActive: 0,
          payeeCardNumberHash: 'yiy',
          payerId: '992233',
          terminalId: '123453',
          payeeBankIfsc: 'yy',
          payerBankIfsc: 'yy',
          payerAccountNumber: 'rpsl456',
          subAgentId: 'y',
          payerAccountType: '12',
          payerCardNumberMask: 'u',
          payeeMcc: '5411',
          channelName: 'uy',
          payeeAccountNumber: 'rpsl123',
          customerIp: 'yu',
          currentStatus: 'yu',
          txnMessageId: 'rpsl010',
          payeeAccountTypeName: '12',
          payerMmid: 'tyu',
          entityId: 'Agent10001',
          payeeMmidMobileNumber: 'yu',
          txnCategoryName: 'tu',
          payerType: 'PERSON',
          txnAmount: 10000,
          payerMobileNumber: '**********',
          bucketId: 1
        }
      ]
    };
    expect(
      investigationReducer(
        {
          transactionHistorySearch: {
            frm: {
              entityId: '',
              selectedDates: [],
              count: 0,
              isLastPage: true,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_SUCCESSFUL_TRANSACTION_HISTORY,
          response,
          selectedDates: [],
          channel: 'frm',
          entityId: 'A'
        }
      )
    ).toEqual({
      transactionHistorySearch: {
        frm: {
          entityId: 'A',
          selectedDates: [],
          count: response.count,
          isLastPage: response.isLastPage,
          list: response.records,
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_FETCH_TRANSACTION_HISTORY_FAILURE', () => {
    expect(
      investigationReducer(
        {
          transactionHistorySearch: {
            frm: {
              entityId: '',
              selectedDates: [],
              count: 0,
              isLastPage: true,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_FETCH_TRANSACTION_HISTORY_FAILURE,
          response: { message: 'error msg' },
          selectedDates: [],
          channel: 'frm',
          entityId: 'A'
        }
      )
    ).toEqual({
      transactionHistorySearch: {
        frm: {
          entityId: 'A',
          selectedDates: [],
          count: 0,
          isLastPage: true,
          list: [],
          loader: false,
          error: true,
          errorMessage: 'error msg'
        }
      }
    });
  });

  it('should handle ON_CLEAR_TRANSACTION_HISTORY_SEARCH', () => {
    expect(
      investigationReducer(
        {
          transactionHistorySearch: {
            frm: {
              entityId: '',
              selectedDates: [],
              count: 0,
              isLastPage: true,
              list: [],
              loader: false,
              error: false,
              errorMessage: ''
            }
          }
        },
        {
          type: types.ON_CLEAR_TRANSACTION_HISTORY_SEARCH
        }
      )
    ).toEqual({
      transactionHistorySearch: {
        frm: {
          entityId: '',
          selectedDates: [],
          count: 0,
          isLastPage: true,
          list: [],
          loader: false,
          error: false,
          errorMessage: ''
        }
      }
    });
  });

  it('should handle ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING', () => {
    expect(
      investigationReducer(
        {
          similarTxnCategoryList: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING
        }
      )
    ).toEqual({
      similarTxnCategoryList: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS', () => {
    const response = {
      similarTxnCategory: [
        {
          label: 'terminalId',
          value: 'terminalId'
        }
      ]
    };

    expect(
      investigationReducer(
        {
          similarTxnCategoryList: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS,
          response: response
        }
      )
    ).toEqual({
      similarTxnCategoryList: {
        list: response.similarTxnCategory,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE', () => {
    expect(
      investigationReducer(
        {
          similarTxnCategoryList: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SIMILAR_TXNS_CATEGORY_FETCH_FAILURE,
          response: { message: 'No similar transactions found' }
        }
      )
    ).toEqual({
      similarTxnCategoryList: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'No similar transactions found'
      }
    });
  });

  it('should handle ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING', () => {
    expect(
      investigationReducer(
        {
          channelCounterpartyId: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING
        }
      )
    ).toEqual({
      channelCounterpartyId: {
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS', () => {
    const response = [
      {
        label: 'terminalId',
        value: 'terminalId'
      }
    ];
    expect(
      investigationReducer(
        {
          channelCounterpartyId: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS,
          response: response
        }
      )
    ).toEqual({
      channelCounterpartyId: {
        list: response,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE', () => {
    expect(
      investigationReducer(
        {
          channelCounterpartyId: {
            list: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_CHANNEL_COUNTER_PARTY_ID_FAILURE,
          response: { message: 'No data found' }
        }
      )
    ).toEqual({
      channelCounterpartyId: {
        list: [],
        loader: false,
        error: true,
        errorMessage: 'No data found'
      }
    });
  });
});
