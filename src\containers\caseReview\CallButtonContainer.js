import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onCallCustomer } from 'actions/communicationActions';
import CallButton from 'components/caseReview/CallButton';

const mapStateToProps = (state) => {
  return {
    communicationLogs: state.customerCommunication.logs
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    callCustomer: bindActionCreators(onCallCustomer, dispatch)
  };
};

const CallButtonContainer = connect(mapStateToProps, mapDispatchToProps)(CallButton);

export default CallButtonContainer;
