import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onCallCustomer } from 'actions/communicationActions';
import CallButton from 'components/caseReview/CallButton';

const mapStateToProps = (state) => ({
  communicationLogs: state.customerCommunication.logs
});

const mapDispatchToProps = (dispatch) => ({
  callCustomer: bindActionCreators(onCallCustomer, dispatch)
});

const CallButtonContainer = connect(mapStateToProps, mapDispatchToProps)(CallButton);

export default CallButtonContainer;
