import { connect } from 'react-redux';
import RuleTable from 'components/ruleEngine/RuleTable';

const mapStateToProps = (state) => {
  return {
    channels: state.auth.userCreds.channels,
    moduleType: state.auth.moduleType,
    ruleCreation: state.ruleCreation,
    hasMakerChecker: state.user.hasMakerChecker,
    hasSandbox: state.user.configurations.sandbox
  };
};

const RuleTableContainer = connect(mapStateToProps, null)(RuleTable);

export default RuleTableContainer;
