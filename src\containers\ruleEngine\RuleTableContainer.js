import { connect } from 'react-redux';

import RuleTable from 'components/ruleEngine/RuleTable';

const mapStateToProps = (state) => ({
  channels: state.auth.userCreds.channels,
  moduleType: state.auth.moduleType,
  ruleCreation: state.ruleCreation,
  hasMakerChecker: state.user.hasMakerChecker,
  hasSandbox: state.user.configurations.sandbox,
  snoozelist: state.snoozeRules.list
});

const RuleTableContainer = connect(mapStateToProps, null)(RuleTable);

export default RuleTableContainer;
