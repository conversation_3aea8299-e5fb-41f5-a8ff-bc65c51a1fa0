import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, FormGroup, Label } from 'reactstrap';
import Datetime from 'react-datetime';
import ModalContainer from 'components/common/ModalContainer';

const DeactivateRuleModal = ({ isOpen, toggleModal, onSubmit }) => {
  const [ruleDeactivationDate, setRuleDeactivationDate] = useState('');

  const handleDeactivateRule = (e) => {
    e.preventDefault();
    if (ruleDeactivationDate) {
      onSubmit(ruleDeactivationDate);
      toggleModal();
    }
  };

  return (
    <ModalContainer
      size="sm"
      theme="rule-modal"
      header="Deactivate Rule"
      isOpen={isOpen}
      toggle={toggleModal}>
      <form onSubmit={handleDeactivateRule}>
        <FormGroup>
          <Label>Select Date</Label>
          <Datetime
            name="ruleDeactivationDate"
            dateFormat="YYYY-MM-DD"
            timeFormat="HH:mm:ss"
            value={ruleDeactivationDate}
            closeOnSelect={true}
            onChange={(dateObj) => setRuleDeactivationDate(dateObj.format('YYYY-MM-DD HH:mm:ss'))}
          />
        </FormGroup>
        <FormGroup className="d-flex justify-content-end">
          <Button disabled={!ruleDeactivationDate} type="submit" size="sm" color="primary">
            Submit
          </Button>
        </FormGroup>
      </form>
    </ModalContainer>
  );
};

DeactivateRuleModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  toggleModal: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired
};

DeactivateRuleModal.defaultProps = {
  isOpen: false
};

export default DeactivateRuleModal;
