#!/usr/bin/env node

/**
 * Performance Test Runner for RuleEngine Module
 *
 * This script runs comprehensive performance and integration tests
 * for all ruleEngine components and generates detailed reports.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  testDir: 'src/components/ruleEngine/__tests__',
  outputDir: 'test-reports/performance',
  timeout: 60000, // 60 seconds
  maxWorkers: 4,
  coverage: true
};

// Test suites to run
const TEST_SUITES = [
  {
    name: 'RuleBuilder Performance',
    pattern: 'RuleBuilder.performance.test.js',
    category: 'component',
    priority: 'high'
  },
  {
    name: 'RuleTable Performance',
    pattern: 'RuleTable.performance.test.js',
    category: 'component',
    priority: 'high'
  },
  {
    name: 'ProductionRuleTable Performance',
    pattern: 'ProductionRuleTable.performance.test.js',
    category: 'component',
    priority: 'high'
  },
  {
    name: 'Sandbox Performance',
    pattern: 'Sandbox.performance.test.js',
    category: 'component',
    priority: 'medium'
  },
  {
    name: 'Additional Components Performance',
    pattern: 'additionalComponents.performance.test.js',
    category: 'component',
    priority: 'medium'
  },
  {
    name: 'Container Performance',
    pattern: 'containers.performance.test.js',
    category: 'container',
    priority: 'high'
  },
  {
    name: 'RuleEngine Integration',
    pattern: 'ruleEngine.integration.test.js',
    category: 'integration',
    priority: 'high'
  },
  {
    name: 'Comprehensive Integration',
    pattern: 'ruleEngine.comprehensive.integration.test.js',
    category: 'integration',
    priority: 'critical'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const createOutputDir = () => {
  if (!fs.existsSync(TEST_CONFIG.outputDir))
    fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true });
};

const runTestSuite = (suite) => {
  log(`\n🧪 Running ${suite.name}...`, 'cyan');

  const startTime = Date.now();
  const testPath = path.join(TEST_CONFIG.testDir, suite.pattern);

  try {
    const command = [
      'npm test',
      `--testPathPattern=${testPath}`,
      `--testTimeout=${TEST_CONFIG.timeout}`,
      `--maxWorkers=${TEST_CONFIG.maxWorkers}`,
      '--verbose',
      '--detectOpenHandles',
      '--forceExit'
    ].join(' ');

    const output = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe'
    });

    const duration = Date.now() - startTime;

    log(`✅ ${suite.name} completed in ${duration}ms`, 'green');

    return {
      name: suite.name,
      category: suite.category,
      priority: suite.priority,
      status: 'passed',
      duration,
      output: output.toString()
    };
  } catch (error) {
    const duration = Date.now() - startTime;

    log(`❌ ${suite.name} failed after ${duration}ms`, 'red');
    log(`Error: ${error.message}`, 'red');

    return {
      name: suite.name,
      category: suite.category,
      priority: suite.priority,
      status: 'failed',
      duration,
      error: error.message,
      output: error.stdout ? error.stdout.toString() : ''
    };
  }
};

const generateReport = (results) => {
  const timestamp = new Date().toISOString();
  const totalTests = results.length;
  const passedTests = results.filter((r) => r.status === 'passed').length;
  const failedTests = results.filter((r) => r.status === 'failed').length;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  const report = {
    timestamp,
    summary: {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      duration: totalDuration,
      successRate: ((passedTests / totalTests) * 100).toFixed(2)
    },
    results: results.map((result) => ({
      name: result.name,
      category: result.category,
      priority: result.priority,
      status: result.status,
      duration: result.duration,
      ...(result.error && { error: result.error })
    })),
    categories: {
      component: results.filter((r) => r.category === 'component'),
      container: results.filter((r) => r.category === 'container'),
      integration: results.filter((r) => r.category === 'integration')
    }
  };

  // Write JSON report
  const jsonReportPath = path.join(TEST_CONFIG.outputDir, `performance-report-${Date.now()}.json`);
  fs.writeFileSync(jsonReportPath, JSON.stringify(report, null, 2));

  // Write HTML report
  const htmlReport = generateHtmlReport(report);
  const htmlReportPath = path.join(TEST_CONFIG.outputDir, `performance-report-${Date.now()}.html`);
  fs.writeFileSync(htmlReportPath, htmlReport);

  return { report, jsonReportPath, htmlReportPath };
};

const generateHtmlReport = (report) => `
<!DOCTYPE html>
<html>
<head>
    <title>RuleEngine Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center; }
        .metric h3 { margin: 0; color: #1976d2; }
        .metric p { margin: 5px 0 0 0; font-size: 24px; font-weight: bold; }
        .results { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .passed { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .failed { background: #ffeaea; border-left: 4px solid #f44336; }
        .category { font-weight: bold; color: #666; }
        .duration { color: #888; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>RuleEngine Performance Test Report</h1>
        <p>Generated: ${report.timestamp}</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <p>${report.summary.total}</p>
        </div>
        <div class="metric">
            <h3>Passed</h3>
            <p style="color: #4caf50;">${report.summary.passed}</p>
        </div>
        <div class="metric">
            <h3>Failed</h3>
            <p style="color: #f44336;">${report.summary.failed}</p>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <p>${report.summary.successRate}%</p>
        </div>
        <div class="metric">
            <h3>Total Duration</h3>
            <p>${(report.summary.duration / 1000).toFixed(2)}s</p>
        </div>
    </div>

    <div class="results">
        <h2>Test Results</h2>
        <table>
            <thead>
                <tr>
                    <th>Test Name</th>
                    <th>Category</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Duration (ms)</th>
                </tr>
            </thead>
            <tbody>
                ${report.results
                  .map(
                    (result) => `
                    <tr class="${result.status}">
                        <td>${result.name}</td>
                        <td class="category">${result.category}</td>
                        <td>${result.priority}</td>
                        <td>${result.status}</td>
                        <td class="duration">${result.duration}</td>
                    </tr>
                `
                  )
                  .join('')}
            </tbody>
        </table>
    </div>
</body>
</html>`;

const printSummary = (report) => {
  log('\n📊 Performance Test Summary', 'bright');
  log('═'.repeat(50), 'blue');
  log(`Total Tests: ${report.summary.total}`, 'cyan');
  log(`Passed: ${report.summary.passed}`, 'green');
  log(`Failed: ${report.summary.failed}`, 'red');
  log(`Success Rate: ${report.summary.successRate}%`, 'yellow');
  log(`Total Duration: ${(report.summary.duration / 1000).toFixed(2)}s`, 'magenta');
  log('═'.repeat(50), 'blue');
};

// Main execution
const main = async () => {
  log('🚀 Starting RuleEngine Performance Tests', 'bright');

  createOutputDir();

  const results = [];

  // Run tests by priority
  const priorityOrder = ['critical', 'high', 'medium', 'low'];

  for (const priority of priorityOrder) {
    const suitesForPriority = TEST_SUITES.filter((suite) => suite.priority === priority);

    if (suitesForPriority.length > 0) {
      log(`\n🎯 Running ${priority.toUpperCase()} priority tests...`, 'yellow');

      for (const suite of suitesForPriority) {
        const result = runTestSuite(suite);
        results.push(result);
      }
    }
  }

  // Generate reports
  log('\n📝 Generating reports...', 'cyan');
  const { report, jsonReportPath, htmlReportPath } = generateReport(results);

  // Print summary
  printSummary(report);

  log(`\n📄 Reports generated:`, 'green');
  log(`JSON: ${jsonReportPath}`, 'cyan');
  log(`HTML: ${htmlReportPath}`, 'cyan');

  // Exit with appropriate code
  const hasFailures = results.some((r) => r.status === 'failed');
  if (hasFailures) {
    log('\n❌ Some tests failed. Check the reports for details.', 'red');
    process.exit(1);
  } else {
    log('\n✅ All tests passed!', 'green');
    process.exit(0);
  }
};

// Run if called directly
if (require.main === module)
  main().catch((error) => {
    log(`\n💥 Test runner failed: ${error.message}`, 'red');
    process.exit(1);
  });

module.exports = { main, runTestSuite, generateReport };
