.ReactTable
  border: 0
  box-shadow: none
  overflow-x: hidden

.ReactTable .rt-tbody
  overflow: visible

.ReactTable .rt-table
  overflow-x: auto !important

.ReactTable .-pagination
  border: 0
  box-shadow: none

.ReactTable .-pagination .-btn
  font: $label-typo
  &:disabled
    cursor: not-allowed

.ReactTable .rt-thead.-header
  box-shadow: none

.ReactTable .rt-thead .rt-th, .ReactTable .rt-thead .rt-td
  border: 0
  padding-right: 10px
  padding-left: 5px
  box-shadow: none

.ReactTable .rt-thead.-filters
  box-shadow: none

.ReactTable .rt-thead.-filters .rt-th
  border: 0
  box-shadow: none
  padding-right: 10px
  padding-left: 5px
  & input, select
    width: 100%
    border-radius: 6px

.ReactTable .rt-thead .rt-resizable-header-content
  font: $label-typo
  text-align: left

.ReactTable .rt-tbody .rt-td
  padding-right: 10px
  padding-left: 5px
  border: 0
  box-shadow: none
  font: $default-typo

.ReactTable .-pagination input, .ReactTable .-pagination select
  width: 100%

.rt-table
  .rt-thead, .rt-tbody
    min-width: -moz-min-content !important
    min-width: -webkit-min-content !important
    min-width: min-content !important

.ReactTable .rt-th, .ReactTable .rt-td
  -webkit-box-flex: 1
  -ms-flex: 1 0 0px
  flex: 1 0 0
  white-space: normal

.rt-noData
  text-transform: uppercase
  font-style: oblique
  font-weight: bold

.ReactTable input[type="checkbox"]:not(input[role="switch"])
  width: 1.25em
  height: 1.25em
  &:focus
    box-shadow: none
