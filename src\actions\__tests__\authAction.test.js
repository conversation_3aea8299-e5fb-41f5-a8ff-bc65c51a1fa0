import responses from 'mocks/responses';

import * as actions from 'actions/authActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userDetails = {
  effectiveRole: 'checker',
  userName: 'test',
  password: '123',
  channel: 'frm'
};

describe('auth actions', () => {
  it('authenticates user', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_LOGIN, response: responses.auth.login },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ auth: {} });

    return store.dispatch(actions.onLogin(userDetails)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should logout user', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_LOGOUT }
    ];
    const store = mockStore({ auth: {} });

    return store
      .dispatch(actions.onLogout(userDetails.userName, userDetails.effectiveRole))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should toggle logout modal', () => {
    const expectedActions = { type: types.ON_TOGGLE_LOGOUT_MODAL, newState: true };
    expect(actions.onToggleLogoutModal(true)).toEqual(expectedActions);
  });

  it('should toggle session idle', () => {
    const expectedActions = { type: types.ON_TOGGLE_SESSION_IDLE, newState: true };
    expect(actions.onToggleSessionIdle(true)).toEqual(expectedActions);
  });

  it('should reset session timeout', () => {
    const expectedActions = { type: types.ON_RESET_SESSION_TIMEOUT };
    expect(actions.onResetSessionTimeout()).toEqual(expectedActions);
  });

  it('should Fetch All Roles user', () => {
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_SUCCESSFUL_FETCH_ALL_ROLES, response: responses.auth.allRoles },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ auth: {} });

    return store.dispatch(actions.onFetchAllRoles()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Update Password', () => {
    const formData = { userName: '123', newPassword: 'asdad' };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: {
          message: 'Password updated successfully'
        }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ auth: {} });

    return store.dispatch(actions.onUpdatePassword(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
