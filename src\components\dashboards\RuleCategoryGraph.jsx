import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { graphic } from 'echarts/core';

import GraphContainer from 'components/common/GraphContainer';
import HelpIcon from 'components/common/HelpIcon';

function RuleCategoryGraph({
  theme,
  period,
  xchannelId,
  ruleCategoryTrend,
  fetchRuleCategoryTrend
}) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      fetchRuleCategoryTrend({
        startDate: period.startDate,
        endDate: period.endDate,
        xchannelId
      });
  }, [period.startDate, xchannelId]);

  const rulesData = ruleCategoryTrend.data?.map((d) => [d.rules, d.category]);
  const alertsData = ruleCategoryTrend.data?.map((d) => [d.alerts, d.category]);
  const actualsData = ruleCategoryTrend.data?.map((d) => [d.frauds, d.category]);

  const config = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '10',
      right: '5%',
      bottom: '10',
      top: '20',
      containLabel: true
    },
    xAxis: [
      {
        type: 'value',
        boundaryGap: [0, 0.01],
        name: 'Alerts'
      },
      {
        type: 'value',
        boundaryGap: [0, 0.01],
        name: 'Rules'
      }
    ],
    yAxis: {
      type: 'category'
    },
    series: [
      {
        name: 'Rules',
        type: 'bar',
        data: rulesData,
        xAxisIndex: 1,
        itemStyle: {
          borderRadius: [0, 30, 30, 0],
          color: new graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 1, color: '#273a72' },
            { offset: 0, color: '#2ca8de' }
          ])
        }
      },
      {
        name: 'Alerts',
        type: 'bar',
        data: alertsData,
        itemStyle: {
          borderRadius: [0, 30, 30, 0],
          color: new graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 1, color: '#c45154' },
            { offset: 0, color: '#d78072' }
          ])
        }
      },
      {
        name: 'Frauds',
        type: 'bar',
        data: actualsData,
        itemStyle: {
          borderRadius: [0, 30, 30, 0],
          color: new graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 1, color: '#14c8d4' },
            { offset: 0, color: '#43eec6' }
          ])
        }
      }
    ]
  };

  const subtitle = (
    <HelpIcon
      size="lg"
      key="behaviourHelp"
      id="behaviourHelp"
      text={
        <small>
          <b>Rules</b> - No of rules in the category.
          <br />
          <b>Alerts</b> - No of alerts for the category.
          <br />
          <b>Frauds</b> - No of fraud cases in the category.
        </small>
      }
    />
  );

  return (
    <GraphContainer
      className={'card-height-700'}
      theme={theme}
      config={config}
      title="Rule Category Trend"
      subtitle={subtitle}
      noData={false}
      loader={ruleCategoryTrend.loader}
      error={{ flag: ruleCategoryTrend.error, errorMessage: ruleCategoryTrend.errorMessage }}
    />
  );
}

RuleCategoryGraph.propTypes = {
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  xchannelId: PropTypes.string.isRequired,
  ruleCategoryTrend: PropTypes.object.isRequired,
  fetchRuleCategoryTrend: PropTypes.func.isRequired
};

export default RuleCategoryGraph;
