import responses from 'mocks/responses';

import * as actions from 'actions/settingsActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const mockedStore = {
  settings: {}
};

describe('settings actions', () => {
  it('should Submit Settings', () => {
    const formData = {
      keyList: [
        { key: 'ReportingEntityName', value: 'ICICI' },
        { key: 'ReportingEntityCategory', value: 'BAPUB' },
        { key: 'RERegistrationNumber', value: '242432423432' },
        { key: 'FIUREID', value: 'XXXXXNNNNN' },
        { key: 'POName', value: 'Akash' },
        { key: 'PODesignation', value: 'QA' },
        { key: 'Address', value: 'kharadi nagar, pune district' },
        { key: 'City', value: 'Pune' },
        { key: 'StateCode', value: 'MH' },
        { key: 'PinCode', value: '410206' },
        { key: 'CountryCode', value: 'IN' },
        { key: 'Telephone', value: '0212-343453455345435345' },
        { key: 'Mobile', value: '+919999999909' },
        { key: 'Fax', value: '022-29939439' },
        { key: 'POEmail', value: '<EMAIL>' }
      ]
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_SETTINGS_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Settings updated successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSubmitSettings(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Settings', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SETTINGS_LOADING },
      {
        type: types.ON_FETCH_SETTINGS_SUCCESS,
        response: responses.settings
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSettings()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
