import React from 'react';
import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';

import ModalContainer from 'components/common/ModalContainer';
import { Label, FormGroup, Input, Button } from 'reactstrap';

class PrefilterForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      categoryId: '',
      identifier: '',
      isActive: 0
    };
    this.setFormData = this._setFormData.bind(this);
    this.handleInputChange = this._handleInputChange.bind(this);
    this.toggleIsActiveCheckbox = this._toggleIsActiveCheckbox.bind(this);
  }

  componentDidMount() {
    if (isEmpty(this.props.category.list)) this.props.actions.onFetchCategory();
    this.setFormData(this.props.formData);
  }
  componentDidUpdate(prevProps) {
    if (this.props.formData != prevProps.formData) this.setFormData(this.props.formData);
  }

  _setFormData = (formData) =>
    this.setState({
      categoryId: formData.categoryId || '',
      identifier: formData.identifier || '',
      isActive: formData.isActive || 0
    });

  _handleInputChange = (e) => this.setState({ [e.target.name]: e.target.value });

  _toggleIsActiveCheckbox = () =>
    this.setState((state) => ({ isActive: state.isActive == 1 ? 0 : 1 }));

  _submitForm = (e) => {
    e.preventDefault();
    let { categoryId, identifier, isActive } = this.state;
    categoryId = parseInt(categoryId);
    const { channelId, type, formData, actions, channel } = this.props;
    const { id } = formData;
    const method = type == 'Add' ? 'POST' : 'PUT';
    const data =
      type == 'Add'
        ? { channelId, categoryId, identifier, isActive }
        : { id, identifier, isActive };
    actions.onSetFilter(data, method, channel);
  };

  render() {
    const { categoryId, identifier, isActive } = this.state;
    const { theme, display, toggle, category, channel, type } = this.props;
    let categoryList = isEmpty(category.list[channel]) ? [] : category.list[channel];
    let categoryOptions = categoryList.map((category) => (
      <option key={category.categoryId} value={category.categoryId}>
        {category.categoryName}
      </option>
    ));
    let selectedCategory = categoryList.find((category) => category.categoryId == categoryId);
    let selectedCategoryName = isEmpty(selectedCategory) ? '' : selectedCategory.categoryName;

    return (
      <ModalContainer
        size="sm"
        theme={theme}
        isOpen={display[channel]}
        toggle={() => toggle(channel)}
        header={type + ' prefilter condition'}>
        <form onSubmit={this._submitForm}>
          <FormGroup>
            <Label for="categoryId">Category</Label>
            {type == 'Edit' ? (
              <Label id="categoryId">
                <br />
                {selectedCategoryName}
              </Label>
            ) : (
              <Input
                type="select"
                id="categoryId"
                name="categoryId"
                value={categoryId}
                onChange={this.handleInputChange}
                required>
                <option value="">-- select --</option>
                {categoryOptions}
              </Input>
            )}
          </FormGroup>
          <FormGroup>
            <Label for="identifier">Identifier</Label>
            <Input
              type="text"
              id="identifier"
              name="identifier"
              value={identifier}
              onChange={this.handleInputChange}
              required
            />
          </FormGroup>
          <FormGroup className="ms-4">
            <Label for="isActive">
              <Input
                type="checkbox"
                id="isActive"
                name="isActive"
                value={isActive}
                checked={isActive == 1}
                onChange={this.toggleIsActiveCheckbox}
              />{' '}
              Activate
            </Label>
          </FormGroup>
          <FormGroup className="d-flex">
            <Button size="sm" color="success" className="ms-auto">
              Save
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    );
  }
}

PrefilterForm.propTypes = {
  formData: PropTypes.object,
  type: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  channelId: PropTypes.number.isRequired,
  display: PropTypes.object.isRequired,
  category: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  toggle: PropTypes.func.isRequired
};

export default PrefilterForm;
