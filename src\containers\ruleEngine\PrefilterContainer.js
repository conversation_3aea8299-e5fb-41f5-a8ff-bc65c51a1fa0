import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefilterActions from 'actions/prefilterActions';
import { onTogglePrefilterModal } from 'actions/toggleActions';
import { onFetchChannels } from 'actions/userManagementActions';
import Prefilter from 'components/ruleEngine/Prefilter';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  channels: state.user.channels,
  prefilter: state.prefilter
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(prefilterActions, dispatch),
  toggleForm: bindActionCreators(onTogglePrefilterModal, dispatch),
  fetchChannels: bindActionCreators(onFetchChannels, dispatch)
});

const PrefilterContainer = connect(mapStateToProps, mapDispatchToProps)(Prefilter);

export default PrefilterContainer;
