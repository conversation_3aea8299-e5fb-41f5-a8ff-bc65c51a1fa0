import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchSTRReportLogs, onSubmitBatchId } from 'actions/strReportActions';
import STRReportLog from 'components/investigation/STRReportLog';

const mapStateToProps = (state) => {
  return {
    strReportLogs: state.strReport.history,
    userRole: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    submitBatchId: bindActionCreators(onSubmitBatchId, dispatch),
    fetchSTRReportLogs: bindActionCreators(onFetchSTRReportLogs, dispatch)
  };
};

const STRReportLogContainer = connect(mapStateToProps, mapDispatchToProps)(STRReportLog);

export default STRReportLogContainer;
