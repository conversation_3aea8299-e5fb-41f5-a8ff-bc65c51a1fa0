import { faHashtag, faRupee } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { BarChart, LineChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  DatasetComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import ReactEcharts from 'echarts-for-react/lib/core';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { Button, ButtonGroup } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import darkTheme from 'constants/chartDarkTheme';
import lightTheme from 'constants/chartLightTheme';

echarts.registerTheme('chart-theme-light', lightTheme);
echarts.registerTheme('chart-theme-dark', darkTheme);

const StatisticsCard = ({ data, theme, title }) => {
  const [chartType, setChartType] = useState('bar');
  const [yKey, setYKey] = useState('amount');
  const [chartDataType, setChartDataType] = useState('');
  const [chartData, setChartData] = useState([]);

  const chartTheme = {
    light: 'chart-theme-light',
    dark: 'chart-theme-dark'
  };

  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DataZoomComponent,
    DatasetComponent,
    LegendComponent,
    BarChart,
    LineChart,
    CanvasRenderer,
    MarkPointComponent
  ]);

  useEffect(() => {
    !_.isEmpty(data.details) && setChartDataType(data.details[0].key);
  }, [data]);

  useEffect(() => {
    const currentChartData = _.filter(data.details, (chart) => chart.key === chartDataType);
    !_.isEmpty(currentChartData) && setChartData(currentChartData[0].values);
  }, [chartDataType, data]);

  const axisData = chartData.map((item) => `${item.name}--${item.code}`);

  const seriesData = chartData.map((item) => item[yKey]);

  const capitalize = (string) => string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();

  const config = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter(params) {
        return `${params[0].axisValue}<br />${params[0].seriesName}: ${params[0].value}`;
      }
    },
    xAxis: {
      scale: true,
      type: chartType === 'hist' ? 'value' : 'category',
      data: chartType === 'hist' ? null : axisData,
      name: chartType === 'hist' ? '' : capitalize(chartDataType),
      nameLocation: 'middle',
      nameGap: 40,
      nameTextStyle: {
        fontSize: 12,
        color: `${theme === 'dark' ? '#9c9d9f' : '#373737'}`
      },
      axisLabel: {
        show: chartType !== 'hist',
        interval: 0,
        formatter(val) {
          const axisVal = _.split(val, '--');
          return axisVal[1];
        }
      },
      axisLine: {
        show: chartType !== 'hist'
      }
    },
    yAxis: {
      scale: true,
      type: chartType === 'hist' ? 'category' : 'value',
      data: chartType === 'hist' ? axisData : null,
      name: chartType === 'hist' ? '' : capitalize(yKey),
      nameLocation: 'middle',
      nameGap: 40,
      nameTextStyle: {
        fontSize: 12,
        color: `${theme === 'dark' ? '#9c9d9f' : '#373737'}`
      },
      axisLabel: {
        show: chartType === 'hist',
        interval: 0,
        formatter(val) {
          const axisVal = _.split(val, '--');
          return axisVal[1];
        }
      },
      axisLine: {
        show: chartType === 'hist'
      }
    },
    series: [
      {
        name: yKey,
        type: 'bar',
        data: seriesData,
        label:
          chartType === 'hist'
            ? {
                show: true,
                position: 'right'
              }
            : null
      }
    ]
  };

  const action = (
    <div className="statistics-button">
      <ButtonGroup>
        <Button
          outline
          size="sm"
          color="secondary"
          onClick={() => setChartType('hist')}
          active={chartType === 'hist'}>
          hist
        </Button>
        <Button
          outline
          size="sm"
          color="secondary"
          onClick={() => setChartType('bar')}
          active={chartType === 'bar'}>
          bar
        </Button>
      </ButtonGroup>

      <ButtonGroup>
        <Button
          outline
          size="sm"
          title="count"
          color="secondary"
          onClick={() => setYKey('count')}
          active={yKey === 'count'}>
          <FontAwesomeIcon icon={faHashtag} />
        </Button>
        <Button
          outline
          size="sm"
          title="amount"
          color="secondary"
          onClick={() => setYKey('amount')}
          active={yKey === 'amount'}>
          <FontAwesomeIcon icon={faRupee} />
        </Button>
      </ButtonGroup>

      <ButtonGroup>
        {data.details.map((item, i) => (
          <Button
            outline
            size="sm"
            color="secondary"
            key={item.key + i}
            onClick={() => setChartDataType(item.key)}
            active={chartDataType === item.key}>
            {item.key}
          </Button>
        ))}
      </ButtonGroup>
    </div>
  );

  const renderContent = () => {
    if (data.loader)
      return (
        <div className="graph-loader">
          <span />
        </div>
      );

    if (data.error) return <div className="no-data-div">{data.errorMessage}</div>;

    if (_.isEmpty(data.details)) return <div className="no-data-div">No data found</div>;

    return (
      <ReactEcharts
        echarts={echarts}
        option={config}
        notMerge={true}
        lazyUpdate={true}
        theme={chartTheme[theme]}
        onEvents={{}}
      />
    );
  };

  return (
    <CardContainer title={title} action={action}>
      {renderContent()}
    </CardContainer>
  );
};

StatisticsCard.propTypes = {
  data: PropTypes.object.isRequired,
  theme: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired
};

export default StatisticsCard;
