// eslint-disable-next-line import/no-unresolved
import { defineConfig } from 'eslint/config';
import { fixupConfigRules, fixupPluginRules } from '@eslint/compat';
import react from 'eslint-plugin-react';
import _import from 'eslint-plugin-import';
import prettier from 'eslint-plugin-prettier';
import globals from 'globals';
import babelParser from '@babel/eslint-parser';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all
});

export default defineConfig([
  {
    extends: fixupConfigRules(
      compat.extends(
        'eslint:recommended',
        'plugin:import/recommended',
        'plugin:import/errors',
        'plugin:import/warnings',
        'plugin:react/recommended',
        'plugin:jsx-a11y/recommended',
        'plugin:prettier/recommended'
      )
    ),

    plugins: {
      react: fixupPluginRules(react),
      import: fixupPluginRules(_import),
      prettier: fixupPluginRules(prettier)
    },

    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.jest
      },

      parser: babelParser,
      ecmaVersion: 2021,
      sourceType: 'module',

      parserOptions: {
        requireConfigFile: false,
        sourceType: 'module',
        babelOptions: {
          presets: ['@babel/preset-env', '@babel/preset-react']
        }
      }
    },

    settings: {
      react: {
        version: 'detect'
      },

      'import/resolver': {
        node: {
          paths: ['src'],
          extensions: ['.js', '.jsx']
        }
      }
    },

    rules: {
      'no-console': 'warn',
      'no-debugger': 'warn',

      'no-unused-vars': [
        'warn',
        {
          argsIgnorePattern: '^_'
        }
      ],

      'no-var': 'warn',

      'import/extensions': 'warn',

      'import/no-unresolved': [
        'error',
        {
          ignore: ['^mocks/.+']
        }
      ],

      'react/display-name': 'off',
      'react/prop-types': 'warn',
      'react/jsx-uses-react': 'warn',
      'react/jsx-uses-vars': 'warn',
      'react/no-unknown-property': 'warn',
      'react/self-closing-comp': 'warn',
      'react/no-danger': 'warn',
      'react/no-direct-mutation-state': 'error',
      'react/prefer-es6-class': 'warn',
      'react/react-in-jsx-scope': 'warn',

      'jsx-a11y/anchor-is-valid': [
        'warn',
        {
          components: ['Link'],
          specialLink: ['to']
        }
      ],

      'prettier/prettier': [
        'warn',
        {},
        {
          usePrettierrc: true
        }
      ]
    }
  }
]);
