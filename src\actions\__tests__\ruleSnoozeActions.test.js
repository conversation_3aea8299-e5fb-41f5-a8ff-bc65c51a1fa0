import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/ruleSnoozeActions';
import responses from 'mocks/responses';

const mockedStore = {
  snoozeRules: {}
};

describe('rule snooze actions', () => {
  it('should Fetch Snooze Attributes List', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SNOOZE_ATTRIBUTES_LOADING },
      {
        type: types.ON_FETCH_SNOOZE_ATTRIBUTES_SUCCESS,
        response: responses.snoozeRules.attributes
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSnoozeAttributesList('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Snooze Rule', () => {
    let formData = {
      code: '15dd9b65-549d-44e1-b922-aa065d98766e',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      reason: 'Rule to be snoozed!',
      merchantId: '123',
      snoozeUntil: '2019-10-11 15:08:31',
      snoozeConditions: []
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Rule snooze requested successfully!' }
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSnoozeRule(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch Snooze Rules List', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SNOOZE_RULE_LIST_LOADING },
      {
        type: types.ON_FETCH_SNOOZE_RULE_LIST_SUCCESS,
        channel: 'frm',
        response: responses.snoozeRules.list
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSnoozeRulesList('frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Snooze Rule Response', () => {
    let formData = {
      code: '15dd9b65-549d-44e1-b922-aa065d98766e',
      name: 'BenfordLawCheck',
      isApproved: 1,
      snoozeUntil: '2019-10-11 15:08:31',
      attributeList: 'mer~=~1234,id~<=~1234',
      merchantId: '123'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Rule snooze approved successfully!` }
      },
      {
        type: types.ON_FETCH_SNOOZE_RULE_LIST_LOADING
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSnoozeRuleResponse(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Unsnooze Rule', () => {
    let formData = {
      code: '15dd9b65-549d-44e1-b922-aa065d98766e',
      name: 'BenfordLawCheck',
      attributeList: 'mer~=~1234,id~<=~1234',
      merchantId: '123'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Rule unsnoozed successfully!` }
      },
      {
        type: types.ON_FETCH_SNOOZE_RULE_LIST_LOADING
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onUnsnoozeRule(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
