import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as userActions from 'actions/userManagementActions';
import UserManagementPage from 'components/userManagement/UserManagementPage';

const mapStateToProps = (state) => {
  return {
    roleslist: state.user.roles,
    channelslist: state.user.channels,
    userRoles: state.auth.userCreds.roles
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    userActions: bindActionCreators(userActions, dispatch)
  };
};

const UserManagementPageContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(UserManagementPage);

export default UserManagementPageContainer;
