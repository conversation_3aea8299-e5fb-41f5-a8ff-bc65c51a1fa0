import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import caseDocumentReducer from 'reducers/caseDocumentReducer';
import initialState from 'reducers/initialState';

describe('case Document Reducer', () => {
  it('should return the intial state', () => {
    expect(caseDocumentReducer(undefined, {})).toEqual(initialState.caseDocument);
  });

  it('should handle ON_FETCH_CASE_DOCUMENTS_LOADING', () => {
    expect(
      caseDocumentReducer(
        {
          data: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_DOCUMENTS_LOADING
        }
      )
    ).toEqual({
      data: [],
      loader: true,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_CASE_DOCUMENTS_SUCCESS', () => {
    expect(
      caseDocumentReducer(
        {
          data: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_DOCUMENTS_SUCCESS,
          response: responses.caseDocument
        }
      )
    ).toEqual({
      data: responses.caseDocument.files,
      loader: false,
      error: false,
      errorMessage: ''
    });
  });

  it('should handle ON_FETCH_CASE_DOCUMENTS_FAILURE', () => {
    expect(
      caseDocumentReducer(
        {
          data: [],
          loader: false,
          error: false,
          errorMessage: ''
        },
        {
          type: types.ON_FETCH_CASE_DOCUMENTS_FAILURE,
          response: { message: 'Insufficient rights to access data' }
        }
      )
    ).toEqual({
      data: [],
      loader: false,
      error: true,
      errorMessage: 'Insufficient rights to access data'
    });
  });
});
