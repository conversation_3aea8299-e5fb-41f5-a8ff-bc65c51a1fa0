/* eslint-disable react/no-multi-comp */
'use strict';
import _ from 'lodash';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUndo } from '@fortawesome/free-solid-svg-icons';

import RuleTableContainer from 'containers/ruleEngine/RuleTableContainer';
import ConfirmAlert from 'components/common/ConfirmAlert';

const ArchievedRuleTable = ({
  channel,
  ruleList,
  role,
  theme,
  ruleConfiguratorActions,
  toggleEditModal
}) => {
  const [tableFilters, setTableFilters] = useState([]);
  const [toggleRestore, setToggleRestore] = useState(false);
  const [restoreRule, setRestoreRule] = useState({});

  const confirmRestore = (isOpen, rule) => {
    if (!_.isEmpty(rule) && !isOpen) {
      setRestoreRule(rule);
      setToggleRestore(true);
    } else {
      setRestoreRule({});
      setToggleRestore(false);
    }
  };

  const restoreSelectedRule = (rule, channel) => {
    ruleConfiguratorActions.onRestoreRule(rule, channel);
    setToggleRestore(false);
  };

  const actionHeaders = _.includes(['checker'], role)
    ? [
        {
          Header: 'Actions',
          filterable: false,
          sortable: false,
          minWidth: 80,
          Cell: (row) => (
            <Button
              outline
              size="sm"
              color="primary"
              className="ms-1"
              title="Restore rule"
              onClick={() => toggleEditModal(row.original, 'archieved')}>
              <FontAwesomeIcon icon={faUndo} />
            </Button>
          )
        }
      ]
    : [];

  return (
    <>
      <RuleTableContainer
        channel={channel}
        data={ruleList}
        tableFilters={tableFilters}
        actionHeaders={actionHeaders}
        setTableFilters={setTableFilters}
        defaultSort={[
          {
            id: 'order',
            desc: false
          }
        ]}
        defaultSortMethod={(a, b) => {
          a = a === null || a === undefined ? '' : a;
          b = b === null || b === undefined ? '' : b;
          a = typeof a === 'string' ? a.toLowerCase() : a;
          b = typeof b === 'string' ? b.toLowerCase() : b;
          if (a === '' && b !== '') return 1;
          if (b === '' && a !== '') return -1;
          if (a > b) return 1;
          if (a < b) return -1;
          return 0;
        }}
      />

      <ConfirmAlert
        theme={theme}
        confirmAlertModal={toggleRestore}
        toggleConfirmAlertModal={() => confirmRestore(toggleRestore)}
        confirmationAction={() => restoreSelectedRule(restoreRule, channel)}
        confirmAlertTitle={`Are you sure you want to restore rule - ${restoreRule?.name || ''} ?`}
      />
    </>
  );
};

ArchievedRuleTable.propTypes = {
  channel: PropTypes.string.isRequired,
  ruleList: PropTypes.object.isRequired,
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  ruleConfiguratorActions: PropTypes.object.isRequired,
  toggleEditModal: PropTypes.func.isRequired
};

export default ArchievedRuleTable;
