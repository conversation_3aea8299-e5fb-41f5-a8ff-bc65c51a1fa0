import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchTransactionDetails } from 'actions/transactionDetailsActions';
import { onFetchUDSEntityDetails } from 'actions/udsActions';
import { onFetchViolatedRules } from 'actions/violatedRulesActions';
import TransactionDetails from 'components/caseReview/TransactionDetails';

const mapStateToProps = (state) => ({
  agentInfo: state.uds.agent,
  customerInfo: state.uds.customer,
  merchantInfo: state.uds.merchant,
  violationDetails: state.violatedRules,
  userName: state.auth.userCreds.userName,
  transactionDetails: state.transactionDetails,
  oneViewData: state.oneView,
  callDetails: state.customerCommunication.call
});

const mapDispatchToProps = (dispatch) => ({
  fetchViolationDetails: bindActionCreators(onFetchViolatedRules, dispatch),
  fetchEntityDetails: bindActionCreators(onFetchUDSEntityDetails, dispatch),
  fetchTransactionDetails: bindActionCreators(onFetchTransactionDetails, dispatch)
});

const TransactionDetailsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(TransactionDetails);

export default TransactionDetailsContainer;
