/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/no-onchange */
/* eslint-disable react/display-name */
/* eslint-disable react/no-multi-comp */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import _, { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import { useParams } from 'react-router-dom';
import ProfilingTable from './ProfilingTable';
import SearchResult from './SearchResult';
import SearchArea from './SearchArea';
import SuggestionsList from './SuggestionsList';
import { useDateRange } from 'context/DateRangeContext';
import { useDebounce, useKeyPress } from 'utility/customHooks';

const EntityProfilingHomePage = ({
  role,
  theme,
  moduleType,
  profiling,
  profilingActions,
  transactionHistorySearchActions,
  statisticsDetails,
  fetchTransactionStatisticsDetails,
  fetchCustomerStatisticsDetails,
  channels
}) => {
  const { list, selected, loader, error, errorMessage } = profiling.profilingList;
  const {
    onFetchEntityList,
    onSelectEntity,
    onFetchCustomerWiseTransactionSummary,
    onFetchProfilingSearchConditions
  } = profilingActions;

  const { startDate, endDate } = useDateRange('entityProfilling');
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);
  const [displayResult, setDisplayResult] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('id');
  const [selectedCategoryDisplay, setSelectedCategoryDisplay] = useState('ID');
  const [searchText, setSearchText] = useState('');
  const [ruleLabel, setRuleLabel] = useState(null);
  const [pageNo, setPageNo] = useState(1);
  const [pageRecords, setPageRecords] = useState(5);
  const [cursor, setCursor] = useState(-1);
  const downPress = useKeyPress('ArrowDown');
  const upPress = useKeyPress('ArrowUp');
  const enterPress = useKeyPress('Enter');
  const debouncedSearchText = useDebounce(searchText, 500);
  const listEl = useRef([]);

  const [isOpenConditionsDropdown, setIsOpenConditionsDropdown] = useState(false);
  const [selectedCondition, setSelectedCondition] = useState('=');
  const [selectedConditionDisplay, setSelectedConditionDisplay] = useState('Exact');

  const { type, value } = useParams();
  const entityType = moduleType === 'issuer' ? 'Customer' : 'Merchant';
  const caseStatus = ['Open', 'New'];
  const primaryChannel = channels[0];

  useEffect(() => {
    if (value !== undefined && type !== undefined) {
      selectedCategoryUpdate(type, 'ID');
      setSearchText(value);
      selectEntity(list[0]);
    }
  }, [type, value, list]);

  useEffect(() => {
    document.title = !_.isEmpty(selected)
      ? `BANKiQ FRC | Profiling - ${selected.id}`
      : 'BANKiQ FRC | Profiling';

    if (!_.isEmpty(selected)) {
      transactionHistorySearchActions.onClearSearch();
      fetchTransactionStatisticsDetails(selected, channels[0]);
      fetchCustomerStatisticsDetails(selected, channels[0]);
    }

    return () => {
      document.title = 'BANKiQ FRC';
      if (!_.isEmpty(selected)) {
        onSelectEntity({});
      }
    };
  }, [
    selected,
    channels,
    fetchTransactionStatisticsDetails,
    fetchCustomerStatisticsDetails,
    transactionHistorySearchActions
  ]);

  useEffect(() => {
    if (_.isEmpty(selected) && debouncedSearchText?.length > 3) {
      onFetchEntityList({ [selectedCategory]: debouncedSearchText, condition: selectedCondition });
      setDisplayResult(true);
      setRuleLabel('');
    } else {
      setDisplayResult(false);
    }
  }, [debouncedSearchText, selectedCategory, onFetchEntityList, selectedCondition]);

  useEffect(() => {
    fetchProfilingData('byPagination');
  }, [pageNo, pageRecords]);

  useEffect(() => {
    isEmpty(profiling.searchConditions.list) && onFetchProfilingSearchConditions();
  }, []);

  useEffect(() => {
    setPageNo(1);
    fetchProfilingData('byDate');
  }, [startDate, endDate, selected]);

  useEffect(() => {
    if (displayResult && downPress)
      setCursor((prevState) => (prevState < list.length - 1 ? prevState + 1 : 0));
  }, [downPress]);

  useEffect(() => {
    if (displayResult && upPress)
      setCursor((prevState) => (prevState > 0 ? prevState - 1 : list.length - 1));
  }, [upPress]);

  useEffect(() => {
    if (displayResult && enterPress) selectEntity(list[cursor]);
  }, [enterPress]);

  useEffect(() => {
    if (displayResult && !_.isEmpty(listEl.current[cursor])) {
      listEl.current[cursor].focus();
      listEl.current[cursor].scrollIntoView();
    }
  }, [cursor]);

  useEffect(() => {
    if (displayResult && !_.isEmpty(listEl.current[cursor])) {
      listEl.current[cursor].focus();
      listEl.current[cursor].scrollIntoView();
    }
  }, [cursor, displayResult]);

  const selectedCategoryUpdate = useCallback((id, name) => {
    setSelectedCategory(id);
    setSelectedCategoryDisplay(name);
    setCursor(-1);
    setSearchText('');
    setDisplayResult(false);
    setSelectedCondition('=');
    setSelectedConditionDisplay('Exact');
  }, []);

  const selectedConditionUpdate = useCallback((id, name) => {
    setSelectedCondition(id);
    setSelectedConditionDisplay(name);
    setCursor(-1);
    setSearchText('');
    setDisplayResult(false);
  }, []);

  const fetchSearchSuggestion = useCallback((e) => {
    e.preventDefault();
    setDisplayResult(true);
  }, []);

  const selectEntity = useCallback(
    (entity) => {
      if (!isEmpty(entity)) {
        onSelectEntity(entity);
        setDisplayResult(false);
        setCursor(-1);
      }
    },
    [onSelectEntity, selectedCategory, selectedCondition]
  );

  const fetchProfilingData = (calledBy) => {
    const data = {
      pageNo: calledBy === 'byPagination' ? pageNo : 1,
      pageRecords,
      startDate,
      endDate
    };
    _.isEmpty(selected) &&
      !displayResult &&
      primaryChannel === 'frm' &&
      startDate &&
      endDate &&
      onFetchCustomerWiseTransactionSummary(data, channels[0], calledBy);
  };

  const onClickBackBtn = useCallback(() => {
    setDisplayResult(false);
    onSelectEntity({});
    setSearchText('');
    setRuleLabel('');
    setSelectedCategoryDisplay('ID');
  }, [onSelectEntity]);

  const onClickSearchProfile = useCallback(
    (entity) => {
      const entityData = {
        id: entity.entityId,
        name: entity.entityName,
        entityCategory: entityType,
        mobileNumber: undefined,
        accountNumber: undefined,
        pageNo: pageNo,
        pageRecords: pageRecords
      };
      selectEntity(entityData);
      setSearchText(entity?.entityName);
      setSelectedCategoryDisplay('Name');
    },
    [entityType]
  );

  const onClickRuleLabel = useCallback(
    (key, entity) => {
      setRuleLabel(key);
      onClickSearchProfile(entity);
    },
    [ruleLabel]
  );

  const onPageSizeChange = useCallback((pageRecords, page) => {
    setPageRecords(pageRecords);
    setPageNo(page);
  }, []);

  const onSetPageNo = useCallback((page) => {
    setPageNo(page);
  }, []);

  const suggestions = useMemo(
    () => (
      <SuggestionsList
        loader={loader}
        error={error}
        errorMessage={errorMessage}
        list={list}
        cursor={cursor}
        listEl={listEl}
        selectEntity={selectEntity}
      />
    ),
    [loader, error, errorMessage, list, cursor, selectEntity]
  );

  return (
    <div className="content-wrapper" onClick={() => setDisplayResult(false)}>
      <SearchArea
        fetchSearchSuggestion={fetchSearchSuggestion}
        isOpenDropdown={isOpenDropdown}
        setIsOpenDropdown={setIsOpenDropdown}
        selectedCategoryDisplay={selectedCategoryDisplay}
        selectedCategoryUpdate={selectedCategoryUpdate}
        searchText={searchText.toString()}
        setSearchText={setSearchText}
        entityType={entityType}
        displayResult={displayResult}
        suggestions={suggestions}
        isSelected={!_.isEmpty(selected)}
        onClickBackBtn={onClickBackBtn}
        selectedCategory={selectedCategory}
        searchConditions={profiling.searchConditions.list}
        isOpenConditionsDropdown={isOpenConditionsDropdown}
        setIsOpenConditionsDropdown={setIsOpenConditionsDropdown}
        selectedConditionDisplay={selectedConditionDisplay}
        selectedConditionUpdate={selectedConditionUpdate}
      />
      {_.isEmpty(selected) && !displayResult && primaryChannel === 'frm' && (
        <ProfilingTable
          customerTransactionSummary={profiling.customerTransactionSummary}
          header={`${entityType} Wise Case Volume`}
          onClickSearchProfile={onClickSearchProfile}
          onClickRuleLabel={onClickRuleLabel}
          onPageSizeChange={onPageSizeChange}
          onSetPageNo={onSetPageNo}
          pageNo={pageNo}
          pageRecords={pageRecords}
          contextKey="entityProfilling"
        />
      )}
      {!_.isEmpty(selected) && (
        <SearchResult
          selected={selected}
          statisticsDetails={statisticsDetails}
          theme={theme}
          role={role}
          ruleLabel={ruleLabel}
          caseStatus={caseStatus}
          primaryChannel={primaryChannel}
          contextKey="entityProfilling"
        />
      )}
    </div>
  );
};

EntityProfilingHomePage.propTypes = {
  role: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  moduleType: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired,
  profiling: PropTypes.object.isRequired,
  profilingActions: PropTypes.object.isRequired,
  statisticsDetails: PropTypes.object.isRequired,
  transactionHistorySearchActions: PropTypes.object.isRequired,
  fetchTransactionStatisticsDetails: PropTypes.func.isRequired,
  fetchCustomerStatisticsDetails: PropTypes.func.isRequired
};

export default EntityProfilingHomePage;
