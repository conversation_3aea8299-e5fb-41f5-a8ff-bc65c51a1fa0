import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { graphic } from 'echarts/core';
import Moment from 'moment';

import GraphContainer from 'components/common/GraphContainer';
import HelpIcon from 'components/common/HelpIcon';

function RuleEffectivenessGraph({
  theme,
  rule,
  period,
  ruleEffectiveness,
  fetchRuleEffectiveness
}) {
  useEffect(() => {
    period.startDate &&
      period.endDate &&
      rule &&
      fetchRuleEffectiveness({
        ruleId: rule,
        startDate: period.startDate,
        endDate: period.endDate
      });
  }, [period.startDate, rule]);

  const datediff = Moment.duration(Moment(period.endDate).diff(Moment(period.startDate))).days();

  const alertData = ruleEffectiveness?.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    ((+d.alert / +d.totalTxn) * 100).toFixed(2)
  ]);
  const manualData = ruleEffectiveness?.data?.map((d) => [
    Moment(d.time).format(datediff > 1 ? 'DD/MM' : 'HH:mm'),
    ((+d.manualAlert / +d.alert) * 100).toFixed(2)
  ]);

  const config = {
    grid: {
      left: 10,
      containLabel: true,
      bottom: 20,
      top: 40,
      right: 40
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['Alert Rate', 'Report Rate']
    },
    xAxis: [
      {
        type: 'category',
        axisPointer: { type: 'shadow' }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: 'Rate',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0]
      }
    ],
    series: [
      {
        name: 'Alert Rate',
        type: 'bar',
        barMaxWidth: '30px',
        barMinWidth: 10,
        itemStyle: {
          borderRadius: [30, 30, 0, 0],
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: '#273a72' },
            { offset: 0, color: '#2ca8de' }
          ])
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        data: alertData
      },
      {
        name: 'Report Rate',
        type: 'bar',
        barMaxWidth: '30px',
        barMinWidth: 10,
        itemStyle: {
          borderRadius: [30, 30, 0, 0],
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: '#14c8d4' },
            { offset: 0, color: '#43eec6' }
          ])
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        data: manualData
      }
    ]
  };

  const subtitle = (
    <HelpIcon
      size="lg"
      key="effectivenessHelp"
      id="effectivenessHelp"
      text={
        <small>
          <b>Alert Rate</b> - Rate of alerts for the rule vs total alerts for the duration.
          <br />
          <b>Report Rate</b> - Rate of manual or reported cases vs total alerts for the rule.
        </small>
      }
    />
  );

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title="Rule Effectiveness"
      subtitle={subtitle}
      loader={ruleEffectiveness.loader}
      error={{ flag: ruleEffectiveness.error, errorMessage: ruleEffectiveness.errorMessage }}
      noData={ruleEffectiveness.data?.length === 0}
    />
  );
}

RuleEffectivenessGraph.propTypes = {
  rule: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired,
  period: PropTypes.object.isRequired,
  ruleEffectiveness: PropTypes.object.isRequired,
  fetchRuleEffectiveness: PropTypes.func.isRequired
};

export default RuleEffectivenessGraph;
