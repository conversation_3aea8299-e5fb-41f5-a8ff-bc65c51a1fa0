import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import GraphContainer from 'components/common/GraphContainer';

function FirstContactResolutionGraph({ theme, formData, firstContactRate, fetchFirstContactRate }) {
  useEffect(() => {
    if (formData.startDate && formData.endDate) fetchFirstContactRate(formData);
  }, [formData]);

  const config = {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        startAngle: 200,
        endAngle: -20,
        min: 0,
        max: 100,
        splitNumber: 5,
        progress: {
          show: true,
          width: 30
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 30
          }
        },
        axisTick: {
          show: false,
          distance: -45,
          splitNumber: 5,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        splitLine: {
          show: false,
          distance: -52,
          length: 14,
          lineStyle: {
            width: 3,
            color: '#999'
          }
        },
        axisLabel: {
          distance: 10,
          color: '#999'
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          fontWeight: 'bolder',
          formatter: '{value}%',
          color: 'inherit'
        },
        data: [
          {
            value: firstContactRate.data ?? 0
          }
        ]
      }
    ]
  };

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title="First Contact Resolution rate"
      noData={false}
      loader={firstContactRate.loader}
      error={{ flag: firstContactRate.error, errorMessage: firstContactRate.errorMessage }}
    />
  );
}

FirstContactResolutionGraph.propTypes = {
  theme: PropTypes.string.isRequired,
  formData: PropTypes.object.isRequired,
  firstContactRate: PropTypes.object.isRequired,
  fetchFirstContactRate: PropTypes.func.isRequired
};

export default FirstContactResolutionGraph;
