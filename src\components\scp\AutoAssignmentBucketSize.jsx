import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import React, { useState, useCallback } from 'react';
import { FormGroup, Label, Input, Col } from 'reactstrap';

import HelpIcon from 'components/common/HelpIcon';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';

function AutoAssignmentBucketSize({
  highlightText,
  saveConfigurations,
  configurationsData,
  toggleRow,
  openRows
}) {
  const [autoAssignmentBucketSize, setAutoAssignmentBucketSize] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  const isDisabled = autoAssignmentBucketSize?.activation?.value === 'disabled';

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        autoAssignmentBucketSize,
        setAutoAssignmentBucketSize,
        saveConfigurations
      ),
    [configurationsData, autoAssignmentBucketSize, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, autoAssignmentBucketSize, setAutoAssignmentBucketSize),
    [autoAssignmentBucketSize]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setAutoAssignmentBucketSize),
    [configurationsData]
  );

  return (
    <ConfigFormWrapper
      configTitle="Auto Assignment Bucket Size"
      activationId="activationAutoAssignmentBucketSize"
      data={autoAssignmentBucketSize}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      highlightText={highlightText}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="size" className="searchable" id="test">
          {highlightText('size:')}
          {!isEmpty(autoAssignmentBucketSize?.size?.desc) && (
            <HelpIcon size="lg" id="size" text={autoAssignmentBucketSize?.size?.desc} />
          )}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="number"
            name="size"
            id="size"
            onChange={(event) =>
              inputChangeDataHandler(event, autoAssignmentBucketSize, setAutoAssignmentBucketSize)
            }
            max={50}
            min={1}
            value={autoAssignmentBucketSize?.size?.value}
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AutoAssignmentBucketSize.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired
};

export default AutoAssignmentBucketSize;
