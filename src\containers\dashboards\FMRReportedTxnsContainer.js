import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchCloseCaseBuckets } from 'actions/caseReviewActions';
import { onFetchFMRReportedCases } from 'actions/complianceDashboardActions';
import FMRReportedTxns from 'components/dashboards/FMRReportedTxns';

const mapStateToProps = (state) => {
  return {
    userRole: state.auth.userCreds.roles,
    fmrReportedCases: state.complianceDashboard.fmrReportedCases,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFmrReportedCases: bindActionCreators(onFetchFMRReportedCases, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch)
  };
};

const FMRReportedTxnsContainer = connect(mapStateToProps, mapDispatchToProps)(FMRReportedTxns);

export default FMRReportedTxnsContainer;
