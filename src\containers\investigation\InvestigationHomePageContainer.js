import { connect } from 'react-redux';
import InvestigationHomePage from 'components/investigation/InvestigationHomePage';

const mapStateToProps = (state) => {
  return {
    userRoles: state.auth.userCreds.roles,
    channels: state.auth.userCreds.channels
  };
};

const InvestigationHomePageContainer = connect(mapStateToProps, null)(InvestigationHomePage);

export default InvestigationHomePageContainer;
