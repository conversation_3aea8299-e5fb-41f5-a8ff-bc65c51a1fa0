import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import * as userActions from 'actions/userManagementActions';
import ActiveList from 'components/prefilters/specializedLists/ActiveList';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  prefiltersList: state.prefiltersList,
  partnerIdList: state.user.partnerIdList
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  actions: bindActionCreators(prefiltersListAction, dispatch),
  userActions: bindActionCreators(userActions, dispatch)
});

const ActiveListContainer = connect(mapStateToProps, mapDispatchToProps)(ActiveList);

export default ActiveListContainer;
