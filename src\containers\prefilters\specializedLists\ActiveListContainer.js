import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ActiveList from 'components/prefilters/specializedLists/ActiveList';
import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import * as userActions from 'actions/userManagementActions';

const mapStateToProps = (state) => {
  return {
    toggle: state.toggle,
    prefiltersList: state.prefiltersList,
    partnerIdList: state.user.partnerIdList
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggleActions: bindActionCreators(toggleActions, dispatch),
    actions: bindActionCreators(prefiltersListAction, dispatch),
    userActions: bindActionCreators(userActions, dispatch)
  };
};

const ActiveListContainer = connect(mapStateToProps, mapDispatchToProps)(ActiveList);

export default ActiveListContainer;
