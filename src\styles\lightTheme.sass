@use 'colors' as *;

.light
  color: $text-high-emphasis-light

  ::-webkit-scrollbar-track
    background: $scroll-track-light

  ::-webkit-scrollbar-thumb
    background: $scroll-thumb-light

  .required-star
    color: $color-error-light

  /* ------------------------------------- */
  // Color and Text
  /* ------------------------------------- */
  .color-primary
    color: $color-primary-light

  .color-success
    color: $color-success-light

  .color-info
    color: $color-info-light

  .color-warning
    color: $color-warning-light

  .color-danger
    color: $color-error-light

  .text-success
    color: $color-success-light !important

  .text-danger
    color: $color-error-light !important

  .text-disabled
    color: $text-disabled-light !important

  .text-medium-emphasis
    color: $text-medium-emphasis-light !important

  /* ------------------------------------- */
  // Badge
  /* ------------------------------------- */
  .badge
    color: $surface-light !important
    &-success
      background-color: $color-success-light
    &-primary
      background-color: $color-primary-light
      color: surface-variant-light !important
    &-warning
      background-color: $color-warning-light
    &-danger
      background-color: $color-error-light
    &-secondary
      background-color: $color-secondary-light
    &-info
      background-color: $color-info-light

  /* ------------------------------------- */
  // Buttons
  /* ------------------------------------- */
  .-btn
    color: $text-high-emphasis-light
    &:focus
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-primary
    background-color: $color-primary-light !important
    color: $surface-light
    border-color: $color-primary-light !important
    &:hover
      background-color: inherit !important
      color: $color-primary-light !important
      border-color: $color-primary-light !important

  .btn-outline-primary
    color: $color-primary-light !important
    border-color: $color-primary-light !important
    &:hover
      background-color: $color-primary-light !important
      color: $surface-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-primary-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-primary-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-success
    background-color: $color-success-light !important
    color: $surface-light
    border-color: $color-success-light !important
    &:hover
      background-color: inherit !important
      color: $color-success-light !important

  .btn-outline-success
    color: $color-success-light !important
    border-color: $color-success-light !important
    &:hover
      background-color: $color-success-light !important
      color: $surface-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-success-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-success-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-info
    background-color: $color-info-light !important
    color: $surface-light !important
    border-color: $color-info-light !important
    &:hover
      background-color: inherit !important
      color: $color-info-light !important
      border-color: $color-info-light !important

  .btn-outline-info
    color: $surface-light
    border-color: $color-info-light !important
    &:hover
      background-color: $color-info-light !important
      color: $surface-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-info-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-info-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-warning
    background-color: $color-warning-light !important
    color: $surface-light !important
    border-color: $color-warning-light !important
    &:hover
      background-color: inherit !important
      color: $color-warning-light !important
      border-color: $color-warning-light !important

  .btn-outline-warning
    color: $color-warning-light !important
    border-color: $color-warning-light !important
    &:hover
      background-color: $color-warning-light !important
      color: $surface-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-warning-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-warning-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-danger
    background-color: $color-error-light !important
    color: $surface-light
    border-color: $color-error-light !important
    &:hover
      background: $surface-light !important
      color: $color-error-light !important
      background-color: $color-error-light !important

  .btn-outline-danger
    color: $color-error-light !important
    border-color: $color-error-light !important
    &:hover
      background-color: $color-error-light !important
      color: $surface-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-error-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-error-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  .btn-secondary
    background-color: $color-secondary-light !important
    color: $surface-light
    border-color: $color-secondary-light !important
    &:hover
      background-color: inherit !important
      color: $color-secondary-light
      border-color: $color-secondary-light !important

  .btn-outline-secondary
    color: $color-secondary-light !important
    border-color: $color-secondary-light !important
    &:hover
      color: $surface-light !important
      background-color: $color-secondary-light !important
    &:active, &.active
      color: $surface-light !important
      background-color: $color-secondary-light !important
    &:focus
      color: $surface-light !important
      background-color: $color-secondary-light !important
      box-shadow: 0 0 0 0.2rem $focus-ring-light

  // Disabled states
  .btn:disabled, .btn.disabled
    color: $disabled-light !important
    background-color: $surface-light !important
    border-color: $disabled-light !important

  .btn-close
    color: $color-error-light !important

  .rule-upload-btn
    color: $color-primary-light !important
    border-color: $color-primary-light !important
    background-color: $background-light !important
    &:hover
      color: $background-light !important
      border-color: $background-light !important
      background-color: $color-primary-light !important

  .download-csv-btn
    a
      color: $color-primary-light !important
      background-color: $surface-light !important
      &:hover
        color: $background-light !important
        background-color: $color-primary-light !important

  .btn.useful-active-no-hover-focus
    &:hover, &:focus
      color: surface-variant-light !important
      border-color: surface-variant-light !important
      background-color: $color-success-light !important

  .btn.useful-inactive-no-hover-focus
    color: $color-secondary-light !important
    border-color: $color-secondary-light !important
    background-color: surface-variant-light !important
    &:hover, &:focus
      color: $color-secondary-light !important
      border-color: $color-secondary-light !important
      background-color: surface-variant-light !important

  .btn.not-useful-active-no-hover-focus
    &:hover, &:focus
      color: surface-variant-light !important
      border-color: surface-variant-light !important
      background-color: $color-error-light !important

  .btn.not-useful-inactive-no-hover-focus
    color: $color-secondary-light !important
    border-color: $color-secondary-light !important
    background-color: surface-variant-light !important
    &:hover, &:focus
      color: $color-secondary-light !important
      border-color: $color-secondary-light !important
      background-color: surface-variant-light !important

  .feedback-buttons:nth-child(odd)
    border-color: $divider-light

  .notation-action button
    border-color: $hover-light !important

  .border-left::before
    background-color: $text-high-emphasis-light !important

  /* ------------------------------------- */
  // Input
  /* ------------------------------------- */
  input:not([type="checkbox"]), select, textarea, .input-group-text, .table,
  .sidebar-primary-title, .sidebar-secondary-title,
  .primary-menu-list li, .secondary-menu-list li,
  .content-placeholder, .modal-body .table,
  .graph-subtitle, .no-comment-text
    color: $text-high-emphasis-light !important
    &:focus
      box-shadow: 0 0 0 0.2rem $focus-ring-dark
      border-color: $color-primary-light

  select option
    background: $background-light !important
    border-color: $border-light !important
    &:hover
      background-color: $hover-light
    &.custom
      background: $color-primary-light !important
      color: $text-high-emphasis-light

  input:not([type="checkbox"]):not([type="radio"]), select, textarea, .input-group-text
    background-color: $surface-light !important
    border-color: $shadow-light !important
    &::placeholder
      color: $text-medium-emphasis-light

  .form-check-input:checked ~ .custom-control-label::before
    color: $text-high-emphasis-light
    border-color: $color-primary-light
    background-color: $color-primary-light

  // Enhanced form states
  .form-control:disabled, .form-control[readonly]
    background-color: $surface-variant-light !important
    color: $disabled-light !important
    border-color: $disabled-light !important

  /* ------------------------------------- */
  // Toast notifications
  /* ------------------------------------- */
  .toast, .toast-header, .toast-body
    background-color: $surface-light
    color: $text-high-emphasis-light
    border: 1px solid $border-light
    box-shadow: 0 4px 12px $shadow-light

  .toast-header
    border-bottom-color: $divider-light

  /* ------------------------------------- */
  // Modal
  /* ------------------------------------- */
  .modal-header, .modal-body
    background-color: $surface-light
    border-color: $border-light

  .modal-header
    border-bottom-color: $divider-light

  .close, .close:hover
    color: $color-error-light

  /* ------------------------------------- */
  // Card
  /* ------------------------------------- */
  .card
    background-color: $surface-light !important
    border: 1px solid $shadow-light
    color: $text-high-emphasis-light

    .card-header
      background-color: $surface-light !important

  .card-main, .citation-card
    background-color: $surface-light
    -webkit-box-shadow: 8px 8px 12px -4px $shadow-light
    -moz-box-shadow: 8px 8px 12px -4px $shadow-light
    box-shadow: 8px 8px 12px -4px $shadow-light

  .kpi-background
    background: $surface-light
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  .kpi-overlap
    color: $text-high-emphasis-light

  /* ------------------------------------- */
  // Dropdown
  /* ------------------------------------- */
  .dropdown-toggle
    background-color: $surface-light
  .dropdown-menu
    background-color: $surface-light
    border: 1px solid $border-light
    -webkit-box-shadow: 6px 6px 6px 2px $shadow-light
    -moz-box-shadow: 6px 6px 6px 2px $shadow-light
    box-shadow: 6px 6px 6px 2px $shadow-light
    .dropdown-header
      color: $text-medium-emphasis-light
    .dropdown-item
      color: $text-high-emphasis-light !important
      &:hover, &:focus
        background-color: $hover-light
      &.active
        background-color: $color-primary-light
        color: $surface-light !important

  .dropdown-card-title, .dropdown-card-title:hover
    background-color: $surface-light !important
    border: none
    color: $text-high-emphasis-light !important

  /* ------------------------------------- */
  // React-table
  /* ------------------------------------- */
  .ReactTable
    .-pagination .-btn
      color: $text-medium-emphasis-light
      background: $background-light
      &:hover
        background: $hover-light
        color: $text-high-emphasis-light

    .rt-thead
      background: $background-light
      .rt-th
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-light !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-light !important
      .rt-td
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $focus-ring-light !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $focus-ring-light !important
      &.-filters
        border-bottom: 1px solid $border-light

    .-headerGroups
      .rt-th:not(:last-of-type)
        border-right: 2px solid $border-light !important

    .rt-tbody .rt-tr-group
      border-bottom: 1px solid $border-light

    &.-striped .rt-tbody .rt-tr.-odd
      background-color: $surface-light
    &.-striped .rt-tbody .rt-tr.-even
      background: $surface-variant-light

    &.-highlight
      .rt-tbody .rt-tr:not(.-padRow):hover
        background: $hover-light

  /* ------------------------------------- */
  // MultiSelect
  /* ------------------------------------- */
  .rmsc
    --rmsc-main: #{$color-primary-light}
    --rmsc-hover: #{$hover-light}
    --rmsc-selected: #{$background-light}
    --rmsc-border: #{$border-light}
    --rmsc-gray: #{$text-medium-emphasis-light}
    --rmsc-bg: #{$surface-light}

  /* ------------------------------------- */
  // React Date-Time
  /* ------------------------------------- */
  .rdt
    .rdtPicker
      border-color: $border-light
      background: $surface-light
      thead th
        border-bottom-color: $border-light
      td:hover, .rdtPrev:hover, .rdtNext:hover, .rdtSwitch:hover
        background: $background-light
      .rdtBtn:hover
        background: $surface-light
      tfoot
        border-top-color: $border-light

  .rdtPicker td.rdtActive,
  .rdtPicker td.rdtActive:hover
    background-color: $color-primary-light

  /* ------------------------------------- */
  //Loaders
  /* ------------------------------------- */
  .loader
    color: $color-primary-light

  .customer-graph-placeholder, .detail-placeholder, .comment-text-placeholder, .card-subtitle-placeholder, .violation-badge-placeholder, .violation-text-placeholder, .graph-loader span
    background: linear-gradient(to right, $surface-variant-light 20%, $text-disabled-light 50%, $surface-variant-light 80%)

  .table-loading
    tr
      border-bottom: 1px solid $divider-light
      td
        &.td-2 span, &.td-5 span
          background-color: $surface-variant-light
        &.td-3 span, &.td-4 span
          background: linear-gradient(to right, $surface-variant-light 20%, $text-disabled-light 50%, $surface-variant-light 80%)

  /* ------------------------------------- */
  // Navbar
  /* ------------------------------------- */
  .nav-tab
    .active-tab
      border-bottom: 3px solid $color-primary-light
      color: $color-primary-light !important

  .nav-pills
    .active-tab
      background: $color-primary-light
      color: $surface-variant-light !important

  .navbar-toggler
    .navbar-toggler-icon, .navbar-toggler-icon::before, .navbar-toggler-icon::after
      background: $text-high-emphasis-light

  .tab-header .nav-link
    color: $text-high-emphasis-light

  .tab-header.vertical-tab
    border-right: 1px solid $hover-light

  /* ------------------------------------- */
  // Notations
  /* ------------------------------------- */
  .comment
    background: $background-light
    border-color: $border-light

  .comment-separator
    background: $divider-light

  .comment-title-placeholder, .comment-timestamp-placeholder
    background-color: $border-light

  /* ------------------------------------- */
  // Header
  /* ------------------------------------- */
  .header-container
    background-color: $surface-light
    -webkit-box-shadow: 0px 5px 2px -3px $shadow-light
    -moz-box-shadow: 0px 5px 2px -3px $shadow-light
    box-shadow: 0px 5px 2px -3px $shadow-light

    a, .header-logo
      color: $text-high-emphasis-light

    ul.navbar-nav .active
      border-bottom: 5px solid $color-primary-light
      color: $color-primary-light

  /* ------------------------------------- */
  // Sidebar
  /* ------------------------------------- */
  .sidebar-container
    background-color: $sidebar-background-light
    .sidebar-primary, .sidebar-secondary
      background-color: $surface-light
      -webkit-box-shadow: 5px 2px 2px -3px $shadow-light
      -moz-box-shadow: 5px 2px 2px -3px $shadow-light
      box-shadow: 5px 2px 2px -3px $shadow-light

    .primary-menu-list li.active, .secondary-menu-list li.active
      border-left: 5px solid $color-success-light
      background-color: $background-light

    .sidebar-pagination
      background: $background-dark
      color: $text-high-emphasis-dark
      border-color: $border-dark
      &:hover
        background: $hover-dark
        color: $text-high-emphasis-dark
        border-color: $border-dark

    .card, .case-box
      background: $background-light
    a
      color: $color-primary-light
      &:hover
        color: $color-info-light

  /* ------------------------------------- */
  // Page Content
  /* ------------------------------------- */
  .content-wrapper
    background-color: $background-light

  /* ------------------------------------- */
  // Login
  /* ------------------------------------- */
  .loginWrap
    background-color: $background-light

  .login-form-flex
    background-color: $surface-light
    border-color: $border-light !important
    color: $text-high-emphasis-light !important
    .form-control
      background-color: $background-light !important
      border: 1px solid $border-light !important
      color: $text-high-emphasis-light !important

  .login-form-container
    background-color: $surface-light
    border: 1px solid $background-light
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  /* ------------------------------------- */
  // Rule Engine
  /* ------------------------------------- */
  .dsl-list-group
    .list-group-item
      background-color: $background-light
      border: 1px solid $hover-light
      &:hover
        background-color: $surface-light
      small
        color: $color-primary-light

  /* ------------------------------------- */
  // Logs
  /* ------------------------------------- */
  .log-list-item
    border-color: $background-light
    &.active
      background: $background-light
      border-color: $hover-light

  /* ------------------------------------- */
  // Citations
  /* ------------------------------------- */
  .citation-comments > .list-group-item
    background-color: $surface-light !important
    border: 0
    border-bottom: 1px solid $shadow-light
    &:last-child
      border-bottom: 0

  /* ------------------------------------- */
  // Prefilters
  /* ------------------------------------- */
  .prefilter-list, .add-list
    border: 1px solid $hover-light !important
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  .add-to-list
    .btn:not(.btn-sm):not(.btn-xs)
      color: $hover-light !important
      &.with-list
        color: $color-info-light !important
    button.active-item
      background-color: $hover-light

  /* ------------------------------------- */
  // Notifications
  /* ------------------------------------- */
  .notification-counter > .badge
    border-color: $surface-light

  .notifications
    .notification-header-list
      .list-group-item
        background-color: $background-light

    .list-group-flush
      .list-group-item
        background-color: inherit
        &.active
          background-color: $background-light
          color: $text-high-emphasis-light
          border-color: $hover-light
        &:hover
          background-color: $hover-light
          color: $text-high-emphasis-light


  /* ------------------------------------- */
  // SCP
  /* ------------------------------------- */
  .highlight-scp-search
    background-color: $color-primary-light
    color: $surface-light

  .scp-page
    .danger-implied-verdict
      color: $color-error-light

    .secondary-implied-verdict
      color: $color-success-light

    .form-check-input:checked
      background-color: $color-primary-light
      border-color: $color-primary-light

    .form-range
      &:focus
        box-shadow: $color-primary-light 0 0 0 0 !important

    .setting-expand-button
      &.btn-secondary
        svg
          color: $text-high-emphasis-light


  .entity-search-result
    .list-group
      .list-group-item
        color: $text-high-emphasis-light !important
        &:hover
          background: $surface-light !important
      .active
        background: $surface-light !important

  .editableDiv:empty:before
    color: $shadow-light

  .call-container
    background-color: $hover-light

  // Form stepper - light theme
  .steps-row::before
    background-color: $border-light
