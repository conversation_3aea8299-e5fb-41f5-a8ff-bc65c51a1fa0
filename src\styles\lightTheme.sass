@import 'colors'

.light
  color: $font-primary-light

  ::-webkit-scrollbar-track
    background: $scroll-track-light

  ::-webkit-scrollbar-thumb
    background: $scroll-thumb-light

  .required-star
    color: $btn-error-light

  // Header
  .header-container
    background-color: $foreground-light
    -webkit-box-shadow: 0px 5px 2px -3px $shadow-light
    -moz-box-shadow: 0px 5px 2px -3px $shadow-light
    box-shadow: 0px 5px 2px -3px $shadow-light

    a, .header-logo
      color: $font-primary-light

    ul.navbar-nav .active
      border-bottom: 5px solid $btn-primary-light
      color: $btn-primary-light

  // Page Content
  .content-wrapper
    background-color: $background-light

  // Color and Text
  .color-danger
    color: $btn-error-light

  .color-warning
    color: $btn-warning-light

  .color-primary
    color: $btn-primary-light

  .color-info
    color: $btn-info-light

  .color-success
    color: $btn-success-light

  .text-danger
    color: $btn-error-light !important

  .text-success
    color: $btn-success-light !important

  // Buttons
  .-btn
    color: $font-primary-light

  .btn-outline-warning
    color: $btn-warning-light !important
    border-color: $btn-warning-light !important
    &:hover
      background-color: $btn-warning-light !important
      color: $foreground-light !important

  .btn-warning
    background-color: $btn-warning-light !important
    color: $foreground-light !important
    border-color: $btn-warning-light !important
    &:hover
      background-color: inherit !important
      color: $btn-warning-light !important

  .btn-outline-primary
    color: $btn-primary-light !important
    border-color: $btn-primary-light !important
    &:hover
      background-color: $btn-primary-light !important
      color: $foreground-light !important
    &:active
      color: $foreground-light !important
      background-color: $btn-primary-light !important
    &:focus
      color: $foreground-light !important
      background-color: $btn-primary-light !important

  .btn-primary
    background-color: $btn-primary-light !important
    border-color: $btn-primary-light !important
    &:hover
      background-color: inherit !important
      color: $btn-primary-light !important

  .btn-outline-secondary
    border-color: $btn-secondary-light !important
    &:hover
      color: $foreground-light !important
      background-color: $btn-secondary-light !important
    &:active
      color: $foreground-light !important
      background-color: $btn-secondary-light !important
    &:focus
      color: $foreground-light !important
      background-color: $btn-secondary-light !important

  .btn-secondary
    background-color: $btn-secondary-light !important
    border-color: $btn-secondary-light !important
    &:hover
      background-color: inherit !important
      color: $font-primary-light !important

  .btn-outline-danger
    border-color: $btn-error-light !important
    &:hover
      background-color: $btn-error-light !important
      color: $foreground-light !important
    &:active
      color: $foreground-light !important
      background-color: $btn-error-light !important
    &:focus
      color: $foreground-light !important
      background-color: $btn-error-light !important

  .btn-danger
    background-color: $btn-error-light !important
    border-color: $btn-error-light !important
    &:hover
      background: $foreground-light !important
      color: $btn-error-light !important

  .btn-outline-success
    border-color: $btn-success-light !important
    &:hover
      background-color: $btn-success-light !important
      color: $foreground-light !important
    &:active
      color: $foreground-light !important
      background-color: $btn-success-light !important
    &:focus
      color: $foreground-light !important
      background-color: $btn-success-light !important

  .btn-success
    background-color: $btn-success-light !important
    border-color: $btn-success-light !important
    &:hover
      background-color: inherit !important
      color: $btn-success-light !important

  .btn-outline-info
    border-color: $btn-info-light !important
    &:hover
      background-color: $btn-info-light !important
      color: $foreground-light !important
    &:active
      color: $foreground-light !important
      background-color: $btn-info-light !important
    &:focus
      color: $foreground-light !important
      background-color: $btn-info-light !important

  .btn-info
    background-color: $btn-info-light !important
    color: $foreground-light !important
    border-color: $btn-info-light !important
    &:hover
      background-color: inherit !important
      color: $btn-info-light !important

  .btn-primary:not(:disabled):not(.disabled).active
    background-color: inherit !important
    border-color: $btn-info-light !important
    color: $btn-info-light !important

  .btn-outline-primary:not(:disabled):not(.disabled).active
    background-color: $btn-info-light !important
    border-color: $btn-info-light !important
    color: $background-light !important

  .statistics-button .btn
    background-color: $shadow-light !important
    color: $font-primary-dark !important
    border-color: $shadow-light !important
    &:hover
      background-color: $shadow-light !important
      color: $font-primary-dark !important
      border-color: $shadow-light !important
    &.active
      background-color: $background-light !important
      color: $font-primary-light !important
      border-color: $shadow-light !important

  .notation-action button
    border-color: $hover-light !important

  // Input
  input:not([type="checkbox"]), select, textarea, .input-group-text, .table,
  .sidebar-primary-title, .sidebar-secondary-title,
  .primary-menu-list li, .secondary-menu-list li,
  .content-placeholder, .modal-body .table,
  .graph-subtitle, .no-comment-text
    color: $font-primary-light !important
    &:focus
      box-shadow: $btn-primary-light 0 0 0 2px
      border-color: $btn-primary-light

  select option
    background: $background-light !important
    border-color: $background-light !important
    &:hover
      background-color: $background-light
    &.custom
      background: $btn-primary-light !important
      color: $foreground-light

  input:not([type="checkbox"]):not([type="radio"]), select, textarea, .input-group-text
    background-color: $foreground-light !important
    border-color: $hover-light !important

  .form-check-input:checked ~ .custom-control-label::before
    color: $font-primary-light
    border-color: $btn-primary-light
    background-color: $btn-primary-light

  // React-table
  .ReactTable
    .-pagination .-btn
        color: $font-primary-light
        background: $background-light
        &:hover
          background: $hover-light

    .rt-thead
      background: $background-light
      .rt-th
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-light !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-light !important
      .rt-td
        &.-sort-asc
          box-shadow: inset 0 3px 0 0 $shadow-light !important
        &.-sort-desc
          box-shadow: inset 0 -3px 0 0 $shadow-light !important
      &.-filters
        border-bottom: 1px solid $hover-light

    .-headerGroups
      .rt-th:not(:last-of-type)
        border-right: 2px solid $hover-light !important

    .rt-tbody .rt-tr-group
      border-bottom: 1px solid $hover-light

    &.-striped .rt-tbody .rt-tr.-odd
      background-color: $foreground-light
    &.-striped .rt-tbody .rt-tr.-even
      background: $background-light

    &.-highlight
      .rt-tbody .rt-tr:not(.-padRow):hover
        background: $hover-light

  // Sidebar
  .sidebar-container
    background-color: $sidebar-background-light
    .sidebar-primary, .sidebar-secondary
      background-color: $foreground-light
      -webkit-box-shadow: 5px 2px 2px -3px $shadow-light
      -moz-box-shadow: 5px 2px 2px -3px $shadow-light
      box-shadow: 5px 2px 2px -3px $shadow-light

    .primary-menu-list li.active, .secondary-menu-list li.active
      border-left: 5px solid $btn-success-light
      background-color: $background-light
    .card, .case-box
      background: $background-light

    a
      color: $btn-primary-light

  // Dropdown
  .dropdown-menu
    background-color: $background-light
    -webkit-box-shadow: 6px 6px 6px 2px $shadow-light
    -moz-box-shadow: 6px 6px 6px 2px $shadow-light
    box-shadow: 6px 6px 6px 2px $shadow-light

    .dropdown-item
      color: $font-primary-light !important
      &:hover, &:focus
        background-color: $hover-light
      &.active
        background-color: $btn-primary-light
        color: $font-primary-dark !important

  .dropdown-card-title, .dropdown-card-title:hover
    background-color: $foreground-light !important
    border: none
    color: $font-primary-light !important

  // Badge
  .badge
    color: $foreground-light !important
    &-success
      background-color: $btn-success-light
    &-primary
      background-color: $btn-primary-light
      color: $font-primary-dark !important
    &-warning
      background-color: $btn-warning-light
    &-danger
      background-color: $btn-error-light

  .highlight-scp-search
    background-color: $btn-primary-light
    color: $foreground-light

  //Modal
  .modal-header, .modal-body
    background-color: $foreground-light

  .modal-header
    border-bottom-color: $hover-light

  .close, .close:hover
    color: $btn-error-light

  // List
  .dsl-list-group
    .list-group-item
      background-color: $background-light
      border: 1px solid $hover-light
      &:hover
        background-color: $foreground-light
      small
        color: $btn-primary-light

  .comment
    background: $background-light
    border-color: $hover-light

  .comment-separator
    background: $font-primary-light

  .log-list-item
    border-color: $background-light
    &.active
      background: $background-light
      border-color: $hover-light

  .citation-comments > .list-group-item
    background-color: $foreground-light !important
    border: 0
    border-bottom: 1px solid $shadow-light
    &:last-child
      border-bottom: 0

  .entity-search-result
    .list-group
      .list-group-item
        color: $font-primary-light !important
        &:hover
          background: $foreground-light !important
      .active
        background: $foreground-light !important

  .prefilter-list, .add-list
    border: 1px solid $hover-light !important
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  .add-to-list
    .btn:not(.btn-sm):not(.btn-xs)
      color: $hover-light !important
      &.with-list
        color: $btn-info-light !important
    button.active-item
      background-color: $hover-light

  // React Date-Time
  .rdt
    .rdtPicker
      border-color: $hover-light
      background: $foreground-light
      thead th
        border-bottom-color: $hover-light
      td:hover, .rdtPrev:hover, .rdtNext:hover, .rdtSwitch:hover
        background: $background-light
      .rdtBtn:hover
        background: $foreground-light
      tfoot
        border-top-color: $hover-light

  .rdtPicker td.rdtActive,
  .rdtPicker td.rdtActive:hover
    background-color: $btn-primary-light

  // Card
  .card
    background-color: $foreground-light
    border: 1px solid $hover-light

  .card-main, .citation-card
    -webkit-box-shadow: 8px 8px 12px -4px $shadow-light
    -moz-box-shadow: 8px 8px 12px -4px $shadow-light
    box-shadow: 8px 8px 12px -4px $shadow-light

  .kpi-background
    background: $foreground-light
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  .kpi-overlap
    color: $font-primary-dark

  // Toast
  .toast, .toast-header, .toast-body
    background-color: $background-light

  .toast-header
    border-color: $hover-light

  // Navbar
  .nav-tab
    .active-tab
      border-bottom: 3px solid $btn-primary-light
      color: $btn-primary-light !important

  .nav-pills
    .active-tab
      background: $btn-primary-light
      color: $font-primary-dark !important

  .navbar-toggler
    .navbar-toggler-icon, .navbar-toggler-icon::before, .navbar-toggler-icon::after
      background: $font-primary-light

  .tab-header .nav-link
    color: $font-primary-light

  .tab-header.vertical-tab
    border-right: 1px solid $hover-light

  // MultiSelect
  .rmsc
    --rmsc-main: #{$btn-primary-light}
    --rmsc-hover: #{$hover-light}
    --rmsc-selected: #{$background-light}
    --rmsc-border: #{$hover-light}
    --rmsc-gray: #{$font-primary-light}
    --rmsc-bg: #{$foreground-light}

  // Login
  .loginWrap
    background-color: $background-light

  .login-form-flex
    background-color: $foreground-light
    color: $font-primary-light !important
    .form-control
      background-color: $background-light !important
      border: 1px solid $hover-light !important
      color: $font-primary-light !important

  .login-form-container
    background-color: $foreground-light
    border: 1px solid $background-light
    -webkit-box-shadow: 5px 5px 2px -3px $shadow-light
    -moz-box-shadow: 5px 5px 2px -3px $shadow-light
    box-shadow: 5px 5px 2px -3px $shadow-light

  .editableDiv:empty:before
    color: $shadow-light

  .call-container
    background-color: $hover-light

  .rule-upload-btn
    color: $btn-primary-light !important
    border-color: $btn-primary-light !important
    background-color: $background-light !important
    &:hover
      color: $background-light !important
      border-color: $background-light !important
      background-color: $btn-primary-light !important

  .download-csv-btn
    a
      color: $btn-primary-light !important
      background-color: $background-light !important
      &:hover
        color: $background-light !important
        background-color: $btn-primary-light !important

  .btn.useful-active-no-hover-focus
    &:hover, &:focus
      color: $font-primary-dark !important
      border-color: $font-primary-dark !important
      background-color: $btn-success-light !important

  .btn.useful-inactive-no-hover-focus
    color: $btn-secondary-light !important
    border-color: $btn-secondary-light !important
    background-color: $font-primary-dark !important
    &:hover, &:focus
      color: $btn-secondary-light !important
      border-color: $btn-secondary-light !important
      background-color: $font-primary-dark !important

  .btn.not-useful-active-no-hover-focus
    &:hover, &:focus
      color: $font-primary-dark !important
      border-color: $font-primary-dark !important
      background-color: $btn-error-light !important

  .btn.not-useful-inactive-no-hover-focus
    color: $btn-secondary-light !important
    border-color: $btn-secondary-light !important
    background-color: $font-primary-dark !important
    &:hover, &:focus
      color: $btn-secondary-light !important
      border-color: $btn-secondary-light !important
      background-color: $font-primary-dark !important

  .feedback-buttons
    &:nth-child(odd)
      border-color: $shadow-light

  .notification-counter > .badge
    border-color: $foreground-light

  .notifications
    .notification-header-list
      .list-group-item
          background-color: $background-light

    .list-group-flush
      .list-group-item
        background-color: inherit
        &.active
          background-color: $background-light
          color: $font-primary-light
          border-color: $hover-light
        &:hover
          background-color: $hover-light
          color: $font-primary-light

  .scp-page
    .danger-implied-verdict
      color: $btn-error-light

    .secondary-implied-verdict
      color: $btn-success-light

    .form-check-input:checked
      background-color: $btn-primary-light
      border-color: $btn-primary-light

    .form-range
      &:focus
        box-shadow: $btn-primary-light 0 0 0 0 !important

    .setting-expand-button
      &.btn-secondary
        svg
          color: $font-primary-light
