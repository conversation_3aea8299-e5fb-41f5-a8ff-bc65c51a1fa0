import * as types from 'constants/actionTypes';
import * as actions from 'actions/toggleActions';

describe('toggle actions', () => {
  it('should toggle theme', () => {
    expect(actions.onToggleTheme()).toEqual({ type: types.ON_TOGGLE_THEME });
  });

  it('should toggle loader', () => {
    expect(actions.onToggleLoader(true)).toEqual({ type: types.ON_TOGGLE_LOADER, state: true });
  });

  it('should toggle add user modal', () => {
    expect(actions.onToggleAddUserModal()).toEqual({ type: types.ON_TOGGLE_ADD_USER_MODAL });
  });

  it('should toggle update user roles modal', () => {
    expect(actions.onToggleUpdateUserRolesModal()).toEqual({
      type: types.ON_TOGGLE_UPDATE_USER_ROLES_MODAL
    });
  });

  it('should toggle shift modal', () => {
    expect(actions.onToggleShiftModal()).toEqual({ type: types.ON_TOGGLE_SHIFT_MODAL });
  });

  it('should toggle assign shift modal', () => {
    expect(actions.onToggleAssignShiftModal()).toEqual({
      type: types.ON_TOGGLE_ASSIGN_SHIFT_MODAL
    });
  });

  it('should toggle verdict modal', () => {
    expect(actions.onToggleVerdictModal('frm')).toEqual({
      type: types.ON_TOGGLE_VERDICT_MODAL,
      channel: 'frm'
    });
  });

  it('should toggle approval modal', () => {
    expect(actions.onToggleApprovalModal()).toEqual({ type: types.ON_TOGGLE_APPROVAL_MODAL });
  });

  it('should toggle escalation modal', () => {
    expect(actions.onToggleEscalationModal()).toEqual({ type: types.ON_TOGGLE_ESCALATION_MODAL });
  });

  it('should toggle blacklist modal', () => {
    expect(actions.onToggleBlacklistModal()).toEqual({ type: types.ON_TOGGLE_BLACKLIST_MODAL });
  });

  it('should toggle watchlist modal', () => {
    expect(actions.onToggleWatchlistModal()).toEqual({ type: types.ON_TOGGLE_WATCHLIST_MODAL });
  });

  it('should toggle create rule modal', () => {
    expect(actions.onToggleRuleCreateModal('rpsl')).toEqual({
      type: types.ON_TOGGLE_RULE_CREATE_MODAL,
      channel: 'rpsl'
    });
  });

  it('should toggle edit rule modal', () => {
    expect(actions.onToggleRuleEditModal('rpsl')).toEqual({
      type: types.ON_TOGGLE_RULE_EDIT_MODAL,
      channel: 'rpsl'
    });
  });

  it('should toggle duplicate rule modal', () => {
    expect(actions.onToggleRuleDuplicateModal('rpsl')).toEqual({
      type: types.ON_TOGGLE_RULE_DUPLICATE_MODAL,
      channel: 'rpsl'
    });
  });

  it('should toggle prefilter modal', () => {
    expect(actions.onTogglePrefilterModal()).toEqual({ type: types.ON_TOGGLE_PREFILTER_MODAL });
  });

  it('should toggle prefilters list modal', () => {
    expect(actions.onTogglePrefiltersListModal('specializedList')).toEqual({
      type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL,
      listType: 'specializedList'
    });
  });

  it('should toggle create list modal', () => {
    expect(actions.onToggleCreateListModal()).toEqual({
      type: types.ON_TOGGLE_CREATE_LIST_MODAL
    });
  });

  it('should toggle release funds modal', () => {
    expect(actions.onToggleReleaseFundsModal()).toEqual({
      type: types.ON_TOGGLE_RELEASE_FUNDS_MODAL
    });
  });

  it('should toggle hold case modal', () => {
    expect(actions.onToggleHoldCaseModal()).toEqual({
      type: types.ON_TOGGLE_HOLD_CASE_MODAL
    });
  });

  it('should toggle request document modal', () => {
    expect(actions.onToggleRequestDocumentModal()).toEqual({
      type: types.ON_TOGGLE_REQUEST_DOCUMENT_MODAL
    });
  });

  it('should toggle confirm alert modal', () => {
    expect(actions.onToggleConfirmAlertModal('specializedList')).toEqual({
      type: types.ON_TOGGLE_CONFIRM_ALERT_MODAL,
      listType: 'specializedList'
    });
  });

  it('should toggle status log modal', () => {
    expect(actions.onToggleStatusLogModal()).toEqual({
      type: types.ON_TOGGLE_STATUS_LOG_MODAL
    });
  });

  it('should toggle add to list confirm alert modal', () => {
    expect(actions.onToggleAddToListConfirmAlertModal()).toEqual({
      type: types.ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL
    });
  });

  it('should Toggle Add Bank Modal', () => {
    expect(actions.onToggleAddBankModal()).toEqual({
      type: types.ON_TOGGLE_ADD_BANK_MODAL
    });
  });

  it('should Toggle Download STR Modal', () => {
    expect(actions.onToggleDownloadSTRModal()).toEqual({
      type: types.ON_TOGGLE_DOWNLOAD_STR_MODAL
    });
  });

  it('should Toggle Create Case Modal', () => {
    expect(actions.onToggleCreateCaseModal()).toEqual({
      type: types.ON_TOGGLE_CREATE_CASE_MODAL
    });
  });

  it('should Toggle Dynamic Counters CreateModal', () => {
    expect(actions.onToggleDynamicCountersCreateModal()).toEqual({
      type: types.ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL
    });
  });

  it('should Toggle Request For Information Modal', () => {
    expect(actions.onToggleRequestForInformationModal()).toEqual({
      type: types.ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL
    });
  });

  it('should Toggle User Case Criteria Modal', () => {
    expect(actions.onToggleUserCaseCriteriaModal()).toEqual({
      type: types.ON_TOGGLE_USER_CASE_CRITERIA_MODAL
    });
  });

  it('should Toggle CPIFR Form Modal', () => {
    expect(actions.onToggleCPIFRFormModal()).toEqual({
      type: types.ON_TOGGLE_CPIFR_FORM_MODAL
    });
  });
});
