import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import objectAssign from 'object-assign';

import CardContainer from 'components/common/CardContainer';
import BucketCasesTable from 'containers/common/BucketCasesTableContainer';

import { BUCKET_NAMES } from 'constants/applicationConstants';

function ClosedCasesTable({ period, conf, fetchCases }) {
  const bucket = BUCKET_NAMES['closedCases'].id;
  let currentConf =
    conf?.bucket == ''
      ? objectAssign({}, conf, {
          role: 'investigator',
          bucket,
          filterConditions: [
            {
              key: 'txnTimestamp',
              condition: 'BETWEEN',
              values: [period.startDate, period.endDate]
            }
          ]
        })
      : conf;

  useEffect(() => {
    let newConf = objectAssign({}, currentConf, {
      filterConditions: [
        {
          key: 'txnTimestamp',
          condition: 'BETWEEN',
          values: [period.startDate, period.endDate]
        }
      ]
    });
    fetchCases(newConf, 'frm');
  }, [period]);

  return (
    <CardContainer title={'Closed Cases'}>
      <BucketCasesTable currentConf={currentConf} userRole={'investigator'} channel={'frm'} />
    </CardContainer>
  );
}

ClosedCasesTable.propTypes = {
  conf: PropTypes.object.isRequired,
  period: PropTypes.object.isRequired,
  fetchCases: PropTypes.func.isRequired
};

export default ClosedCasesTable;
