import objectAssign from 'object-assign';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import CardContainer from 'components/common/CardContainer';
import { BUCKET_NAMES } from 'constants/applicationConstants';
import BucketCasesTable from 'containers/common/BucketCasesTableContainer';

function ClosedCasesTable({ period, conf, fetchCases }) {
  const bucket = BUCKET_NAMES['closedCases'].id;
  const currentConf =
    conf?.bucket === ''
      ? objectAssign({}, conf, {
          role: 'investigator',
          bucket,
          filterConditions: [
            {
              key: 'txnTimestamp',
              condition: 'BETWEEN',
              values: [period.startDate, period.endDate]
            }
          ]
        })
      : conf;

  useEffect(() => {
    const newConf = objectAssign({}, currentConf, {
      filterConditions: [
        {
          key: 'txnTimestamp',
          condition: 'BETWEEN',
          values: [period.startDate, period.endDate]
        }
      ]
    });
    fetchCases(newConf, 'frm');
  }, [period]);

  return (
    <CardContainer title="Closed Cases">
      <BucketCasesTable currentConf={currentConf} userRole="investigator" channel="frm" />
    </CardContainer>
  );
}

ClosedCasesTable.propTypes = {
  conf: PropTypes.object.isRequired,
  period: PropTypes.object.isRequired,
  fetchCases: PropTypes.func.isRequired
};

export default ClosedCasesTable;
