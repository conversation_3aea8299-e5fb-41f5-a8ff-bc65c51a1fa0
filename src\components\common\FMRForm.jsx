import PropTypes from 'prop-types';
import React, { useState } from 'react';
import Datetime from 'react-datetime';
import { Button, FormGroup, Label, Input, Row, Col, TabPane } from 'reactstrap';

import FormStepper from './FormStepper';
import ModalContainer from './ModalContainer';

function FMRForm({ theme }) {
  const [display, setDisplay] = useState(false);
  const [active, setActive] = useState(0);
  const [branchCode, setBranchCode] = useState('');
  const [branchName, setBranchName] = useState('');
  const [perpetratorName, setPerpetratorName] = useState('');
  const [accountName, setAccountName] = useState('');
  const [perpetratorActivityName, setPerpetratorActivityName] = useState('');
  const [perpetratorPan, setPerpetratorPan] = useState('');
  const [operationArea, setOperationArea] = useState('');
  const [borrowalAccountFraud, setBorrowalAccountFraud] = useState('No');
  const [consortiumAdvance, setConsortiumAdvance] = useState('No');
  const [consortiumLead, setConsortiumLead] = useState('');
  const [consortiumLeadAmount, setConsortiumLeadAmount] = useState(0);
  const [fraudNature, setFraudNature] = useState('');
  const [fraudType, setFraudType] = useState('');
  const [computerUsed, setComputerUsed] = useState('No');
  const [computerUsedDetails, setComputerUsedDetails] = useState('');
  const [amountInvolved, setAmountInvolved] = useState(0);
  const [amountFrozen, setAmountFrozen] = useState(0);
  const [occuranceDate, setOccuranceDate] = useState('');
  const [detectionDate, setDetectionDate] = useState('');
  const [detectionDelayReason, setDetectionDelayReason] = useState('');
  const [reportingDate, setReportingDate] = useState('');
  const [reportingDelayReason, setReportingDelayReason] = useState('');
  const [fraudDetected, setFraudDetected] = useState('');
  const [briefHistory, setBriefHistory] = useState('');
  const [modusOperandi, setModusOperandi] = useState('');
  const [committedBy, setCommittedBy] = useState('');
  const [controllingOfficeScrutiny, setControllingOfficeScrutiny] = useState('No');
  const [improveInformationSystem, setImproveInformationSystem] = useState('No');
  const [auditConducted, setAuditConducted] = useState('No');
  const [auditNotDetectReason, setAuditNotDetectReason] = useState('');
  const [auditNotDetectAction, setAuditNotDetectAction] = useState('');
  const [complaintLodged, setComplaintLodged] = useState('No');
  const [branchOfLEA, setBranchOfLEA] = useState('');
  const [complaintDate, setComplaintDate] = useState('');
  const [casePresentPosition, setCasePresentPosition] = useState('');
  const [completionDate, setCompletionDate] = useState('');
  const [submissionDate, setSubmissionDate] = useState('');
  const [noReportReason, setNoReportReason] = useState('');
  const [recoverySuitFilingDate, setRecoverySuitFilingDate] = useState('');
  const [recoverySuitCurrentPosition, setRecoverySuitCurrentPosition] = useState('');
  const [recoverySuitInsuranceClaim, setRecoverySuitInsuranceClaim] = useState('No');
  const [noRecoverySuitReason, setNoRecoverySuitReason] = useState('');
  const [internalInvestigationConducted, setInternalInvestigationConducted] = useState('No');
  const [investigationCompletionDate, setInvestigationCompletionDate] = useState('');
  const [departmentalEnquiryConducted, setDepartmentalEnquiryConducted] = useState('No');
  const [noStaffSideActionReason, setNoStaffSideActionReason] = useState('');
  const [incidentAvoidanceSteps, setIncidentAvoidanceSteps] = useState('');
  const [amountRecoveredTotal, setAmountRecoveredTotal] = useState(0);
  const [amountRecoveredParties, setAmountRecoveredParties] = useState(0);
  const [amountRecoveredInsurance, setAmountRecoveredInsurance] = useState(0);
  const [amountRecoveredOthers, setAmountRecoveredOthers] = useState(0);
  const [bankLossExtent, setBankLossExtent] = useState(0);
  const [provisionHeld, setProvisionHeld] = useState(0);
  const [writeOffAmount, setWriteOffAmount] = useState(0);
  const [suggestion, setSuggestion] = useState('');

  const disableFraudNext =
    !branchCode ||
    !branchName ||
    !perpetratorName ||
    !accountName ||
    !perpetratorActivityName ||
    !perpetratorPan ||
    !operationArea ||
    !consortiumLead ||
    consortiumLeadAmount === 0;

  const disableConsortiumNext =
    !fraudNature ||
    !fraudType ||
    (computerUsed === 'Yes' && !computerUsedDetails) ||
    amountInvolved === 0 ||
    !occuranceDate ||
    !detectionDate ||
    !reportingDate ||
    !fraudDetected ||
    !briefHistory ||
    !modusOperandi ||
    !committedBy ||
    (internalInvestigationConducted === 'Yes' && (!auditNotDetectReason || !auditNotDetectAction));

  const disableInvestigationNext =
    (complaintLodged === 'Yes'
      ? !branchOfLEA || !complaintDate || !casePresentPosition || !completionDate || !submissionDate
      : !noReportReason) ||
    !recoverySuitFilingDate ||
    !recoverySuitCurrentPosition ||
    (recoverySuitInsuranceClaim === 'No' && !noRecoverySuitReason);

  return (
    <div>
      <Button outline color="primary" onClick={() => setDisplay(true)}>
        FMR Report
      </Button>
      <ModalContainer
        size="lg"
        theme={theme}
        header="FMR Report"
        isOpen={display}
        toggle={() => setDisplay(!display)}>
        <form>
          <FormStepper
            steps={['Fraud Report', 'Consortium', 'Investigation', 'Recovery']}
            active={active}>
            <TabPane tabId={0}>
              <Row className="align-items-end">
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="branchCode">Branch Code</Label>
                    <Input
                      type="text"
                      name="branchCode"
                      id="branchCode"
                      value={branchCode}
                      onChange={(e) => setBranchCode(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="branchName">Branch Name</Label>
                    <Input
                      type="text"
                      name="branchName"
                      id="branchName"
                      value={branchName}
                      onChange={(e) => setBranchName(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="perpetratorName">Name of Perpetrator</Label>
                    <Input
                      type="text"
                      name="perpetratorName"
                      id="perpetratorName"
                      value={perpetratorName}
                      onChange={(e) => setPerpetratorName(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="accountName">Name of Account</Label>
                    <Input
                      type="text"
                      name="accountName"
                      id="accountName"
                      value={accountName}
                      onChange={(e) => setAccountName(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="perpetratorActivityName">
                      Name of activtity of Perpetrator
                    </Label>
                    <Input
                      type="text"
                      name="perpetratorActivityName"
                      id="perpetratorActivityName"
                      value={perpetratorActivityName}
                      onChange={(e) => setPerpetratorActivityName(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="perpetratorPan">PAN of Perpetrator</Label>
                    <Input
                      type="text"
                      name="perpetratorPan"
                      id="perpetratorPan"
                      value={perpetratorPan}
                      onChange={(e) => setPerpetratorPan(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="operationArea">
                      Area of operation where the fraud has occurred
                    </Label>
                    <Input
                      type="select"
                      name="operationArea"
                      id="operationArea"
                      value={operationArea}
                      onChange={(e) => setOperationArea(e.target.value)}
                      required>
                      <option value=""> -- SELECT --</option>
                      <option>Cash</option>
                      <option>Deposits (Savings/Current/Term)</option>
                      <option>Non-resident accounts</option>
                      <option>Advances (Cash credit/Term Loans/Bills/Others)</option>
                      <option>Inter-branch accounts</option>
                      <option>Cheques/demand drafts, etc.</option>
                      <option>Clearing, etc. accounts</option>
                      <option>
                        Off-balance sheet (Letters of credit/Guarantee/Co-acceptance/Others)
                      </option>
                      <option> Card/Internet - Credit Card</option>
                      <option>ATM/Debit Cards</option>
                      <option>Internet Banking</option>
                      <option>Others</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="3">
                  <FormGroup>
                    <Label htmlFor="borrowalAccountFraud">
                      Whether fraud has occurred in a borrowal account ?
                    </Label>
                    <Input
                      type="select"
                      name="borrowalAccountFraud"
                      id="borrowalAccountFraud"
                      value={borrowalAccountFraud}
                      onChange={(e) => setBorrowalAccountFraud(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="3">
                  <FormGroup>
                    <Label htmlFor="consortiumAdvance">
                      Whether the advance was consortuim advance ?
                    </Label>
                    <Input
                      type="select"
                      name="consortiumAdvance"
                      id="consortiumAdvance"
                      value={consortiumAdvance}
                      onChange={(e) => setConsortiumAdvance(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="consortiumLead">Name of consortium lead bank</Label>
                    <Input
                      type="text"
                      name="consortiumLead"
                      id="consortiumLead"
                      value={consortiumLead}
                      onChange={(e) => setConsortiumLead(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="consortiumLeadAmount">Consortium Leader Amount</Label>
                    <Input
                      type="number"
                      name="consortiumLeadAmount"
                      id="consortiumLeadAmount"
                      value={consortiumLeadAmount}
                      onChange={(e) => setConsortiumLeadAmount(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup className="d-flex justify-content-end">
                    <Button
                      color="info"
                      type="button"
                      size="sm"
                      disabled={disableFraudNext}
                      onClick={() => setActive(1)}>
                      Next
                    </Button>
                  </FormGroup>
                </Col>
              </Row>
            </TabPane>
            <TabPane tabId={1}>
              <Row className="align-items-end">
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="fraudNature">Nature of fraud</Label>
                    <Input
                      type="select"
                      name="fraudNature"
                      id="fraudNature"
                      value={fraudNature}
                      onChange={(e) => setFraudNature(e.target.value)}>
                      <option value=""> -- SELECT -- </option>
                      <option>Misappropriation and criminal breach of trust</option>
                      <option>
                        Fraudulent encashment through forged instruments/manipulation of books of
                        account or through fictitious accounts and conversion of property
                      </option>
                      <option>
                        Unauthorised credit facilities extended for reward or for illegal
                        gratification
                      </option>
                      <option>Negligence and cash shortages</option>
                      <option>Cheating and forgery</option>
                      <option>Irregularities in foreign exchange transactions</option>
                      <option>Others</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="fraudType">Type of Fraud</Label>
                    <Input
                      type="text"
                      name="fraudType"
                      id="fraudType"
                      value={fraudType}
                      onChange={(e) => setFraudType(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md={computerUsed === 'Yes' ? 3 : 12}>
                  <FormGroup>
                    <Label htmlFor="computerUsed">
                      Whether computer is used in committing the fraud ?
                    </Label>
                    <Input
                      type="select"
                      name="computerUsed"
                      id="computerUsed"
                      value={computerUsed}
                      onChange={(e) => setComputerUsed(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {computerUsed === 'Yes' && (
                  <Col md="9">
                    <FormGroup>
                      <Label htmlFor="computerUsedDetails">If yes, details</Label>
                      <Input
                        type="text"
                        name="computerUsedDetails"
                        id="computerUsedDetails"
                        disabled={computerUsed === 'No'}
                        value={computerUsedDetails}
                        onChange={(e) => setComputerUsedDetails(e.target.value)}
                      />
                    </FormGroup>
                  </Col>
                )}
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="amountInvolved">Total amount involved</Label>
                    <Input
                      type="number"
                      name="amountInvolved"
                      id="amountInvolved"
                      value={amountInvolved}
                      onChange={(e) => setAmountInvolved(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="amountFrozen">Amount Frozen</Label>
                    <Input
                      type="number"
                      name="amountFrozen"
                      id="amountFrozen"
                      value={amountFrozen}
                      onChange={(e) => setAmountFrozen(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="occuranceDate">Date of Occurance</Label>
                    <Datetime
                      name="occuranceDate"
                      id="occuranceDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat={false}
                      value={occuranceDate}
                      onChange={(dateObj) => setOccuranceDate(dateObj._d)}
                      closeOnSelect={true}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="detectionDate">Date of Detection</Label>
                    <Datetime
                      name="detectionDate"
                      id="detectionDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat={false}
                      value={detectionDate}
                      onChange={(dateObj) => setDetectionDate(dateObj._d)}
                      closeOnSelect={true}
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup>
                    <Label htmlFor="detectionDelayReason">
                      Reasons for delay, if any, in detecting the fraud
                    </Label>
                    <Input
                      type="text"
                      name="detectionDelayReason"
                      id="detectionDelayReason"
                      value={detectionDelayReason}
                      onChange={(e) => setDetectionDelayReason(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="3">
                  <FormGroup>
                    <Label htmlFor="reportingDate">Date of Reporting to RBI</Label>
                    <Datetime
                      name="reportingDate"
                      id="reportingDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat={false}
                      value={reportingDate}
                      onChange={(dateObj) => setReportingDate(dateObj._d)}
                      closeOnSelect={true}
                    />
                  </FormGroup>
                </Col>
                <Col md="9">
                  <FormGroup>
                    <Label htmlFor="reportingDelayReason">
                      Reasons for delay, if any, in reporting the fraud to RBI
                    </Label>
                    <Input
                      type="text"
                      name="reportingDelayReason"
                      id="reportingDelayReason"
                      value={reportingDelayReason}
                      onChange={(e) => setReportingDelayReason(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup>
                    <Label htmlFor="fraudDetected">How the fraud was detected?</Label>
                    <Input
                      type="text"
                      name="fraudDetected"
                      id="fraudDetected"
                      value={fraudDetected}
                      onChange={(e) => setFraudDetected(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup>
                    <Label htmlFor="briefHistory">
                      Brief History including Root Cause analysis
                    </Label>
                    <Input
                      type="textarea"
                      name="briefHistory"
                      id="briefHistory"
                      value={briefHistory}
                      onChange={(e) => setBriefHistory(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="9">
                  <FormGroup>
                    <Label htmlFor="modusOperandi">Modus operandi</Label>
                    <Input
                      type="text"
                      name="modusOperandi"
                      id="modusOperandi"
                      value={modusOperandi}
                      onChange={(e) => setModusOperandi(e.target.value)}
                      required
                    />
                  </FormGroup>
                </Col>
                <Col md="3">
                  <FormGroup>
                    <Label htmlFor="committedBy">Fraud committed by</Label>
                    <Input
                      type="select"
                      name="committedBy"
                      id="committedBy"
                      value={committedBy}
                      onChange={(e) => setCommittedBy(e.target.value)}
                      required>
                      <option value="">-- SELECT --</option>
                      <option>Staff</option>
                      <option>Customers</option>
                      <option>Outsiders</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="controllingOfficeScrutiny">
                      Whether the controlling office (Regional/Zonal) could detect the fraud by a
                      scrutiny of control returns submitted by the branch
                    </Label>
                    <Input
                      type="select"
                      name="controllingOfficeScrutiny"
                      id="controllingOfficeScrutiny"
                      value={controllingOfficeScrutiny}
                      onChange={(e) => setControllingOfficeScrutiny(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="improveInformationSystem">
                      Whether there is need to improve the information system?
                    </Label>
                    <Input
                      type="select"
                      name="improveInformationSystem"
                      id="improveInformationSystem"
                      value={improveInformationSystem}
                      onChange={(e) => setImproveInformationSystem(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="auditConducted">
                      Whether internal inspection/ audit (including concurrent audit) was conducted
                      at the branch(es) during the period between the date of first occurrence of
                      the fraud and its detection?
                    </Label>
                    <Input
                      type="select"
                      name="auditConducted"
                      id="auditConducted"
                      value={auditConducted}
                      onChange={(e) => setAuditConducted(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {auditConducted === 'Yes' && (
                  <>
                    <Col md="6">
                      <FormGroup>
                        <Label htmlFor="auditNotDetectReason">
                          If yes, why the fraud could not have been detected during such
                          inspection/audit.
                        </Label>
                        <Input
                          type="text"
                          name="auditNotDetectReason"
                          id="auditNotDetectReason"
                          value={auditNotDetectReason}
                          onChange={(e) => setAuditNotDetectReason(e.target.value)}
                        />
                      </FormGroup>
                    </Col>
                    <Col md="6">
                      <FormGroup>
                        <Label htmlFor="auditNotDetectAction">
                          What action has been taken for non-detection of the fraud during such
                          inspection/audit
                        </Label>
                        <Input
                          type="text"
                          name="auditNotDetectAction"
                          id="auditNotDetectAction"
                          value={auditNotDetectAction}
                          onChange={(e) => setAuditNotDetectAction(e.target.value)}
                        />
                      </FormGroup>
                    </Col>
                  </>
                )}
                <Col md="12">
                  <FormGroup className="d-flex justify-content-between">
                    <Button color="warning" type="button" size="sm" onClick={() => setActive(0)}>
                      Previous
                    </Button>
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      disabled={disableConsortiumNext}
                      onClick={() => setActive(2)}>
                      Next
                    </Button>
                  </FormGroup>
                </Col>
              </Row>
            </TabPane>
            <TabPane tabId={2}>
              <Row className="align-items-end">
                <Col md="3">
                  <FormGroup>
                    <Label htmlFor="complaintLodged">
                      Whether any complaint has been lodged with the Police/CBI?
                    </Label>
                    <Input
                      type="select"
                      name="complaintLodged"
                      id="complaintLodged"
                      value={complaintLodged}
                      onChange={(e) => setComplaintLodged(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {complaintLodged === 'Yes' ? (
                  <>
                    <Col md="9">
                      <FormGroup>
                        <Label htmlFor="branchOfLEA">
                          If yes, name of office/ branch of CBI/ Police
                        </Label>
                        <Input
                          type="text"
                          name="branchOfLEA"
                          id="branchOfLEA"
                          value={branchOfLEA}
                          onChange={(e) => setBranchOfLEA(e.target.value)}
                        />
                      </FormGroup>
                    </Col>
                    <Col md="3">
                      <FormGroup>
                        <Label htmlFor="complaintDate">Date of reference</Label>
                        <Datetime
                          name="complaintDate"
                          id="complaintDate"
                          dateFormat="YYYY-MM-DD"
                          timeFormat={false}
                          value={complaintDate}
                          onChange={(dateObj) => setComplaintDate(dateObj._d)}
                          closeOnSelect={true}
                        />
                      </FormGroup>
                    </Col>
                    <Col md="9">
                      <FormGroup>
                        <Label htmlFor="casePresentPosition">Present position of the case</Label>
                        <Input
                          type="text"
                          name="casePresentPosition"
                          id="casePresentPosition"
                          value={casePresentPosition}
                          onChange={(e) => setCasePresentPosition(e.target.value)}
                        />
                      </FormGroup>
                    </Col>
                    <Col md="6">
                      <FormGroup>
                        <Label htmlFor="completionDate">
                          Date of completion of Police/CBI investigation
                        </Label>
                        <Datetime
                          name="completionDate"
                          id="completionDate"
                          dateFormat="YYYY-MM-DD"
                          timeFormat={false}
                          value={completionDate}
                          onChange={(dateObj) => setCompletionDate(dateObj._d)}
                          closeOnSelect={true}
                        />
                      </FormGroup>
                    </Col>
                    <Col md="6">
                      <FormGroup>
                        <Label htmlFor="submissionDate">
                          Date of submission of investigation report by Police/CBI
                        </Label>
                        <Datetime
                          name="submissionDate"
                          id="submissionDate"
                          dateFormat="YYYY-MM-DD"
                          timeFormat={false}
                          value={submissionDate}
                          onChange={(dateObj) => setSubmissionDate(dateObj._d)}
                          closeOnSelect={true}
                        />
                      </FormGroup>
                    </Col>
                  </>
                ) : (
                  <Col md="12">
                    <FormGroup>
                      <Label htmlFor="noReportReason">
                        If not reported to Police/CBI, reasons therefor.
                      </Label>
                      <Input
                        type="textarea"
                        name="noReportReason"
                        id="noReportReason"
                        value={noReportReason}
                        onChange={(e) => setNoReportReason(e.target.value)}
                      />
                    </FormGroup>
                  </Col>
                )}
                <Label>Recovery suit with DRT/Court</Label>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="recoverySuitFilingDate">Date of filing</Label>
                    <Datetime
                      name="recoverySuitFilingDate"
                      id="recoverySuitFilingDate"
                      dateFormat="YYYY-MM-DD"
                      timeFormat={false}
                      value={recoverySuitFilingDate}
                      onChange={(dateObj) => setRecoverySuitFilingDate(dateObj._d)}
                      closeOnSelect={true}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="recoverySuitCurrentPosition">Present position</Label>
                    <Input
                      type="text"
                      name="recoverySuitCurrentPosition"
                      id="recoverySuitCurrentPosition"
                      value={recoverySuitCurrentPosition}
                      onChange={(e) => setRecoverySuitCurrentPosition(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="recoverySuitInsuranceClaim">
                      Whether any claim has been lodged with an insurance company?
                    </Label>
                    <Input
                      type="select"
                      name="recoverySuitInsuranceClaim"
                      id="recoverySuitInsuranceClaim"
                      value={recoverySuitInsuranceClaim}
                      onChange={(e) => setRecoverySuitInsuranceClaim(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {recoverySuitInsuranceClaim === 'No' && (
                  <Col md="6">
                    <FormGroup>
                      <Label htmlFor="noRecoverySuitReason">If not, reasons therefor.</Label>
                      <Input
                        type="text"
                        name="noRecoverySuitReason"
                        id="noRecoverySuitReason"
                        value={noRecoverySuitReason}
                        onChange={(e) => setNoRecoverySuitReason(e.target.value)}
                      />
                    </FormGroup>
                  </Col>
                )}
                <Col md="12">
                  <FormGroup className="d-flex justify-content-between">
                    <Button color="warning" type="button" size="sm" onClick={() => setActive(1)}>
                      Previous
                    </Button>
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      disabled={disableInvestigationNext}
                      onClick={() => setActive(3)}>
                      Next
                    </Button>
                  </FormGroup>
                </Col>
              </Row>
            </TabPane>
            <TabPane tabId={3}>
              <Row className="align-items-end">
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="internalInvestigationConducted">
                      Whether any internal investigation has been/is proposed to be conducted?
                    </Label>
                    <Input
                      type="select"
                      name="internalInvestigationConducted"
                      id="internalInvestigationConducted"
                      value={internalInvestigationConducted}
                      onChange={(e) => setInternalInvestigationConducted(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {internalInvestigationConducted === 'Yes' && (
                  <Col md="6">
                    <FormGroup>
                      <Label htmlFor="investigationCompletionDate">
                        If yes, date of completion/expected date of completion
                      </Label>
                      <Datetime
                        name="investigationCompletionDate"
                        id="investigationCompletionDate"
                        dateFormat="YYYY-MM-DD"
                        timeFormat={false}
                        value={investigationCompletionDate}
                        onChange={(dateObj) => setInvestigationCompletionDate(dateObj._d)}
                        closeOnSelect={true}
                      />
                    </FormGroup>
                  </Col>
                )}
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="departmentalEnquiryConducted">
                      Whether any departmental enquiry has been/is proposed to be conducted?
                    </Label>
                    <Input
                      type="select"
                      name="departmentalEnquiryConducted"
                      id="departmentalEnquiryConducted"
                      value={departmentalEnquiryConducted}
                      onChange={(e) => setDepartmentalEnquiryConducted(e.target.value)}>
                      <option>Yes</option>
                      <option>No</option>
                    </Input>
                  </FormGroup>
                </Col>
                {departmentalEnquiryConducted === 'No' && (
                  <Col md="6">
                    <FormGroup>
                      <Label htmlFor="noStaffSideActionReason">If not, reasons therefor</Label>
                      <Input
                        type="textarea"
                        name="noStaffSideActionReason"
                        id="noStaffSideActionReason"
                        value={noStaffSideActionReason}
                        onChange={(e) => setNoStaffSideActionReason(e.target.value)}
                      />
                    </FormGroup>
                  </Col>
                )}
                <Col md="12">
                  <FormGroup>
                    <Label htmlFor="incidentAvoidanceSteps">
                      Steps taken/proposed to be taken to avoid such incidents
                    </Label>
                    <Input
                      type="textarea"
                      name="incidentAvoidanceSteps"
                      id="incidentAvoidanceSteps"
                      value={incidentAvoidanceSteps}
                      onChange={(e) => setIncidentAvoidanceSteps(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="amountRecoveredTotal">Amount recovered</Label>
                    <Input
                      type="number"
                      name="amountRecoveredTotal"
                      id="amountRecoveredTotal"
                      value={amountRecoveredTotal}
                      readOnly={true}
                      onChange={(e) => setAmountRecoveredTotal(e.target.value)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label htmlFor="amountRecoveredParties">
                      Amount recovered from party/parties concerned
                    </Label>
                    <Input
                      type="number"
                      name="amountRecoveredParties"
                      id="amountRecoveredParties"
                      value={amountRecoveredParties}
                      onChange={(e) => setAmountRecoveredParties(e.target.value)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label htmlFor="amountRecoveredInsurance">
                      Amount recovered from insurance
                    </Label>
                    <Input
                      type="number"
                      name="amountRecoveredInsurance"
                      id="amountRecoveredInsurance"
                      value={amountRecoveredInsurance}
                      onChange={(e) => setAmountRecoveredInsurance(e.target.value)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label htmlFor="amountRecoveredOthers">
                      Amount recovered from other sources
                    </Label>
                    <Input
                      type="number"
                      name="amountRecoveredOthers"
                      id="amountRecoveredOthers"
                      value={amountRecoveredOthers}
                      onChange={(e) => setAmountRecoveredOthers(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label htmlFor="bankLossExtent">Extent of loss to the bank</Label>
                    <Input
                      type="number"
                      name="bankLossExtent"
                      id="bankLossExtent"
                      value={bankLossExtent}
                      onChange={(e) => setBankLossExtent(e.target.value)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label htmlFor="provisionHeld">Provision held</Label>
                    <Input
                      type="number"
                      name="provisionHeld"
                      id="provisionHeld"
                      value={provisionHeld}
                      onChange={(e) => setProvisionHeld(e.target.value)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label htmlFor="writeOffAmount">Amount written off </Label>
                    <Input
                      type="number"
                      name="writeOffAmount"
                      id="writeOffAmount"
                      value={writeOffAmount}
                      onChange={(e) => setWriteOffAmount(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup>
                    <Label htmlFor="suggestion">Suggestion</Label>
                    <Input
                      type="suggestion"
                      name="suggestion"
                      id="suggestion"
                      value={suggestion}
                      onChange={(e) => setSuggestion(e.target.value)}
                    />
                  </FormGroup>
                </Col>
                <Col md="12">
                  <FormGroup className="d-flex justify-content-between">
                    <Button color="warning" type="button" size="sm" onClick={() => setActive(2)}>
                      Previous
                    </Button>
                    <Button
                      color="info"
                      size="sm"
                      type="button"
                      disabled={disableInvestigationNext}>
                      Submit
                    </Button>
                  </FormGroup>
                </Col>
              </Row>
            </TabPane>
          </FormStepper>
        </form>
      </ModalContainer>
    </div>
  );
}

FMRForm.propTypes = {
  theme: PropTypes.string.isRequired
};

export default FMRForm;
