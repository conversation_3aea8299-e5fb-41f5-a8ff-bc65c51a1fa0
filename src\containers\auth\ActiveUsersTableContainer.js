import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as userActions from 'actions/userManagementActions';
import ActiveUsersTable from 'components/auth/ActiveUsersTable';

const mapStateToProps = (state) => ({
  channelslist: state.user.channels,
  userslist: state.user.userslist,
  userRoles: state.auth.userCreds.roles,
  partnerIdList: state.user.partnerIdList,
  adminlist: state.user.adminlist,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  userActions: bindActionCreators(userActions, dispatch)
});

const ActiveUsersTableContainer = connect(mapStateToProps, mapDispatchToProps)(ActiveUsersTable);

export default ActiveUsersTableContainer;
