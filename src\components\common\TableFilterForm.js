import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { find, map } from 'lodash';
import moment from 'moment';
import objectAssign from 'object-assign';
import PropTypes from 'prop-types';
import React, { useEffect, useMemo, useReducer } from 'react';
import Datetime from 'react-datetime';
import { MultiSelect } from 'react-multi-select-component';
import { FormGroup, Label, Input, Row, Col, Button } from 'reactstrap';

import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';
import RuleListMultiSelectContainer from 'containers/common/RuleListMultiSelectContainer';
import TxnTypeDropDownContainer from 'containers/common/TxnTypeDropDownContainer';
import XChannelDropDownContainer from 'containers/common/XChannelDropDownContainer';

// Initial state for the form
const initialState = {
  xchannelId: '',
  txnType: '',
  startDate: '',
  endDate: '',
  entityId: '',
  txnId: '',
  attribute2: '',
  rules: [],
  sortBy: 'updatedTimestamp-desc',
  selectedUsers: []
};

// Reducer function to manage form state
const formReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FIELD':
      return { ...state, [action.field]: action.value };
    case 'RESET_DATES':
      return { ...state, startDate: '', endDate: '' };
    case 'SET_CONDITIONS':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

const TableFilterForm = ({
  hasProvisionalFields,
  channel,
  currentConf,
  fetchCases,
  dateRange = {},
  sortable = true,
  channelFilter = true,
  txnTypeFilter = true,
  userList
}) => {
  const [state, dispatch] = useReducer(formReducer, initialState);

  const {
    xchannelId,
    txnType,
    startDate,
    endDate,
    entityId,
    txnId,
    attribute2,
    rules,
    sortBy,
    selectedUsers
  } = state;

  const userOptions = useMemo(
    () =>
      map(userList, ({ userName }) => ({
        label: userName,
        value: userName
      })),
    [userList]
  );

  const valid = (current) =>
    dateRange?.startDate
      ? current.isSameOrAfter(moment(dateRange?.startDate)) &&
        current.isSameOrBefore(dateRange?.endDate)
      : true;

  useEffect(() => {
    const { sortBy: confSortBy, sortOrder: confSortOrder, filterCondition = [] } = currentConf;

    if (sortBy !== `${confSortBy}-${confSortOrder}`)
      dispatch({ type: 'SET_FIELD', field: 'sortBy', value: `${confSortBy}-${confSortOrder}` });

    // Helper function to find filter condition by key
    const findConditionByKey = (key) => find(filterCondition, (condition) => condition.key === key);

    // Date filter
    const dateFilter = findConditionByKey('txnTimestamp');
    if (dateFilter)
      dispatch({
        type: 'SET_CONDITIONS',
        payload: {
          startDate: moment(dateFilter?.values[0]).format('YYYY-MM-DDTHH:mm:ss'),
          endDate: moment(dateFilter?.values[1]).format('YYYY-MM-DDTHH:mm:ss')
        }
      });
    else dispatch({ type: 'RESET_DATES' });

    // Set other fields from filter conditions, with conditional logic for attribute2
    const conditionsPayload = {
      entityId: findConditionByKey('entityId')?.values.toString() || '',
      txnId: findConditionByKey('txnId')?.values.toString() || '',
      xchannelId: findConditionByKey('xchannelId')?.values.toString() || '',
      txnType: findConditionByKey('txnTypeName')?.values.toString() || ''
    };

    if (hasProvisionalFields === 1)
      conditionsPayload.attribute2 = findConditionByKey('attribute2')?.values.toString() || '';

    dispatch({
      type: 'SET_CONDITIONS',
      payload: conditionsPayload
    });
  }, [currentConf, hasProvisionalFields]);

  const generateFilterCondition = () => {
    const filterCondition = [];
    if (startDate && endDate)
      filterCondition.push({
        key: 'txnTimestamp',
        condition: 'BETWEEN',
        values: [
          moment(startDate).format('YYYY-MM-DDTHH:mm:ss'),
          moment(endDate).format('YYYY-MM-DDTHH:mm:ss')
        ]
      });

    entityId &&
      filterCondition.push({
        key: 'entityId',
        condition: 'EQUAL',
        values: [entityId]
      });
    txnId &&
      filterCondition.push({
        key: 'txnId',
        condition: 'EQUAL',
        values: [txnId]
      });
    attribute2 &&
      filterCondition.push({
        key: 'attribute2',
        condition: 'EQUAL',
        values: [attribute2]
      });

    xchannelId &&
      channelFilter &&
      filterCondition.push({
        key: 'xchannelId',
        condition: 'EQUAL',
        values: [xchannelId]
      });

    txnType &&
      txnTypeFilter &&
      filterCondition.push({
        key: 'txnTypeName',
        condition: 'EQUAL',
        values: [txnType]
      });

    rules.length > 0 &&
      filterCondition.push({
        key: 'reVioalatedRules',
        condition: 'EQUAL',
        values: rules.map((d) => d.value)
      });

    selectedUsers.length > 0 &&
      filterCondition.push({
        key: 'analyst',
        condition: 'EQUAL',
        values: selectedUsers.map((user) => user.value)
      });

    return filterCondition;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const sort = sortBy.split('-');
    fetchCases(
      objectAssign({}, currentConf, {
        pageNo: 1,
        ...(sortable && { sortBy: sort[0], sortOrder: sort[1] }),
        filterCondition: generateFilterCondition()
      }),
      channel
    );
  };

  const setFieldValue = (field, value) => {
    dispatch({ type: 'SET_FIELD', field, value });
  };

  return (
    <form id="searchform" onSubmit={handleSubmit} className="mt-3">
      <Row>
        {channelFilter && (
          <Col lg={3} md={4}>
            <XChannelDropDownContainer
              value={xchannelId}
              onChange={(val) => setFieldValue('xchannelId', val)}
            />
          </Col>
        )}
        {txnTypeFilter && (
          <Col lg={3} md={4}>
            <TxnTypeDropDownContainer
              value={txnType}
              onChange={(val) => setFieldValue('txnType', val)}
            />
          </Col>
        )}
        <Col lg={3} md={4}>
          <FormGroup>
            <Label>Entity ID</Label>
            <Input
              type="text"
              name="entityId"
              value={entityId}
              onChange={(e) => setFieldValue('entityId', e.target.value)}
            />
          </FormGroup>
        </Col>
        <Col lg={3} md={4}>
          <FormGroup>
            <Label>Transaction ID</Label>
            <Input
              type="text"
              name="txnId"
              value={txnId}
              onChange={(e) => setFieldValue('txnId', e.target.value)}
            />
          </FormGroup>
        </Col>
        {hasProvisionalFields === 1 && (
          <Col lg={3} md={4}>
            <FormGroup>
              <Label>
                <ProvisionalFieldsValueContainer attrName="attribute2" />
              </Label>
              <Input
                type="text"
                name="attribute2"
                value={attribute2}
                onChange={(e) => setFieldValue('attribute2', e.target.value)}
              />
            </FormGroup>
          </Col>
        )}
        <Col lg={3} md={4}>
          <FormGroup>
            <Label>Start Date</Label>
            <Datetime
              name="startDate"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={startDate}
              onChange={(date) => setFieldValue('startDate', date._d)}
              inputProps={{ ...(endDate && { required: true }) }}
              renderInput={(props) => (
                // eslint-disable-next-line react/prop-types
                <input {...props} value={startDate ? props.value : ''} />
              )}
              isValidDate={valid}
            />
          </FormGroup>
        </Col>
        <Col lg={3} md={4}>
          <FormGroup>
            <Label>End Date</Label>
            <Datetime
              name="endDate"
              dateFormat="YYYY-MM-DD"
              timeFormat="HH:mm:ss"
              value={endDate}
              onChange={(date) => setFieldValue('endDate', date._d)}
              inputProps={{ ...(startDate && { required: true }) }}
              renderInput={(props) => (
                // eslint-disable-next-line react/prop-types
                <input {...props} value={endDate ? props.value : ''} />
              )}
              isValidDate={valid}
            />
          </FormGroup>
        </Col>
        <Col lg={3} md={4}>
          <FormGroup>
            <RuleListMultiSelectContainer
              channel={channel}
              ruleList={rules}
              setRuleList={(val) => setFieldValue('rules', val)}
            />
          </FormGroup>
        </Col>
        {currentConf.role === 'supervisor' && (
          <Col lg={3} md={4}>
            <FormGroup>
              <Label>Users</Label>
              <MultiSelect
                options={userOptions}
                value={selectedUsers}
                onChange={(val) => setFieldValue('selectedUsers', val)}
                labelledBy="Select"
                overrideStrings={{
                  selectSomeItems: 'Select users',
                  allItemsAreSelected: 'All users are selected'
                }}
              />
            </FormGroup>
          </Col>
        )}
        <Col lg={3} md={4}>
          <FormGroup>
            <Label>Sort By</Label>
            <Input
              type="select"
              name="sortBy"
              value={sortBy}
              onChange={(e) => setFieldValue('sortBy', e.target.value)}>
              <option value="ruleOrder-asc">Rule Order</option>
              <option value="ruleLabel-asc">Rule Label</option>
              <option value="txnTimestamp-desc">Date - Latest to Oldest</option>
              <option value="txnTimestamp-asc">Date - Oldest to Latest</option>
              <option value="updatedTimestamp-desc">Case Activity - Latest to Oldest</option>
              <option value="updatedTimestamp-asc">Case Activity - Oldest to Latest</option>
            </Input>
          </FormGroup>
        </Col>
      </Row>
      <Row>
        <Col className="mt-1">
          <Button title="search" type="submit" color="primary">
            <FontAwesomeIcon icon={faSearch} /> Search
          </Button>
        </Col>
      </Row>
    </form>
  );
};

TableFilterForm.propTypes = {
  hasProvisionalFields: PropTypes.number,
  channel: PropTypes.string,
  currentConf: PropTypes.object.isRequired,
  fetchCases: PropTypes.func.isRequired,
  dateRange: PropTypes.object,
  sortable: PropTypes.bool,
  channelFilter: PropTypes.bool,
  txnTypeFilter: PropTypes.bool,
  userList: PropTypes.array
};

export default TableFilterForm;
