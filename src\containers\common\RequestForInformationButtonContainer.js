import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onRequestForInformation } from 'actions/caseDetailsActions';
import { onToggleRequestForInformationModal } from 'actions/toggleActions';
import RequestForInformationButton from 'components/common/RequestForInformationButton';
import { onShowFailureAlert } from 'actions/alertActions';

const mapStateToProps = (state) => {
  return {
    entityInfo: state.uds,
    theme: state.toggle.theme,
    requestForInformationModal: state.toggle.requestForInformationModal
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    requestForInformation: bindActionCreators(onRequestForInformation, dispatch),
    toggleRequestForInformationModal: bindActionCreators(
      onToggleRequestForInformationModal,
      dispatch
    )
  };
};

const RequestForInformationButtonContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RequestForInformationButton);

export default RequestForInformationButtonContainer;
