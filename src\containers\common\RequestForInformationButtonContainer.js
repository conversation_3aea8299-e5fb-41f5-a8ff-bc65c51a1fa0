import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onShowFailureAlert } from 'actions/alertActions';
import { onRequestForInformation } from 'actions/caseDetailsActions';
import { onToggleRequestForInformationModal } from 'actions/toggleActions';
import RequestForInformationButton from 'components/common/RequestForInformationButton';

const mapStateToProps = (state) => ({
  entityInfo: state.uds,
  theme: state.toggle.theme,
  requestForInformationModal: state.toggle.requestForInformationModal
});

const mapDispatchToProps = (dispatch) => ({
  showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
  requestForInformation: bindActionCreators(onRequestForInformation, dispatch),
  toggleRequestForInformationModal: bindActionCreators(onToggleRequestForInformationModal, dispatch)
});

const RequestForInformationButtonContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RequestForInformationButton);

export default RequestForInformationButtonContainer;
