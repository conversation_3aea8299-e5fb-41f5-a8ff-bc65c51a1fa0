import { combineReducers } from 'redux';
import storage from 'redux-persist/lib/storage';

import { ON_SUCCESSFUL_LOGOUT } from 'constants/actionTypes';

import advanceSearchTxns from './advanceSearchTxnsReducer';
import alert from './alertReducer';
import auth from './authReducer';
import businessDashboard from './businessDashboardReducer';
import caseAssignment from './caseAssignmentReducer';
import caseDocument from './caseDocumentReducer';
import citations from './citationsReducer';
import cognitiveStatus from './cognitiveStatusReducer';
import complianceDashboard from './complianceDashboardReducer';
import customerCommunication from './CustomerCommunicationReducer';
import customerEvents from './customerEventsReducer';
import demographicDetails from './demographicDetailsReducer';
import dynamicCounters from './dynamicCountersReducer';
import incidents from './incidentsReducer';
import initialState from './initialState';
import investigation from './investigationReducer';
import logs from './logsReducer';
import monitor from './monitoringReducer';
import notations from './notationsReducer';
import notifications from './notificationsReducer';
import oneView from './oneViewReducer';
import partnerBanks from './partnerBanksReducer';
import prefilter from './prefilterReducer';
import prefiltersList from './prefiltersListReducer';
import profiling from './profilingReducer';
import releaseFunds from './releaseFundsReducer';
import rfiReports from './rfiReportsReducer';
import ruleConfigurator from './ruleConfiguratorReducer';
import ruleCreation from './ruleCreationReducer';
import ruleDashboard from './ruleDashboardReducer';
import sandboxing from './sandboxingReducer';
import scp from './scpReducer';
import settings from './settingsReducer';
import slaDashboard from './slaDashboardReducer';
import snoozeRules from './snoozeRulesReducer';
import statisticsDetails from './statisticsDetailsReducer';
import strReport from './strReportReducer';
import toggle from './toggleReducer';
import transactionDetails from './transactionDetailsReducer';
import uds from './udsReducer';
import user from './userReducer';
import violatedRules from './violatedRulesReducer';

const appReducer = combineReducers({
  auth,
  user,
  partnerBanks,
  alert,
  toggle,
  logs,
  violatedRules,
  monitor,
  caseAssignment,
  notations,
  investigation,
  ruleCreation,
  dynamicCounters,
  ruleConfigurator,
  transactionDetails,
  prefilter,
  scp,
  cognitiveStatus,
  releaseFunds,
  prefiltersList,
  uds,
  profiling,
  demographicDetails,
  statisticsDetails,
  advanceSearchTxns,
  sandboxing,
  snoozeRules,
  citations,
  strReport,
  settings,
  rfiReports,
  caseDocument,
  incidents,
  slaDashboard,
  businessDashboard,
  ruleDashboard,
  oneView,
  complianceDashboard,
  customerCommunication,
  notifications,
  customerEvents
});

const rootReducer = (state, action) => {
  if (action && action.type === ON_SUCCESSFUL_LOGOUT) {
    Object.keys(state).forEach((key) => {
      storage.removeItem(`persist:${key}`);
    });
    state = initialState;
  }
  return appReducer(state, action);
};

export default rootReducer;
