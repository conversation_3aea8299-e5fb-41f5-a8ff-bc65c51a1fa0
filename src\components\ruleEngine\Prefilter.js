'use strict';
import { faPencil, faCheck, faTrash, faRefresh, faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import ReactTable from 'react-table';
import { Button, FormGroup, Input, Label, Form, ButtonGroup } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import PrefilterFormContainer from 'containers/ruleEngine/PrefilterFormContainer';

class Prefilter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tpsLimit: '',
      editing: false,
      isEdited: false,
      filterData: {
        id: '',
        identifier: '',
        channelId: '',
        categoryId: '',
        isActive: 0
      },
      type: 'Add'
    };
    this.setTpsLimit = this._setTpsLimit.bind(this);
    this.removeFilter = this._removeFilter.bind(this);
    this.editTpsToggle = this._editTpsToggle.bind(this);
    this.changeTpsLimit = this._changeTpsLimit.bind(this);
    this.submitTpsLimit = this._submitTpsLimit.bind(this);
    this.toggleFilterModal = this._toggleFilterModal.bind(this);
  }

  componentDidMount() {
    this.props.fetchChannels();
    this.props.actions.onFetchTps();
    this.props.actions.onFetchFilter();
    this.setTpsLimit();
  }

  componentDidUpdate(prevProps) {
    if (this.props.prefilter !== prevProps.prefilter) this.setTpsLimit();
  }

  _editTpsToggle = () => this.setState((state) => ({ editing: !state.editing }));

  _setTpsLimit = () => {
    const { prefilter, channel } = this.props;
    const channelTps = _.find(prefilter.tps.list, (tps) => tps.channelName === channel);
    const tpsLimit = _.isEmpty(channelTps) ? '' : channelTps.tpsLimit;
    this.setState({ tpsLimit, isEdited: false });
  };

  _changeTpsLimit = (e) =>
    this.setState({
      tpsLimit: e.target.value,
      isEdited: e.target.value.length > 0
    });

  _toggleFilterModal = (type, filterData) => {
    const { toggleForm, channel } = this.props;
    type === 'Edit'
      ? this.setState({ type, filterData })
      : this.setState({
          type,
          filterData: {
            id: '',
            identifier: '',
            channelId: '',
            categoryId: '',
            isActive: 0
          }
        });
    toggleForm(channel);
  };

  _submitTpsLimit = (e) => {
    e.preventDefault();
    const { tpsLimit } = this.state;
    const { prefilter, channels, channel, actions } = this.props;
    const channelId = _.find(channels, (d) => d.name === channel).id;
    const channelTps = _.find(prefilter.tps.list, (tps) => tps.channelName === channel);
    const method = _.isEmpty(channelTps) ? 'POST' : 'PUT';
    const formData = {
      tpsLimit: parseInt(tpsLimit),
      channelId: parseInt(channelId)
    };
    actions.onSetTps(formData, method);
  };

  _removeFilter = (id) => {
    if (confirm('Are you sure you wish to remove this filter ?'))
      this.props.actions.onDeleteFilter(id);
  };

  render() {
    const { tpsLimit, isEdited, editing, type, filterData } = this.state;
    const { channel, prefilter, actions, channels } = this.props;
    const { filter, tps } = prefilter;
    const channelTps = _.find(prefilter.tps.list, (tps) => tps.channelName === channel);
    const channelTpsLimit = _.isEmpty(channelTps) ? 'NA' : channelTps.tpsLimit;
    const filterList = _.isEmpty(filter.list[channel]) ? [] : filter.list[channel];
    const selectedChannel = _.find(channels, (d) => d.name === channel);
    const channelId = _.isEmpty(selectedChannel) ? '' : parseInt(selectedChannel.id);

    const header = [
      { Header: 'Category', accessor: 'categoryName' },
      { Header: 'Identifier', accessor: 'identifier' },
      {
        Header: 'Actions',
        accessor: 'id',
        Cell: (row) => (
          <ButtonGroup>
            <Button
              size="sm"
              color="warning"
              title="Edit condition"
              className="me-sm-2"
              onClick={() => this.toggleFilterModal('Edit', row.original)}>
              <FontAwesomeIcon icon={faPencil} />
            </Button>
            <Button
              size="sm"
              color="danger"
              title="Delete condition"
              onClick={() => this.removeFilter(row.original.id)}>
              <FontAwesomeIcon icon={faTrash} />
            </Button>
          </ButtonGroup>
        )
      }
    ];

    const tpsForm = editing ? (
      <Form inline onSubmit={(e) => this._submitTpsLimit(e)}>
        <FormGroup className="mb-2 me-sm-4 mb-sm-0">
          <Label className="me-sm-4">TPS Limit: </Label>

          <Input
            type="number"
            id="tpsLimit"
            name="tpsLimit"
            inputMode="numeric"
            pattern="[0-9]*"
            value={tpsLimit}
            onChange={this.changeTpsLimit}
            required
            disabled={tps.loading || tps.error}
          />
        </FormGroup>
        <FormGroup>
          <Button
            size="sm"
            color="warning"
            title="reset"
            className="me-sm-2"
            onClick={() => this.editTpsToggle()}>
            <FontAwesomeIcon icon={faRefresh} />
          </Button>
          {isEdited && (
            <Button size="sm" color="success" title="update">
              <FontAwesomeIcon icon={faCheck} />
            </Button>
          )}
        </FormGroup>
      </Form>
    ) : (
      <span>
        <Label className="me-sm-4">TPS Limit: {channelTpsLimit}</Label>
        <Button
          size="sm"
          color="warning"
          title="edit"
          className="me-sm-2"
          onClick={() => this.editTpsToggle()}>
          <FontAwesomeIcon icon={faPencil} />
        </Button>
        <Button
          size="sm"
          color="danger"
          title="delete"
          onClick={() => actions.onDeleteTps(channelId)}>
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      </span>
    );

    return (
      <CardContainer
        title={`${_.upperCase(channel)} pre-filters`}
        kpi={tpsForm}
        action={
          <Button size="sm" color="primary" onClick={() => this.toggleFilterModal('Add')}>
            <FontAwesomeIcon icon={faPlus} /> Add filter
          </Button>
        }>
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
          }
          columns={header}
          data={filterList}
          noDataText="No filters found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          defaultPageSize={10}
          minRows={3}
          className="-highlight  -striped"
        />
        <PrefilterFormContainer
          type={type}
          channel={channel}
          channelId={channelId}
          formData={filterData}
        />
      </CardContainer>
    );
  }
}

Prefilter.propTypes = {
  channel: PropTypes.string.isRequired,
  prefilter: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  channels: PropTypes.array.isRequired,
  toggleForm: PropTypes.func.isRequired,
  fetchChannels: PropTypes.func.isRequired
};

export default Prefilter;
