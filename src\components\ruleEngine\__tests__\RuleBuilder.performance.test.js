import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { createStore } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import RuleBuilder from '../RuleBuilder';

// Mock the debounced search hook
jest.mock('hooks/useDebouncedSearch', () => ({
  useMultipleDebouncedSearches: (searchKeys) => {
    const [searches, setSearches] = React.useState(
      searchKeys.reduce((acc, key) => ({ ...acc, [key]: '' }), {})
    );

    return {
      searches,
      debouncedSearches: searches,
      handleSearchChange: (key) => (event) => {
        setSearches((prev) => ({ ...prev, [key]: event.target.value }));
      }
    };
  }
}));

// Mock store
const createMockStore = () => {
  const initialState = {};
  return createStore(() => initialState);
};

describe('RuleBuilder Performance Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const defaultProps = {
    channel: 'frm',
    formName: 'create',
    helperList: {
      frm: {
        prefix: [
          { name: 'TXN', description: 'Transaction prefix' },
          { name: 'CARD', description: 'Card prefix' },
          { name: 'MERCHANT', description: 'Merchant prefix' }
        ],
        functions: [
          { name: 'COUNT', description: 'Count function' },
          { name: 'SUM', description: 'Sum function' },
          { name: 'AVG', description: 'Average function' }
        ],
        counters: [
          { name: 'DAILY_COUNT', description: 'Daily counter' },
          { name: 'WEEKLY_COUNT', description: 'Weekly counter' }
        ],
        parameters: [
          { name: 'AMOUNT', description: 'Transaction amount' },
          { name: 'CURRENCY', description: 'Currency code' }
        ],
        operators: [
          { name: '>', description: 'Greater than' },
          { name: '<', description: 'Less than' },
          { name: '==', description: 'Equal to' }
        ]
      }
    },
    prefilterLists: {
      data: [
        { categoryName: 'Country', listName: 'High Risk Countries' },
        { categoryName: 'BIN', listName: 'Blocked BINs' }
      ]
    },
    combinedRuleList: [
      { code: '1', logic: 'TXN.AMOUNT > 1000', name: 'Rule 1' },
      { code: '2', logic: 'CARD.TYPE == "CREDIT"', name: 'Rule 2' }
    ],
    ruleData: {
      code: null,
      methodType: 'GET',
      logic: '',
      comments: ''
    },
    validation: { status: true, message: '' },
    ruleCreationActions: {
      onFetchDSLHelpers: jest.fn(),
      onVerifyDSL: jest.fn().mockResolvedValue({}),
      onClearValidation: jest.fn()
    },
    updateRuleData: jest.fn(),
    invalidateLogic: jest.fn(),
    moduleType: 'acquirer'
  };

  const renderWithStore = (component) => render(<Provider store={store}>{component}</Provider>);

  it('should render efficiently with large helper lists', () => {
    // Create large helper lists to test performance
    const largeHelperList = {
      frm: {
        prefix: Array.from({ length: 100 }, (_, i) => ({
          name: `PREFIX_${i}`,
          description: `Prefix ${i} description`
        })),
        functions: Array.from({ length: 50 }, (_, i) => ({
          name: `FUNCTION_${i}`,
          description: `Function ${i} description`
        })),
        counters: Array.from({ length: 75 }, (_, i) => ({
          name: `COUNTER_${i}`,
          description: `Counter ${i} description`
        }))
      }
    };

    const propsWithLargeLists = {
      ...defaultProps,
      helperList: largeHelperList
    };

    performanceMonitor.startTiming('large_list_render');

    renderWithStore(<RuleBuilder {...propsWithLargeLists} />);

    const duration = performanceMonitor.endTiming('large_list_render');
    expect(duration).toBeLessThan(200); // Should render within 200ms even with large lists
  });

  it('should handle search input efficiently with debouncing', async () => {
    renderWithStore(<RuleBuilder {...defaultProps} />);

    const searchInput = screen.getByDisplayValue('');

    performanceMonitor.startTiming('search_input_handling');

    // Simulate rapid typing
    for (let i = 0; i < 10; i++) fireEvent.change(searchInput, { target: { value: `TXN${i}` } });

    const duration = performanceMonitor.endTiming('search_input_handling');
    expect(duration).toBeLessThan(50); // Should handle rapid input efficiently
  });

  it('should memoize list computations correctly', () => {
    const { rerender } = renderWithStore(<RuleBuilder {...defaultProps} />);

    // Re-render with same props
    rerender(
      <Provider store={store}>
        <RuleBuilder {...defaultProps} />
      </Provider>
    );

    // Lists should be memoized and not recomputed
    expect(screen.getByText('TXN')).toBeInTheDocument();
    expect(screen.getByText('COUNT')).toBeInTheDocument();
  });

  it('should validate DSL asynchronously without blocking UI', async () => {
    const mockVerifyDSL = jest
      .fn()
      .mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100)));

    const propsWithAsyncValidation = {
      ...defaultProps,
      ruleData: { ...defaultProps.ruleData, logic: 'TXN.AMOUNT > 1000' },
      ruleCreationActions: {
        ...defaultProps.ruleCreationActions,
        onVerifyDSL: mockVerifyDSL
      }
    };

    renderWithStore(<RuleBuilder {...propsWithAsyncValidation} />);

    const validateButton = screen.getByText('Validate');

    performanceMonitor.startTiming('async_validation');

    fireEvent.click(validateButton);

    // Should not block immediately
    const immediateTime = performanceMonitor.endTiming('async_validation');
    expect(immediateTime).toBeLessThan(10);

    // Wait for async operation to complete
    await waitFor(() => {
      expect(mockVerifyDSL).toHaveBeenCalled();
    });
  });

  it('should handle invalid logic check efficiently', () => {
    const propsWithInvalidLogic = {
      ...defaultProps,
      ruleData: { ...defaultProps.ruleData, logic: 'TXN.AMOUNT > 1000' }, // Same as existing rule
      combinedRuleList: [{ code: '1', logic: 'TXN.AMOUNT > 1000', name: 'Existing Rule' }]
    };

    performanceMonitor.startTiming('invalid_logic_check');

    renderWithStore(<RuleBuilder {...propsWithInvalidLogic} />);

    const duration = performanceMonitor.endTiming('invalid_logic_check');
    expect(duration).toBeLessThan(30);
  });

  it('should not cause memory leaks during tab switching', () => {
    renderWithStore(<RuleBuilder {...defaultProps} />);

    performanceMonitor.takeMemorySnapshot('before_tab_switching');

    // Simulate tab switching multiple times
    const tabs = ['Prefix', 'Parameters', 'Functions', 'Counters'];

    tabs.forEach((tabName) => {
      const tab = screen.getByText(tabName);
      fireEvent.click(tab);
    });

    performanceMonitor.takeMemorySnapshot('after_tab_switching');
    performanceMonitor.checkMemoryLeaks();

    // Test passes if no memory leak warnings
    expect(true).toBe(true);
  });

  it('should handle appendDSL operations efficiently', () => {
    const updateRuleData = jest.fn();
    const propsWithMockUpdate = {
      ...defaultProps,
      updateRuleData
    };

    renderWithStore(<RuleBuilder {...propsWithMockUpdate} />);

    performanceMonitor.startTiming('append_dsl_operation');

    // Click on a DSL element to append it
    const dslElement = screen.getByText('TXN');
    fireEvent.click(dslElement);

    const duration = performanceMonitor.endTiming('append_dsl_operation');
    expect(duration).toBeLessThan(20);
    expect(updateRuleData).toHaveBeenCalledWith('logic', ' TXN ');
  });

  it('should maintain stable references for memoized callbacks', () => {
    let capturedAppendDSL = null;

    const TestWrapper = (props) => {
      const appendDSL = React.useCallback(
        (text) => {
          // eslint-disable-next-line react/prop-types
          props.updateRuleData('logic', `${props.ruleData.logic + text} `);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps, react/prop-types
        [props.ruleData.logic, props.updateRuleData]
      );

      capturedAppendDSL = appendDSL;
      return <div>Test</div>;
    };

    const { rerender } = render(<TestWrapper {...defaultProps} />);
    const initialAppendDSL = capturedAppendDSL;

    // Re-render with same logic
    rerender(<TestWrapper {...defaultProps} />);

    // Reference should be stable when dependencies haven't changed
    expect(capturedAppendDSL).toBe(initialAppendDSL);
  });
});
