import responses from 'mocks/responses';

import * as actions from 'actions/caseReviewActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const { caseAssignment } = responses;
const {
  fraudTypes,
  investigatedTxns,
  selectedCase,
  buckets,
  closeCaseBuckets,
  fraudTypesWithBuckets,
  liability,
  transaction,
  bucketCases
} = caseAssignment;

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  moduleType: 'acquirer'
};

const conf = {
  bucket: 'OpenCases',
  role: 'checker',
  pageNo: 1,
  pageRecords: 10,
  sortBy: 'weight',
  sortOrder: 'desc',
  filterCondition: []
};

const mockedStore = {
  auth: { userCreds, moduleType: 'acquirer' },
  caseAssignment: {
    cases: {
      frm: {
        list: bucketCases.cases,
        conf,
        count: bucketCases.count,
        isLastPage: bucketCases.isLastPage,
        loader: false,
        error: false,
        errorMessage: ''
      }
    },
    selectedCase
  }
};

describe('case assignment actions', () => {
  it('should fetch liability list', () => {
    const expectedActions = [
      { type: types.ON_LIABILITY_LIST_FETCH_LOADING },
      { type: types.ON_SUCCESSFUL_LIABILITY_LIST_FETCH, response: liability }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchLiabilityList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch fraud type list', () => {
    const expectedActions = [
      { type: types.ON_FRAUD_TYPE_LIST_FETCH_LOADING },
      { type: types.ON_SUCCESSFUL_FRAUD_TYPE_LIST_FETCH, response: fraudTypes }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchFraudTypesList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch past investigations', () => {
    const formData = {
      channel: 'frm',
      entityId: 'Agent10036',
      data: {
        pageNo: 1,
        pageRecords: 10
      }
    };
    const expectedActions = [
      { type: types.ON_FETCH_PAST_INVESTIGATED_TXNS_LOADING, channel: 'frm' },
      {
        type: types.ON_SUCCESSFUL_FETCH_PAST_INVESTIGATED_TXNS,
        response: investigatedTxns,
        entityId: formData.entityId,
        channel: 'frm'
      }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchPastInvestigations(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should close a case', () => {
    const formData = {
      remark: 'explanation',
      checkerToBeEscalatedTo: 1,
      investigationVerdictId: 1,
      caseRefNo: 1,
      bucketId: 1
    };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_VERDICT_MODAL, channel: 'frm' },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Case(s) closed successfully' } },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onCloseCase(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should select a case', () => {
    const expectedActions = [
      { type: types.ON_FETCH_TRANSACTION_DETAIL_LOADING },
      { type: types.ON_SUCCESSFUL_FETCH_TRANSACTION_DETAIL, response: transaction },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_DOCUMENT_STATUS_LOADING }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSelectCase(selectedCase)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should clear the selected case', () => {
    const expectedActions = { type: types.ON_CLEAR_SELECTED_CASE };
    expect(actions.onClearSelectedCase()).toEqual(expectedActions);
  });

  it('should request supervisor approval', () => {
    const formData = {
      remark: 'explanation',
      checkerToBeEscalatedTo: 1,
      investigationVerdictId: 1,
      caseRefNo: 1,
      bucketId: 1
    };
    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_VERDICT_MODAL, channel: 'frm' },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully marked case(s) for checker approval' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onRequestSupervisorApproval(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should respond to supervisor approval', () => {
    const formData = {
      caseRefNo: '123',
      comment: 'Approved',
      isApproved: true,
      bucketId: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESSFUL_SUPERVISOR_APPROVAL,
        newStatus: 'Closed',
        data: formData
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully approved case(s) for closure' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: ['123'], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSupervisorApproval(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch users case buckets', () => {
    const expectedActions = [
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_SUCCESSFUL_BUCKETS_FETCH, channel: 'frm', response: buckets }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchBuckets('checker', 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch users bucket cases', () => {
    const expectedActions = [
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      {
        type: types.ON_SUCCESSFUL_CASES_FETCH,
        channel: 'frm',
        response: bucketCases,
        conf,
        refresh: false
      }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchCases(conf, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch close case buckets', () => {
    const expectedActions = [
      { type: types.ON_FETCH_CLOSE_CASE_BUCKETS_LOADING },
      { type: types.ON_FETCH_CLOSE_CASE_BUCKETS_SUCCESS, response: closeCaseBuckets }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchCloseCaseBuckets()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should remove cases from list', () => {
    const expectedActions = {
      type: types.ON_REMOVE_CASES_FROM_LIST,
      cases: ['123'],
      channel: 'frm'
    };
    expect(actions.onRemoveCasesFromList(['123'], 'frm')).toEqual(expectedActions);
  });

  it('should fetch fraud types with buckets', () => {
    const expectedActions = [
      { type: types.ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_LOADING },
      { type: types.ON_FETCH_FRAUD_TYPES_WITH_BUCKETS_SUCCESS, response: fraudTypesWithBuckets }
    ];
    const store = mockStore({ caseAssignment: {} });

    return store.dispatch(actions.onFetchFraudTypesWithBuckets()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Request Partner Approval', () => {
    const formData = {
      remark: 'explanation',
      investigationLiability: 1,
      bucketId: 1,
      investigationVerdictId: 1,
      merchantId: '123',
      caseRefNo: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_VERDICT_MODAL, channel: 'frm' },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully marked case(s) for partner approval' }
      },
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onRequestPartnerApproval(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch XChannel List', () => {
    const expectedActions = [
      { type: types.ON_FETCH_XCHANNEL_LIST_LOADING },
      { type: types.ON_FETCH_XCHANNEL_LIST_SUCCESS, response: responses.caseAssignment.xChannel }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchXChannelList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Snooze Conditions List', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SNOOZE_CONDITIONS_LIST_LOADING },
      {
        type: types.ON_FETCH_SNOOZE_CONDITIONS_LIST_SUCCESS,
        response: responses.caseAssignment.snoozeConditions
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSnoozeConditionsList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Cases From Master Queue', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: {
          message: 'Successfully pulled cases from master queue. Refresh after sometime.'
        }
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCasesFromMasterQueue()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Add Rules To Case', () => {
    const formData = { caseRefNo: 1, channel: 'frm', ruleIds: [] };

    const expectedActions = [
      { type: types.ON_REMOVE_CASES_FROM_LIST, cases: [1], channel: 'frm' },
      { type: types.ON_BUCKETS_FETCH_LOADING, channel: 'frm' },
      { type: types.ON_CASES_FETCH_LOADING, channel: 'frm', conf },
      { type: types.ON_FETCH_CASE_DETAIL_LOADING },
      { type: types.ON_FETCH_UDS_ENTITY_DETAILS_LOADING, entity: 'merchant' },
      { type: types.ON_FETCH_SELECTED_CASE_LOGS_LOADING },
      { type: types.ON_FETCH_SELECTED_ENTITY_LOGS_LOADING },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Successfully added rules to case.' }
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onAddRulesToCase(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
