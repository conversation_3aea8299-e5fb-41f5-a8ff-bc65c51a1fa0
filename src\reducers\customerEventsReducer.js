import objectAssign from 'object-assign';

import {
  ON_FETCH_CUSTOMER_EVENTS_LOADING,
  ON_FETCH_CUSTOMER_EVENTS_FAILURE,
  ON_FETCH_CUSTOMER_EVENTS_SUCCESS
} from 'constants/actionTypes';

import initialState from './initialState';

export default function alertReducer(state = initialState.customerEvents, action) {
  switch (action.type) {
    case ON_FETCH_CUSTOMER_EVENTS_LOADING:
      return objectAssign({}, state, {
        customerId: action.customerId,
        custAccountNumber: action.custAccountNumber,
        list: [],
        loader: true,
        error: false,
        errorMessage: ''
      });
    case ON_FETCH_CUSTOMER_EVENTS_SUCCESS:
      return objectAssign({}, state, {
        loader: false,
        list: action.response.customerEventResponse
      });
    case ON_FETCH_CUSTOMER_EVENTS_FAILURE:
      return objectAssign({}, state, {
        loader: false,
        error: true,
        errorMessage: action.response.errorMessage
      });
    default:
      return state;
  }
}
