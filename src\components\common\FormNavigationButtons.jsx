import PropTypes from 'prop-types';
import React from 'react';
import { FormGroup, Button } from 'reactstrap';

const FormNavigationButtons = ({
  prev,
  next,
  disableNext,
  prevLabel = 'Back',
  nextLabel = 'Next',
  submit = false
}) => {
  let flexClass = 'd-flex mt-4 justify-content-end';
  if ((next || submit) && prev) flexClass = 'd-flex mt-4 justify-content-between';
  else if (!next && !submit) flexClass = 'd-flex mt-4 justify-content-start';

  const nextButtonProps = {
    ...(next && { onClick: next }),
    ...(submit && { type: 'submit', color: 'success' }),
    disabled: disableNext
  };
  return (
    <FormGroup className={flexClass}>
      {prev && (
        <Button size="sm" onClick={prev}>
          {prevLabel}
        </Button>
      )}
      {(next || submit) && (
        <Button size="sm" color="primary" {...nextButtonProps}>
          {nextLabel}
        </Button>
      )}
    </FormGroup>
  );
};

FormNavigationButtons.propTypes = {
  submit: PropTypes.bool,
  disableNext: PropTypes.bool,
  prevLabel: PropTypes.string,
  nextLabel: PropTypes.string,
  prev: PropTypes.func,
  next: PropTypes.func
};

export default FormNavigationButtons;
