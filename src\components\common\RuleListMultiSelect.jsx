import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { MultiSelect } from 'react-multi-select-component';
import { Label } from 'reactstrap';

function RuleListMultiSelect({
  channel,
  ruleNames,
  setRuleList,
  fetchRuleNamesList,
  ruleList = []
}) {
  useEffect(() => {
    if (ruleNames.list[channel].length === 0 && !ruleNames.loader) fetchRuleNamesList(channel);
  }, [channel]);

  const violatedRulesOptions = ruleNames.list[channel]?.map((d) => ({
    label: d.name,
    value: d.code,
    disabled: ruleList.find((rule) => rule.value === 'NA')
  }));

  const violationOptions = [
    {
      label: 'none',
      value: 'NA',
      disabled: ruleList.length > 0 && !ruleList.find((rule) => rule.value === 'NA')
    },
    ...(violatedRulesOptions || [])
  ];

  return (
    <>
      <Label>Violated Rules</Label>
      <MultiSelect
        options={violationOptions}
        labelledBy="-- SELECT --"
        name="violatedRules"
        value={ruleList}
        onChange={setRuleList}
        hasSelectAll={false}
        overrideStrings={{
          selectSomeItems: '-- SELECT --'
        }}
      />
    </>
  );
}

RuleListMultiSelect.propTypes = {
  channel: PropTypes.string.isRequired,
  ruleList: PropTypes.array.isRequired,
  ruleNames: PropTypes.object.isRequired,
  setRuleList: PropTypes.func.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired
};

export default RuleListMultiSelect;
