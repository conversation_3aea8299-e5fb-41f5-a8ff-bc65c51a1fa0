import { unionBy, unionWith } from 'lodash';
import objectAssign from 'object-assign';

import {
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS,
  ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS,
  ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS,
  ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS,
  ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS,
  ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE,
  ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING,
  ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS,
  ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE,
  ON_FETCH_TOP_MERCHANT_COUNT_LOADING,
  ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS,
  ON_FETCH_TOP_MERCHANT_COUNT_FAILURE,
  ON_FETCH_TOP_MERCHANT_DATA_LOADING,
  ON_FETCH_TOP_MERCHANT_DATA_SUCCESS,
  ON_FETCH_TOP_MERCHANT_DATA_FAILURE,
  ON_FETCH_NBFC_TRXNS_COUNT_LOADING,
  ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS,
  ON_FETCH_NBFC_TRXNS_COUNT_FAILURE,
  ON_FETCH_NBFC_TRXNS_DATA_LOADING,
  ON_FETCH_NBFC_TRXNS_DATA_SUCCESS,
  ON_FETCH_NBFC_TRXNS_DATA_FAILURE,
  ON_FETCH_HRC_TRXNS_COUNT_LOADING,
  ON_FETCH_HRC_TRXNS_COUNT_SUCCESS,
  ON_FETCH_HRC_TRXNS_COUNT_FAILURE,
  ON_FETCH_HRC_TRXNS_DATA_LOADING,
  ON_FETCH_HRC_TRXNS_DATA_SUCCESS,
  ON_FETCH_HRC_TRXNS_DATA_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function rfiReportsReducer(state = initialState.rfiReports, action) {
  switch (action.type) {
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_LOADING:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, initialState.rfiReports.highValueClosedAccount, {
          count: objectAssign({}, initialState.rfiReports.highValueClosedAccount.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_SUCCESS:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, state.highValueClosedAccount, {
          count: objectAssign({}, state.highValueClosedAccount.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_COUNT_FAILURE:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, state.highValueClosedAccount, {
          count: objectAssign({}, state.highValueClosedAccount.count, {
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_LOADING:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, state.highValueClosedAccount, {
          data: objectAssign({}, state.highValueClosedAccount.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_SUCCESS:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, state.highValueClosedAccount, {
          data: objectAssign({}, state.highValueClosedAccount.data, {
            loader: false,
            records:
              action.conf.pageNo === 1
                ? action.response.merchantDetails
                : unionBy(
                    state.highValueClosedAccount.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_CLOSED_ACCOUNT_DATA_FAILURE:
      return objectAssign({}, state, {
        highValueClosedAccount: objectAssign({}, state.highValueClosedAccount, {
          data: objectAssign({}, state.highValueClosedAccount.data, {
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_LOADING:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, initialState.rfiReports.highValueNewAccount, {
          count: objectAssign({}, initialState.rfiReports.highValueNewAccount.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_SUCCESS:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, state.highValueNewAccount, {
          count: objectAssign({}, state.highValueNewAccount.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_COUNT_FAILURE:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, state.highValueNewAccount, {
          count: objectAssign({}, state.highValueNewAccount.count, {
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_LOADING:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, state.highValueNewAccount, {
          data: objectAssign({}, state.highValueNewAccount.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_SUCCESS:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, state.highValueNewAccount, {
          data: objectAssign({}, state.highValueNewAccount.data, {
            loader: false,
            records:
              action.conf.pageNo === 1
                ? action.response.merchantDetails
                : unionBy(
                    state.highValueNewAccount.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_HIGH_VALUE_NEW_ACCOUNT_DATA_FAILURE:
      return objectAssign({}, state, {
        highValueNewAccount: objectAssign({}, state.highValueNewAccount, {
          data: objectAssign({}, state.highValueNewAccount.data, {
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_LOADING:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, initialState.rfiReports.fraudToSaleRatio, {
          count: objectAssign({}, initialState.rfiReports.fraudToSaleRatio.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_SUCCESS:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, state.fraudToSaleRatio, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.fraudToSaleRatio.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_COUNT_FAILURE:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, state.fraudToSaleRatio, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.fraudToSaleRatio.count, {
            value:
              state.fraudToSaleRatio.cummValue !== action.conf?.cummValue
                ? 0
                : state.fraudToSaleRatio.count.value,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_LOADING:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, state.fraudToSaleRatio, {
          data: objectAssign({}, state.fraudToSaleRatio.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_SUCCESS:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, state.fraudToSaleRatio, {
          data: objectAssign({}, state.fraudToSaleRatio.data, {
            loader: false,
            records:
              action.conf.pageNo === 1 || action.conf.cummValue !== state.fraudToSaleRatio.cummValue
                ? action.response.merchantDetails
                : unionBy(
                    state.fraudToSaleRatio.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_FRAUD_TO_SALE_RATIO_DATA_FAILURE:
      return objectAssign({}, state, {
        fraudToSaleRatio: objectAssign({}, state.fraudToSaleRatio, {
          data: objectAssign({}, state.fraudToSaleRatio.data, {
            loader: false,
            records:
              action.conf.cummValue !== state.fraudToSaleRatio.cummValue
                ? []
                : state.fraudToSaleRatio.data.records,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_COUNT_LOADING:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, initialState.rfiReports.topMerchant, {
          count: objectAssign({}, initialState.rfiReports.topMerchant.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_COUNT_SUCCESS:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, state.topMerchant, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.topMerchant.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_COUNT_FAILURE:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, state.topMerchant, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.topMerchant.count, {
            value:
              state.topMerchant.cummValue !== action.conf?.cummValue
                ? 0
                : state.topMerchant.count.value,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_DATA_LOADING:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, state.topMerchant, {
          data: objectAssign({}, state.topMerchant.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_DATA_SUCCESS:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, state.topMerchant, {
          data: objectAssign({}, state.topMerchant.data, {
            loader: false,
            records:
              action.conf.pageNo === 1 || action.conf.cummValue !== state.topMerchant.cummValue
                ? action.response.merchantDetails
                : unionBy(
                    state.topMerchant.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_TOP_MERCHANT_DATA_FAILURE:
      return objectAssign({}, state, {
        topMerchant: objectAssign({}, state.topMerchant, {
          data: objectAssign({}, state.topMerchant.data, {
            loader: false,
            records:
              action.conf.cummValue !== state.topMerchant.cummValue
                ? []
                : state.topMerchant.data.records,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_COUNT_LOADING:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, initialState.rfiReports.nbfcTrxns, {
          count: objectAssign({}, initialState.rfiReports.nbfcTrxns.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_COUNT_SUCCESS:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, state.nbfcTrxns, {
          cummValue: action.conf?.cummValue,
          period: action.conf?.period,
          count: objectAssign({}, state.nbfcTrxns.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_COUNT_FAILURE:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, state.nbfcTrxns, {
          cummValue: action.conf?.cummValue,
          period: action.conf?.period,
          count: objectAssign({}, state.nbfcTrxns.count, {
            value:
              state.nbfcTrxns.cummValue !== action.conf?.cummValue ||
              state.nbfcTrxns.period !== action.conf?.period
                ? 0
                : state.nbfcTrxns.count.value,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_DATA_LOADING:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, state.nbfcTrxns, {
          data: objectAssign({}, state.nbfcTrxns.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_DATA_SUCCESS:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, state.nbfcTrxns, {
          data: objectAssign({}, state.nbfcTrxns.data, {
            loader: false,
            records:
              action.conf.pageNo === 1 ||
              action.conf.cummValue !== state.nbfcTrxns.cummValue ||
              action.conf.period !== state.nbfcTrxns.period
                ? action.response.merchantDetails
                : unionBy(
                    state.nbfcTrxns.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_NBFC_TRXNS_DATA_FAILURE:
      return objectAssign({}, state, {
        nbfcTrxns: objectAssign({}, state.nbfcTrxns, {
          data: objectAssign({}, state.nbfcTrxns.data, {
            loader: false,
            records:
              action.conf.cummValue !== state.nbfcTrxns.cummValue ||
              action.conf.period !== state.nbfcTrxns.period
                ? []
                : state.nbfcTrxns.data.records,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_COUNT_LOADING:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, initialState.rfiReports.hrcTrxns, {
          count: objectAssign({}, initialState.rfiReports.hrcTrxns.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_COUNT_SUCCESS:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, state.hrcTrxns, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.hrcTrxns.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_COUNT_FAILURE:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, state.hrcTrxns, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.hrcTrxns.count, {
            value:
              state.hrcTrxns.cummValue !== action.conf?.cummValue ? 0 : state.hrcTrxns.count.value,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_DATA_LOADING:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, state.hrcTrxns, {
          data: objectAssign({}, state.hrcTrxns.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_DATA_SUCCESS:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, state.hrcTrxns, {
          data: objectAssign({}, state.hrcTrxns.data, {
            loader: false,
            records:
              action.conf.pageNo === 1 || action.conf.cummValue !== state.hrcTrxns.cummValue
                ? action.response.merchantDetails
                : unionWith(
                    state.hrcTrxns.data.records,
                    action.response.merchantDetails,
                    (x, y) =>
                      x.merchantID === y.merchantID && x.customerIdentifier === y.customerIdentifier
                  )
          })
        })
      });
    case ON_FETCH_HRC_TRXNS_DATA_FAILURE:
      return objectAssign({}, state, {
        hrcTrxns: objectAssign({}, state.hrcTrxns, {
          data: objectAssign({}, state.hrcTrxns.data, {
            records:
              action.conf.cummValue !== state.hrcTrxns.cummValue ? [] : state.hrcTrxns.data.records,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_COUNT_LOADING:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, initialState.rfiReports.unusualDeclineTurnover, {
          count: objectAssign({}, initialState.rfiReports.unusualDeclineTurnover.count, {
            loader: true
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_COUNT_SUCCESS:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, state.unusualDeclineTurnover, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.unusualDeclineTurnover.count, {
            loader: false,
            value: action.response.count
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_COUNT_FAILURE:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, state.unusualDeclineTurnover, {
          cummValue: action.conf?.cummValue,
          count: objectAssign({}, state.unusualDeclineTurnover.count, {
            value:
              state.unusualDeclineTurnover.cummValue !== action.conf?.cummValue
                ? 0
                : state.unusualDeclineTurnover.count.value,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_DATA_LOADING:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, state.unusualDeclineTurnover, {
          data: objectAssign({}, state.unusualDeclineTurnover.data, {
            loader: true
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_DATA_SUCCESS:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, state.unusualDeclineTurnover, {
          data: objectAssign({}, state.unusualDeclineTurnover.data, {
            loader: false,
            records:
              action.conf.pageNo === 1 ||
              action.conf.cummValue !== state.unusualDeclineTurnover.cummValue
                ? action.response.merchantDetails
                : unionBy(
                    state.unusualDeclineTurnover.data.records,
                    action.response.merchantDetails,
                    'merchantID'
                  )
          })
        })
      });
    case ON_FETCH_UNUSUAL_DECLINE_DATA_FAILURE:
      return objectAssign({}, state, {
        unusualDeclineTurnover: objectAssign({}, state.unusualDeclineTurnover, {
          data: objectAssign({}, state.unusualDeclineTurnover.data, {
            records:
              action.conf.cummValue !== state.unusualDeclineTurnover.cummValue
                ? []
                : state.unusualDeclineTurnover.data.records,
            loader: false,
            error: true,
            errorMessage: action.response?.message || 'Unknown error'
          })
        })
      });
    default:
      return state;
  }
}
