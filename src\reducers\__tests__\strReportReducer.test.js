import responses from 'mocks/responses';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import strReportReducer from 'reducers/strReportReducer';

describe('strReport Reducer', () => {
  it('should return the intial state', () => {
    expect(strReportReducer(undefined, {})).toEqual(initialState.strReport);
  });

  it('should handle ON_FETCH_STR_REPORT_MASTERS_LOADING', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_MASTERS_LOADING
        }
      )
    ).toEqual({
      masters: {
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_MASTERS_SUCCESS', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_MASTERS_SUCCESS,
          response: responses.strReport.masters
        }
      )
    ).toEqual({
      masters: {
        data: responses.strReport.masters,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_MASTERS_FAILURE', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_MASTERS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      masters: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_DETAILS_LOADING', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_DETAILS_LOADING
        }
      )
    ).toEqual({
      details: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_DETAILS_SUCCESS', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_DETAILS_SUCCESS,
          response: responses.strReport.details
        }
      )
    ).toEqual({
      details: {
        data: responses.strReport.details,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_DETAILS_FAILURE', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_DETAILS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      details: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_LOGS_LOADING', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_LOGS_LOADING
        }
      )
    ).toEqual({
      history: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_LOGS_SUCCESS', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_LOGS_SUCCESS,
          response: responses.strReport.history
        }
      )
    ).toEqual({
      history: {
        data: responses.strReport.history,
        loader: false
      }
    });
  });

  it('should handle ON_FETCH_STR_REPORT_LOGS_FAILURE', () => {
    expect(
      strReportReducer(
        {},
        {
          type: types.ON_FETCH_STR_REPORT_LOGS_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      history: {
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });
});
