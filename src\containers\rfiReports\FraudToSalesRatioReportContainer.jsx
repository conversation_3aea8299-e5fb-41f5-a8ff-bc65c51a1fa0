import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  onFetchFraudToSaleRatioCount,
  onFetchFraudToSaleRatioData
} from 'actions/rfiReportActions';
import FraudToSalesRatioReport from 'components/rfiReports/FraudToSalesRatioReport';

const mapStateToProps = (state) => {
  return {
    fraudToSaleRatio: state.rfiReports.fraudToSaleRatio
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchFraudToSalesRatioCount: bindActionCreators(onFetchFraudToSaleRatioCount, dispatch),
    fetchFraudToSalesRatioData: bindActionCreators(onFetchFraudToSaleRatioData, dispatch)
  };
};

const FraudToSalesRatioReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(FraudToSalesRatioReport);

export default FraudToSalesRatioReportContainer;
