import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import {
  onFetchFraudToSaleRatioCount,
  onFetchFraudToSaleRatioData
} from 'actions/rfiReportActions';
import FraudToSalesRatioReport from 'components/rfiReports/FraudToSalesRatioReport';

const mapStateToProps = (state) => ({
  fraudToSaleRatio: state.rfiReports.fraudToSaleRatio
});

const mapDispatchToProps = (dispatch) => ({
  fetchFraudToSalesRatioCount: bindActionCreators(onFetchFraudToSaleRatioCount, dispatch),
  fetchFraudToSalesRatioData: bindActionCreators(onFetchFraudToSaleRatioData, dispatch)
});

const FraudToSalesRatioReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(FraudToSalesRatioReport);

export default FraudToSalesRatioReportContainer;
