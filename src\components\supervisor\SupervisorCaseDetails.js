import _ from 'lodash';
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'reactstrap';
import { useHistory, useParams } from 'react-router-dom';

import Log from 'containers/common/LogContainer';
import CaseDetailCard from 'containers/common/CaseDetailCardContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import ViolatedRulesCardContainer from 'containers/common/ViolatedRulesCardContainer';
import TransactionDetailCardContainer from 'containers/common/TransactionDetailCardContainer';
import CustomerInfoCardContainer from 'containers/common/CustomerInfoCardContainer';
import MerchantInfoCardContainer from 'containers/common/MerchantInfoCardContainer';
import AgentInfoCardContainer from 'containers/common/AgentInfoCardContainer';

const SupervisorCaseDetails = ({
  txnDetails,
  selectCase,
  selectedCase,
  documentStatus,
  clearSelectedCase
}) => {
  const history = useHistory();
  const { txnId } = useParams();
  useEffect(() => {
    if (txnId) selectCase({ txnId, channel: 'frm' });
  }, [txnId]);

  useEffect(() => {
    selectedCase && (document.title = 'BANKiQ FRC | Supervise Case - ' + selectedCase.caseRefNo);

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [selectedCase]);

  const getActions = () => {
    const { currentStatus, caseRefNo, isHold } = selectedCase;

    return currentStatus == 'New' || currentStatus == 'Open' || currentStatus == 'Rejected' ? (
      <UserListDropdownContainer
        bucket={currentStatus}
        caseId={caseRefNo}
        isHold={isHold}
        channel={'frm'}
        showText
      />
    ) : null;
  };

  const cognitiveResponse = !_.isEmpty(txnDetails.details.cognitiveResponse)
    ? JSON.parse(txnDetails.details.cognitiveResponse)
    : {};
  const cognitiveViolations = _.isEmpty(cognitiveResponse.unusualMethods)
    ? []
    : cognitiveResponse.unusualMethods;

  const entityId = _.has(txnDetails.details, 'entityId') ? txnDetails.details?.entityId?.value : '';

  return (
    <div className={'content-wrapper'}>
      <span className="d-flex justify-content-end mb-3">
        <Button
          outline
          size="sm"
          color="secondary"
          onClick={() => {
            clearSelectedCase();
            history.goBack();
          }}>
          Back
        </Button>
      </span>
      <CaseDetailCard
        action={getActions()}
        caseDetails={selectedCase}
        documentStatus={documentStatus}
        channel={'frm'}
      />
      <TransactionDetailCardContainer channel={'frm'} />

      {_.lowerCase(txnDetails.details.entityCategory) === 'merchant' ? (
        <MerchantInfoCardContainer merchantId={entityId} channel="frm" />
      ) : _.lowerCase(txnDetails.details.entityCategory) === 'customer' ? (
        <CustomerInfoCardContainer customerId={entityId} channel="frm" />
      ) : _.lowerCase(txnDetails.details.entityCategory) === 'agent' ? (
        <AgentInfoCardContainer agentId={entityId} channel="frm" />
      ) : null}

      {!_.isEmpty(txnDetails.details) && (
        <ViolatedRulesCardContainer
          transactionId={selectedCase.txnId}
          txnTimestamp={txnDetails?.details?.transactionInfo?.txnTimestamp}
          reViolatedRules={txnDetails?.details?.reViolatedRules || []}
          cognitiveViolations={cognitiveViolations}
          channel={'frm'}
        />
      )}
      <Log module="Case" id={selectedCase.caseRefNo} />
    </div>
  );
};

SupervisorCaseDetails.propTypes = {
  userName: PropTypes.string.isRequired,
  userslist: PropTypes.array.isRequired,
  txnDetails: PropTypes.object.isRequired,
  selectedCase: PropTypes.object.isRequired,
  documentStatus: PropTypes.object.isRequired,
  clearSelectedCase: PropTypes.func.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  selectCase: PropTypes.func.isRequired
};

export default SupervisorCaseDetails;
