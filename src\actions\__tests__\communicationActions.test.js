import { mockStore } from 'store/mockStoreConfiguration';
import * as types from 'constants/actionTypes';
import * as actions from 'actions/communicationActions';
import responses from 'mocks/responses';

const mockedStore = {
  customerCommunication: {}
};

describe('communication actions', () => {
  it('should call customer', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';
    const channel = 'frm';
    const entityId = 'entity1';

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Placing call to customer...' }
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onCallCustomer(channel, caseRefNo, entityId)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should email customer', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';
    const channel = 'frm';
    const entityId = 'entity1';
    const caseDetails = { caseId: '123', txnId: '123' };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Email sent to customer' }
      }
    ];

    const store = mockStore(mockedStore);

    return store
      .dispatch(actions.onEmailCustomer({ channel, caseRefNo, entityId, caseDetails }))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should sms customer', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';
    const channel = 'frm';
    const entityId = 'entity1';
    const caseDetails = { caseId: '123', txnId: '123' };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'SMS sent to customer' }
      }
    ];

    const store = mockStore(mockedStore);

    return store
      .dispatch(actions.onSMSCustomer({ channel, caseRefNo, entityId, caseDetails }))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should Fetch Customer Communication Logs', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';
    const channel = 'frm';

    const expectedActions = [
      { type: types.ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_LOADING },
      {
        type: types.ON_FETCH_CUSTOMER_COMMUNICATION_LOGS_SUCCESS,
        response: responses.customerCommunication
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCustomerCommunicationLogs(channel, caseRefNo)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
