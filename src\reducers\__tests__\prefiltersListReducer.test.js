import { remove, differenceBy, isEmpty, concat, has } from 'lodash';
import responses from 'mocks/responses';
import objectAssign from 'object-assign';

import * as types from 'constants/actionTypes';
import initialState from 'reducers/initialState';
import prefiltersListReducer from 'reducers/prefiltersListReducer';

const removeItem = (data, entity, listType) => {
  remove(data, function (item) {
    return listType === 'specializedList' || listType === 'customizedList'
      ? item.identifier === entity.identifier && item.categoryName === entity.categoryName
      : item[entity.keyName] === entity.type;
  });
  return data;
};

const addDataToMerchantLimit = (data, resp, fetchFrom) => {
  if (has(resp, 'merchantdaily')) {
    const filteredResp = differenceBy(resp.merchantdaily, data.merchantdaily, 'merchantId');
    return isEmpty(data) || (fetchFrom && fetchFrom === 'search')
      ? resp
      : objectAssign({}, resp, {
          merchantdaily: concat(data.merchantdaily, filteredResp)
        });
  } else {
    const filteredResp = differenceBy(resp.merchant, data.merchant, 'id');
    return isEmpty(data) || (fetchFrom && fetchFrom === 'search')
      ? resp
      : objectAssign({}, resp, {
          merchant: concat(data.merchant, filteredResp)
        });
  }
};

describe('prefilters List Reducer', () => {
  it('should return the intial state', () => {
    expect(prefiltersListReducer(undefined, {})).toEqual(initialState.prefiltersList);
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          specializedList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_LOADING
        }
      )
    ).toEqual({
      specializedList: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          specializedList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_SUCCESS,
          response: responses.listsandlimits.list
        }
      )
    ).toEqual({
      specializedList: {
        data: responses.listsandlimits.list,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          specializedList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      specializedList: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          category: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING
        }
      )
    ).toEqual({
      category: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          category: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS,
          response: responses.listsandlimits.categories
        }
      )
    ).toEqual({
      category: {
        data: responses.listsandlimits.categories,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          category: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_CATEGORIES_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      category: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          specializedList: {
            data: responses.listsandlimits.list,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS,
          formData: {
            categoryName: 'IFSC',
            identifier: 'iden1t2',
            remark: 'remark',
            isActive: 1
          }
        }
      )
    ).toEqual({
      specializedList: {
        data: responses.listsandlimits.list,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM', () => {
    expect(
      prefiltersListReducer(
        {
          specializedList: {
            data: responses.listsandlimits.list,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM,
          formData: {
            categoryName: 'IFSC',
            identifier: 'iden1t2',
            remark: 'remark',
            isActive: 1
          }
        }
      )
    ).toEqual({
      specializedList: {
        data: responses.listsandlimits.list,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          limitList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_LOADING
        }
      )
    ).toEqual({
      limitList: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          limitList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_SUCCESS,
          response: responses.listsandlimits.onboardingLimit
        }
      )
    ).toEqual({
      limitList: {
        data: responses.listsandlimits.onboardingLimit,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          limitList: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      limitList: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_TYPE_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          limitType: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_TYPE_LOADING
        }
      )
    ).toEqual({
      limitType: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_TYPE_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          limitType: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_TYPE_SUCCESS,
          response: responses.listsandlimits.limitType
        }
      )
    ).toEqual({
      limitType: {
        data: responses.listsandlimits.limitType,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_TYPE_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          limitType: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_TYPE_FAILURE,
          response: { message: 'error message' }
        }
      )
    ).toEqual({
      limitType: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error message'
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          specializedListTypes: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING
        }
      )
    ).toEqual({
      specializedListTypes: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          specializedListTypes: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS,
          response: responses.listsandlimits.types
        }
      )
    ).toEqual({
      specializedListTypes: {
        data: responses.listsandlimits.types,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          specializedListTypes: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      specializedListTypes: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM', () => {
    const formData = {
      type: 'test',
      keyName: 'onboardingType'
    };
    expect(
      prefiltersListReducer(
        {
          limitList: {
            data: responses.listsandlimits.onboardingLimit,
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM,
          entity: formData
        }
      )
    ).toEqual({
      limitList: {
        data: removeItem(responses.listsandlimits.onboardingLimit, formData, 'limitList'),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_RESET_LIMIT_LIST_WITH_PAGINATION', () => {
    expect(
      prefiltersListReducer(
        {
          limitListWithPagination: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_RESET_LIMIT_LIST_WITH_PAGINATION
        }
      )
    ).toEqual({
      limitListWithPagination: {
        data: {},
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_ADD_TO_LIST_ITEM_CLICK', () => {
    const currentItemInfo = {
      currentListInfo: {
        prefilterName: 'Negative List',
        prefilterId: 1,
        prefilterValue: 'Negative'
      },
      currentIndex: 2,
      currentCategoryName: 'IFSC',
      currentIdentifier: 'iden128'
    };

    expect(
      prefiltersListReducer(
        {
          addToListCurrentItems: {
            currentListInfo: {},
            currentIndex: '',
            currentCategoryName: '',
            currentIdentifier: ''
          }
        },
        {
          type: types.ON_ADD_TO_LIST_ITEM_CLICK,
          currentItemInfo
        }
      )
    ).toEqual({
      addToListCurrentItems: {
        currentListInfo: currentItemInfo.currentListInfo,
        currentIndex: currentItemInfo.currentIndex,
        currentCategoryName: currentItemInfo.currentCategoryName,
        currentIdentifier: currentItemInfo.currentIdentifier
      }
    });
  });

  it('should handle ON_FETCH_ALL_LISTS_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          allLists: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_LISTS_LOADING
        }
      )
    ).toEqual({
      allLists: {
        data: [],
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_LISTS_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          allLists: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_LISTS_SUCCESS,
          response: responses.listsandlimits.types
        }
      )
    ).toEqual({
      allLists: {
        data: responses.listsandlimits.types,
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_ALL_LISTS_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          allLists: {
            data: [],
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_ALL_LISTS_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      allLists: {
        data: [],
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING', () => {
    expect(
      prefiltersListReducer(
        {
          limitListWithPagination: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING
        }
      )
    ).toEqual({
      limitListWithPagination: {
        data: {},
        loader: true,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS', () => {
    expect(
      prefiltersListReducer(
        {
          limitListWithPagination: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS,
          response: responses.listsandlimits.merchantPaginated
        }
      )
    ).toEqual({
      limitListWithPagination: {
        data: addDataToMerchantLimit({}, responses.listsandlimits.merchantPaginated),
        loader: false,
        error: false,
        errorMessage: ''
      }
    });
  });

  it('should handle ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE', () => {
    expect(
      prefiltersListReducer(
        {
          limitListWithPagination: {
            data: {},
            loader: false,
            error: false,
            errorMessage: ''
          }
        },
        {
          type: types.ON_FETCH_LIMIT_LIST_WITH_PAGINATION_FAILURE,
          response: { message: 'error msg' }
        }
      )
    ).toEqual({
      limitListWithPagination: {
        data: {},
        loader: false,
        error: true,
        errorMessage: 'error msg'
      }
    });
  });
});
