import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import {
  onFetchHighValueClosedAccountCount,
  onFetchHighValueClosedAccountData
} from 'actions/rfiReportActions';
import HighValueClosedAccountsReport from 'components/rfiReports/HighValueClosedAccountsReport';

const mapStateToProps = (state) => ({
  highValueClosedAccount: state.rfiReports.highValueClosedAccount
});

const mapDispatchToProps = (dispatch) => ({
  fetchHighValueClosedAccountCount: bindActionCreators(
    onFetchHighValueClosedAccountCount,
    dispatch
  ),
  fetchHighValueClosedAccountData: bindActionCreators(onFetchHighValueClosedAccountData, dispatch)
});

const HighValueClosedAccountsReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(HighValueClosedAccountsReport);

export default HighValueClosedAccountsReportContainer;
