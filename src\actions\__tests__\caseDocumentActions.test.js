import responses from 'mocks/responses';

import * as actions from 'actions/caseDocumentActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const mockedStore = {
  caseDocument: {}
};

describe('caseDocument actions', () => {
  beforeAll(() => {
    function FormDataMock() {
      this.append = jest.fn();
    }
    global.FormData = FormDataMock;
  });

  it('should Fetch Case Documents', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';

    const expectedActions = [
      { type: types.ON_FETCH_CASE_DOCUMENTS_LOADING },
      {
        type: types.ON_FETCH_CASE_DOCUMENTS_SUCCESS,
        response: responses.caseDocument
      }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchCaseDocuments(caseRefNo)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Submit Case Document', () => {
    const caseRefNo = '38f486fe-44f1-4592-a636-792a2f0c5669';
    const formData = { file: 'fileData' };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_FETCH_CASE_DOCUMENTS_LOADING },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Document uploaded successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];

    const store = mockStore(mockedStore);

    return store.dispatch(actions.onSubmitCaseDocument(caseRefNo, formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
