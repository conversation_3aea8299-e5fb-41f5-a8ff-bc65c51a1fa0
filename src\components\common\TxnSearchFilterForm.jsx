import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, FormGroup, Label, Button } from 'reactstrap';
import { MultiSelect } from 'react-multi-select-component';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { findValueInTxnDetail } from '../../utility/utils';
import _ from 'lodash';

const TxnSearchFilterForm = ({
  txnCategory,
  setTxnCategory,
  id,
  categoryList,
  onClickSearchBtn,
  txnDetails
}) => {
  // Generate category options with formatted labels
  const txnCategoryOptions = useMemo(() => {
    return _.map(categoryList, (category) => ({
      label: `${category} - ${findValueInTxnDetail(category, txnDetails) || 'NA'}`,
      value: category
    }));
  }, [categoryList, txnDetails]);

  // handle default selected catagories
  const formattedTxnCategory = useMemo(() => {
    return txnCategory.map((selected) => {
      const matchingOption = txnCategoryOptions.find((option) => option.value === selected.value);
      return matchingOption || selected;
    });
  }, [txnCategory, txnCategoryOptions]);

  return (
    <form id={id} className="mt-3">
      <Row>
        <Col lg="10" md="10" sm="9" xs="8">
          <FormGroup>
            <Label>Select category</Label>
            <MultiSelect
              options={txnCategoryOptions}
              labelledBy="select category"
              name="txnCategory"
              value={formattedTxnCategory}
              onChange={setTxnCategory}
              className={
                formattedTxnCategory.length === 1
                  ? `similar-category-multi-select not-clearable`
                  : `similar-category-multi-select`
              }
            />
          </FormGroup>
        </Col>

        <Col lg="2" md="2" sm="3" xs="4" className="d-flex align-items-end">
          <FormGroup>
            <Button
              size="sm"
              color="primary"
              title="search"
              type="submit"
              disabled={_.isEmpty(txnCategory)}
              onClick={(e) => {
                e.preventDefault();
                onClickSearchBtn();
              }}>
              <FontAwesomeIcon icon={faSearch} className="me-1" /> {'Search'}
            </Button>
          </FormGroup>
        </Col>
      </Row>
    </form>
  );
};

TxnSearchFilterForm.propTypes = {
  id: PropTypes.string.isRequired,
  txnCategory: PropTypes.array.isRequired,
  setTxnCategory: PropTypes.func.isRequired,
  categoryList: PropTypes.array.isRequired,
  onClickSearchBtn: PropTypes.func.isRequired,
  txnDetails: PropTypes.object.isRequired
};

export default TxnSearchFilterForm;
