import { each, isEmpty, isEqual } from 'lodash';

export function inputChangeDataHandler(event, currentConfig, setStateFunction) {
  const { name, value, type, checked } = event.target;
  const activationVal = value === 'disabled' ? 'enabled' : 'disabled';
  const val = type === 'checkbox' ? checked : value;
  const tempConfig = {
    ...currentConfig,
    [name]: {
      value: name === 'activation' ? activationVal : val,
      desc: currentConfig[name]?.desc
    },
    isEdited: true
  };

  setStateFunction(tempConfig);
}

export function decisionMatrixActionChangeHandler(
  event,
  transactionRiskScore,
  setTransactionRiskScore,
  id,
  configurationsData
) {
  const { name, value } = event.target;
  const decisionMatrix = transactionRiskScore?.decisionMatrix?.value || [];
  const configMatrix = configurationsData?.configPoints?.decisionMatrix?.value || [];

  const updatedDecisionMatrix = decisionMatrix.map((item, index) => {
    if (item.id !== id) return item;

    let impliedVerdict;
    if (value === 'ALERT_SUPPRESS_REVERSE' || value === 'ALERT_ESCALATE_REVERSE')
      impliedVerdict = item.reOutcome === 'ACCEPTED' ? 'REJECTED' : 'ACCEPTED';
    else impliedVerdict = configMatrix[index]?.impliedVerdict;

    return {
      ...item,
      [name]: value,
      impliedVerdict
    };
  });

  setTransactionRiskScore({
    ...transactionRiskScore,
    decisionMatrix: {
      value: updatedDecisionMatrix,
      desc: transactionRiskScore?.decisionMatrix?.desc
    },
    isEdited: true
  });
}

export function saveConfigurationsDataHandler(
  event,
  configurationsData,
  currentConfig,
  setStateFunction,
  saveConfigurations
) {
  event.preventDefault();

  setStateFunction((prevState) => ({
    ...prevState,
    isEdited: false
  }));

  const formData = {
    configType: configurationsData.configType.toString(),
    updateConfigPoints: {}
  };

  each(Object.keys(currentConfig), (currentConfItem) => {
    if (currentConfItem === 'isEdited') return;

    const currentItem = currentConfig[currentConfItem];
    const originalItem = configurationsData.configPoints[currentConfItem];

    if (!isEqual(currentItem, originalItem))
      formData.updateConfigPoints[currentConfItem] = currentItem.value;
  });

  if (!isEmpty(formData.updateConfigPoints)) saveConfigurations(formData);
}

export function resetConfigurationsDataHandler(configurationsData, setStateFunction) {
  const tempConfig = {
    ...configurationsData.configPoints,
    isEdited: false
  };

  setStateFunction(tempConfig);
}
