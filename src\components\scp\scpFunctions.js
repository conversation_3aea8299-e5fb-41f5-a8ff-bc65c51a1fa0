import { each, isEmpty, isEqual } from 'lodash';

export function inputChangeDataHandler(event, currentConfig, setStateFunction) {
  const { name, value, type, checked } = event.target;
  const activationVal = value === 'disabled' ? 'enabled' : 'disabled';
  const val = type === 'checkbox' ? checked : value;
  const tempConfig = {
    ...currentConfig,
    [name]: {
      value: name === 'activation' ? activationVal : val,
      desc: currentConfig[name]?.desc
    },
    isEdited: true
  };

  setStateFunction(tempConfig);
}

export function decisionMatrixActionChangeHandler(
  event,
  transactionRiskScore,
  setTransactionRiskScore,
  id,
  configurationsData
) {
  const { name, value } = event.target;

  const updatedDecisionMatrix = transactionRiskScore?.decisionMatrix?.value.map((item, index) =>
    item.id === id
      ? {
          ...item,
          [name]: value,
          impliedVerdict: ['ALERT_SUPPRESS_REVERSE', 'ALERT_ESCALATE_REVERSE'].includes(value)
            ? item.reOutcome === 'ACCEPTED'
              ? 'REJECTED'
              : 'ACCEPTED'
            : configurationsData.configPoints.decisionMatrix.value[index].impliedVerdict
        }
      : item
  );

  const tempConfig = {
    ...transactionRiskScore,
    decisionMatrix: {
      value: updatedDecisionMatrix,
      desc: transactionRiskScore?.decisionMatrix?.desc
    },
    isEdited: true
  };

  setTransactionRiskScore(tempConfig);
}

export function saveConfigurationsDataHandler(
  event,
  configurationsData,
  currentConfig,
  setStateFunction,
  saveConfigurations
) {
  event.preventDefault();

  setStateFunction((prevState) => ({
    ...prevState,
    isEdited: false
  }));

  const formData = {
    configType: configurationsData.configType.toString(),
    updateConfigPoints: {}
  };

  each(Object.keys(currentConfig), (currentConfItem) => {
    if (currentConfItem === 'isEdited') return;

    const currentItem = currentConfig[currentConfItem];
    const originalItem = configurationsData.configPoints[currentConfItem];

    if (!isEqual(currentItem, originalItem)) {
      formData.updateConfigPoints[currentConfItem] = currentItem.value;
    }
  });

  if (!isEmpty(formData.updateConfigPoints)) {
    saveConfigurations(formData);
  }
}

export function resetConfigurationsDataHandler(configurationsData, setStateFunction) {
  const tempConfig = {
    ...configurationsData.configPoints,
    isEdited: false
  };

  setStateFunction(tempConfig);
}
