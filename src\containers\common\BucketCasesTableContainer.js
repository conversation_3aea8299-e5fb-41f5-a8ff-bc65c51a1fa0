import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as toggleActions from 'actions/toggleActions';
import {
  onFetchCases,
  onFetchCloseCaseBuckets,
  onFetchFraudTypesList,
  onOpenSTRCase
} from 'actions/caseReviewActions';
import * as caseActions from 'actions/caseAssignmentActions';
import { onFetchStages } from 'actions/userManagementActions';
import BucketCasesTable from 'components/common/BucketCasesTable';

const mapStateToProps = (state, ownProps) => {
  return {
    loginType: state.auth.loginType,
    data: state.caseAssignment.cases[ownProps.channel],
    fraudTypes: state.caseAssignment.fraudTypes,
    closeCaseBuckets: state.caseAssignment.closeCaseBuckets,
    stages: state.user.stages,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCases: bindActionCreators(onFetchCases, dispatch),
    caseActions: bindActionCreators(caseActions, dispatch),
    fetchStages: bindActionCreators(onFetchStages, dispatch),
    toggleActions: bindActionCreators(toggleActions, dispatch),
    fetchFraudTypesList: bindActionCreators(onFetchFraudTypesList, dispatch),
    fetchCloseCaseBuckets: bindActionCreators(onFetchCloseCaseBuckets, dispatch),
    openSTRCase: bindActionCreators(onOpenSTRCase, dispatch)
  };
};

const BucketCasesTableContainer = connect(mapStateToProps, mapDispatchToProps)(BucketCasesTable);

export default BucketCasesTableContainer;
