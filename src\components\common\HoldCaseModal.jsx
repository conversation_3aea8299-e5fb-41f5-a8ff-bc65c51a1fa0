import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Form, FormGroup, Label, Input, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';

const HoldCaseModal = ({ toggle, toggleHoldCaseModal, handleHoldCaseSubmit }) => {
  const [comment, setComment] = useState('');

  useEffect(() => setComment(''), [toggle.holdCaseModal]);

  const onSubmit = (e) => {
    e.preventDefault();
    handleHoldCaseSubmit(comment);
  };

  return (
    <>
      <Button
        outline
        color="warning"
        className="ms-2"
        size="sm"
        onClick={() => toggleHoldCaseModal()}>
        Hold Case
      </Button>
      <ModalContainer
        theme={toggle.theme}
        isOpen={toggle.holdCaseModal}
        header="Hold case"
        size="md"
        toggle={() => toggleHoldCaseModal()}>
        <Form onSubmit={onSubmit}>
          <FormGroup>
            <Label>Description</Label>
            <Input
              type="textarea"
              name="description"
              rows="5"
              value={comment}
              onChange={(event) => setComment(event.target.value)}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button size="sm" type="submit" color="success">
              Submit
            </Button>
          </FormGroup>
        </Form>
      </ModalContainer>
    </>
  );
};

HoldCaseModal.propTypes = {
  toggle: PropTypes.object.isRequired,
  toggleHoldCaseModal: PropTypes.func.isRequired,
  handleHoldCaseSubmit: PropTypes.func.isRequired
};

export default HoldCaseModal;
