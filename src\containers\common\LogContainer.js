import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchCaseLogs } from 'actions/logsActions';
import Log from 'components/common/Log';

const mapStateToProps = (state) => ({
  logs: state.logs.caseLogs,
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  fetchCaseLogs: bindActionCreators(onFetchCaseLogs, dispatch)
});

const LogContainer = connect(mapStateToProps, mapDispatchToProps)(Log);

export default LogContainer;
