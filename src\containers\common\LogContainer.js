import { connect } from 'react-redux';
import Log from 'components/common/Log';
import { bindActionCreators } from 'redux';
import { onFetchCaseLogs } from 'actions/logsActions';

const mapStateToProps = (state) => {
  return {
    logs: state.logs.caseLogs,
    theme: state.toggle.theme
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchCaseLogs: bindActionCreators(onFetchCaseLogs, dispatch)
  };
};

const LogContainer = connect(mapStateToProps, mapDispatchToProps)(Log);

export default LogContainer;
