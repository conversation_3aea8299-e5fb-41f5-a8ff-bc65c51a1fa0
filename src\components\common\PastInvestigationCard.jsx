import {
  faChevronRight,
  faCaretDown,
  faCaretRight,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import ReactTable from 'react-table';
import { Card, Button, Input, ButtonGroup } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import DurationSelector from 'components/common/DurationSelector';
import { getScreen, getCurrentPageCases } from 'constants/functions';
import CombinedViolationDropdownContainer from 'containers/common/CombinedViolationDropdownContainer';
import ProvisionalFieldsValueContainer from 'containers/common/ProvisionalFieldsValueContainer';
import UserListDropdownContainer from 'containers/common/UserListDropdownContainer';
import VerdictModalContainer from 'containers/common/VerdictModalContainer';
import ViolatedRuleNameBadgeContainer from 'containers/common/ViolatedRuleNameBadgeContainer';
import { useDateRange } from 'context/DateRangeContext';

// TODO: the component is huge (700+). It should be broken down into atomic components
const PastInvestigationCard = ({
  role,
  channel,
  entityId,
  ruleNames,
  closeCaseBuckets,
  pastInvestigations,
  hasProvisionalFields,
  toggleActions,
  fetchRuleNamesList,
  fetchCloseCaseBuckets,
  fetchPastInvestigations,
  contextKey,
  childTxns = [],
  caseStatus = [],
  showActions = false,
  ruleLabel = null,
  transactionId = null,
  addToCase = () => null
}) => {
  const history = useHistory();
  const { startDate, endDate } = useDateRange(contextKey);
  const { list, loader, errorMessage, count } = pastInvestigations[channel];

  const [pageNo, setPageNo] = useState(1);
  const [pageRecords, setPageRecords] = useState(5);
  const [tableFilters, setTableFilters] = useState([]);
  const [bulkCaseIds, setBulkCaseIds] = useState([]);
  const [selectedCase, setSelectedCase] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  const [currentPageCases, setCurrentPageCases] = useState(list || []);

  useEffect(() => {
    if (_.isEmpty(closeCaseBuckets.list) && !closeCaseBuckets.loader) fetchCloseCaseBuckets();
    if (_.isEmpty(ruleNames.list[channel]) && !ruleNames.loader) fetchRuleNamesList(channel);
  }, []);

  useEffect(() => {
    if (!entityId) return;
    handleFetchInvestigation('byDate');
  }, [entityId, ruleLabel, channel, fetchPastInvestigations, startDate, endDate]);

  useEffect(() => {
    if (!entityId) return;
    handleFetchInvestigation('byPagination');
  }, [pageNo, pageRecords]);

  useEffect(() => {
    const debouncedSetPageNo = _.debounce(() => {
      setPageNo(1);
    }, 500);

    debouncedSetPageNo();

    return () => {
      debouncedSetPageNo.cancel();
    };
  }, [tableFilters]);

  // reset checkbox when list is updated
  useEffect(() => {
    setBulkCaseIds([]);
    setSelectAll(false);
  }, [list]);

  useEffect(() => {
    setCurrentPageCases(getCurrentPageCases(pageNo, pageRecords, list, tableFilters));
  }, [list, pageNo, pageRecords, tableFilters]);

  const tablePageCountProp = _.isEmpty(tableFilters)
    ? { pages: count / pageRecords > 1 ? Math.ceil(count / pageRecords) : 1 }
    : {};

  const showSTRAction = (txnId) =>
    !_.includes(childTxns, txnId) ? (
      <Button
        outline
        size="sm"
        className="ms-2"
        title="Add to case"
        color="danger"
        onClick={() => addTxns([txnId])}>
        Add
      </Button>
    ) : (
      <Button
        size="sm"
        className="ms-2"
        title="Click to remove from case"
        color="danger"
        onClick={() => addTxns([txnId])}>
        Added
      </Button>
    );

  const getBulkActions = () => {
    switch (true) {
      case channel === 'str' && role === 'maker':
        if (contextKey === 'entityProfilling') return null;

        return (
          <Button
            outline
            color="danger"
            disabled={_.isEmpty(bulkCaseIds)}
            onClick={() => addTxns(bulkCaseIds)}>
            Add Cases
          </Button>
        );
      case ['maker', 'checker'].includes(role) && channel === 'frm':
        return (
          <ButtonGroup>
            {channel === 'frm' && (
              <>
                <Button
                  outline
                  size="sm"
                  color="danger"
                  className="ms-1"
                  title="Close Case"
                  disabled={_.isEmpty(bulkCaseIds)}
                  onClick={() => toggleActions.onToggleVerdictModal('frm')}>
                  <FontAwesomeIcon icon={faTimes} /> {' Close Cases'}
                </Button>
                <UserListDropdownContainer
                  bucket="Open"
                  bulkCaseIds={bulkCaseIds}
                  channel={channel}
                  showText
                  selfAssign={true}
                />
              </>
            )}
          </ButtonGroup>
        );
      case channel === 'frm' && role === 'supervisor':
        return (
          <UserListDropdownContainer
            bucket="Open"
            bulkCaseIds={bulkCaseIds}
            channel={channel}
            showText
          />
        );
      default:
        return null;
    }
  };

  const getActions = (caseItem = null) => {
    switch (true) {
      case ['maker', 'checker'].includes(role) && channel === 'frm':
        return (
          <>
            <Button
              outline
              size="sm"
              color="danger"
              className="ms-1"
              title="Close Case"
              onClick={() => {
                resetCheckBox();
                setSelectedCase(caseItem);
                toggleActions.onToggleVerdictModal(channel);
              }}>
              <FontAwesomeIcon icon={faTimes} />
            </Button>
            <UserListDropdownContainer
              bucket={caseItem.bucket}
              channel={channel}
              caseId={caseItem.caseRefNo}
              selfAssign={true}
              onClick={() => {
                resetCheckBox();
                setSelectedCase(caseItem);
              }}
            />
          </>
        );
      case channel === 'frm' && role === 'supervisor':
        return (
          <UserListDropdownContainer
            bucket={caseItem.bucket}
            channel={channel}
            caseId={caseItem.caseRefNo}
            selfAssign={true}
            onClick={() => {
              setBulkCaseIds([]);
              setSelectAll(false);
              setSelectedCase(caseItem);
            }}
          />
        );
      case channel === 'str' && role === 'maker':
        if (contextKey === 'entityProfilling') return null;

        return showSTRAction(caseItem.txnId);
      default:
        return null;
    }
  };

  const resetCheckBox = () => {
    setBulkCaseIds([]);
    setSelectAll(false);
  };

  const handleSelectAll = () => {
    if (selectAll) resetCheckBox();
    else {
      setBulkCaseIds(
        currentPageCases
          .filter((cases) => cases.txnId !== transactionId)
          .map((pageCase) => pageCase[channel === 'frm' ? 'caseRefNo' : 'txnId'])
      );
      setSelectAll(true);
    }
  };

  const handleCheckboxChange = (caseRefNo) => {
    let checkedCases = [...bulkCaseIds];
    const index = checkedCases.indexOf(caseRefNo);
    if (index > -1) {
      checkedCases.splice(index, 1);
      setBulkCaseIds([...checkedCases]);
    } else {
      checkedCases = [...checkedCases, caseRefNo];
      setBulkCaseIds([...checkedCases]);
    }

    setSelectAll(checkedCases.length === currentPageCases.length);
  };

  const handleFetchInvestigation = (calledBy) => {
    if (calledBy === 'byDate') setPageNo(1);

    const page = calledBy === 'byPagination' ? pageNo : 1;
    const data = {
      pageNo: page,
      pageRecords,
      filterCondition: [
        ...(startDate && endDate
          ? [
              {
                key: 'txnTimestamp',
                condition: 'BETWEEN',
                values: [startDate, endDate]
              }
            ]
          : []),
        ...(ruleLabel
          ? [
              {
                key: 'ruleLabel',
                condition: 'EQUAL',
                values: [ruleLabel]
              }
            ]
          : []),

        ...(caseStatus.length > 0
          ? [
              {
                key: 'caseStatus',
                condition: 'EQUAL',
                values: caseStatus
              }
            ]
          : [])
      ]
    };
    fetchPastInvestigations({ channel, entityId, data }, calledBy);
  };

  const addTxns = (txnIds) => {
    addToCase({ txnIds, parentTxnId: transactionId });
    resetCheckBox();
  };

  const getPage = (role, caseItem) => `${getScreen(role)}/${channel}/${caseItem.txnId}`;

  const bucketOptions =
    !closeCaseBuckets.error &&
    closeCaseBuckets.list.map((bucket) => (
      <option key={bucket.id} value={bucket.id}>
        {bucket.name}
      </option>
    ));

  const tableHeader = [
    {
      expander: true,
      // eslint-disable-next-line react/prop-types
      Expander: ({ isExpanded, ...rest }) => {
        if (_.isEmpty(rest.original.reViolatedRules)) return null;
        else return <FontAwesomeIcon icon={isExpanded ? faCaretDown : faCaretRight} />;
      },
      getProps: (state, rowInfo) => {
        if (rowInfo)
          if (_.isEmpty(rowInfo.original.reViolatedRules))
            return {
              onClick: (e) => {
                e.preventDefault();
              }
            };

        return { className: 'cursor-pointer' };
      }
    },
    ...(showActions &&
    !['investigator'].includes(role) &&
    !(contextKey === 'entityProfilling' && channel === 'str')
      ? [
          {
            Header: (
              <Input
                bsSize="md"
                type="checkbox"
                id="selectAll"
                onChange={() => handleSelectAll()}
                checked={selectAll}
                disabled={currentPageCases.length === 0}
              />
            ),
            id: 'select',
            accessor: channel === 'frm' ? 'caseRefNo' : 'txnId',
            searchable: false,
            sortable: false,
            filterable: false,
            minWidth: 50,

            Cell: (row) =>
              row.original?.txnId !== transactionId ? (
                <Input
                  type="checkbox"
                  value={row.value}
                  id={row.value}
                  name="tableSelect[]"
                  checked={_.includes(bulkCaseIds, row.value)}
                  onChange={() => handleCheckboxChange(row.value)}
                />
              ) : null
          }
        ]
      : []),
    {
      Header: '',
      searchable: false,
      filterable: false,
      sortable: false,
      fixed: true,
      minWidth: channel === 'frm' ? 150 : 120,
      style: { overflow: 'visible', whiteSpace: 'nowrap' },
      Cell: (row) => {
        if (row.original?.txnId === transactionId) return null;
        return (
          <>
            <Button
              outline
              size="sm"
              title="View"
              color="primary"
              onClick={() => history.push(getPage(role, row.original))}
              onContextMenu={() => window.open(getPage(role, row.original))}>
              <FontAwesomeIcon icon={faChevronRight} />
            </Button>
            {showActions && getActions(row.original)}
          </>
        );
      }
    },
    {
      Header: 'Transaction Timestamp',
      accessor: 'txnTimestamp',
      Cell: ({ value }) => moment(value).format('YYYY-MM-DD hh:mm A'),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Entity', accessor: 'entityId' },
    { Header: 'Transaction ID', accessor: 'txnId' },
    ...(hasProvisionalFields === 1
      ? [
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute1" />,
            accessor: 'attribute1'
          },
          {
            Header: <ProvisionalFieldsValueContainer attrName="attribute2" />,
            accessor: 'attribute2'
          }
        ]
      : []),
    { Header: 'Case ID', accessor: 'caseRefNo', show: false },
    {
      Header: 'Amount',
      accessor: 'txnAmount',
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          placeholder="Amount greater than"
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'txnAmount']))
              ? _.find(tableFilters, ['id', 'txnAmount']).value
              : ''
          }
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    {
      Header: 'Amount Foreign',
      accessor: 'amountForeign',
      filterMethod: (filter, row) =>
        !isNaN(row[filter.id]) && parseFloat(row[filter.id]) >= parseFloat(filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <input
          type="number"
          min="0"
          step="0.01"
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'amountForeign']))
              ? _.find(tableFilters, ['id', 'amountForeign']).value
              : ''
          }
          placeholder="Amount greater than"
          onChange={(event) => onChange(event.target.value)}
        />
      )
    },
    { Header: 'Currency', accessor: 'txnCurrency' },
    {
      Header: 'Label',
      accessor: 'label',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'label']))
              ? _.find(tableFilters, ['id', 'label']).value
              : ''
          }>
          <option value="">All</option>
          <option>HIGH</option>
          <option>MEDIUM</option>
          <option>LOW</option>
        </select>
      )
    },
    { Header: 'Response Code', accessor: 'responseCode' },
    ...(channel === 'str'
      ? [
          { Header: 'Agency', accessor: 'agencyType', show: false },
          { Header: 'Enquiry Details', accessor: 'enquiryDetails', show: false }
        ]
      : []),
    { Header: 'MCC', accessor: 'payeeMcc' },
    { Header: 'Channel', accessor: 'channel' },
    { Header: 'Payee ID', accessor: 'payeeId' },
    { Header: 'Payer ID', accessor: 'payerId' },
    { Header: 'Customer Account', accessor: 'customerAcc', show: false },
    { Header: 'Beneficiary Account', accessor: 'beneficiaryAcc', show: false },
    { Header: 'Sender Masked Card', accessor: 'senderMaskedCard' },
    { Header: 'Sender Hashed Card', accessor: 'senderHashedCard' },
    { Header: 'Type', accessor: 'txnType' },
    { Header: 'Device ID', accessor: 'deviceId', show: false },
    {
      Header: 'Violated Rules',
      accessor: 'reViolatedRules',
      Cell: ({ value }) => (value ? _.split(value, ',').length : 0),
      filterMethod: (filter, row) => row[filter.id] && _.includes(row[filter.id], filter.value),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <CombinedViolationDropdownContainer
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'reViolatedRules']))
              ? _.find(tableFilters, ['id', 'reViolatedRules']).value
              : ''
          }
          onChange={(value) => onChange(value)}
          defaultOption="All"
        />
      )
    },
    {
      Header: 'IFRM Pre Auth Action',
      accessor: 'ifrmVerdict',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'ifrmVerdict']))
              ? _.find(tableFilters, ['id', 'ifrmVerdict']).value
              : ''
          }>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    {
      Header: 'IFRM Post Auth Action',
      accessor: 'ifrmPostauthVerdictName',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'ifrmPostauthVerdictName']))
              ? _.find(tableFilters, ['id', 'ifrmPostauthVerdictName']).value
              : ''
          }>
          <option value="">All</option>
          <option>ACCEPTED</option>
          <option>REJECTED</option>
          <option>OTP</option>
          <option>MPIN</option>
          <option>PASSWORD</option>
          <option>CC BLOCK</option>
          <option>N/A</option>
        </select>
      )
    },
    ...(channel === 'str'
      ? [
          {
            Header: 'Suggested Action',
            accessor: 'makerAction',
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'makerAction']))
                    ? _.find(tableFilters, ['id', 'makerAction']).value
                    : ''
                }>
                <option value="">All</option>
                <option>File STR</option>
                <option>Close with false positive</option>
              </select>
            )
          }
        ]
      : [
          {
            Header: 'Bucket',
            accessor: 'bucket',
            show: false,
            // eslint-disable-next-line react/prop-types
            Cell: ({ value }) => {
              const closeCaseBucket = _.find(
                closeCaseBuckets.list,
                (bucket) => bucket.id === value
              );

              if (!closeCaseBucket) return null;

              const getBucketClassName = (bucketId) => {
                switch (bucketId) {
                  case 1:
                    return 'color-danger';
                  case 2:
                    return 'color-primary';
                  case 3:
                    return 'color-warning';
                  default:
                    return '';
                }
              };

              return (
                <div className={getBucketClassName(closeCaseBucket.id)}>{closeCaseBucket.name}</div>
              );
            },
            filterMethod: (filter, row) => row[filter.id] && row[filter.id] === filter.value,
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'bucket']))
                    ? _.find(tableFilters, ['id', 'bucket']).value
                    : ''
                }>
                <option value="">All</option>
                {bucketOptions}
              </select>
            )
          },
          {
            Header: 'Case Verdict',
            accessor: 'caseVerdict',
            // eslint-disable-next-line react/prop-types
            Filter: ({ onChange }) => (
              <select
                onChange={(event) => onChange(event.target.value)}
                value={
                  !_.isEmpty(_.find(tableFilters, ['id', 'caseVerdict']))
                    ? _.find(tableFilters, ['id', 'caseVerdict']).value
                    : ''
                }>
                <option value="">All</option>
                <option>Confirmed Fraud</option>
                <option>Undetermined</option>
                <option>Confirmed Genuine</option>
                <option>Assumed Genuine</option>
              </select>
            )
          }
        ]),
    { Header: 'Stage', accessor: 'stage' },
    { Header: 'Assigned To', accessor: 'assignedTo' },
    { Header: 'Entity Category', accessor: 'entityCategory' },
    {
      Header: 'Assignment Timestamp',
      accessor: 'assignmentTimeStamp',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    {
      Header: 'Status',
      accessor: 'currentStatus',
      show: false,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select
          onChange={(event) => onChange(event.target.value)}
          value={
            !_.isEmpty(_.find(tableFilters, ['id', 'currentStatus']))
              ? _.find(tableFilters, ['id', 'currentStatus']).value
              : ''
          }>
          <option value="">All</option>
          <option>New</option>
          <option>Open</option>
          <option>Pending</option>
          <option>Rejected</option>
          <option>On hold</option>
          <option>Closed</option>
        </select>
      )
    },
    { Header: 'Terminal ID', accessor: 'terminalId', show: false }
  ];

  const action = useMemo(() => <DurationSelector contextKey={contextKey} />, [contextKey]);

  const investigatedTrasactionTitle = ruleLabel
    ? `Alerted Transactions For ${ruleLabel[0].toUpperCase() + ruleLabel.slice(1)}`
    : 'Alerted Transactions';

  return (
    <CardContainer action={action} title={investigatedTrasactionTitle}>
      {_.isEmpty(entityId) && _.isEmpty(list) ? (
        <div className="no-data-div">No entityId found</div>
      ) : (
        <Card>
          <div>
            {showActions && (
              <div className="d-flex justify-content-end mt-2 me-2">
                <span className="me-1">{getBulkActions()}</span>
              </div>
            )}
            <ReactTable
              filterable
              columns={tableHeader}
              data={list}
              SubComponent={(row) => (
                <ViolatedRuleNameBadgeContainer
                  violatedRulesList={row.original?.reViolatedRules || ''}
                  taggedRulesList={row.original?.taggedRule || ''}
                />
              )}
              loading={loader}
              noDataText={errorMessage || 'No case history'}
              showPaginationTop={true}
              showPaginationBottom={false}
              filtered={tableFilters}
              showPageJump={false}
              minRows={5}
              page={pageNo - 1}
              pageSize={pageRecords}
              pageSizeOptions={[5, 10, 20, 30, 40, 50]}
              onPageChange={(page) => setPageNo(page + 1)}
              onPageSizeChange={(pageSize, page) => {
                setPageNo(page + 1);
                setPageRecords(pageSize);
              }}
              className="-highlight -striped"
              defaultFilterMethod={(filter, row) =>
                row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
              }
              onFilteredChange={(filtered) => setTableFilters(filtered)}
              {...tablePageCountProp}
            />
          </div>
        </Card>
      )}

      {channel === 'frm' && (
        <VerdictModalContainer
          caseId={selectedCase.caseRefNo}
          bulkCaseIds={bulkCaseIds}
          channel={channel}
          caseDetails={selectedCase}
          singleType="/singleFromTable"
          partnerId={selectedCase.partnerId}
          violatedRules={selectedCase?.reViolatedRules}
        />
      )}
    </CardContainer>
  );
};

PastInvestigationCard.propTypes = {
  addToCase: PropTypes.func,
  childTxns: PropTypes.array,
  caseStatus: PropTypes.array,
  showActions: PropTypes.bool,
  ruleLabel: PropTypes.string,
  transactionId: PropTypes.string,
  role: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  entityId: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  ruleNames: PropTypes.object.isRequired,
  toggleActions: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  pastInvestigations: PropTypes.object.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  fetchRuleNamesList: PropTypes.func.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  fetchPastInvestigations: PropTypes.func.isRequired
};

export default PastInvestigationCard;
