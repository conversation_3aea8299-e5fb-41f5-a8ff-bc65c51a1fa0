module.exports = {
  clearMocks: true,
  collectCoverage: true,
  collectCoverageFrom: ['./src/**'],
  coverageReporters: ['json', 'html', 'text'], // Add 'text' to display in the terminal
  coverageDirectory: './coverage',
  globals: {
    NODE_ENV: 'test'
  },
  testEnvironment: 'jest-fixed-jsdom',
  setupFilesAfterEnv: ['./jest.setup.js'],
  moduleDirectories: ['node_modules', 'src'],
  moduleFileExtensions: ['js', 'jsx', 'json'],
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/tools/assetsTransformer.js',
    '\\.(css)$': '<rootDir>/tools/assetsTransformer.js',
    '^actions/(.+)$': '<rootDir>/src/actions/$1',
    '^components/(.+)$': '<rootDir>/src/components/$1',
    '^constants/(.+)$': '<rootDir>/src/constants/$1',
    '^containers/(.+)$': '<rootDir>/src/containers/$1',
    '^images/(.+)$': '<rootDir>/src/images/$1',
    '^reducers/(.+)$': '<rootDir>/src/reducers/$1',
    '^store/(.+)$': '<rootDir>/src/store/$1',
    '^utility/(.+)$': '<rootDir>/src/utility/$1',
    '^context/(.+)$': '<rootDir>/src/context/$1',
    '^mocks/(.+)$': '<rootDir>/mocks/$1'
  },
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/'],
  watchPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/']
};
