import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchSTRReportLogs } from 'actions/strReportActions';
import DownloadSTRButton from 'components/investigation/DownloadSTRButton';

const mapDispatchToProps = (dispatch) => ({
  getSTRLogs: bindActionCreators(onFetchSTRReportLogs, dispatch)
});

const DownloadSTRButtonContainer = connect(null, mapDispatchToProps)(DownloadSTRButton);

export default DownloadSTRButtonContainer;
