import responses from 'mocks/responses';

import * as actions from 'actions/prefiltersListAction';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('prefilter lists actions', () => {
  beforeEach(() => {
    function FormDataMock() {
      this.append = jest.fn();
    }
    global.FormData = FormDataMock;
  });

  it('should create new list', () => {
    const formData = {
      listName: 'test'
    };

    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'List created successfully' } },
      { type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING },
      { type: types.ON_TOGGLE_CREATE_LIST_MODAL }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onCreateNewList(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch specialized list types', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_LOADING },
      {
        type: types.ON_FETCH_SPECIALIZED_LIST_TYPE_SUCCESS,
        response: responses.listsandlimits.types
      }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchSpecializedListTypes()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch categories', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SPECIALIZED_LIST_CATEGORIES_LOADING },
      {
        type: types.ON_FETCH_SPECIALIZED_LIST_CATEGORIES_SUCCESS,
        response: responses.listsandlimits.categories
      }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchCategories()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch specialized list', () => {
    const currentList = {
      prefilterName: 'Negative List',
      prefilterId: 1,
      prefilterValue: 'Negative'
    };

    const expectedActions = [
      { type: types.ON_FETCH_SPECIALIZED_LIST_LOADING },
      { type: types.ON_FETCH_SPECIALIZED_LIST_SUCCESS, response: responses.listsandlimits.list }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchSpecializedList(currentList)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should toggle specialized list item status', () => {
    const currentList = {
      prefilterName: 'Negative List',
      prefilterId: 1,
      prefilterValue: 'Negative'
    };

    const formData = {
      categoryName: 'IFSC',
      identifier: 'iden1t2',
      remark: 'remark',
      isActive: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_UPDATE_SPECIALIZED_LIST_ITEM_SUCCESS,
        formData
      },
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Enabled successfully' } },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(
        actions.onUpdateSpecializedListItem(formData, currentList, 'specializedList', false)
      )
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should delete specialized list item', () => {
    const currentList = {
      prefilterName: 'Negative List',
      prefilterId: 1,
      prefilterValue: 'Negative'
    };

    const formData = {
      categoryName: 'IFSC',
      identifier: 'iden1t2',
      remark: 'remark',
      isActive: 1
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_CONFIRM_ALERT_MODAL, listType: 'specializedList' },
      {
        type: types.ON_SUCCESSFUL_DELETE_SPECIALIZED_LIST_ITEM,
        formData
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entry removed from ${currentList.prefilterValue} list successfully` }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({
      prefiltersList: {},
      transactionDetails: {
        details: responses.caseAssignment.transaction
      }
    });

    return store
      .dispatch(actions.onDeleteSpecializedListItem(formData, currentList, '', 'specializedList'))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should add single item to specializedList', () => {
    const currentList = {
      prefilterName: 'Negative List',
      prefilterId: 1,
      prefilterValue: 'Negative'
    };

    const formData = {
      categoryName: 'IFSC',
      identifier: 'iden1t2',
      remark: 'remark',
      isActive: 1
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entity added to ${currentList.prefilterValue} list successfully` }
      },
      { type: types.ON_FETCH_SPECIALIZED_LIST_LOADING },
      { type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL, listType: 'specializedList' }
    ];
    const store = mockStore({
      prefiltersList: {},
      transactionDetails: {
        details: responses.caseAssignment.transaction
      }
    });

    return store
      .dispatch(
        actions.onAddSingleItemToSpecializedList(formData, currentList, 'specializedList', '')
      )
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should add item in bulk  to specializedList', () => {
    const currentList = {
      prefilterName: 'Negative List',
      prefilterId: 1,
      prefilterValue: 'Negative'
    };

    const formData = {
      Negative: 'csv file'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entities added successfully` }
      },
      { type: types.ON_FETCH_SPECIALIZED_LIST_LOADING },
      { type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL, listType: 'specializedList' }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onAddItemsInBulkToSpecializedList(formData, currentList, 'specializedList'))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should fetch limit list with pagination', () => {
    const formData = {
      pageNo: 1,
      pageRecords: 10
    };

    const currentList = {
      prefilterName: 'Merchant Limit',
      prefilterId: 2,
      prefilterValue: 'merchant'
    };

    const expectedActions = [
      { type: types.ON_FETCH_LIMIT_LIST_WITH_PAGINATION_LOADING },
      {
        type: types.ON_FETCH_LIMIT_LIST_WITH_PAGINATION_SUCCESS,
        response: responses.listsandlimits.merchantPaginated,
        fetchFrom: '',
        currentId: ''
      }
    ];

    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onFetchLimitListWithPagination(currentList, formData, '', ''))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should reset limit list with pagination', () => {
    const expectedAction = { type: types.ON_RESET_LIMIT_LIST_WITH_PAGINATION };
    expect(actions.onResetLimitListWithPagination()).toEqual(expectedAction);
  });

  it('should fetch limit list', () => {
    const currentList = {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    };

    const expectedActions = [
      { type: types.ON_FETCH_LIMIT_LIST_LOADING },
      {
        type: types.ON_FETCH_LIMIT_LIST_SUCCESS,
        response: responses.listsandlimits.onboardingLimit
      }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchLimitList(currentList)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should update limit list', () => {
    const formData = {
      onboardingType: 'test'
    };

    const currentList = {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: 'Onboarding Type Limit Item Updated Successfully' }
      },
      { type: types.ON_FETCH_LIMIT_LIST_LOADING },
      { type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL, listType: 'limitList' },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onUpdateLimitList(formData, currentList, 'limitList'))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should fetch limit type', () => {
    const expectedActions = [
      { type: types.ON_FETCH_LIMIT_TYPE_LOADING },
      { type: types.ON_FETCH_LIMIT_TYPE_SUCCESS, response: responses.listsandlimits.limitType }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchLimitType()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add items in bulk to limit list', () => {
    const currentList = {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    };

    const formData = {
      onboarding: 'csv file'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entities added successfully` }
      },
      { type: types.ON_FETCH_LIMIT_LIST_LOADING },
      { type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL, listType: 'limitList' }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onAddItemsInBulkToLimitList(formData, currentList, 'limitList'))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should add single item to limitList', () => {
    const currentList = {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    };

    const formData = {
      onboardingType: 'test'
    };

    const expectedActions = [
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entity added successfully` }
      },
      { type: types.ON_FETCH_LIMIT_LIST_LOADING },
      { type: types.ON_TOGGLE_PREFILTERS_LIST_MODAL, listType: 'limitList' }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onAddSingleItemToLimitList(formData, currentList, 'limitList', ''))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should delete limit list item', () => {
    const currentList = {
      prefilterName: 'Onboarding Type Limit',
      prefilterId: 5,
      prefilterValue: 'onboarding'
    };

    const formData = {
      type: 'test',
      keyName: 'onboardingType'
    };

    const expectedActions = [
      { type: types.ON_TOGGLE_LOADER, state: true },
      { type: types.ON_TOGGLE_CONFIRM_ALERT_MODAL, listType: 'limitList' },
      {
        type: types.ON_SUCCESSFUL_DELETE_LIMIT_LIST_ITEM,
        entity: formData
      },
      {
        type: types.ON_SUCCESS_ALERT,
        response: { message: `Entry removed successfully` }
      },
      { type: types.ON_TOGGLE_LOADER, state: false }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store
      .dispatch(actions.onDeleteLimitListItem(formData, currentList, 'limitList'))
      .then(() => {
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it('should fetch all lists', () => {
    const expectedActions = [
      { type: types.ON_FETCH_ALL_LISTS_LOADING },
      { type: types.ON_FETCH_ALL_LISTS_SUCCESS, response: responses.listsandlimits.types }
    ];
    const store = mockStore({ prefiltersList: {} });

    return store.dispatch(actions.onFetchAllLists()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should add to list item click', () => {
    const currentItemInfo = {
      currentListInfo: {
        prefilterName: 'Negative List',
        prefilterId: 1,
        prefilterValue: 'Negative'
      },
      currentIndex: 2,
      currentCategoryName: 'IFSC',
      currentIdentifier: 'iden128'
    };

    const expectedAction = { type: types.ON_ADD_TO_LIST_ITEM_CLICK, currentItemInfo };
    expect(actions.onAddToListItemClick(currentItemInfo)).toEqual(expectedAction);
  });
});
