import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import RuleListMultiSelect from 'components/common/RuleListMultiSelect';

const mapStateToProps = (state) => ({
  ruleNames: state.ruleConfigurator.ruleNames
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch)
});

const RuleListMultiSelectContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleListMultiSelect);

export default RuleListMultiSelectContainer;
