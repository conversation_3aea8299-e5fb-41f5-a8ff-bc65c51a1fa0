import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Button } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import CategoryBasedRulesDropdownContainer from 'containers/common/CategoryBasedRulesDropdownContainer';

function CaseRuleTagModal({ theme, caseRefNo, addRulesToCase }) {
  const [display, setDisplay] = useState(false);
  const [txnCategory, setTxnCategory] = useState([]);
  const [selectedRule, setSelectedRule] = useState([]);

  useEffect(() => {
    setTxnCategory([]);
    setSelectedRule([]);
  }, [display]);

  function tagRuletoCase(ruleList) {
    if (ruleList.length > 0)
      addRulesToCase({ caseRefNo, channel: 'frm', ruleIds: ruleList.map((d) => d.value) });
    setDisplay(false);
  }

  return (
    <>
      <Button outline size="sm" color="success" className="ms-1" onClick={() => setDisplay(true)}>
        Add Rules
      </Button>
      <ModalContainer
        theme={theme}
        header="Add Rules to Case"
        toggle={() => setDisplay(!display)}
        isOpen={display}>
        <CategoryBasedRulesDropdownContainer
          categoryState={{ value: txnCategory, onChange: setTxnCategory }}
          ruleState={{ value: selectedRule, onChange: setSelectedRule }}
        />
        <FormGroup className="d-flex justify-content-end">
          <Button
            size="sm"
            color="primary"
            disabled={selectedRule.length < 1}
            onClick={() => tagRuletoCase(selectedRule)}>
            Submit
          </Button>
        </FormGroup>
      </ModalContainer>
    </>
  );
}

CaseRuleTagModal.propTypes = {
  theme: PropTypes.string.isRequired,
  caseRefNo: PropTypes.string.isRequired,
  addRulesToCase: PropTypes.func.isRequired
};

export default CaseRuleTagModal;
