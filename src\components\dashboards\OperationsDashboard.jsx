import React from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';

const reportsList = [
  {
    name: 'Closed Cases report',
    url: '/knowage-vue/document-browser/document-composite/Closed%20Cases'
  },
  {
    name: 'Blocked accounts report',
    url: '/knowage-vue/document-browser/document-composite/blocked%20accounts'
  },
  {
    name: 'Analyst TAT report',
    url: '/knowage-vue/document-browser/document-composite/Analyst%20Tat'
  },
  {
    name: 'Hold Cases report',
    url: '/knowage-vue/document-browser/document-composite/hold%20cases'
  },
  {
    name: 'Released Transactions report',
    url: '/knowage-vue/document-browser/document-composite/release%20transactions'
  },
  {
    name: 'Verdicts Summary report',
    url: '/knowage-vue/document-browser/document-composite/verdict%20summary'
  }
];

const tabNames = reportsList.map((d) => d.name);

function OperationsDashboard() {
  return (
    <div>
      <iframe
        className="login-iframe"
        title="login iframe"
        src="/knowage/servlet/AdapterHTTP?PAGE=LoginPage&NEW_SESSION=TRUE&userID=demo_user&password=demo_user"
        width={1}
        height={1}
        loading="eager"
      />
      <Tabs tabNames={tabNames}>
        {reportsList.map((report, idx) => (
          <TabPane key={idx} tabId={idx}>
            <iframe
              className="report-iframe"
              key={idx}
              title={report.name}
              src={report.url}
              loading="lazy"
              width="100%"
              height={600}
            />
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
}

export default OperationsDashboard;
