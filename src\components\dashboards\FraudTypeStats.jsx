import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Row, Col, CardTitle, CardSubtitle } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

function FraudTypeStats({ period, xchannelId, businessKpis, fetchBusinessKpis, size }) {
  useEffect(() => {
    fetchBusinessKpis({ ...period, xchannelId });
  }, [period.startDate, xchannelId]);

  return (
    <Row>
      <Col lg={size || 2} md="4" sm="6">
        <CardContainer>
          <CardTitle className="text-info">{businessKpis?.data?.totalAlerts ?? 0}</CardTitle>
          <CardSubtitle>Total Alerts</CardSubtitle>
        </CardContainer>
      </Col>
      <Col lg={size || 2} md="4" sm="6">
        <CardContainer>
          <CardTitle className="text-info">{businessKpis?.data?.notFraudCases ?? 0}</CardTitle>
          <CardSubtitle>Not Fraud cases</CardSubtitle>
        </CardContainer>
      </Col>
      <Col lg={size || 3} md="4" sm="6">
        <CardContainer>
          <CardTitle className="text-info">
            {businessKpis?.data?.fraudCasesWithViolations ?? 0}
          </CardTitle>
          <CardSubtitle>Fraud cases w/ violations</CardSubtitle>
        </CardContainer>
      </Col>
      <Col lg={size || 3} md="4" sm="6">
        <CardContainer>
          <CardTitle className="text-info">
            {businessKpis?.data?.fraudCasesWithoutViolations ?? 0}
          </CardTitle>
          <CardSubtitle>Fraud cases w/o violations</CardSubtitle>
        </CardContainer>
      </Col>
      <Col lg={size || 2} md="4" sm="6">
        <CardContainer>
          <CardTitle className="text-info">{businessKpis?.data?.totalFraudAmount ?? 0}</CardTitle>
          <CardSubtitle>Total Fraud Amount</CardSubtitle>
        </CardContainer>
      </Col>
    </Row>
  );
}

FraudTypeStats.propTypes = {
  size: PropTypes.string,
  period: PropTypes.object.isRequired,
  xchannelId: PropTypes.string.isRequired,
  businessKpis: PropTypes.object.isRequired,
  fetchBusinessKpis: PropTypes.func.isRequired
};

export default FraudTypeStats;
