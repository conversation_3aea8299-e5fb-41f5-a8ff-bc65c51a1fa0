export default {
  color: [
    '#2ca8de',
    '#3ba272',
    '#c1232b',
    '#fac858',
    '#ee6666',
    '#91cc75',
    '#73c0de',
    '#fc8452',
    '#5470c6',
    '#9a60b4',
    '#ea7ccc',
    '#27727b',
    '#fcce10',
    '#e87c25',
    '#b5c334',
    '#fe8463',
    '#60c0dd',
    '#9bca63',
    '#26c0c0',
    '#c6e579',
    '#fad860',
    '#f3a43b',
    '#d7504b',
    '#f4e001',
    '#f0805a'
  ],
  backgroundColor: 'rgba(0,0,0,0)',
  textStyle: {},
  title: {
    textStyle: {
      color: '#343E41'
    },
    subtextStyle: {
      color: '#343E41'
    }
  },
  legend: {
    textStyle: {
      color: '#999999',
      fontSize: '16px',
      fontFamily: 'roboto'
    }
  },
  grid: {
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#cccccc',
          width: 1
        },
        crossStyle: {
          color: '#cccccc',
          width: 1
        }
      }
    }
  },
  line: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: '3'
    },
    symbolSize: '5',
    symbol: 'emptyCircle',
    smooth: false
  },
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: '3'
    },
    symbolSize: '5',
    symbol: 'emptyCircle',
    smooth: false
  },
  bar: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  pie: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  gauge: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    emphasis: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    }
  },
  candlestick: {
    itemStyle: {
      color: '#fc97af',
      color0: 'transparent',
      borderColor: '#fc97af',
      borderColor0: '#87f7cf',
      borderWidth: '2'
    }
  },
  graph: {
    itemStyle: {
      borderWidth: 0,
      borderColor: '#ccc'
    },
    lineStyle: {
      width: 1,
      color: '#aaaaaa'
    },
    symbolSize: '5',
    symbol: 'emptyCircle',
    smooth: false,
    color: [
      '#c1232b',
      '#27727b',
      '#fcce10',
      '#e87c25',
      '#b5c334',
      '#fe8463',
      '#9bca63',
      '#fad860',
      '#f3a43b',
      '#60c0dd',
      '#d7504b',
      '#c6e579',
      '#f4e001',
      '#f0805a',
      '#26c0c0'
    ],
    label: {
      color: '#eeeeee'
    }
  },
  map: {
    itemStyle: {
      areaColor: '#dddddd',
      borderColor: '#eeeeee',
      borderWidth: 0.5
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(254,153,78,1)',
        borderColor: '#444444',
        borderWidth: 1
      },
      label: {
        color: 'rgb(100,0,0)'
      }
    },
    label: {
      color: '#c1232b'
    }
  },
  geo: {
    itemStyle: {
      areaColor: '#dddddd',
      borderColor: '#eeeeee',
      borderWidth: 0.5
    },
    emphasis: {
      itemStyle: {
        areaColor: 'rgba(254,153,78,1)',
        borderColor: '#444444',
        borderWidth: 1
      },
      label: {
        color: 'rgb(100,0,0)'
      }
    },
    label: {
      color: '#c1232b'
    }
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#27727b'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#27727b'
      }
    },
    axisLabel: {
      show: true,
      color: '#333'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ['#ccc']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      }
    }
  },
  valueAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#333'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#333'
      }
    },
    axisLabel: {
      show: true,
      color: '#333'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ['#ccc']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      }
    }
  },
  logAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#27727b'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#333'
      }
    },
    axisLabel: {
      show: true,
      color: '#333'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ['#ccc']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      }
    }
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: '#27727b'
      }
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: '#333'
      }
    },
    axisLabel: {
      show: true,
      color: '#333'
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ['#ccc']
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      }
    }
  },
  toolbox: {
    iconStyle: {
      borderColor: '#c1232b'
    },
    emphasis: {
      iconStyle: {
        borderColor: '#e87c25'
      }
    }
  },
  timeline: {
    lineStyle: {
      color: '#293c55',
      width: 1
    },
    itemStyle: {
      color: '#27727b',
      borderWidth: 1
    },
    emphasis: {
      color: '#72d4e0',
      controlStyle: {
        color: '#27727b',
        borderColor: '#27727b',
        borderWidth: 0.5
      },
      label: {
        color: '#87f7cf'
      }
    },
    controlStyle: {
      color: '#27727b',
      borderColor: '#27727b',
      borderWidth: 0.5
    },
    checkpointStyle: {
      color: '#c1232b',
      borderColor: 'rgba(194,53,49,0.5)'
    },
    label: {
      color: '#293c55'
    }
  },
  visualMap: {
    color: ['#c1232b', '#fcce10']
  },
  dataZoom: {
    backgroundColor: 'rgba(0,0,0,0)',
    dataBackgroundColor: 'rgba(181,195,52,0.3)',
    fillerColor: 'rgba(181,195,52,0.2)',
    handleColor: '#27727b',
    handleSize: '100%',
    textStyle: {
      color: '#999999'
    }
  },
  markPoint: {
    label: {
      color: '#293441'
    },
    emphasis: {
      label: {
        color: '#eeeeee'
      }
    }
  }
};
