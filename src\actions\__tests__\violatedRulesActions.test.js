import responses from 'mocks/responses';

import {
  onFetchViolatedRules,
  onFetchViolatedRulesTransactions
} from 'actions/violatedRulesActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('violated rules actions', () => {
  it('should fetch violated rules for channel-txn', () => {
    const expectedActions = [
      {
        type: types.ON_SUCCESSFUL_VIOLATED_RULES_FETCH,
        response: {
          transactionId: 1,
          list: responses.caseAssignment.violatedRules
        }
      }
    ];
    const store = mockStore({ violatedRules: {} });

    return store.dispatch(onFetchViolatedRules(1, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Violated Rules Transactions', () => {
    const expectedActions = [
      {
        type: types.ON_FETCH_RULE_VIOLATION_TRANSACTIONS_LOADING,
        ruleCode: '124'
      },
      {
        type: types.ON_FETCH_RULE_VIOLATION_TRANSACTIONS_SUCCESS,
        ruleCode: '124',
        response: responses.caseAssignment.violatedRules
      }
    ];
    const store = mockStore({ violatedRules: {} });

    return store.dispatch(onFetchViolatedRulesTransactions(1, 'frm', { code: '124' })).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
