import _ from 'lodash';

import { isCooperative } from 'constants/publicKey';

export const filterAccessibleRoutes = (
  modulesList,
  role,
  channel,
  moduleType,
  loginType,
  hasHoldAndRelease,
  hasKnowageReports
) =>
  _.chain(modulesList)
    .filter((module) => {
      if (!_.includes(module.role, role)) return false;
      if (
        role !== 'super-admin' &&
        role !== 'admin' &&
        _.intersection(module.channel, channel).length === 0
      )
        return false;
      if (!_.includes(module.moduleType, moduleType)) return false;
      if (hasHoldAndRelease === 0 && module.route === '/release-funds') return false;
      if (hasKnowageReports === 0 && module.route === '/reports') return false;
      if (isCooperative && !_.includes(module.loginType, loginType.toLowerCase())) return false;
      // The settings route is handled separately in the Header component, so we exclude it here.
      if (module.route === '/settings') return false;
      return true;
    })
    .value();
