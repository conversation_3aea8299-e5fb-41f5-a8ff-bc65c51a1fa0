import PropTypes from 'prop-types';
import React from 'react';
import { Label, Input, ListGroup } from 'reactstrap';

const DSLHelperList = ({ label, searchValue, onSearchChange, list }) => (
  <>
    <Label className="list-heading">{label}</Label>
    <Input
      type="text"
      placeholder="search..."
      value={searchValue}
      onChange={onSearchChange}
      bsSize="sm"
    />
    <ListGroup className="dsl-list-group">{list}</ListGroup>
  </>
);

DSLHelperList.propTypes = {
  label: PropTypes.string.isRequired,
  searchValue: PropTypes.string.isRequired,
  onSearchChange: PropTypes.func.isRequired,
  list: PropTypes.arrayOf(PropTypes.node).isRequired
};

export default React.memo(DSLHelperList);
