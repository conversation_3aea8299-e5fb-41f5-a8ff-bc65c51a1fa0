import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { ButtonGroup, Button } from 'reactstrap';

import DateRangePopOverContainer from 'containers/common/DateRangePopOverContainer';
import { useDateRange } from 'context/DateRangeContext';

function DurationSelector({ contextKey }) {
  const { startDate, endDate, updateDateRange, duration, updateDuration } =
    useDateRange(contextKey);

  useEffect(() => {
    const now = moment();
    const eod = now.endOf('day');
    const eodFormatted = eod.format('YYYY-MM-DDTHH:mm:ss');
    let startDateTime = '';
    let endDateTime = '';
    switch (duration) {
      case 1:
        startDateTime = now.startOf('day').format('YYYY-MM-DDTHH:mm:ss');
        endDateTime = eodFormatted;
        break;
      case 2:
        startDateTime = eod.subtract(2, 'days').startOf('day').format('YYYY-MM-DDTHH:mm:ss');
        endDateTime = eodFormatted;
        break;
      case 3:
        startDateTime = eod.subtract(6, 'days').startOf('day').format('YYYY-MM-DDTHH:mm:ss');
        endDateTime = eodFormatted;
        break;
      case 4:
        startDateTime = eod.subtract(14, 'days').startOf('day').format('YYYY-MM-DDTHH:mm:ss');
        endDateTime = eodFormatted;
        break;
      case 5:
        startDateTime = eod.subtract(29, 'days').startOf('day').format('YYYY-MM-DDTHH:mm:ss');
        endDateTime = eodFormatted;
        break;
      default:
        break;
    }

    if (startDateTime !== '' && endDateTime !== '') updateDateRange(startDateTime, endDateTime);
  }, [duration, updateDateRange]);

  const setDurationHandler = () => {
    if (startDate !== '' && endDate !== '') {
      const durationInDays = new Map([
        [0, 1],
        [2, 2],
        [6, 3],
        [14, 4],
        [29, 5]
      ]);

      const daysDifference = moment(endDate).diff(moment(startDate), 'days');
      const calculatedDuration = durationInDays.get(daysDifference) || 6;
      if (calculatedDuration !== duration) updateDuration(calculatedDuration);

      if (calculatedDuration === 6) updateDateRange(startDate, endDate);
    }
  };

  useEffect(() => {
    setDurationHandler();
  }, []);

  function setCustomDateRange(newStartDate, newEndDate) {
    updateDuration(6);
    updateDateRange(
      moment(newStartDate).startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
      moment(newEndDate).endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    );
  }

  return (
    <ButtonGroup>
      <Button outline size="sm" onClick={() => updateDuration(1)} active={duration === 1}>
        Today
      </Button>
      <Button outline size="sm" onClick={() => updateDuration(2)} active={duration === 2}>
        3 Days
      </Button>
      <Button outline size="sm" onClick={() => updateDuration(3)} active={duration === 3}>
        7 Days
      </Button>
      <Button outline size="sm" onClick={() => updateDuration(4)} active={duration === 4}>
        15 Days
      </Button>
      <Button outline size="sm" onClick={() => updateDuration(5)} active={duration === 5}>
        30 Days
      </Button>
      <DateRangePopOverContainer
        active={duration === 6}
        onSubmit={(startdate, enddate) => setCustomDateRange(startdate, enddate)}
      />
    </ButtonGroup>
  );
}

DurationSelector.propTypes = {
  contextKey: PropTypes.string.isRequired
};

export default DurationSelector;
