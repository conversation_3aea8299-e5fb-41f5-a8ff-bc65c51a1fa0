import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for debounced search functionality
 * Optimizes performance by reducing the frequency of search operations
 *
 * @param {string} initialValue - Initial search value
 * @param {number} delay - Debounce delay in milliseconds (default: 300ms)
 * @returns {Object} - { searchValue, debouncedValue, setSearchValue, clearSearch }
 */
export const useDebouncedSearch = (initialValue = '', delay = 300) => {
  const [searchValue, setSearchValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(searchValue);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [searchValue, delay]);

  const clearSearch = useCallback(() => {
    setSearchValue('');
    setDebouncedValue('');
  }, []);

  const handleSearchChange = useCallback((event) => {
    setSearchValue(event.target.value);
  }, []);

  return {
    searchValue,
    debouncedValue,
    setSearchValue,
    clearSearch,
    handleSearchChange
  };
};

/**
 * Hook for managing multiple search states efficiently
 * Useful for components with multiple search inputs
 *
 * @param {Array} searchKeys - Array of search key names
 * @param {number} delay - Debounce delay in milliseconds
 * @returns {Object} - { searches, debouncedSearches, updateSearch, clearAllSearches }
 */
export const useMultipleDebouncedSearches = (searchKeys = [], delay = 300) => {
  const [searches, setSearches] = useState(
    searchKeys.reduce((acc, key) => ({ ...acc, [key]: '' }), {})
  );

  const [debouncedSearches, setDebouncedSearches] = useState(
    searchKeys.reduce((acc, key) => ({ ...acc, [key]: '' }), {})
  );

  useEffect(() => {
    const handlers = searchKeys.map((key) =>
      setTimeout(() => {
        setDebouncedSearches((prev) => ({
          ...prev,
          [key]: searches[key]
        }));
      }, delay)
    );

    return () => {
      handlers.forEach((handler) => clearTimeout(handler));
    };
  }, [searches, searchKeys, delay]);

  const updateSearch = useCallback((key, value) => {
    setSearches((prev) => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const handleSearchChange = useCallback(
    (key) => (event) => {
      updateSearch(key, event.target.value);
    },
    [updateSearch]
  );

  const clearAllSearches = useCallback(() => {
    const emptySearches = searchKeys.reduce((acc, key) => ({ ...acc, [key]: '' }), {});
    setSearches(emptySearches);
    setDebouncedSearches(emptySearches);
  }, [searchKeys]);

  const clearSearch = useCallback(
    (key) => {
      updateSearch(key, '');
    },
    [updateSearch]
  );

  return {
    searches,
    debouncedSearches,
    updateSearch,
    handleSearchChange,
    clearAllSearches,
    clearSearch
  };
};
