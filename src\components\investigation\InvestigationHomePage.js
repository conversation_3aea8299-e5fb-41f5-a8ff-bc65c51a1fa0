import { map, upperCase } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useRef } from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import BucketCasesCardContainer from 'containers/common/BucketCasesCardContainer';

const InvestigationHomePage = ({ channels, userRoles }) => {
  useEffect(() => {
    document.title = 'BANKiQ FRC | Investigation';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const tabNames = map(channels, (tab) => upperCase(tab));

  const tabRef = useRef(null);

  return (
    <div className="content-wrapper">
      {channels.length > 1 ? (
        <Tabs ref={tabRef} tabNames={tabNames} pills>
          {channels.map((channel, idx) => (
            <TabPane tabId={idx} key={channel}>
              <BucketCasesCardContainer userRole={userRoles} channel={channel} />
            </TabPane>
          ))}
        </Tabs>
      ) : (
        <BucketCasesCardContainer userRole={userRoles} channel={channels[0]} />
      )}
    </div>
  );
};

InvestigationHomePage.propTypes = {
  userRoles: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired
};

export default InvestigationHomePage;
