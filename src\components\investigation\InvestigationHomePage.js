import React, { useEffect, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { map, upperCase } from 'lodash';

import BucketCasesCardContainer from 'containers/common/BucketCasesCardContainer';
import Tabs from 'components/common/Tabs';
import { TabPane } from 'reactstrap';

const InvestigationHomePage = ({ channels, userRoles }) => {
  useEffect(() => {
    document.title = 'BANKiQ FRC | Investigation';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  const tabNames = map(channels, (tab) => upperCase(tab));

  const tabRef = useRef(null);

  const memoizedCasesTable = (channel) =>
    useMemo(() => <BucketCasesCardContainer userRole={userRoles} channel={channel} />, [1]);

  return (
    <div className={'content-wrapper'}>
      {channels.length > 1 ? (
        <Tabs ref={tabRef} tabNames={tabNames} pills>
          {channels.map((channel, idx) => (
            <TabPane tabId={idx} key={channel}>
              {memoizedCasesTable(channel)}
            </TabPane>
          ))}
        </Tabs>
      ) : (
        memoizedCasesTable(channels[0])
      )}
    </div>
  );
};

InvestigationHomePage.propTypes = {
  userRoles: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired
};

export default InvestigationHomePage;
