import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';

import Loader from 'components/loader/Loader';
import OneViewVerdictModalContainer from 'containers/caseReview/CaseOneVerdictModalContainer';
import OneViewActionsContainer from 'containers/caseReview/OneViewActionsContainer';
import TransactionDetailsContainer from 'containers/caseReview/TransactionDetailsContainer';
import HistoryTxnTableContainer from 'containers/common/HistoryTxnTablewActionsContainer';

function CaseOneView({
  userId,
  userName,
  oneViewData,
  clearHistory,
  oneViewActions,
  closeCaseBuckets,
  communicationActions,
  fetchCloseCaseBuckets
}) {
  const history = useHistory();
  const { txnId } = useParams();
  const fetchCase = (txnId) =>
    oneViewActions.onFetchReviewerCase({
      userId,
      userName,
      channel: 'frm',
      ...(txnId && { txnId })
    });
  const { case: oneViewCase } = oneViewData;
  const [verdict, setVerdict] = useState('');
  const [displayModal, setDisplayModal] = useState(false);

  useEffect(() => {
    document.title = 'BANKiQ FRC | Review';
    clearHistory();
    fetchCase(txnId);
    if (closeCaseBuckets.list.length === 0) fetchCloseCaseBuckets();
  }, []);

  useEffect(() => {
    if (oneViewCase?.data?.txnId) {
      document.title = `BANKiQ FRC | Review - ${oneViewCase?.data?.caseRefNo}`;
      communicationActions.onFetchCustomerCommunicationLogs(
        oneViewCase?.data?.channel,
        oneViewCase?.data?.caseRefNo
      );
    }

    return () => {
      document.title = 'BANKiQ FRC | Review';
    };
  }, [oneViewCase?.data]);

  if (oneViewCase.loader) return <Loader show={true} />;

  if (oneViewCase.error)
    return (
      <div className="no-data-div justify-content-center align-items-center flex-column">
        <p className="text-muted">{oneViewCase.errorMessage}</p>
        <Button
          data-testid="get-case-btn"
          size="sm"
          color="primary"
          className="ms-1 mt-2"
          onClick={() => fetchCase()}>
          Get Case
        </Button>
      </div>
    );

  const addVerdict = (bucket) => {
    setVerdict(bucket);
    setDisplayModal(true);
  };

  const closeCase = (e) => {
    const { commentOption, comment, investigationVerdict, fraudType } = e.target;
    const bucket = _.find(closeCaseBuckets.list, (d) =>
      _.lowerCase(verdict) === 'fraud'
        ? _.lowerCase(d.name) === 'fraud'
        : _.lowerCase(d.name) === 'non fraud'
    )?.id;
    const formData = {
      caseRefNo: oneViewCase?.data?.caseRefNo,
      bucketId: +bucket,
      partnerId: oneViewCase?.data?.partnerId || 0,
      remark: commentOption.value !== 'Others' ? commentOption.value : comment.value,
      ...(investigationVerdict && { investigationVerdict: +investigationVerdict.value }),
      ...(fraudType && { fraudType: +fraudType.value }),
      ...(oneViewCase?.data?.childTxns && { childTxns: oneViewCase?.data?.childTxns })
    };
    oneViewActions.onReviewerCaseClose(formData);
    setVerdict('');
    setDisplayModal(false);
  };

  return (
    oneViewCase.data && (
      <div className="content-wrapper p-2">
        <TransactionDetailsContainer oneViewCase={oneViewCase.data} />
        {!_.isEmpty(oneViewCase?.data?.caseRefNo) && (
          <OneViewActionsContainer
            history={history}
            fetchCase={fetchCase}
            addVerdict={(value) => addVerdict(value)}
            oneViewData={oneViewCase?.data}
            txnId={txnId}
          />
        )}
        {!_.isEmpty(oneViewCase.data?.txnTimestamp) && (
          <HistoryTxnTableContainer
            entityId={oneViewCase?.data?.entityId}
            entityCategory={oneViewCase?.data?.entityCategory || ''}
            channel="frm"
            searchDate={oneViewCase.data?.txnTimestamp}
            txnId={oneViewCase?.data?.txnId}
            childTxns={oneViewCase?.data?.childTxns}
            addToCase={oneViewActions.onReviewerAddToCase}
            disableAddAction={
              oneViewCase?.data?.assignedTo !== userName ||
              oneViewCase.data?.investigationStatus !== 'Open'
            }
          />
        )}
        {!_.isEmpty(verdict) && (
          <OneViewVerdictModalContainer
            isOpen={displayModal}
            toggleModal={() => setDisplayModal(!displayModal)}
            verdict={verdict}
            closeCase={(e) => closeCase(e)}
          />
        )}
      </div>
    )
  );
}

CaseOneView.propTypes = {
  theme: PropTypes.string.isRequired,
  userId: PropTypes.number.isRequired,
  userName: PropTypes.string.isRequired,
  oneViewData: PropTypes.object.isRequired,
  oneViewActions: PropTypes.object.isRequired,
  closeCaseBuckets: PropTypes.object.isRequired,
  communicationActions: PropTypes.object.isRequired,
  fetchCloseCaseBuckets: PropTypes.func.isRequired,
  clearHistory: PropTypes.func.isRequired,
  parkCase: PropTypes.func.isRequired
};

export default CaseOneView;
