import objectAssign from 'object-assign';

import {
  ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING,
  ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS,
  ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE,
  ON_<PERSON>ETCH_CONDITIONAL_ATTRIBUTES_LOADING,
  ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS,
  ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE,
  ON_FETCH_ALL_ATTRIBUTES_LOADING,
  ON_FETCH_ALL_ATTRIBUTES_SUCCESS,
  ON_FETCH_ALL_ATTRIBUTES_FAILURE,
  ON_FETCH_SUB_ATTRIBUTES_LOADING,
  ON_FETCH_SUB_ATTRIBUTES_SUCCESS,
  ON_FETCH_SUB_ATTRIBUTES_FAILURE
} from 'constants/actionTypes';

import initialState from './initialState';

export default function dynamicCountersReducer(state = initialState.dynamicCounters, action) {
  switch (action.type) {
    case ON_FETCH_DYNAMIC_COUNTERS_LIST_LOADING:
      return objectAssign({}, state, {
        counters: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_DYNAMIC_COUNTERS_LIST_SUCCESS:
      return objectAssign({}, state, {
        counters: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_DYNAMIC_COUNTERS_LIST_FAILURE:
      return objectAssign({}, state, {
        counters: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || ''
        }
      });
    case ON_FETCH_CONDITIONAL_ATTRIBUTES_LOADING:
      return objectAssign({}, state, {
        conditionalAttributes: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CONDITIONAL_ATTRIBUTES_SUCCESS:
      return objectAssign({}, state, {
        conditionalAttributes: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_CONDITIONAL_ATTRIBUTES_FAILURE:
      return objectAssign({}, state, {
        conditionalAttributes: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || ''
        }
      });
    case ON_FETCH_ALL_ATTRIBUTES_LOADING:
      return objectAssign({}, state, {
        allAttributes: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_ATTRIBUTES_SUCCESS:
      return objectAssign({}, state, {
        allAttributes: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_ALL_ATTRIBUTES_FAILURE:
      return objectAssign({}, state, {
        allAttributes: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || ''
        }
      });
    case ON_FETCH_SUB_ATTRIBUTES_LOADING:
      return objectAssign({}, state, {
        subAttributes: {
          list: [],
          loader: true,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SUB_ATTRIBUTES_SUCCESS:
      return objectAssign({}, state, {
        subAttributes: {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        }
      });
    case ON_FETCH_SUB_ATTRIBUTES_FAILURE:
      return objectAssign({}, state, {
        subAttributes: {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || ''
        }
      });
    default:
      return state;
  }
}
