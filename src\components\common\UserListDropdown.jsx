import { faUserPlus, faPeopleArrows } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { DropdownItem } from 'reactstrap';

import DropdownButton from 'components/common/DropdownButton';
import { isCooperative } from 'constants/publicKey';

const UserListDropdown = ({
  role,
  bucket,
  stages,
  userName,
  userslist,
  caseActions,
  fetchStages,
  onClickAction,
  fetchUsersList,
  channel,
  isHold = 0,
  caseId = '',
  bulkCaseIds = [],
  showText = false,
  selfAssign = false,
  showMaker = true,
  isMasterQueue = false
}) => {
  useEffect(() => {
    if (_.isEmpty(userslist)) fetchUsersList();
    if (_.isEmpty(stages)) fetchStages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const assignCase = (user, status, stageId, bulkCaseIds, caseId) => {
    const formData = {
      stageId,
      assignedTo: user.id,
      assignedToName: user.userName
    };
    _.isEmpty(bulkCaseIds) ? (formData.caseRefNo = caseId) : (formData.caseRefList = bulkCaseIds),
      status === 'New'
        ? caseActions.onAssignNewCase(formData, !_.isEmpty(bulkCaseIds), channel)
        : caseActions.onReassignCase(formData, !_.isEmpty(bulkCaseIds), channel);
  };

  const getStageId = (role) => _.find(stages, (stage) => _.lowerCase(stage.stageName) === role)?.id;

  const createUserslistByChannelRole = (list, role, channel) => {
    const stageId = getStageId(role);
    return _.chain(list)
      .filter(
        (user) =>
          _.includes(user.channelRoles, `${channel}:${role}`) &&
          (role !== 'supervisor' ? user.userName !== userName : true)
      )
      .map((user) => (
        <DropdownItem
          key={role + user.id}
          onClick={() => {
            !_.isEmpty(onClickAction) && onClickAction();
            assignCase(user, bucket, stageId || 0, bulkCaseIds, caseId);
          }}>
          {user.userName}
        </DropdownItem>
      ))
      .value();
  };

  const selfAssignList = (
    <DropdownItem
      key="self"
      onClick={() => {
        !_.isEmpty(onClickAction) && onClickAction();
        assignCase(
          userslist.find((d) => d.userName === userName),
          bucket,
          getStageId(role) || 0,
          bulkCaseIds,
          caseId
        );
      }}>
      Self
    </DropdownItem>
  );

  const assignmentList = () => (
    <>
      {showMaker &&
        ((channel === 'frm' && !isCooperative && role === 'maker' && bucket !== 'Pending') ||
          (channel === 'str' && role === 'maker') ||
          (role === 'supervisor' &&
            ['MasterQueue', 'New', 'Open', 'Rejected'].includes(bucket))) && (
          <>
            <DropdownItem header>Makers</DropdownItem>
            {createUserslistByChannelRole(userslist, 'maker', channel)}
          </>
        )}
      {showMaker && channel === 'frm' && role !== 'maker' && bucket !== 'Pending' && (
        <DropdownItem divider />
      )}
      {((channel === 'frm' && role !== 'maker' && isHold !== 1) ||
        (channel === 'str' && role === 'checker')) && (
        <>
          <DropdownItem header>Checkers</DropdownItem>
          {createUserslistByChannelRole(userslist, 'checker', channel)}
        </>
      )}
    </>
  );

  const assignmentTypeTernary =
    selfAssign && (bucket === 'New' || bucket === 'MasterQueue' || isMasterQueue)
      ? selfAssignList
      : assignmentList();

  return (
    <DropdownButton
      name={
        <>
          <FontAwesomeIcon
            icon={
              selfAssign && (bucket === 'New' || bucket === 'MasterQueue')
                ? faUserPlus
                : faPeopleArrows
            }
          />{' '}
          {showText && (bucket === 'New' ? ' Assign to' : ' Re-assign to')}
        </>
      }
      title={bucket === 'New' || bucket === 'MasterQueue' ? 'Assign' : 'Re-assign'}
      disabled={_.isEmpty(caseId) && _.isEmpty(bulkCaseIds)}>
      {isCooperative && channel === 'frm'
        ? createUserslistByChannelRole(userslist, 'reviewer', channel)
        : assignmentTypeTernary}
    </DropdownButton>
  );
};

UserListDropdown.propTypes = {
  isHold: PropTypes.number,
  caseId: PropTypes.string,
  showText: PropTypes.bool,
  showMaker: PropTypes.bool,
  selfAssign: PropTypes.bool,
  isMasterQueue: PropTypes.bool,
  bulkCaseIds: PropTypes.array,
  onClickAction: PropTypes.func,
  role: PropTypes.string.isRequired,
  bucket: PropTypes.string.isRequired,
  channel: PropTypes.string.isRequired,
  userName: PropTypes.string.isRequired,
  stages: PropTypes.array.isRequired,
  userslist: PropTypes.array.isRequired,
  caseActions: PropTypes.object.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  fetchStages: PropTypes.func.isRequired
};

export default UserListDropdown;
