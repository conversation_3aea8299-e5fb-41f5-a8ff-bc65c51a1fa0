import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import RuleEfficiencyDashboard from 'components/dashboards/RuleEfficiencyDashboard';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleNames: state.ruleConfigurator.ruleNames,
    hasSandbox: state.user.configurations.sandbox,
    channels: state.auth.userCreds.channels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch)
  };
};

const RuleEfficiencyDashboardContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleEfficiencyDashboard);

export default RuleEfficiencyDashboardContainer;
