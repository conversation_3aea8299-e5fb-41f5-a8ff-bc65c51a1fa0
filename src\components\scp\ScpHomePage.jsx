import { isEmpty, some, find } from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState, useRef } from 'react';
import { FormGroup, Input, Table } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import AuthMailer from 'components/scp/AuthMailer';
import AutoAssignmentBucketSize from 'components/scp/AutoAssignmentBucketSize';
import DualRole from 'components/scp/DualRole';
import TransactionRiskScoring from 'components/scp/TransactionRiskScoring';
import { isCooperative } from 'constants/publicKey';
import AutoClosureContainer from 'containers/scp/AutoClosureContainer';

function ScpHomePage({
  configurationsData,
  saveConfigurations,
  fetchConfigurationsList,
  userRoles,
  theme
}) {
  const tableRef = useRef(null);
  const [searchValue, setSearchValue] = useState('');
  const [noSearchResults, setNoSearchResults] = useState(false);

  const [openRows, setOpenRows] = useState({});

  const toggleRow = (configType) => {
    setOpenRows((prev) => ({ ...prev, [configType]: !prev[configType] }));
  };

  useEffect(() => {
    document.title = 'BANKiQ FRC | Settings';

    if (userRoles !== 'supervisor' && userRoles !== 'admin') history.goBack();

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, [userRoles]);

  useEffect(() => {
    if (isEmpty(configurationsData.list)) fetchConfigurationsList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearch = (e) => {
    const searchText = e.target.value.toLowerCase();
    setSearchValue(searchText);
    setNoSearchResults(true);

    if (tableRef.current) {
      const rows = Array.from(tableRef.current.getElementsByTagName('tr'));
      rows.forEach((row) => {
        const cells = Array.from(row.getElementsByClassName('searchable'));
        const matches = cells.some((cell) => cell.textContent.toLowerCase().includes(searchText));

        if (matches) {
          row.classList.remove('hide');
          setNoSearchResults(false);
        } else row.classList.add('hide');
      });
    }
  };

  const escapeRegExp = (str) => str.replace(/([*+?^=!:${}()|[\]/\\])/g, '\\$1');

  const highlightText = (rowVal) => {
    if (!isEmpty(rowVal) && !isEmpty(searchValue)) {
      const newSearchValue = escapeRegExp(searchValue.trim());
      const parts = rowVal.split(new RegExp(`(${newSearchValue})`, 'gi'));
      return parts.map((part, i) => (
        <span
          key={i}
          className={
            part.toLowerCase() === newSearchValue.toLowerCase() ? 'highlight-scp-search' : ''
          }>
          {part}
        </span>
      ));
    }
    return <span>{rowVal}</span>;
  };

  const filterCurrentConfig = (configType) => find(configurationsData.list, { configType });

  return (
    <div className="content-wrapper scp-page">
      <CardContainer title="Settings and Configurations">
        <FormGroup>
          <Input
            type="search"
            name="search"
            id="search"
            placeholder="search..."
            onChange={handleSearch}
            autoComplete="off"
          />
        </FormGroup>
        {noSearchResults && <div className="no-data-div">No match found</div>}
        <Table responsive borderless innerRef={tableRef} className={theme}>
          <tbody>
            {some(configurationsData.list, { configType: 'autoClosure' }) && (
              <AutoClosureContainer
                highlightText={highlightText}
                saveConfigurations={saveConfigurations}
                configurationsData={filterCurrentConfig('autoClosure')}
                toggleRow={toggleRow}
                openRows={openRows}
              />
            )}
            {some(configurationsData.list, { configType: 'dualRole' }) && (
              <DualRole
                highlightText={highlightText}
                saveConfigurations={saveConfigurations}
                configurationsData={filterCurrentConfig('dualRole')}
                toggleRow={toggleRow}
                openRows={openRows}
              />
            )}
            {!isCooperative &&
              some(configurationsData.list, { configType: 'autoAssignmentBucketSize' }) && (
                <AutoAssignmentBucketSize
                  highlightText={highlightText}
                  saveConfigurations={saveConfigurations}
                  configurationsData={filterCurrentConfig('autoAssignmentBucketSize')}
                  toggleRow={toggleRow}
                  openRows={openRows}
                />
              )}
            {some(configurationsData.list, { configType: 'authMailer' }) && (
              <AuthMailer
                highlightText={highlightText}
                saveConfigurations={saveConfigurations}
                configurationsData={filterCurrentConfig('authMailer')}
                toggleRow={toggleRow}
                openRows={openRows}
              />
            )}
            {some(configurationsData.list, { configType: 'transactionRiskScore' }) && (
              <TransactionRiskScoring
                highlightText={highlightText}
                saveConfigurations={saveConfigurations}
                configurationsData={filterCurrentConfig('transactionRiskScore')}
                toggleRow={toggleRow}
                openRows={openRows}
              />
            )}
          </tbody>
        </Table>
      </CardContainer>
    </div>
  );
}

ScpHomePage.propTypes = {
  configurationsData: PropTypes.object.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  fetchConfigurationsList: PropTypes.func.isRequired,
  userRoles: PropTypes.string.isRequired,
  theme: PropTypes.string.isRequired
};

export default ScpHomePage;
