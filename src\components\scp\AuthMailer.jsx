import { isEmpty } from 'lodash';
import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { FormGroup, Label, Input, Col } from 'reactstrap';
import ConfigFormWrapper from 'components/scp/ConfigFormWrapper';
import {
  inputChangeDataHandler,
  saveConfigurationsDataHandler,
  resetConfigurationsDataHandler
} from 'components/scp/scpFunctions';
import HelpIcon from 'components/common/HelpIcon';

function AuthMailer({
  highlightText,
  saveConfigurations,
  configurationsData,
  toggleRow,
  openRows
}) {
  const [authMailer, setAuthMailer] = useState({
    isEdited: false,
    ...configurationsData.configPoints
  });

  const isDisabled = authMailer?.activation?.value === 'disabled';

  const handleSaveConfigurations = useCallback(
    (event) =>
      saveConfigurationsDataHandler(
        event,
        configurationsData,
        authMailer,
        setAuthMailer,
        saveConfigurations
      ),
    [configurationsData, authMailer, saveConfigurations]
  );

  const handleInputChange = useCallback(
    (event) => inputChangeDataHandler(event, authMailer, setAuthMailer),
    [authMailer]
  );

  const handleResetConfigurations = useCallback(
    () => resetConfigurationsDataHandler(configurationsData, setAuthMailer),
    [configurationsData]
  );

  return (
    <ConfigFormWrapper
      configTitle="Auth Mailer"
      activationId="activationAuthMailer"
      highlightText={highlightText}
      data={authMailer}
      configType={configurationsData.configType}
      configDesc={configurationsData.desc}
      toggleRow={toggleRow}
      openRows={openRows}
      handleSaveConfigurations={handleSaveConfigurations}
      handleInputChange={handleInputChange}
      handleResetConfigurations={handleResetConfigurations}>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="userName" className="searchable">
          {highlightText('User name:')}
          {!isEmpty(authMailer?.userName?.desc) && <HelpIcon
            size="lg"
            id='userName'
            text={authMailer?.userName?.desc}
          />}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="email"
            name="userName"
            id="userName"
            onChange={(event) => inputChangeDataHandler(event, authMailer, setAuthMailer)}
            max={24}
            min={0}
            value={authMailer?.userName?.value}
            step="any"
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>
      <FormGroup row>
        <Label sm={4} md={3} lg={2} for="password" className="searchable">
          {highlightText('password:')}
          {!isEmpty(authMailer?.password?.desc) && <HelpIcon
            size="lg"
            id='password'
            text={authMailer?.password?.desc}
          />}
        </Label>
        <Col sm={4} md={3} lg={2} className="setting-input-padding">
          <Input
            type="text"
            name="password"
            id="password"
            onChange={(event) => inputChangeDataHandler(event, authMailer, setAuthMailer)}
            value={authMailer?.password?.value}
            disabled={isDisabled}
          />
        </Col>
      </FormGroup>
    </ConfigFormWrapper>
  );
}

AuthMailer.propTypes = {
  highlightText: PropTypes.func.isRequired,
  saveConfigurations: PropTypes.func.isRequired,
  configurationsData: PropTypes.object.isRequired,
  toggleRow: PropTypes.func.isRequired,
  openRows: PropTypes.object.isRequired
};

export default AuthMailer;
