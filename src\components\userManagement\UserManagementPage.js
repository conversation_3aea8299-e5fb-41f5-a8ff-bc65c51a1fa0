import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';

import ShiftsTableContainer from 'containers/userManagement/ShiftsTableContainer';
import UsersTableContainer from 'containers/userManagement/UsersTableContainer';

function UserManagementPage({ roleslist, userActions, channelslist }) {
  useEffect(() => {
    document.title = 'BANKiQ FRC | User Management';
    if (_.isEmpty(roleslist)) userActions.onFetchRoles();
    if (_.isEmpty(channelslist)) userActions.onFetchChannels();

    return () => {
      document.title = 'BANKiQ FRC';
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="content-wrapper ">
      <ShiftsTableContainer />
      <UsersTableContainer />
    </div>
  );
}

UserManagementPage.propTypes = {
  userRoles: PropTypes.string.isRequired,
  roleslist: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  userActions: PropTypes.object.isRequired
};

export default UserManagementPage;
