import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import { createStore, combineReducers } from 'redux';

import performanceMonitor from 'utility/performanceMonitor';

import RuleBuilder from '../RuleBuilder';
import RuleForm from '../RuleForm';

// Mock Redux store with realistic data
const createMockStore = () => {
  const initialState = {
    ruleCreation: {
      loader: false,
      error: false,
      errorMessage: '',
      actionList: [
        { actionCode: '01', actionName: 'ACCEPTED' },
        { actionCode: '02', actionName: 'DECLINED' }
      ],
      alertCategories: [
        { id: 1, categoryName: 'High Risk' },
        { id: 2, categoryName: 'Medium Risk' }
      ],
      ruleChannels: [{ name: 'FRM' }, { name: 'STR' }],
      fraudCategories: [
        { id: 1, name: 'Card Fraud' },
        { id: 2, name: 'Account <PERSON><PERSON>' }
      ],
      ruleLabels: [
        { id: 1, name: 'Critical' },
        { id: 2, name: 'Important' }
      ],
      validation: { status: true, message: '' },
      helperList: {
        frm: {
          prefix: [{ name: 'TXN' }, { name: 'CARD' }],
          functions: [{ name: 'COUNT', description: 'Count function' }],
          counters: [{ name: 'DAILY_COUNT', description: 'Daily counter' }],
          parameters: [{ name: 'AMOUNT', description: 'Transaction amount' }]
        }
      },
      nonProductionRules: {
        list: { frm: [] }
      },
      checkListOptions: { list: [] },
      checkList: { list: [] }
    },
    ruleConfigurator: {
      productionRules: {
        list: {
          frm: [
            { code: '1', name: 'Rule 1', logic: 'TXN.AMOUNT > 1000', order: 1 },
            { code: '2', name: 'Rule 2', logic: 'CARD.TYPE == "CREDIT"', order: 2 }
          ]
        }
      }
    },
    auth: {
      moduleType: 'acquirer',
      userCreds: { roles: 'checker', channels: ['frm'] }
    },
    user: {
      hasMakerChecker: true,
      configurations: {
        sandbox: 1,
        cognitive: 0,
        acquirerPortals: 1
      }
    },
    toggle: { theme: 'light' },
    prefiltersList: { allLists: {} },
    snoozeRules: { list: {} },
    sandboxing: { testHistory: [] }
  };

  const rootReducer = combineReducers({
    ruleCreation: (state = initialState.ruleCreation) => state,
    ruleConfigurator: (state = initialState.ruleConfigurator) => state,
    auth: (state = initialState.auth) => state,
    user: (state = initialState.user) => state,
    toggle: (state = initialState.toggle) => state,
    prefiltersList: (state = initialState.prefiltersList) => state,
    snoozeRules: (state = initialState.snoozeRules) => state,
    sandboxing: (state = initialState.sandboxing) => state
  });

  return createStore(rootReducer);
};

describe('RuleEngine Module Integration Performance Tests', () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    performanceMonitor.reset();
    performanceMonitor.setEnabled(true);
    jest.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.setEnabled(false);
  });

  const renderWithStore = (component) => render(<Provider store={store}>{component}</Provider>);

  describe('RuleForm Performance', () => {
    const defaultRuleFormProps = {
      channel: 'frm',
      formName: 'create',
      formData: {},
      ruleList: { frm: [] },
      ruleCreation: store.getState().ruleCreation,
      fetchPrefilterLists: jest.fn(),
      ruleCreationActions: {
        onFetchActionList: jest.fn(),
        onFetchAlertCategories: jest.fn(),
        onFetchRuleChannelsList: jest.fn(),
        onFetchRuleFraudCategoriesList: jest.fn(),
        onFetchRuleLabels: jest.fn()
      },
      submit: jest.fn(),
      hasSandbox: 1,
      moduleType: 'acquirer',
      isSandbox: false
    };

    it('should render RuleForm efficiently', () => {
      performanceMonitor.startTiming('ruleform_initial_render');

      renderWithStore(<RuleForm {...defaultRuleFormProps} />);

      const duration = performanceMonitor.endTiming('ruleform_initial_render');
      expect(duration).toBeLessThan(100);
    });

    it('should handle prop changes efficiently', () => {
      const { rerender } = renderWithStore(<RuleForm {...defaultRuleFormProps} />);

      performanceMonitor.startTiming('ruleform_prop_change');

      rerender(
        <Provider store={store}>
          <RuleForm {...defaultRuleFormProps} channel="str" />
        </Provider>
      );

      const duration = performanceMonitor.endTiming('ruleform_prop_change');
      expect(duration).toBeLessThan(50);
    });

    it('should memoize expensive computations', () => {
      const { rerender } = renderWithStore(<RuleForm {...defaultRuleFormProps} />);

      // Re-render with same props multiple times
      for (let i = 0; i < 5; i++)
        rerender(
          <Provider store={store}>
            <RuleForm {...defaultRuleFormProps} />
          </Provider>
        );

      const report = performanceMonitor.getReport();
      // Should not have excessive re-renders due to memoization
      expect(Object.keys(report.renderCounts).length).toBeLessThan(10);
    });
  });

  describe('RuleBuilder Performance', () => {
    const defaultRuleBuilderProps = {
      channel: 'frm',
      formName: 'create',
      helperList: store.getState().ruleCreation.helperList,
      prefilterLists: {},
      combinedRuleList: [],
      ruleData: { logic: '', code: null },
      validation: store.getState().ruleCreation.validation,
      ruleCreationActions: {
        onFetchDSLHelpers: jest.fn(),
        onVerifyDSL: jest.fn(),
        onClearValidation: jest.fn()
      },
      updateRuleData: jest.fn(),
      invalidateLogic: jest.fn(),
      moduleType: 'acquirer'
    };

    it('should render helper lists efficiently', () => {
      performanceMonitor.startTiming('rulebuilder_render');

      renderWithStore(<RuleBuilder {...defaultRuleBuilderProps} />);

      const duration = performanceMonitor.endTiming('rulebuilder_render');
      expect(duration).toBeLessThan(150);
    });

    it('should handle search filtering efficiently', () => {
      renderWithStore(<RuleBuilder {...defaultRuleBuilderProps} />);

      const searchInput = screen.getByPlaceholderText('search...');

      performanceMonitor.startTiming('search_filter');

      fireEvent.change(searchInput, { target: { value: 'TXN' } });

      const duration = performanceMonitor.endTiming('search_filter');
      expect(duration).toBeLessThan(30);
    });

    it('should memoize list computations', () => {
      const { rerender } = renderWithStore(<RuleBuilder {...defaultRuleBuilderProps} />);

      // Change unrelated props
      rerender(
        <Provider store={store}>
          <RuleBuilder {...defaultRuleBuilderProps} formName="edit" />
        </Provider>
      );

      // Lists should be memoized and not recomputed
      expect(screen.getByText('TXN')).toBeInTheDocument();
    });
  });

  describe('Memory Usage Tests', () => {
    it('should not leak memory during component lifecycle', () => {
      performanceMonitor.takeMemorySnapshot('before_components');

      // Render multiple components
      const components = [
        <RuleForm
          key={1}
          channel="frm"
          formName="create"
          formData={{}}
          ruleList={{ frm: [] }}
          ruleCreation={store.getState().ruleCreation}
          fetchPrefilterLists={jest.fn()}
          ruleCreationActions={{
            onFetchActionList: jest.fn(),
            onFetchAlertCategories: jest.fn(),
            onFetchRuleChannelsList: jest.fn(),
            onFetchRuleFraudCategoriesList: jest.fn(),
            onFetchRuleLabels: jest.fn()
          }}
          submit={jest.fn()}
          hasSandbox={1}
          moduleType="acquirer"
        />
      ];

      const renders = components.map((component) => renderWithStore(component));

      performanceMonitor.takeMemorySnapshot('after_render');

      // Unmount components
      renders.forEach(({ unmount }) => unmount());

      performanceMonitor.takeMemorySnapshot('after_unmount');

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        performanceMonitor.takeMemorySnapshot('after_gc');
      }

      performanceMonitor.checkMemoryLeaks();

      // Test passes if no memory leak warnings
      expect(true).toBe(true);
    });
  });

  describe('Selector Performance', () => {
    it('should use memoized selectors efficiently', () => {
      // This test would require actual selector usage
      // For now, we'll test that the store state is structured correctly
      const state = store.getState();

      expect(state.ruleCreation.actionList).toBeDefined();
      expect(state.ruleConfigurator.productionRules).toBeDefined();
      expect(Array.isArray(state.ruleCreation.actionList)).toBe(true);
    });
  });
});
