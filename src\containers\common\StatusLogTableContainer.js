import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onFetchEntityLogs } from 'actions/logsActions';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import { onToggleStatusLogModal } from 'actions/toggleActions';
import { onFetchUsersList } from 'actions/userManagementActions';
import StatusLogTable from 'components/common/StatusLogTable';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  logs: state.logs.entityLogs,
  role: state.auth.userCreds.roles,
  display: state.toggle.statusLogModal,
  ruleNames: state.ruleConfigurator.ruleNames,
  userslist: state.user.userslist
});

const mapDispatchToProps = (dispatch) => ({
  fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch),
  fetchEntityLogs: bindActionCreators(onFetchEntityLogs, dispatch),
  toggleStatusLogModal: bindActionCreators(onToggleStatusLogModal, dispatch),
  fetchUsersList: bindActionCreators(onFetchUsersList, dispatch)
});

const StatusLogTableContainer = connect(mapStateToProps, mapDispatchToProps)(StatusLogTable);

export default StatusLogTableContainer;
