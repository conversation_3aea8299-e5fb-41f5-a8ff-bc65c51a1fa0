import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import _ from 'lodash';

import { useDateRange } from 'context/DateRangeContext';
import GraphContainer from 'components/common/GraphContainer';
import DurationSelector from 'components/common/DurationSelector';

function AnalystTATTrend({ theme, operatorId, analystTAT, fetchAnalystTAT, contextKey }) {
  const { startDate, endDate } = useDateRange(contextKey);
  const duration = moment(endDate).diff(startDate, 'days');

  useEffect(() => {
    !_.isEmpty(startDate) &&
      !_.isEmpty(endDate) &&
      operatorId &&
      fetchAnalystTAT({
        usersId: operatorId,
        startDate: startDate,
        endDate: endDate
      });
  }, [startDate, endDate, operatorId]);

  const trendData = _.map(analystTAT.data, (d) => [
    moment(d.timeInterval).format(duration > 1 ? 'DD/MM' : 'HH:mm'),
    d.timetaken
  ]);

  const config = {
    grid: {
      left: 40,
      bottom: 40,
      top: 40,
      right: 40
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      }
    },
    legend: {
      data: ['']
    },
    xAxis: {
      type: 'category',
      axisPointer: { type: 'shadow' }
    },
    yAxis: { type: 'value', name: 'Time in mins' },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0]
      }
    ],
    series: [
      {
        name: 'Operator TAT',
        type: 'line',
        smooth: true,
        showAllSymbol: true,
        symbol: 'emptyCircle',
        symbolSize: 8,
        lineStyle: {
          width: 2
        },
        data: trendData,
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: 'Average TAT' }]
        }
      }
    ]
  };

  return (
    <GraphContainer
      theme={theme}
      config={config}
      title=""
      noData={analystTAT.data?.length === 0}
      loader={analystTAT.loader}
      error={{ flag: analystTAT.error, errorMessage: analystTAT.errorMessage }}
      graphForm={<DurationSelector contextKey={contextKey} />}
    />
  );
}

AnalystTATTrend.propTypes = {
  theme: PropTypes.string.isRequired,
  contextKey: PropTypes.string.isRequired,
  operatorId: PropTypes.number.isRequired,
  analystTAT: PropTypes.object.isRequired,
  fetchAnalystTAT: PropTypes.func.isRequired
};

export default AnalystTATTrend;
