import React from 'react';
import PropTypes from 'prop-types';
import {
  InputGroup,
  ButtonDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Input,
  ListGroup,
  Button
} from 'reactstrap';
import _ from 'lodash';

const SearchArea = ({
  fetchSearchSuggestion,
  isOpenDropdown,
  setIsOpenDropdown,
  selectedCategoryDisplay,
  selectedCategoryUpdate,
  searchText,
  setSearchText,
  entityType,
  displayResult,
  suggestions,
  onClickBackBtn,
  isSelected,
  searchConditions,
  selectedConditionUpdate,
  selectedCategory,
  isOpenConditionsDropdown,
  setIsOpenConditionsDropdown,
  selectedConditionDisplay
}) => {
  const searchEntityOptions = _.map(searchConditions, (item) => (
    <DropdownItem
      key={item.id}
      onClick={() =>
        selectedCategoryUpdate(item.id, _.startCase(item.id.replace(/([A-Z])/g, ' $1')))
      }>
      {_.startCase(item.id.replace(/([A-Z])/g, ' $1'))}
    </DropdownItem>
  ));

  const selectedEntityConditions = _.find(searchConditions, { id: selectedCategory })?.condition;

  const searchConditionsOptions = _.map(selectedEntityConditions, (cond) => (
    <DropdownItem
      key={cond}
      onClick={() => selectedConditionUpdate(cond, cond === '=' ? 'Exact' : 'Partial')}>
      {cond === '=' ? 'Exact' : 'Partial'}
    </DropdownItem>
  ));

  return (
    <div className="search-area mb-4">
      <form className="d-flex" onSubmit={fetchSearchSuggestion}>
        <InputGroup className="profiling-search">
          <ButtonDropdown isOpen={isOpenDropdown} toggle={() => setIsOpenDropdown(!isOpenDropdown)}>
            <DropdownToggle color="primary" caret>
              <b>{selectedCategoryDisplay}</b>
            </DropdownToggle>
            <DropdownMenu>{searchEntityOptions}</DropdownMenu>
          </ButtonDropdown>

          <ButtonDropdown
            isOpen={isOpenConditionsDropdown}
            toggle={() =>
              setIsOpenConditionsDropdown(
                selectedEntityConditions && selectedEntityConditions.length > 1
                  ? !isOpenConditionsDropdown
                  : false
              )
            }>
            <DropdownToggle color="primary" caret>
              <b>{selectedConditionDisplay}</b>
            </DropdownToggle>
            <DropdownMenu>{searchConditionsOptions}</DropdownMenu>
          </ButtonDropdown>

          <Input
            bsSize="lg"
            className="p-3"
            value={searchText}
            list="entitiesList"
            placeholder={`Search ${entityType} by ${selectedCategoryDisplay}`}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </InputGroup>

        {isSelected && (
          <Button color="secondary" className="btn-sm" onClick={onClickBackBtn}>
            Back
          </Button>
        )}
      </form>
      {displayResult && (
        <div className="entity-search-result">
          <ListGroup>{suggestions}</ListGroup>
        </div>
      )}
    </div>
  );
};

SearchArea.propTypes = {
  fetchSearchSuggestion: PropTypes.func.isRequired,
  isOpenDropdown: PropTypes.bool.isRequired,
  setIsOpenDropdown: PropTypes.func.isRequired,
  selectedCategoryDisplay: PropTypes.string.isRequired,
  selectedCategoryUpdate: PropTypes.func.isRequired,
  searchText: PropTypes.string.isRequired,
  setSearchText: PropTypes.func.isRequired,
  entityType: PropTypes.string.isRequired,
  displayResult: PropTypes.bool.isRequired,
  suggestions: PropTypes.node.isRequired,
  onClickBackBtn: PropTypes.func.isRequired,
  isSelected: PropTypes.bool.isRequired,
  selectedConditionUpdate: PropTypes.func.isRequired,
  searchConditions: PropTypes.array.isRequired,
  selectedCategory: PropTypes.string.isRequired,
  isOpenConditionsDropdown: PropTypes.bool.isRequired,
  setIsOpenConditionsDropdown: PropTypes.func.isRequired,
  selectedConditionDisplay: PropTypes.string.isRequired
};

export default SearchArea;
