import {
  ON_TOG<PERSON><PERSON>_THEME,
  ON_TOGGLE_LOADER,
  ON_TOGGLE_UPLOAD_LOADER,
  ON_TOGGLE_SHIFT_MOD<PERSON>,
  ON_TOGGLE_VERDICT_MODAL,
  ON_TOGGLE_DOWNLOAD_STR_MODAL,
  ON_TOGGLE_APPROVAL_MODAL,
  ON_TOGGLE_CREATE_CASE_MODAL,
  ON_TOGGLE_ADD_USER_MODAL,
  ON_TOGGLE_ADD_BANK_MOD<PERSON>,
  ON_TOGGLE_BLACKLIST_MOD<PERSON>,
  ON_TOGGLE_WATCHLIST_MODAL,
  ON_TOGGLE_PREFILTER_MOD<PERSON>,
  ON_TOGGLE_RULE_EDIT_MOD<PERSON>,
  ON_TOG<PERSON>LE_ESCALATION_MODAL,
  ON_TOGGLE_RULE_CREATE_MODAL,
  ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL,
  ON_TOGGLE_ASSIGN_SHIFT_MOD<PERSON>,
  ON_TOGGLE_RULE_DUPLIC<PERSON><PERSON>_MODAL,
  ON_TOGGLE_UPDATE_USER_ROLES_MODAL,
  ON_TOGGLE_PREFILTERS_LIST_MODAL,
  ON_TOGGLE_CREATE_LIST_MODAL,
  ON_TOGGLE_RELEASE_FUNDS_MODAL,
  ON_TOGGLE_HOLD_CASE_MODAL,
  ON_TOGGLE_REQUEST_DOCUMENT_MODAL,
  ON_TOGGLE_CONFIRM_ALERT_MODAL,
  ON_TOGGLE_STATUS_LOG_MODAL,
  ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL,
  ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL,
  ON_TOGGLE_CPIFR_FORM_MODAL,
  ON_TOGGLE_USER_CASE_CRITERIA_MODAL,
  ON_TOGGLE_CREATE_LABEL_MODAL,
  ON_TOGGLE_RULE_FEEDBACK_MODAL,
  ON_TOGGLE_RESET_PASSWORD_MODAL
} from 'constants/actionTypes';

function onToggleTheme() {
  return { type: ON_TOGGLE_THEME };
}

function onToggleLoader(state) {
  return { type: ON_TOGGLE_LOADER, state };
}

function onToggleUploadLoader(state) {
  return { type: ON_TOGGLE_UPLOAD_LOADER, state };
}

function onToggleAddUserModal() {
  return { type: ON_TOGGLE_ADD_USER_MODAL };
}

function onToggleAddBankModal() {
  return { type: ON_TOGGLE_ADD_BANK_MODAL };
}

function onToggleUpdateUserRolesModal() {
  return { type: ON_TOGGLE_UPDATE_USER_ROLES_MODAL };
}

function onToggleShiftModal() {
  return { type: ON_TOGGLE_SHIFT_MODAL };
}

function onToggleAssignShiftModal() {
  return { type: ON_TOGGLE_ASSIGN_SHIFT_MODAL };
}

function onToggleVerdictModal(channel) {
  return {
    type: ON_TOGGLE_VERDICT_MODAL,
    channel
  };
}

function onToggleRuleFeedbackModal() {
  return { type: ON_TOGGLE_RULE_FEEDBACK_MODAL };
}

function onToggleDownloadSTRModal() {
  return { type: ON_TOGGLE_DOWNLOAD_STR_MODAL };
}

function onToggleApprovalModal() {
  return { type: ON_TOGGLE_APPROVAL_MODAL };
}

function onToggleCreateCaseModal() {
  return { type: ON_TOGGLE_CREATE_CASE_MODAL };
}

function onToggleEscalationModal() {
  return { type: ON_TOGGLE_ESCALATION_MODAL };
}

function onToggleBlacklistModal() {
  return { type: ON_TOGGLE_BLACKLIST_MODAL };
}

function onTogglePrefiltersListModal(listType) {
  return { type: ON_TOGGLE_PREFILTERS_LIST_MODAL, listType };
}

function onToggleReleaseFundsModal() {
  return { type: ON_TOGGLE_RELEASE_FUNDS_MODAL };
}

function onToggleHoldCaseModal() {
  return { type: ON_TOGGLE_HOLD_CASE_MODAL };
}

function onToggleRequestDocumentModal() {
  return { type: ON_TOGGLE_REQUEST_DOCUMENT_MODAL };
}

function onToggleCreateListModal() {
  return { type: ON_TOGGLE_CREATE_LIST_MODAL };
}

function onToggleConfirmAlertModal(listType) {
  return { type: ON_TOGGLE_CONFIRM_ALERT_MODAL, listType };
}

function onToggleWatchlistModal() {
  return { type: ON_TOGGLE_WATCHLIST_MODAL };
}

function onToggleRuleCreateModal(channel) {
  return { type: ON_TOGGLE_RULE_CREATE_MODAL, channel };
}

function onToggleDynamicCountersCreateModal() {
  return { type: ON_TOGGLE_DYNAMIC_COUNTERS_CREATE_MODAL };
}
function onToggleRuleEditModal(channel) {
  return { type: ON_TOGGLE_RULE_EDIT_MODAL, channel };
}

function onToggleRuleDuplicateModal(channel) {
  return { type: ON_TOGGLE_RULE_DUPLICATE_MODAL, channel };
}

function onTogglePrefilterModal(channel) {
  return { type: ON_TOGGLE_PREFILTER_MODAL, channel };
}

function onToggleStatusLogModal() {
  return { type: ON_TOGGLE_STATUS_LOG_MODAL };
}

function onToggleAddToListConfirmAlertModal() {
  return { type: ON_TOGGLE_ADD_TO_LIST_CONFIRM_ALERT_MODAL };
}

function onToggleRequestForInformationModal() {
  return { type: ON_TOGGLE_REQUEST_FOR_INFORMATION_MODAL };
}

function onToggleCPIFRFormModal() {
  return { type: ON_TOGGLE_CPIFR_FORM_MODAL };
}

function onToggleUserCaseCriteriaModal() {
  return { type: ON_TOGGLE_USER_CASE_CRITERIA_MODAL };
}

function onToggleCreateLabelModal() {
  return { type: ON_TOGGLE_CREATE_LABEL_MODAL };
}

function onToggleResetPasswordModal() {
  return { type: ON_TOGGLE_RESET_PASSWORD_MODAL };
}

export {
  onToggleTheme,
  onToggleLoader,
  onToggleUploadLoader,
  onToggleAddUserModal,
  onToggleAddBankModal,
  onToggleUpdateUserRolesModal,
  onToggleShiftModal,
  onToggleAssignShiftModal,
  onToggleVerdictModal,
  onToggleDownloadSTRModal,
  onToggleApprovalModal,
  onToggleCreateCaseModal,
  onToggleEscalationModal,
  onToggleBlacklistModal,
  onToggleWatchlistModal,
  onTogglePrefiltersListModal,
  onToggleCreateListModal,
  onToggleReleaseFundsModal,
  onToggleHoldCaseModal,
  onToggleRequestDocumentModal,
  onToggleConfirmAlertModal,
  onToggleRuleCreateModal,
  onToggleDynamicCountersCreateModal,
  onToggleRuleEditModal,
  onToggleRuleDuplicateModal,
  onTogglePrefilterModal,
  onToggleStatusLogModal,
  onToggleAddToListConfirmAlertModal,
  onToggleRequestForInformationModal,
  onToggleCPIFRFormModal,
  onToggleUserCaseCriteriaModal,
  onToggleCreateLabelModal,
  onToggleRuleFeedbackModal,
  onToggleResetPasswordModal
};
