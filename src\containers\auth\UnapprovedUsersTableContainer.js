import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as userActions from 'actions/userManagementActions';
import UnapprovedUsersTable from 'components/auth/UnapprovedUsersTable';

const mapStateToProps = (state) => {
  return {
    channelslist: state.user.channels,
    unapprovedUserslist: state.user.unapprovedUserslist,
    userName: state.auth.userCreds.userName,
    theme: state.toggle.theme
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    userActions: bindActionCreators(userActions, dispatch)
  };
};

const UnapprovedUsersTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(UnapprovedUsersTable);

export default UnapprovedUsersTableContainer;
