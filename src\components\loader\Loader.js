import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import React from 'react';

const Loader = (props) =>
  props.show ? (
    <div>
      <div className="d-flex align-items-center justify-content-center loader-background">
        <div className="loader">
          <FontAwesomeIcon icon={faSpinner} className="fa-spin fa-4x fa-pulse" />
        </div>
      </div>
    </div>
  ) : null;

Loader.propTypes = {
  show: PropTypes.bool.isRequired
};

export default Loader;
