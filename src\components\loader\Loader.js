import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';
import PropTypes from 'prop-types';

const Loader = (props) => {
  return props.show ? (
    <div>
      <div className="d-flex align-items-center justify-content-center loader-background">
        <div className="loader">
          <FontAwesomeIcon icon={faSpinner} className="fa-spin fa-4x fa-pulse" />
        </div>
      </div>
    </div>
  ) : null;
};

Loader.propTypes = {
  show: PropTypes.bool.isRequired
};

export default Loader;
