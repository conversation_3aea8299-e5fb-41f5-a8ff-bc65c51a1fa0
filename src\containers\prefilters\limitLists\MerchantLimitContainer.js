import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import * as toggleActions from 'actions/toggleActions';
import MerchantLimit from 'components/prefilters/limitLists/MerchantLimit';

const mapStateToProps = (state) => ({
  toggle: state.toggle,
  prefiltersList: state.prefiltersList
});

const mapDispatchToProps = (dispatch) => ({
  toggleActions: bindActionCreators(toggleActions, dispatch),
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const BlockedListContainer = connect(mapStateToProps, mapDispatchToProps)(MerchantLimit);

export default BlockedListContainer;
