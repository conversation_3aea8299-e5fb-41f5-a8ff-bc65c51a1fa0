import { faGavel } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Button, FormGroup, Input, Label } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { isCooperative } from 'constants/publicKey';
import STRReportFormContainer from 'containers/investigation/STRReportFormContainer';

const VerdictModalSTR = ({
  theme,
  userList,
  caseRefNo,
  bulkCaseIds,
  closeCase,
  fileStr,
  gosDetails,
  getGosDetails,
  singleType = '',
  showText = false,
  disabled = false,
  childTxns = []
}) => {
  const [displayModal, setDisplayModal] = useState(false);
  const [report, setReport] = useState('');
  const [notation, setNotation] = useState('');
  const [selectedChecker, setSelectedChecker] = useState('');

  const checkerList = _.filter(userList, (user) => _.includes(user.channelRoles, 'str:checker'));
  const poList = userList?.filter((user) => user.channelRoles?.includes('str:principal-officer'));

  useEffect(() => {
    !gosDetails.loader &&
      caseRefNo &&
      singleType !== '/singleFromTable' &&
      getGosDetails(caseRefNo);
  }, []);

  useEffect(() => {
    if (checkerList.length > 0) setSelectedChecker(`${checkerList[0].id}`);
  }, [userList]);

  const submitCase = (event) => {
    event.preventDefault();

    const files = [];
    const {
      roleMainAssociate,
      sourceOfFunds,
      destinationOfFunds,
      sourceOfAlert,
      suspicionDueTo,
      redFlagIndicator,
      typeOfSuspicion,
      narration,
      fileCheckbox,
      kycType,
      otherRedFlagIndicator
    } = event.target;

    fileCheckbox?.length > 1
      ? fileCheckbox?.forEach((d) => d.checked && files.push(d.value))
      : fileCheckbox?.checked && files.push(fileCheckbox.value);

    const formData = {
      channel: 'str',
      remark: notation,
      ...(!_.isEmpty(bulkCaseIds) ? { caseRefNos: bulkCaseIds } : { caseRefNo }),
      childTxns,
      ...(report === 'str' && {
        ...gosDetails.data,
        roleMainAssociate: roleMainAssociate.value,
        sourceOfFunds: sourceOfFunds.value,
        destinationOfFunds: destinationOfFunds.value,
        sourceOfAlert: sourceOfAlert.value,
        suspicionDueTo: suspicionDueTo.value,
        redFlagIndicator: redFlagIndicator.value,
        otherRedFlagIndicator:
          _.lowerCase(redFlagIndicator.value) === 'others' ? otherRedFlagIndicator?.value : '',
        typeOfSuspicion: typeOfSuspicion.value,
        narration: narration.value,
        files,
        checkerToBeEscalatedTo: (() => {
          if (isCooperative || checkerList.length === 0) return `${poList[0].id}`;

          if (checkerList.length === 1) return `${checkerList[0].id}`;

          return selectedChecker;
        })(),
        kycType: kycType.value
      })
    };

    report === 'str'
      ? fileStr(formData, 'str', singleType)
      : closeCase(formData, 'str', !_.isEmpty(bulkCaseIds) ? '/bulk' : singleType);
    setDisplayModal(false);
  };

  return (
    <>
      <Button
        outline
        size="sm"
        color="success"
        className="ms-1"
        title="Suggest Action"
        disabled={disabled}
        onClick={() => {
          setDisplayModal(true);
          setReport('');
          setNotation('');
        }}>
        <FontAwesomeIcon icon={faGavel} /> {showText ? ' Suggest Action' : ''}
      </Button>
      <ModalContainer
        header="Suggest Case Action"
        toggle={() => setDisplayModal(!displayModal)}
        isOpen={displayModal}
        theme={theme}
        size={report === 'str' ? 'lg' : 'md'}>
        <form onSubmit={submitCase}>
          <FormGroup>
            <Label>Suggested Action</Label>
            <Input
              type="select"
              name="report"
              id="report"
              value={report}
              onChange={(e) => setReport(e.target.value)}
              required>
              <option value="">-- SELECT --</option>
              <option value="str" disabled={!_.isEmpty(bulkCaseIds) || singleType !== ''}>
                File STR
              </option>
              <option value="close">Close with false positive</option>
            </Input>
          </FormGroup>
          <FormGroup>
            <Label>Notation</Label>
            <Input
              type="textarea"
              name="notation"
              id="notation"
              value={notation}
              onChange={(e) => setNotation(e.target.value)}
              required
            />
          </FormGroup>
          {report === 'str' && <STRReportFormContainer caseId={caseRefNo} />}
          {report === 'str' && !isCooperative && checkerList.length > 1 && (
            <FormGroup>
              <Label>Checker</Label>
              <Input
                type="select"
                name="checker"
                id="checker"
                value={selectedChecker}
                onChange={(e) => setSelectedChecker(e.target.value)}
                required>
                {_.map(checkerList, (checker) => (
                  <option key={checker.id} value={checker.id}>
                    {checker.userName}
                  </option>
                ))}
              </Input>
            </FormGroup>
          )}
          <FormGroup className="d-flex justify-content-end">
            <Button outline size="sm" color="primary" className="ml-auto">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

VerdictModalSTR.propTypes = {
  showText: PropTypes.bool,
  disabled: PropTypes.bool,
  childTxns: PropTypes.array,
  singleType: PropTypes.string,
  caseRefNo: PropTypes.string,
  bulkCaseIds: PropTypes.array,
  alertSourceType: PropTypes.string,
  theme: PropTypes.string.isRequired,
  fileStr: PropTypes.func.isRequired,
  closeCase: PropTypes.func.isRequired,
  getGosDetails: PropTypes.func.isRequired,
  gosDetails: PropTypes.object.isRequired,
  userList: PropTypes.array.isRequired
};

export default VerdictModalSTR;
