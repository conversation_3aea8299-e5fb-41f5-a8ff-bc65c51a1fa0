import PropTypes from 'prop-types';
import React from 'react';
import { Card } from 'reactstrap';

import Card<PERSON>ontainer from 'components/common/CardContainer';
import TableFilterForm from 'components/common/TableFilterForm';
import PriorityCasesTableHOC from 'containers/incidents/PriorityCasesTableHOC';

function PriorityCasesTable({ data, hasProvisionalFields, fetchCases }) {
  return (
    <CardContainer title="Priority Cases">
      <TableFilterForm
        currentConf={data.conf}
        fetchCases={fetchCases}
        channel="frm"
        hasProvisionalFields={hasProvisionalFields}
      />
      <Card>
        <PriorityCasesTableHOC currentConf={data.conf} userRole="investigator" channel="frm" />
      </Card>
    </CardContainer>
  );
}

PriorityCasesTable.propTypes = {
  data: PropTypes.object.isRequired,
  hasProvisionalFields: PropTypes.number.isRequired,
  fetchCases: PropTypes.func.isRequired
};

export default PriorityCasesTable;
