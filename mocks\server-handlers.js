import { http, HttpResponse } from 'msw';
import { isEmpty } from 'lodash';
import responses from './responses';

const handlers = [
  //Auth
  //get user role
  http.get('*/api/v1/ifrm/useraccessmanagement/:username/role', async () => {
    return HttpResponse.json(responses.auth.userRoles);
  }),
  //login
  http.post('*/api/v1/ifrm/useraccessmanagement/login', async () => {
    return HttpResponse.json(responses.auth.login);
  }),
  //logout
  http.post('*/api/v1/ifrm/useraccessmanagement/logout', async ({ request }) => {
    const data = await request.json();
    if (isEmpty(data.userName)) {
      return HttpResponse('Value cannot be empty', {
        staus: 400
      });
    }
    return HttpResponse.json({});
  }),
  //change password
  http.post('*/api/v1/ifrm/useraccessmanagement/updatePassword', async ({ request }) => {
    const data = await request.json();
    if (isEmpty(data.userName)) {
      return HttpResponse('Value cannot be empty', {
        staus: 400
      });
    }
    return HttpResponse.json({});
  }),

  //UAM
  //get all users
  http.get('*/api/v1/ifrm/useraccessmanagement/user', () => {
    return HttpResponse.json(responses.uam.users);
  }),
  //add user
  http.post('*/api/v1/ifrm/useraccessmanagement/user', async () => {
    return HttpResponse.json('Ok');
  }),
  //add supervisor
  http.post('*/api/v1/ifrm/useraccessmanagement/user/supervisor', async () => {
    return HttpResponse.json('Ok');
  }),
  //update user
  http.put('*/api/v1/ifrm/useraccessmanagement/user', async () => {
    return HttpResponse.json({});
  }),
  //get all roles
  http.get('*/api/v1/ifrm/useraccessmanagement/role', async () => {
    return HttpResponse.json(responses.uam.roles);
  }),
  //get all channels
  http.get('*/api/v1/ifrm/useraccessmanagement/channel', async () => {
    return HttpResponse.json(responses.uam.channels);
  }),
  //get all shifts
  http.get('*/api/v1/ifrm/useraccessmanagement/shift', async () => {
    return HttpResponse.json(responses.uam.shifts);
  }),
  //add shifts
  http.post('*/api/v1/ifrm/useraccessmanagement/shift', async () => {
    return HttpResponse.json({});
  }),
  //assign shifts
  http.post('*/api/v1/ifrm/useraccessmanagement/shift/user/assign', async () => {
    return HttpResponse.json({});
  }),
  //get all external checkers
  http.get('*/api/v1/ifrm/useraccessmanagement/external/checker/user', async () => {
    return HttpResponse.json(responses.uam.externalChecker);
  }),
  //toggle autocase
  http.put('*/api/v1/ifrm/useraccessmanagement/user/autocase', async ({ request }) => {
    const { userName } = await request.json();
    if (isEmpty(userName)) {
      return HttpResponse('Value cannot be empty', {
        staus: 400
      });
    }
    return HttpResponse.json({});
  }),
  //assign case criteria
  http.post('*/api/v1/ifrm/useraccessmanagement/user/caseCriteria', async () => {
    return HttpResponse.json({});
  }),
  //get case criteria attributes
  http.get('*/api/v1/ifrm/useraccessmanagement/caseCriteria', async () => {
    return HttpResponse.json(responses.uam.caseCriteriaAttributes);
  }),

  //Case Management
  //create case and assign
  http.post('*/api/v1/ifrm/uds/:channel/createalert', async () => {
    return HttpResponse.json(responses.caseAssignment.createCaseAndAssign);
  }),
  //reassign case
  http.post('*/api/v1/ifrm/casereview/case/:channel/user/reassign', async () => {
    return HttpResponse.json({});
  }),
  //reassign case bulk
  http.post('*/api/v1/ifrm/casereview/case/user/reassign/bulk', async () => {
    return HttpResponse.json({});
  }),
  //reassign case supervisor
  http.post('*/api/v1/ifrm/casereview/case/user/approval/supervisor/reassign', async () => {
    return HttpResponse.json({});
  }),
  //assign new case
  http.post('*/api/v1/ifrm/casereview/case/system/assign/user', async () => {
    return HttpResponse.json({});
  }),
  //assign new case bulk
  http.post('*/api/v1/ifrm/casereview/case/system/assign/user/bulk', async () => {
    return HttpResponse.json({});
  }),
  //escalate case
  http.post('*/api/v1/ifrm/casereview/case/user/escalate', async () => {
    return HttpResponse.json({});
  }),
  //acknowledge case
  http.post('*/api/v1/ifrm/casereview/case/:caseRefNo/:channel/acknowledge', async () => {
    return HttpResponse.json({});
  }),
  //get cases for closure
  http.post('*/api/v1/ifrm/casereview/case/fetch/cases', async () => {
    return HttpResponse.json(responses.caseAssignment.closureCases);
  }),
  //reopen case
  http.put('*/api/v1/ifrm/casereview/case/:userId/:channel/reopen', async () => {
    return HttpResponse.json({});
  }),
  //park case
  http.put('*/api/v1/ifrm/casereview/case/:userId/park', async () => {
    return HttpResponse.json({});
  }),
  //unpark case
  http.put('*/api/v1/ifrm/casereview/case/:userId/unpark', async () => {
    return HttpResponse.json({});
  }),
  //get master queue
  http.get('*/api/v1/ifrm/casereview/frm/fetch/cases/master/queue', async () => {
    return HttpResponse.json({});
  }),
  //Case Management - Case Detail
  //get case details
  http.get('*/api/v1/ifrm/casereview/channel/:channel/txnId/:txnId/case', async () => {
    return HttpResponse.json(responses.caseAssignment.caseDetail);
  }),
  //request information
  http.post('*/api/v1/ifrm/casereview/case/additional/info', async () => {
    return HttpResponse.json({});
  }),
  //get all investigation stages
  http.get('*/api/v1/ifrm/casereview/case/stage', async () => {
    return HttpResponse.json(responses.caseAssignment.stages);
  }),
  //Case Management - Case Review
  //one view
  http.post('*/api/v1/ifrm/casereview/case/reviewer/:channel/bucket', () => {
    return HttpResponse.json(responses.oneView.case);
  }),
  //one view close case
  http.put('*/api/v1/ifrm/casereview/case/reviewer/close', async () => {
    return HttpResponse.json({});
  }),
  //get user bucket count
  http.get('*/api/v1/ifrm/casereview/case/:role/bucket/:channel/count', async () => {
    return HttpResponse.json(responses.caseAssignment.buckets);
  }),
  //get user bucket cases
  http.post('*/api/v1/ifrm/casereview/case/:role/:channel/bucket', async () => {
    return HttpResponse.json(responses.caseAssignment.bucketCases);
  }),
  //get all close bucket types
  http.get('*/api/v1/ifrm/casereview/case/close/bucket', async () => {
    return HttpResponse.json(responses.caseAssignment.closeCaseBuckets);
  }),
  //get all liability
  http.get('*/api/v1/ifrm/casereview/case/liability/type', async () => {
    return HttpResponse.json(responses.caseAssignment.liability);
  }),
  //get all fraud types
  http.get('*/api/v1/ifrm/casereview/case/fraud/type', async () => {
    return HttpResponse.json(responses.caseAssignment.fraudTypes);
  }),
  //get all fraud bucket types
  http.get('*/api/v1/ifrm/casereview/case/verdict/bucket', async () => {
    return HttpResponse.json(responses.caseAssignment.fraudTypesWithBuckets);
  }),
  //get past investigations
  http.post('*/api/v1/ifrm/casereview/:channel/entityId/:entityId/case', async () => {
    return HttpResponse.json(responses.caseAssignment.investigatedTxns);
  }),
  //close case
  http.put('*/api/v1/ifrm/casereview/case/user/close', async () => {
    return HttpResponse.json({});
  }),
  //request supervisor approval
  http.post('*/api/v1/ifrm/casereview/case/user/approval/checker', async () => {
    return HttpResponse.json({});
  }),
  //post supervisor approval
  http.post('*/api/v1/ifrm/casereview/case/user/approval/checker/response', async () => {
    return HttpResponse.json({});
  }),
  //request partner approval
  http.post('*/api/v1/ifrm/casereview/case/user/approval/ext/checker', async () => {
    return HttpResponse.json({});
  }),
  //add rule to case
  http.put('*/api/v1/ifrm/casereview/update/ruleids/case', async () => {
    return HttpResponse.json({});
  }),
  //fetch document
  http.get('*/api/v1/ifrm/casereview/case/:caseRefNo/files', async () => {
    return HttpResponse.json(responses.caseDocument);
  }),
  //upload case files
  http.post('*/api/v1/ifrm/casereview/case/:caseRefNo/upload', async () => {
    return HttpResponse.json({});
  }),
  //fetch snooze conditions list
  http.get('*/api/v1/ifrm/casereview/frm/case/auto/close/conditions', async () => {
    return HttpResponse.json(responses.caseAssignment.snoozeConditions);
  }),
  //fetch xchannel list
  http.get('*/api/v1/ifrm/casereview/case/xchannelId/type', async () => {
    return HttpResponse.json(responses.caseAssignment.xChannel);
  }),

  //Citation
  //get case citations
  http.get('*/api/v1/ifrm/casereview/case/:caseRefNo/citation/fetch/all', async () => {
    return HttpResponse.json(responses.str.citation);
  }),
  //add case citation
  http.post('*/api/v1/ifrm/casereview/case/capture/citation/response', async () => {
    return HttpResponse.json({
      caseRefNo: '123',
      citationId: 45,
      response: 'test',
      txnId: 'iucaaTxn20230421063',
      userId: '1',
      userName: 'admin',
      userRole: 'principal-officer'
    });
  }),

  //Cognitive
  //get cognitive status
  http.get('*/api/v1/ifrm/:channel/ruleengine/cognitive/status', async () => {
    return HttpResponse.json(true);
  }),
  //toggle cognitive status
  http.put('*/api/v1/ifrm/:channel/ruleengine/cognitive/status/activate', async () => {
    return HttpResponse.json({});
  }),

  //Notations
  //get case notations
  http.get('*/api/v1/ifrm/casereview/case/:id/:channel/notation', async () => {
    return HttpResponse.json(responses.caseAssignment.notations.case);
  }),
  //add case notations
  http.post('*/api/v1/ifrm/casereview/case/notation', async ({ request }) => {
    const { caseId } = await request.json();
    if (isEmpty(caseId)) {
      return HttpResponse('Value cannot be empty', {
        staus: 400
      });
    }
    return HttpResponse.json({});
  }),
  //fetch master notations
  http.get('*/api/v1/ifrm/casereview/notation/fetch/masters', async () => {
    return HttpResponse.json(responses.caseAssignment.notations.master);
  }),
  //add in master notations
  http.post('*/api/v1/ifrm/casereview/notation/add/notation', async () => {
    return HttpResponse.json({});
  }),
  //update in master notations
  http.put('*/api/v1/ifrm/casereview/notation/update/notation', async () => {
    return HttpResponse.json({});
  }),
  //delete from master notations
  http.put('*/api/v1/ifrm/casereview/notation/delete/id/:id', async () => {
    return HttpResponse.json({});
  }),

  //Customer
  //get vulnerability detail
  http.get(
    '*/api/v1/ifrm/customerservice/channel/:channel' +
      '/customer-id/:customerId/account-number/:accountNo/risk',
    async () => {
      return HttpResponse.json('high');
    }
  ),
  //get customer detail
  http.get(
    '*/api/v1/ifrm/customerservice/channel/:channel/customer/:customerId/:accountNo',
    async () => {
      return HttpResponse.json(responses.demographics.customerInfo);
    }
  ),

  //Dynamic Counter
  //create dynamic counter
  http.post('*/api/v1/ifrm/:channel/ruleengine/create/counter', async () => {
    return HttpResponse.json({});
  }),
  //get conditions
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/conditions', async () => {
    return HttpResponse.json(responses.dynamicCounters.conditionalAttributes);
  }),
  //get primary attributes
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/primary/attributes', async () => {
    return HttpResponse.json(responses.dynamicCounters.allAttributes);
  }),
  //get secondary attributes
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/secondary/attributes', async () => {
    return HttpResponse.json(responses.dynamicCounters.subAttributes);
  }),

  //Investigation
  //get customer trend
  http.post('*/api/v1/ifrm/uds/:channel/trends/customer/:customerId', async () => {
    return HttpResponse.json({
      trendTimeGraph: responses.trend.trendTimeGraph,
      perPayeeTrend: responses.trend.perPayeeTrend
    });
  }),
  //get similar transaction
  http.post('*/api/v1/ifrm/casereview/similar/:channel/txns', async () => {
    return HttpResponse.json(responses.caseAssignment.similar);
  }),
  //get similar category
  http.get('*/api/v1/ifrm/casereview/similar/txns/category', async () => {
    return HttpResponse.json(responses.caseAssignment.similarCategory);
  }),
  //email escalator
  http.put('*/api/v1/ifrm/investigation/case/user/escalate/mailer', async () => {
    return HttpResponse.json({});
  }),
  //fetch channelwise counterparty Id
  http.get('*/api/v1/ifrm/casereview/channel/list/identifiers', async () => {
    return HttpResponse.json([
      {
        label: 'terminalId',
        value: 'terminalId'
      }
    ]);
  }),

  //Logs
  //get case log
  http.get('*/api/v1/ifrm/audit/:module/:id/log', async () => {
    return HttpResponse.json(responses.caseAssignment.caseLog);
  }),
  //get entity log
  http.post('*/api/v1/ifrm/casereview/case/statuslog/:entityId/:channel', async () => {
    return HttpResponse.json(responses.caseAssignment.statusLog);
  }),

  //Prefilter list
  //create new list
  http.post('*/api/v1/ifrm/listsandlimits/list/:listName/save', async () => {
    return HttpResponse.json({});
  }),
  //get list type
  http.get('*/api/v1/ifrm/listsandlimits/list/type', async () => {
    return HttpResponse.json(responses.listsandlimits.types);
  }),
  //get list category
  http.get('*/api/v1/ifrm/listsandlimits/list/category', async () => {
    return HttpResponse.json(responses.listsandlimits.categories);
  }),
  //get specialized list
  http.get('*/api/v1/ifrm/listsandlimits/list/info/:list', async () => {
    return HttpResponse.json(responses.listsandlimits.list);
  }),
  //add specialized list item
  http.post('*/api/v1/ifrm/listsandlimits/list/:list/info', async () => {
    return HttpResponse.json({});
  }),
  //add specialized list items bulk
  http.post('*/api/v1/ifrm/listsandlimits/list/:list/listInfo-bulk-upload', async () => {
    return HttpResponse.json({});
  }),
  //toggle specialized list item
  http.put(
    '*/api/v1/ifrm/listsandlimits/list/:list/category/:category/identifier/:identifier',
    async () => {
      return HttpResponse.json({});
    }
  ),
  //delete specialized list item
  http.delete(
    '*/api/v1/ifrm/listsandlimits/list/:list/category/:category/identifier/:identifier',
    async () => {
      return HttpResponse.json({});
    }
  ),
  //get limit type
  http.get('*/api/v1/ifrm/listsandlimits/limit/type', async () => {
    return HttpResponse.json(responses.listsandlimits.limitType);
  }),
  //get limit list
  http.get('*/api/v1/ifrm/listsandlimits/limit/:list', async () => {
    return HttpResponse.json(responses.listsandlimits.onboardingLimit);
  }),
  //get limit list paginated
  http.post('*/api/v1/ifrm/listsandlimits/limit/:list/data', async () => {
    return HttpResponse.json(responses.listsandlimits.merchantPaginated);
  }),
  //update limit list
  http.put('*/api/v1/ifrm/listsandlimits/limit/:list', async () => {
    return HttpResponse.json({});
  }),
  //add limit item
  http.post('*/api/v1/ifrm/listsandlimits/limit/:list', async () => {
    return HttpResponse.json({});
  }),
  //add limit item bulk
  http.post('*/api/v1/ifrm/listsandlimits/limit/:list/limitInfo-bulk-upload', async () => {
    return HttpResponse.json({});
  }),
  //delete limit item
  http.delete('*/api/v1/ifrm/listsandlimits/limit/:list/:type', async () => {
    return HttpResponse.json({});
  }),
  //get all lists
  http.get('*/api/v1/ifrm/listsandlimits/list/info', async () => {
    return HttpResponse.json(responses.listsandlimits.types);
  }),

  //UDS
  //get violated rules
  http.post('*/api/v1/ifrm/uds/:channel/violated-rules/:id', async () => {
    return HttpResponse.json(responses.caseAssignment.violatedRules);
  }),
  //get violated rules transactions
  http.post('*/api/v1/ifrm/uds/:channel/txn-details/violated-rules/:txnId', async () => {
    return HttpResponse.json(responses.caseAssignment.violatedRules);
  }),
  //get transaction details
  http.get('*/api/v1/ifrm/uds/:channel/alert-transaction/:txnId', () => {
    return HttpResponse.json(responses.caseAssignment.transaction);
  }),

  //Prefilter
  //fetch TPS
  http.get('*/api/v1/ifrm/prefilter/tps', () => {
    return HttpResponse.json(responses.prefilter.tps);
  }),
  //add TPS
  http.post('*/api/v1/ifrm/prefilter/tps', () => {
    return HttpResponse.json({});
  }),
  //update TPS
  http.put('*/api/v1/ifrm/prefilter/tps', () => {
    return HttpResponse.json({});
  }),
  //delete TPS
  http.delete('*/api/v1/ifrm/prefilter/tps/channel/:channel', () => {
    return HttpResponse.json({});
  }),
  //get prefilter categories
  http.get('*/api/v1/ifrm/prefilter/category', () => {
    return HttpResponse.json(responses.prefilter.category);
  }),
  //get filters
  http.get('*/api/v1/ifrm/prefilter/filter', () => {
    return HttpResponse.json(responses.prefilter.filter);
  }),
  //add filters
  http.post('*/api/v1/ifrm/prefilter/filter', () => {
    return HttpResponse.json({});
  }),
  //update filters
  http.put('*/api/v1/ifrm/prefilter/filter', () => {
    return HttpResponse.json({});
  }),
  //delete filters
  http.delete('*/api/v1/ifrm/prefilter/filter/id/:id', () => {
    return HttpResponse.json({});
  }),

  //Release Funds
  //get release funds list
  http.get('*/api/v1/ifrm/alertmanager/heldfunds/:userId/torelease', () => {
    return HttpResponse.json(responses.release.list);
  }),
  //release funds bulk
  http.put('*/api/v1/ifrm/casereview/:userId/bulk/funds/release', () => {
    return HttpResponse.json({});
  }),
  //release funds
  http.put('*/api/v1/ifrm/casereview/user/funds/release', () => {
    return HttpResponse.json({});
  }),
  //get document status
  http.get('*/api/v1/ifrm/alertmanager/holdrelease/:txnId/docstatus', () => {
    return HttpResponse.json(responses.release.docStatus);
  }),
  //hold cases
  http.put('*/api/v1/ifrm/casereview/funds/hold', () => {
    return HttpResponse.json({});
  }),
  //request documents
  http.put('*/api/v1/ifrm/alertmanager/holdrelease/:txnId/update/docstatus', () => {
    return HttpResponse.json({});
  }),

  //DSL
  //get components
  http.get('*/api/v1/ifrm/:channel/ruleengine/rule/dsl/components', () => {
    return HttpResponse.json(responses.rules.components);
  }),
  //verify DSL
  http.post('*/api/v1/ifrm/:channel/ruleengine/validate', () => {
    return HttpResponse.json('OK');
  }),
  //create rule
  http.post('*/api/v1/ifrm/:channel/ruleengine/rule/approval/supervisor', () => {
    return HttpResponse.json(responses.rules.createRule);
  }),
  //create rule
  http.post('*/api/v1/ifrm/:channel/ruleengine/rule/approval/supervisor/:rule/update', () => {
    return HttpResponse.json(responses.rules.createRule);
  }),
  //get nonproduction rules
  http.get('*/api/v1/ifrm/:channel/ruleengine/rule/approval/:role/fetch', () => {
    return HttpResponse.json(responses.rules.approval);
  }),
  //get action lists
  http.get('*/api/v1/ifrm/:channel/ruleengine/action', () => {
    return HttpResponse.json(responses.rules.actionCode);
  }),
  //approve rule
  http.put('*/api/v1/ifrm/:channel/ruleengine/rule/approval/:role/response', () => {
    return HttpResponse.json('Ok');
  }),
  //get rules
  http.get('*/api/v1/ifrm/:channel/ruleengine/rule', () => {
    return HttpResponse.json(responses.rules.list);
  }),
  //toggle rule status
  http.put('*/api/v1/ifrm/:channel/ruleengine/rule/:code/activate', () => {
    return HttpResponse.json({});
  }),
  //toggle rule activation
  http.put('*/api/v1/ifrm/:channel/ruleengine/rule/:code/enable', () => {
    return HttpResponse.json({});
  }),
  //toggle rule activation
  http.put('*/api/v1/ifrm/:channel/ruleengine/rule/:code/explicit', () => {
    return HttpResponse.json({});
  }),
  //get rulenames
  http.get('*/api/v1/ifrm/:channel/ruleengine/rulenames', () => {
    return HttpResponse.json(responses.rules.ruleNames);
  }),
  //update action order
  http.post('*/api/v1/ifrm/:channel/ruleengine/action/order/update', () => {
    return HttpResponse.json({});
  }),
  //get citation list
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/citations/rule/:ruleId', () => {
    return HttpResponse.json([]);
  }),
  //get all citations
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/citations/all', () => {
    return HttpResponse.json([]);
  }),
  //get alert cateogries
  http.get('*/api/v1/ifrm/frm/ruleengine/fetch/alert/categories', () => {
    return HttpResponse.json(responses.rules.alertCategories);
  }),

  //Monitoring
  //get transaction count
  http.get('*/api/v1/ifrm/:channel/ruleengine/transaction/today/count', () => {
    return HttpResponse.json(responses.monitoring.count);
  }),
  //get transactions
  http.get('*/api/v1/ifrm/transactionmonitoring/transactions/day/user', () => {
    return HttpResponse.json(responses.monitoring.transaction);
  }),

  //Statistics
  //get transaction stats
  http.get('*/api/v1/ifrm/uds/:channel/transaction/statistics/entityId/:id', () => {
    return HttpResponse.json(responses.statisticsDetails.transactionStatistics);
  }),
  //get customer stats
  http.get('*/api/v1/ifrm/uds/:channel/customer/statistics/entityId/:id', () => {
    return HttpResponse.json(responses.statisticsDetails.customerStatistics);
  }),

  //SCP
  //get config
  http.get('*/api/v1/ifrm/serviceconfigportal/config', () => {
    return HttpResponse.json(responses.scp.config);
  }),
  //set config
  http.put('*/api/v1/ifrm/serviceconfigportal/config', () => {
    return HttpResponse.json({});
  }),

  //History
  //get transaction history
  http.post('*/api/v1/ifrm/uds/:channel/transactions/history/category/:category', () => {
    return HttpResponse.json(responses.caseAssignment.cases);
  }),
  //get advance search
  http.post('*/api/v1/ifrm/uds/:channel/advance-search', () => {
    return HttpResponse.json(responses.caseAssignment.cases);
  }),
  //business dashboard
  //fetch business KPIs
  http.post('*/api/v1/ifrm/casereview/case/investigator/business/:channel/count', () => {
    return HttpResponse.json(responses.businessDashboard.businessKpis);
  }),
  //fetch action share
  http.post('*/api/v1/ifrm/casereview/case/investigator/business/:channel/txn/actions', () => {
    return HttpResponse.json(responses.businessDashboard.actionShare);
  }),
  //fetch High Alert Customers
  http.post(
    '*/api/v1/ifrm/casereview/case/investigator/business/:channel/customers/reports/frauds',
    () => {
      return HttpResponse.json(responses.businessDashboard.highAlertCustomers);
    }
  ),
  //fetch No Violations Fraud
  http.post('*/api/v1/ifrm/casereview/case/investigator/:channel/fraud/cases', () => {
    return HttpResponse.json(responses.businessDashboard.noViolationFraud);
  }),
  //fetch Rule Category Trend
  http.post(
    '*/api/v1/ifrm/casereview/case/investigator/business/:channel/rule/category/trend',
    () => {
      return HttpResponse.json(responses.businessDashboard.ruleCategoryTrend);
    }
  ),
  //citation
  http.get('*/api/v1/ifrm/casereview/case/:caseRefNo/citations/fetch/all', () => {
    return HttpResponse.json(responses.citations);
  }),
  http.post('*/api/v1/ifrm/casereview/case/capture/citation/response', () => {
    return HttpResponse.json({});
  }),
  //communication
  http.get('*/api/v1/ifrm/casereview/:channel/case/:caseRefNo/call/:customerId', () => {
    return HttpResponse.json({});
  }),
  http.post('*/api/v1/ifrm/casereview/:channel/case/:caseRefNo/email/:customerId', () => {
    return HttpResponse.json({});
  }),
  http.post('*/api/v1/ifrm/casereview/:channel/case/:caseRefNo/sms/:customerId', () => {
    return HttpResponse.json({});
  }),
  http.get('*/api/v1/ifrm/casereview/:channel/case/:caseRefNo/commdetails', () => {
    return HttpResponse.json(responses.customerCommunication);
  }),
  //compliance dashboard
  http.get('*/api/v1/ifrm/casereview/case/investigator/:channel/frm/report/cases', () => {
    return HttpResponse.json({ cases: responses.complianceDashboard });
  }),
  //dynamic counter
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/dynamic/counters', () => {
    return HttpResponse.json(responses.dynamicCounters.counters);
  }),
  http.post('*/api/v1/ifrm/:channel/ruleengine/create/counter', () => {
    return HttpResponse.json({});
  }),
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/conditions', () => {
    return HttpResponse.json(responses.dynamicCounters.conditionalAttributes);
  }),
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/primary/attributes', () => {
    return HttpResponse.json(responses.dynamicCounters.allAttributes);
  }),
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/secondary/attributes', () => {
    return HttpResponse.json(responses.dynamicCounters.subAttributes);
  }),
  //incident
  http.post('*/api/v1/ifrm/casereview/cpifr/create/incident', () => {
    return HttpResponse.json(responses.incidents.incident);
  }),
  http.get('*/api/v1/ifrm/casereview/cpifr/fetch/incidents/all', () => {
    return HttpResponse.json(responses.incidents.allIncidents);
  }),
  http.get('*/api/v1/ifrm/casereview/cpifr/fetch/incidents/:incidentId', () => {
    return HttpResponse.json(responses.incidents.incident);
  }),
  http.post('*/api/v1/ifrm/casereview/cpifr/against/incident/report', () => {
    return HttpResponse.text(responses.incidents.download);
  }),
  http.get('*/api/v1/ifrm/casereview/cpifr/fetch/masters', () => {
    return HttpResponse.json(responses.incidents.masters);
  }),
  http.post('*/api/v1/ifrm/casereview/cpifr/closed/cases/bucket', () => {
    return HttpResponse.json(responses.incidents.closedList);
  }),
  http.post('*/api/v1/ifrm/casereview/cpifr/create/incident', () => {
    return HttpResponse.json(responses.incidents.incident);
  }),
  http.post('*/api/v1/ifrm/casereview/cpifr/update/frn', () => {
    return HttpResponse.json({});
  }),
  http.post('*/api/v1/ifrm/casereview/case/cpifr/download/history', () => {
    return HttpResponse.json([]);
  }),
  //partner bank
  http.get('*/api/v1/ifrm/useraccessmanagement/getPartners', () => {
    return HttpResponse.json(responses.partnerBanks);
  }),
  http.post('*/api/v1/ifrm/useraccessmanagement/partner/register', () => {
    return HttpResponse.json({});
  }),
  //rfi Report
  http.get('*/api/v1/ifrm/uds/str/high-value-txn-closed-account/count', () => {
    return HttpResponse.json(responses.rfiReports.highValueClosedAccount.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/high-value-txn-closed-account', () => {
    return HttpResponse.json(responses.rfiReports.highValueClosedAccount.data);
  }),
  http.post('*/api/v1/ifrm/uds/str/unusual-decline-turnover/count', () => {
    return HttpResponse.json(responses.rfiReports.unusualDeclineTurnover.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/unusual-decline-turnover', () => {
    return HttpResponse.json(responses.rfiReports.unusualDeclineTurnover.data);
  }),
  http.get('*/api/v1/ifrm/uds/str/high-value-txn-new-account/count', () => {
    return HttpResponse.json(responses.rfiReports.highValueNewAccount.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/high-value-txn-new-account', () => {
    return HttpResponse.json(responses.rfiReports.highValueNewAccount.data);
  }),
  http.post('*/api/v1/ifrm/uds/str/f2s-ratio/count', () => {
    return HttpResponse.json(responses.rfiReports.fraudToSaleRatio.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/f2s-ratio', () => {
    return HttpResponse.json(responses.rfiReports.fraudToSaleRatio.data);
  }),
  http.post('*/api/v1/ifrm/uds/str/top-n-merchant-txns/count', () => {
    return HttpResponse.json(responses.rfiReports.topMerchant.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/top-n-merchant-txns', () => {
    return HttpResponse.json(responses.rfiReports.topMerchant.data);
  }),
  http.post('*/api/v1/ifrm/uds/str/nbfc-txns/count', () => {
    return HttpResponse.json(responses.rfiReports.nbfcTrxns.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/nbfc-txns', () => {
    return HttpResponse.json(responses.rfiReports.nbfcTrxns.data);
  }),
  http.post('*/api/v1/ifrm/uds/str/high-risk-country-txns/count', () => {
    return HttpResponse.json(responses.rfiReports.hrcTrxns.count);
  }),
  http.post('*/api/v1/ifrm/uds/str/high-risk-country-txns', () => {
    return HttpResponse.json(responses.rfiReports.hrcTrxns.data);
  }),
  //rule Dashboard
  http.post('*/api/v1/ifrm/casereview/case/investigator/rule/:channel/count', () => {
    return HttpResponse.json(responses.ruleDashboard.stats);
  }),
  http.post('*/api/v1/ifrm/casereview/case/investigator/rule/:channel/efficacy', () => {
    return HttpResponse.json(responses.ruleDashboard.efficacy);
  }),
  http.post('*/api/v1/ifrm/uds/frm/rule/efficacy', () => {
    return HttpResponse.json(responses.ruleDashboard.efficacy);
  }),
  http.post('*/api/v1/ifrm/casereview/case/investigator/rule/:channel/efficiency', () => {
    return HttpResponse.json(responses.ruleDashboard.efficiency);
  }),
  http.post('*/api/v1/ifrm/casereview/case/investigator/rule/:channel/effectiveness', () => {
    return HttpResponse.json(responses.ruleDashboard.effectiveness);
  }),
  http.post('*/api/v1/ifrm/casereview/case/investigator/rule/:channel/behaviour', () => {
    return HttpResponse.json(responses.ruleDashboard.behaviour);
  }),
  //rule snooze
  http.get('*/api/v1/ifrm/:channel/ruleengine/fetch/snooze/attributes', () => {
    return HttpResponse.json(responses.snoozeRules.attributes);
  }),
  http.post('*/api/v1/ifrm/:channel/ruleengine/rule/recommend/snooze', () => {
    return HttpResponse.json({});
  }),
  http.get('*/api/v1/ifrm/:channel/ruleengine/rules/all/snooze', () => {
    return HttpResponse.json(responses.snoozeRules.list);
  }),
  http.post('*/api/v1/ifrm/:channel/ruleengine/rule/process/snooze', () => {
    return HttpResponse.json({});
  }),
  http.post('*/api/v1/ifrm/:channel/ruleengine/rule/process/unsnooze', () => {
    return HttpResponse.json({});
  }),
  //sandbox
  http.get('*/api/v1/ifrm/uds/frm/fetchdaterange', () => {
    return HttpResponse.json(responses.sandboxing.dateRange);
  }),
  http.post('*/api/v1/ifrm/uds/frm/backtestrule', () => {
    return HttpResponse.json(responses.sandboxing.testingMsg);
  }),
  http.post('*/api/v1/ifrm/uds/frm/checkbacktestingstatus', () => {
    return HttpResponse.json(responses.sandboxing.testing);
  }),
  http.post('*/api/v1/ifrm/uds/frm/gettxnsdetailforbacktest', () => {
    return HttpResponse.json(responses.sandboxing.violationDetails);
  }),
  http.get('*/api/v1/ifrm/uds/frm/getAllBacktestResult', () => {
    return HttpResponse.json(responses.sandboxing.testHistory);
  }),
  //settings
  http.post('*/api/v1/ifrm/casereview/str/filing/dictionary/update', () => {
    return HttpResponse.json({});
  }),
  http.get('*/api/v1/ifrm/casereview/str/filing/fetch', () => {
    return HttpResponse.json(responses.settings);
  }),
  //product configurations
  http.get('*/api/v1/ifrm/casereview/fetch/product/feature/details', () => {
    return HttpResponse.json(responses.configurations);
  }),
  //sla dashboard
  http.get('*/api/v1/ifrm/useraccessmanagement/user/shift/active', () => {
    return HttpResponse.json(responses.slaDashboard.shiftDetails);
  }),
  http.post('*/api/v1/ifrm/casereview/case/supervisor/:channel/count', () => {
    return HttpResponse.json(responses.slaDashboard.slaKpis);
  }),
  http.post('*/api/v1/ifrm/casereview/case/supervisor/:channel/analysts/sla', () => {
    return HttpResponse.json(responses.slaDashboard.employeeSla);
  }),
  http.get('*/api/v1/ifrm/useraccessmanagement/reviewer/occupancyrate', () => {
    return HttpResponse.json(responses.slaDashboard.occupancyRate);
  }),
  http.post('*/api/v1/ifrm/casereview/case/supervisor/:channel/sla/breaches', () => {
    return HttpResponse.json(responses.slaDashboard.slaBreachCases);
  }),
  http.post('*/api/v1/ifrm/casereview/case/supervisor/:channel/resolution/rate', () => {
    return HttpResponse.json(responses.slaDashboard.firstContactRate);
  }),
  http.post('*/api/v1/ifrm/casereview/case/supervisor/:channel/analysts/sla/trends', () => {
    return HttpResponse.json(responses.slaDashboard.analystTAT);
  }),
  //str report
  http.get('*/api/v1/ifrm/casereview/str/case/suspicion/master/all', () => {
    return HttpResponse.json(responses.strReport.masters);
  }),
  http.post('*/api/v1/ifrm/casereview/str/case/suspicion/details', () => {
    return HttpResponse.json({});
  }),
  http.get('*/api/v1/ifrm/casereview/fetch/str/case/:caseRefNo/gos/details', () => {
    return HttpResponse.json(responses.strReport.details);
  }),
  http.put(
    '*/api/v1/ifrm/casereview/case/:caseRefNo/merchant/:merchantId/xmlRecordDownload',
    async () => {
      return HttpResponse.json({});
    }
  ),
  http.get('*/api/v1/ifrm/casereview/case/fetch/:caseRefNo/str/download/history', () => {
    return HttpResponse.json(responses.strReport.history);
  }),
  http.post('*/api/v1/ifrm/casereview/case/fetch/str/upload/update', () => {
    return HttpResponse.json({});
  }),

  //Demographics
  //get Entity Demographics
  http.get('*/api/v1/ifrm/prefilter/:entity/:entityId', async () => {
    return HttpResponse.json(JSON.stringify(responses.demographics.merchant));
  }),
  //get entity
  http.get('*/api/v1/ifrm/uds/:entity/:entityId', async () => {
    return HttpResponse.json(responses.demographics.merchant);
  }),
  //get customer details with all accounts
  http.get('*/api/v1/ifrm/uds/frm/customerwithallaccounts/:customerId', async () => {
    return HttpResponse.json(responses.demographics.customerAllAccounts);
  }),
  //get entity score
  http.get('*/api/v1/ifrm/uds/:entity/:entityId/score', async () => {
    return HttpResponse.json(responses.uds.merchantScore);
  }),
  //get facctum details
  http.post('*/api/v1/ifrm/uds/str/facctum/match', async () => {
    return HttpResponse.json({});
  }),
  //get entity list
  http.post('*/api/v1/ifrm/uds/entity-profile', async () => {
    return HttpResponse.json(responses.uds.profile);
  })
];

export { handlers };
