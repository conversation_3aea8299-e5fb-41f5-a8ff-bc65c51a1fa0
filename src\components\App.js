/* eslint-disable import/no-named-as-default */
import React from 'react';
import { Switch, Route } from 'react-router-dom';
import Loader from 'containers/loader/LoaderContainer';
import LayoutContainer from 'containers/LayoutContainer';
import Snackbar from 'containers/common/SnackbarContainer';
import LoginContainer from 'containers/auth/LoginContainer';
import PageNotFound from 'components/PageNotFound';

class App extends React.Component {
  render() {
    return (
      <div className="root-div">
        <Switch>
          <Route exact path="/">
            <LoginContainer />
          </Route>
          <Route path="/">{(props) => <LayoutContainer {...props} />}</Route>
          <Route>
            <PageNotFound />
          </Route>
        </Switch>
        <Loader />
        <Snackbar />
      </div>
    );
  }
}

export default App;
