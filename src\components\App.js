import React from 'react';
import { Switch, Route } from 'react-router-dom';

import PageNotFound from 'components/PageNotFound';
import LoginContainer from 'containers/auth/LoginContainer';
import Snackbar from 'containers/common/SnackbarContainer';
import LayoutContainer from 'containers/LayoutContainer';
import Loader from 'containers/loader/LoaderContainer';

class App extends React.Component {
  render() {
    return (
      <div className="root-div">
        <Switch>
          <Route exact path="/">
            <LoginContainer />
          </Route>
          <Route path="/">{(props) => <LayoutContainer {...props} />}</Route>
          <Route>
            <PageNotFound />
          </Route>
        </Switch>
        <Loader />
        <Snackbar />
      </div>
    );
  }
}

export default App;
