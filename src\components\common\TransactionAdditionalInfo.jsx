import React from 'react';
import PropTypes from 'prop-types';
import { renderDataColumnList } from 'utility/customRenders';
import { addItemToList } from 'constants/functions';

const getAdditionalInfoList = (details) => {
  let additionalInfoList = [];

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.activityID,
    'Activity ID',
    details?.masterFields?.txnAdditionalFields?.activityID,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.processingCode,
    'Processing Code',
    details?.masterFields?.txnAdditionalFields?.processingCode,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.functionCode,
    'Function Code',
    details?.masterFields?.txnAdditionalFields?.functionCode,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.credentialType,
    'Credential Type',
    details?.masterFields?.txnAdditionalFields?.credentialType,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialSubType,
    'Credential SubType',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialSubType,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataCode,
    'Credential Data Code',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataCode,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataKi,
    'Credential Data Ki',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataKi,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataValue,
    'Credential Data Value',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.credentialDataValue,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.fixDeposit,
    'Fix Deposit',
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.fixDeposit,
    additionalInfoList
  );
  addItemToList(
    details?.masterFields?.txnAdditionalFields?.accountBalance,
    'Account Balance',
    details?.masterFields?.txnAdditionalFields?.accountBalance,
    additionalInfoList
  );

  addItemToList(
    details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.checkerId ||
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.checkerName,
    'Checker',
    `${details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.checkerId || ''}, ${
      details?.masterFields?.txnAdditionalFields?.moreTxnAdditionalFields?.checkerName || ''
    }`,
    additionalInfoList,
    null,
    true
  );

  addItemToList(
    details?.transactionInfo?.txnMessageId,
    'Message',
    details?.transactionInfo?.txnMessageId,
    additionalInfoList
  );
  addItemToList(details?.remarks, 'Remarks', details?.remarks, additionalInfoList);

  return additionalInfoList;
};
function TransactionAdditionalInfo({ details }) {
  const additionalInfoList = getAdditionalInfoList(details);

  if (additionalInfoList.length === 0) {
    return null;
  }

  return (
    <div className="transaction-item">
      <b>Additional Details</b>
      {renderDataColumnList(additionalInfoList, details?.identifiers?.partnerId)}
    </div>
  );
}

TransactionAdditionalInfo.propTypes = {
  details: PropTypes.object.isRequired
};

export default TransactionAdditionalInfo;
