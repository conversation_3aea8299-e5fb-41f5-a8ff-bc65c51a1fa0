import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleEfficiency } from 'actions/ruleDashboardActions';
import RuleEfficiencyGraph from 'components/dashboards/RuleEfficiencyGraph';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    ruleEfficiency: state.ruleDashboard.efficiency
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleEfficiency: bindActionCreators(onFetchRuleEfficiency, dispatch)
  };
};

const RuleEfficiencyGraphContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(RuleEfficiencyGraph);

export default RuleEfficiencyGraphContainer;
