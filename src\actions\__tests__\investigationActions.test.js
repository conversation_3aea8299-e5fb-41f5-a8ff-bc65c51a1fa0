import responses from 'mocks/responses';

import * as actions from 'actions/investigationActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('investigation actions', () => {
  it('should fetch transaction trend for customer', () => {
    const formData = {
      channel: 'frm',
      customerId: '123',
      filters: {
        startDate: '2023-08-01T13:44:10',
        endDate: '2023-08-03T13:44:10'
      }
    };
    const response = {
      trendTimeGraph: responses.trend.trendTimeGraph,
      perPayeeTrend: responses.trend.perPayeeTrend
    };
    const expectedActions = [
      { type: types.ON_CUSTOMER_TRENDS_FETCH_LOADING },
      { type: types.ON_SUCCESSFUL_CUSTOMER_TRENDS_FETCH, response }
    ];
    const store = mockStore({ investigation: {} });

    return store.dispatch(actions.onFetchCustomerTrend(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should fetch similar txns for trasaction', () => {
    const formData = {
      similarTxnCategory: 'terminalId',
      pageNo: 1,
      pageSize: 10
    };

    const expectedActions = [
      { type: types.ON_FETCH_SIMILAR_TXNS_LOADING },
      {
        type: types.ON_SUCCESSFUL_FETCH_SIMILAR_TXNS,
        response: responses.caseAssignment.similar,
        similarTxnCategory: 'terminalId'
      }
    ];
    const store = mockStore({ investigation: {} });

    return store.dispatch(actions.onFetchSimilarTransactions(formData, 'frm')).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should escalate by mail', () => {
    const formData = {
      to: '<EMAIL>',
      subject: 'escalateSubject',
      message: 'escalateMessage',
      from: '<EMAIL>',
      mobileNo: 9876542321,
      id: '123'
    };

    const expectedActions = [
      { type: types.ON_SUCCESS_ALERT, response: { message: 'Mail sent successfully' } },
      { type: types.ON_TOGGLE_VERDICT_MODAL, channel: 'frm' }
    ];
    const store = mockStore();

    return store.dispatch(actions.onEscalateByMail(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Similar Txn CategoryList', () => {
    const expectedActions = [
      { type: types.ON_SIMILAR_TXNS_CATEGORY_FETCH_LOADING },
      {
        type: types.ON_SIMILAR_TXNS_CATEGORY_FETCH_SUCCESS,
        response: {
          similarTxnCategory: [
            {
              label: 'terminalId',
              value: 'terminalId'
            }
          ]
        }
      }
    ];
    const store = mockStore({ similarTxnCategoryList: {} });

    return store.dispatch(actions.onFetchSimilarTxnCategoryList()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Channelwise Counterparty Id', () => {
    const expectedActions = [
      { type: types.ON_FETCH_CHANNEL_COUNTER_PARTY_ID_LOADING },
      {
        type: types.ON_FETCH_CHANNEL_COUNTER_PARTY_ID_SUCCESS,
        response: [
          {
            label: 'terminalId',
            value: 'terminalId'
          }
        ]
      }
    ];
    const store = mockStore({ channelCounterpartyId: {} });

    return store.dispatch(actions.onFetchChannelwiseCounterpartyId()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
