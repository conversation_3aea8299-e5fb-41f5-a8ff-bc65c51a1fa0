import {
  ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING,
  ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH,
  ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE,
  ON_DECISION_MATRIX_ACTIONS_FETCH_LOADING,
  ON_SUCCESSFUL_DECISION_MATRIX_ACTIONS_FETCH,
  ON_DECISION_MATRIX_ACTIONS_FETCH_FAILURE
} from 'constants/actionTypes';
import objectAssign from 'object-assign';
import initialState from './initialState';

export default function scpReducer(state = initialState.scp, action) {
  switch (action.type) {
    case ON_SCP_CONFIGURATIONS_LIST_FETCH_LOADING:
      return objectAssign({}, state, {
        configurationsData: objectAssign({}, state.configurationsData, { loader: true })
      });
    case ON_SUCCESSFUL_SCP_CONFIGURATIONS_LIST_FETCH:
      return objectAssign({}, state, {
        configurationsData: objectAssign({}, state.configurationsData, {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_SCP_CONFIGURATIONS_LIST_FETCH_FAILURE:
      return objectAssign({}, state, {
        configurationsData: objectAssign({}, state.configurationsData, {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    case ON_DECISION_MATRIX_ACTIONS_FETCH_LOADING:
      return objectAssign({}, state, {
        decisionMatrixActions: objectAssign({}, state.decisionMatrixActions, { loader: true })
      });
    case ON_SUCCESSFUL_DECISION_MATRIX_ACTIONS_FETCH:
      return objectAssign({}, state, {
        decisionMatrixActions: objectAssign({}, state.decisionMatrixActions, {
          list: action.response,
          loader: false,
          error: false,
          errorMessage: ''
        })
      });
    case ON_DECISION_MATRIX_ACTIONS_FETCH_FAILURE:
      return objectAssign({}, state, {
        decisionMatrixActions: objectAssign({}, state.decisionMatrixActions, {
          list: [],
          loader: false,
          error: true,
          errorMessage: action.response?.message || 'Unknown error'
        })
      });
    default:
      return state;
  }
}
