import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import LimitLists from 'components/prefilters/limitLists/LimitLists';
import * as prefiltersListAction from 'actions/prefiltersListAction';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    actions: bindActionCreators(prefiltersListAction, dispatch)
  };
};

const LimitListsContainer = connect(mapStateToProps, mapDispatchToProps)(LimitLists);

export default LimitListsContainer;
