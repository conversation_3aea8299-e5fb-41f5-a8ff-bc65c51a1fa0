import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as prefiltersListAction from 'actions/prefiltersListAction';
import LimitLists from 'components/prefilters/limitLists/LimitLists';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme
});

const mapDispatchToProps = (dispatch) => ({
  actions: bindActionCreators(prefiltersListAction, dispatch)
});

const LimitListsContainer = connect(mapStateToProps, mapDispatchToProps)(LimitLists);

export default LimitListsContainer;
