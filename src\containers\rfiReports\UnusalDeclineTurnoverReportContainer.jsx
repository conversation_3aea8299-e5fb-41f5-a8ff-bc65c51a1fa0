import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import {
  onFetchUnusalDeclineTurnoverCount,
  onFetchUnusalDeclineTurnoverData
} from 'actions/rfiReportActions';
import UnusualDeclineTurnoverReport from 'components/rfiReports/UnusualDeclineTurnoverReport';

const mapStateToProps = (state) => ({
  unusualDeclineTurnover: state.rfiReports.unusualDeclineTurnover
});

const mapDispatchToProps = (dispatch) => ({
  fetchUnusualDeclineTurnoverCount: bindActionCreators(onFetchUnusalDeclineTurnoverCount, dispatch),
  fetchUnusualDeclineTurnoverData: bindActionCreators(onFetchUnusalDeclineTurnoverData, dispatch)
});

const UnusualDeclineTurnoverReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(UnusualDeclineTurnoverReport);

export default UnusualDeclineTurnoverReportContainer;
