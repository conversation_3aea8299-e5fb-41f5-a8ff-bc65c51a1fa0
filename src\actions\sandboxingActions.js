import {
  ON_<PERSON>ET<PERSON>_SANDBOX_DATE_RANGE_LOADING,
  ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS,
  ON_FETCH_SANDBOX_DATE_RANGE_FAILURE,
  ON_TEST_SANDBOX_RULES_LOADING,
  ON_TEST_SANDBOX_RULES_SUCCESS,
  ON_TEST_SANDBOX_RULES_FAILURE,
  ON_FETCH_SANDBOX_STATUS_SUCCESS,
  ON_FETCH_SANDBOX_STATUS_FAILURE,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS,
  ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE,
  ON_FETCH_SANDBOX_HISTORY_LOADING,
  ON_FETCH_SANDBOX_HISTORY_SUCCESS,
  ON_FETCH_SANDBOX_HISTORY_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchSandboxDateRange() {
  return client({
    headers: {
      env: 'sandbox'
    },
    url: `uds/frm/fetchdaterange`
  });
}

function onFetchSandboxDateRangeLoading() {
  return { type: ON_FETCH_SANDBOX_DATE_RANGE_LOADING };
}

function onFetchSandboxDateRangeSuccess(response) {
  return {
    type: ON_FETCH_SANDBOX_DATE_RANGE_SUCCESS,
    response
  };
}

function onFetchSandboxDateRangeFailure(response) {
  return {
    type: ON_FETCH_SANDBOX_DATE_RANGE_FAILURE,
    response
  };
}

function onFetchSandboxDateRange() {
  return function (dispatch) {
    dispatch(onFetchSandboxDateRangeLoading());
    return fetchSandboxDateRange().then(
      (success) => dispatch(onFetchSandboxDateRangeSuccess(success)),
      (error) => dispatch(onFetchSandboxDateRangeFailure(error))
    );
  };
}

function testSandboxRules(formData) {
  return client({
    method: 'POST',
    headers: {
      env: 'sandbox'
    },
    url: `uds/frm/backtestrule`,
    data: formData
  });
}

function onTestSandboxRulesLoading() {
  return { type: ON_TEST_SANDBOX_RULES_LOADING };
}

function onTestSandboxRulesSuccess(response, ruleName, ruleCode) {
  return {
    type: ON_TEST_SANDBOX_RULES_SUCCESS,
    response,
    ruleName,
    ruleCode
  };
}

function onTestSandboxRulesFailure(response) {
  return {
    type: ON_TEST_SANDBOX_RULES_FAILURE,
    response
  };
}

function onTestSandboxRules(formData, ruleName, ruleCode) {
  return function (dispatch) {
    dispatch(onTestSandboxRulesLoading());
    return testSandboxRules(formData).then(
      (success) => dispatch(onTestSandboxRulesSuccess(success, ruleName, ruleCode)),
      (error) => dispatch(onTestSandboxRulesFailure(error))
    );
  };
}

function fetchSandboxTestStatus(testId, ruleName, ruleId) {
  return client({
    method: 'POST',
    headers: {
      env: 'sandbox'
    },
    url: `uds/frm/checkbacktestingstatus`,
    data: { ruleName, testId, ruleId }
  });
}

function onFetchSandboxTestStatusSuccess(response) {
  return {
    type: ON_FETCH_SANDBOX_STATUS_SUCCESS,
    response
  };
}

function onFetchSandboxTestStatusFailure(response) {
  return {
    type: ON_FETCH_SANDBOX_STATUS_FAILURE,
    response
  };
}

function onFetchSandboxTestStatus(testId, ruleName, ruleCode) {
  return function (dispatch) {
    return fetchSandboxTestStatus(testId, ruleName, ruleCode).then(
      (success) => dispatch(onFetchSandboxTestStatusSuccess(success)),
      (error) => dispatch(onFetchSandboxTestStatusFailure(error))
    );
  };
}

function fetchSandboxViolationDetails(formData) {
  return client({
    method: 'POST',
    headers: {
      env: 'sandbox'
    },
    url: `uds/frm/gettxnsdetailforbacktest`,
    data: formData
  });
}

function onFetchSandboxViolationDetailsLoading() {
  return {
    type: ON_FETCH_SANDBOX_VIOLATION_DETAILS_LOADING
  };
}

function onFetchSandboxViolationDetailsSuccess(response, date) {
  return {
    type: ON_FETCH_SANDBOX_VIOLATION_DETAILS_SUCCESS,
    response,
    date
  };
}

function onFetchSandboxViolationDetailsFailure(response, date) {
  return {
    type: ON_FETCH_SANDBOX_VIOLATION_DETAILS_FAILURE,
    response,
    date
  };
}

function onFetchSandboxViolationDetails(formData) {
  return function (dispatch) {
    dispatch(onFetchSandboxViolationDetailsLoading());
    return fetchSandboxViolationDetails(formData).then(
      (success) => dispatch(onFetchSandboxViolationDetailsSuccess(success, formData.date)),
      (error) => dispatch(onFetchSandboxViolationDetailsFailure(error, formData.date))
    );
  };
}

function fetchSandboxHistory() {
  return client({
    headers: {
      env: 'sandbox'
    },
    url: `uds/frm/getAllBacktestResult`
  });
}

function onFetchSandboxHistoryLoading() {
  return {
    type: ON_FETCH_SANDBOX_HISTORY_LOADING
  };
}

function onFetchSandboxHistorySuccess(response) {
  return {
    type: ON_FETCH_SANDBOX_HISTORY_SUCCESS,
    response
  };
}

function onFetchSandboxHistoryFailure(response) {
  return {
    type: ON_FETCH_SANDBOX_HISTORY_FAILURE,
    response
  };
}

function onFetchSandboxHistory() {
  return function (dispatch) {
    dispatch(onFetchSandboxHistoryLoading());
    return fetchSandboxHistory().then(
      (success) => dispatch(onFetchSandboxHistorySuccess(success)),
      (error) => dispatch(onFetchSandboxHistoryFailure(error))
    );
  };
}

export {
  onFetchSandboxDateRange,
  onTestSandboxRules,
  onFetchSandboxTestStatus,
  onFetchSandboxViolationDetails,
  onFetchSandboxHistory
};
