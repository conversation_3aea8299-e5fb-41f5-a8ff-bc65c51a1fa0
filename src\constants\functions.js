import _ from 'lodash';
import moment from 'moment';

import { ATTRIBUTES_VALUE_VALIDATION_PATTERNS } from 'constants/applicationConstants';

export const getDateDifference = (date1, date2) => {
  const diff = moment.duration(moment(date2).diff(moment(date1)));

  if (diff.years() > 0 && diff.months() > 0)
    return `${diff.years()} years and ${diff.months()} months`;

  if (diff.years() > 0) return `${diff.years()} years`;

  if (diff.months() > 0 && diff.days() > 0)
    return `${diff.months()} months and ${diff.days()} days`;

  return `${diff.days()} days`;
};

export const countFormatter = (number, formatType = 'western') => {
  if (typeof number !== 'number') return number;

  switch (formatType) {
    case 'indian':
      return number.toLocaleString('en-IN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });

    case 'western':
      return number.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });

    case 'european':
      return number.toLocaleString('de-DE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });

    default:
      return number;
  }
};

export const getScreen = (role) => {
  switch (role) {
    case 'supervisor':
    case 'principal-officer':
      return '/cases';
    case 'reviewer':
      return '/review';
    case 'maker':
    case 'checker':
    case 'investigator':
      return '/investigation';
    case 'monitor':
      return '/monitor';
    case 'auditor':
      return '/audit';
    default:
      return '';
  }
};

export const checkValue = (parentObject, childElement, isInt) =>
  !_.isEmpty(parentObject) &&
  _.has(parentObject, childElement) &&
  (!_.isEmpty(parentObject[childElement]) || isInt);

export const formatMobile = (number) => number?.substring(number.length - 10, number.length);

export const getCombinedRuleNames = (channels, ruleNames) => {
  let tempCombinedRuleNames = [];
  channels.map((channel) => {
    tempCombinedRuleNames = _.concat(tempCombinedRuleNames, ruleNames.list[channel]);
  });

  return _.uniqBy(tempCombinedRuleNames, 'name');
};

export const handleAttributeValueValidationProps = (dataType) => {
  switch (true) {
    case dataType === 'text':
      return {
        pattern: ATTRIBUTES_VALUE_VALIDATION_PATTERNS.alphanumeric,
        title: 'Please enter an alphanumeric value'
      };
    case dataType === 'int':
      return {
        pattern: ATTRIBUTES_VALUE_VALIDATION_PATTERNS.integer,
        title: 'Please enter an integer'
      };
    case dataType === 'numeric':
      return {
        pattern: ATTRIBUTES_VALUE_VALIDATION_PATTERNS.decimal,
        title: 'Please enter a decimal'
      };
    default:
      return {
        pattern: ATTRIBUTES_VALUE_VALIDATION_PATTERNS.default,
        title: ''
      };
  }
};

export function removeEmptyItemsFromObject(obj) {
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([, v]) => !_.isEmpty(v))
      .map(([k, v]) => [k, v === Object(v) ? removeEmptyItemsFromObject(v) : v])
  );
}

export function getCurrentPageCases(pageNo, pageSize, data = [], filters = []) {
  return _.chain(data)
    .filter((item) => {
      const filteredData =
        filters.length === 0
          ? true
          : _.chain(filters)
              .map((d) =>
                d.id === 'txnAmount' ? item?.[d?.id] >= d.value : item?.[d?.id]?.includes(d.value)
              )
              .reduce((sum, val) => sum && val)
              .value();
      return filteredData;
    })
    .slice((pageNo - 1) * pageSize, (pageNo - 1) * pageSize + pageSize)
    .value();
}

export const addItemToList = (
  condition,
  label,
  value,
  list,
  entityDetails = null,
  showToolTip = false
) => {
  if (condition !== null && condition !== undefined && condition !== '')
    list.push({ label, value, entityDetails, showToolTip });
};
