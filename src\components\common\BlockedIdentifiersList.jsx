import { map, forEach, isEmpty, split, includes } from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { Alert } from 'reactstrap';

function BlockedIdentifiersList({
  txnDetails,
  channelCounterpartyId,
  blockedIdentifiersList,
  fetchChannelCounterpartyId,
  fetchBlockedIdentifersList
}) {
  const txnId = txnDetails?.transactionInfo?.txnId;

  useEffect(() => {
    isEmpty(channelCounterpartyId.list) &&
      !channelCounterpartyId.loader &&
      !channelCounterpartyId.error &&
      fetchChannelCounterpartyId();
  }, []);

  useEffect(() => {
    forEach(getIdentifiers(), (data) => {
      !!txnId &&
        !!data.categoryName &&
        !!data.identifier &&
        !includes(blockedIdentifiersList.data, data.identifier) &&
        fetchBlockedIdentifersList(txnId, data);
    });
  }, [txnId]);

  const getIdentifiers = () => {
    const counterpartyId = channelCounterpartyId.list?.find((d) => {
      const channel = split(txnDetails?.masterFields?.channelName, ', ');
      return includes(channel, d.channel);
    });

    return [
      {
        categoryName: counterpartyId?.payeeInfo?.payeeAttribute,
        identifier:
          txnDetails?.payeeAccount?.[counterpartyId?.payeeInfo.payeeId]?.value ??
          txnDetails?.deviceInfo?.[counterpartyId?.payeeInfo.payeeId]?.value,
        partnerId: txnDetails?.identifiers?.partnerId
      },
      {
        categoryName: counterpartyId?.payerInfo?.payerAttribute,
        identifier:
          txnDetails?.payerAccount?.[counterpartyId?.payerInfo.payerId]?.value ??
          txnDetails?.deviceInfo?.[counterpartyId?.payerInfo.payerId]?.value,
        partnerId: txnDetails?.identifiers?.partnerId
      }
    ];
  };

  return (
    <>
      {map(blockedIdentifiersList.data, (d) => (
        <Alert color="warning" key={d.id} className="bg-warning">
          {d.categoryName} ({d.identifier}) was BLOCKED{' '}
          {d.startDate &&
            d.endDate &&
            `for ${moment.duration(moment(d.endDate).diff(moment(d.startDate), 'hours'))}hrs `}
          by reviewer on {moment(d.startDate).format('YYYY-MM-DD hh:mm A')}. Block status -
          {d.isActive ? 'Active' : 'Inactive'}
        </Alert>
      ))}
      {map(
        blockedIdentifiersList.errorMessage,
        (d, i) =>
          !!d && (
            <Alert color="error" key={`error-${i}`}>
              {d}
            </Alert>
          )
      )}
    </>
  );
}

BlockedIdentifiersList.propTypes = {
  txnDetails: PropTypes.object.isRequired,
  channelCounterpartyId: PropTypes.object.isRequired,
  blockedIdentifiersList: PropTypes.object.isRequired,
  fetchChannelCounterpartyId: PropTypes.func.isRequired,
  fetchBlockedIdentifersList: PropTypes.func.isRequired
};

export default BlockedIdentifiersList;
