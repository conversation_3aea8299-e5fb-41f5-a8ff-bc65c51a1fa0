import {
  ON_FETCH_COGNITIVE_STATUS_LOADING,
  ON_FETCH_COGNITIVE_STATUS_FAILURE,
  ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS
} from 'constants/actionTypes';
import { onShowFailureAlert, onShowSuccessAlert } from 'actions/alertActions';
import client from 'utility/apiClient';

function onFetchCognitiveStatusLoading() {
  return { type: ON_FETCH_COGNITIVE_STATUS_LOADING };
}

function fetchCognitiveStatus(channel) {
  return client({
    url: `${channel}/ruleengine/cognitive/status`,
    badRequestMessage: 'Curently unable to fetch cognitive status'
  });
}

function onSuccessfulFetchCognitiveStatus(channel, response) {
  return {
    type: ON_SUCCESSFUL_FETCH_COGNITIVE_STATUS,
    channel,
    response
  };
}

function onFetchCognitiveStatusFailure(response) {
  return {
    type: ON_FETCH_COGNITIVE_STATUS_FAILURE,
    response
  };
}

function onFetchCognitiveStatus(channel) {
  return function (dispatch) {
    dispatch(onFetchCognitiveStatusLoading());
    return fetchCognitiveStatus(channel).then(
      (success) => dispatch(onSuccessfulFetchCognitiveStatus(channel, success)),
      (error) => dispatch(onFetchCognitiveStatusFailure(error))
    );
  };
}

function toggleCognitiveStatus(channel) {
  return client({
    method: 'PUT',
    url: `${channel}/ruleengine/cognitive/status/activate`,
    badRequestMessage: 'Currently unable to activate/suspend cognitive'
  });
}

function onToggleCognitiveStatus(channel, currentStatus) {
  return function (dispatch) {
    return toggleCognitiveStatus(channel).then(
      () => {
        dispatch(
          onShowSuccessAlert({
            message: currentStatus ? 'Cognitive System Deactivated' : 'Cognitive System Activated'
          })
        );
        dispatch(onFetchCognitiveStatus(channel));
      },
      (error) => dispatch(onShowFailureAlert(error))
    );
  };
}

export { onFetchCognitiveStatus, onToggleCognitiveStatus };
