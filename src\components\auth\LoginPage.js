import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Row, Col, TabContent, TabPane } from 'reactstrap';

import { isCooperative } from 'constants/publicKey';
import LoginFormContainer from 'containers/auth/LoginFormContainer';
import bankiq_logo from 'images/BankIQ_FRC_Gradient.png';
import { encryptData } from 'utility/utils';

import LoginOTPForm from './LoginOTPForm';

const LoginPage = ({
  theme,
  has2FA,
  authDetails,
  login,
  submitOTP,
  validateUser2FA,
  fetchConfigurations
}) => {
  const history = useHistory();
  const { userCreds, session, loginType } = authDetails;
  const { userName, roles, isFirstLogin, channels } = userCreds;
  const [loginToken, setLoginToken] = useState(false);
  const [tab, setTab] = useState(0);
  const apiRequested = useRef(false);

  useEffect(() => {
    setTab(0);
    fetchConfigurations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (userName && has2FA && apiRequested.current) setTab(1);
  }, [has2FA, userCreds, userName]);

  useEffect(() => {
    if (session.isLoggedIn)
      redirectOnLogin({
        history,
        loginType,
        isFirstLogin,
        isCooperative,
        role: roles,
        channel: channels[0]
      });
  }, [channels, history, isFirstLogin, loginType, roles, session.isLoggedIn]);

  const handleLoginSubmit = (e) => {
    e.preventDefault();
    const { username, password, loginType, channel, role } = e.target;
    const formData = {
      channel: channel.value,
      effectiveRole: role.value,
      userName: username.value,
      password: password.value,
      timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
      ...(isCooperative && { loginType: loginType?.value || '' })
    };
    const loginToken = encryptData(JSON.stringify(formData));
    if (has2FA && role.value !== 'super-admin') {
      setLoginToken(() => loginToken);
      apiRequested.current = true;
      validateUser2FA(formData, loginToken);
    } else login(formData, encryptData(JSON.stringify(formData)));
  };

  const handleOTPSubmit = (e) => {
    e.preventDefault();
    const { otp } = e.target;
    const formData = {
      username: userName,
      otp: otp.value,
      loginToken
    };
    submitOTP(formData);
  };

  const redirectOnLogin = ({
    role,
    channel,
    history,
    loginType,
    isCooperative,
    isFirstLogin = false
  }) => {
    const roleRedirects = {
      'super-admin': '/users',
      admin: '/users',
      auditor: '/audit',
      monitoring: '/monitor',
      maker: '/investigation',
      'principal-officer': '/cases',
      investigator: '/dashboard',
      reviewer: isFirstLogin ? '/search' : '/review',
      supervisor:
        isCooperative && channel === 'frm' && loginType.toLowerCase() !== 'pulse'
          ? '/dashboard'
          : '/employees',
      checker: (() => {
        if (!isCooperative) return '/investigation';
        return channel === 'str' ? '/dsl' : '/dashboard';
      })()
    };

    const redirectPath = roleRedirects[role];

    if (redirectPath) history.push(redirectPath);
  };

  const backClick = () => {
    apiRequested.current = false;
    setTab(0);
  };

  return (
    <div className={`loginWrap ${theme}`}>
      <Row className="login-form-flex bg-img">
        <Col md="7" className="" />
        <Col md="5" className="form-col">
          <div className="login-form-container">
            <center>
              <img src={bankiq_logo} alt="BankIQ FRC" className="biq-logo" />
            </center>
            <div className="login-form-card">
              <TabContent activeTab={tab}>
                <TabPane tabId={0}>
                  <LoginFormContainer handleLoginSubmit={handleLoginSubmit} />
                </TabPane>
                {has2FA === 1 && (
                  <TabPane tabId={1}>
                    <LoginOTPForm
                      userName={userName}
                      submitOTP={handleOTPSubmit}
                      backClick={backClick}
                    />
                  </TabPane>
                )}
              </TabContent>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

LoginPage.propTypes = {
  theme: PropTypes.string.isRequired,
  has2FA: PropTypes.number.isRequired,
  authDetails: PropTypes.object.isRequired,
  login: PropTypes.func.isRequired,
  submitOTP: PropTypes.func.isRequired,
  validateUser2FA: PropTypes.func.isRequired,
  fetchConfigurations: PropTypes.func.isRequired
};

export default LoginPage;
