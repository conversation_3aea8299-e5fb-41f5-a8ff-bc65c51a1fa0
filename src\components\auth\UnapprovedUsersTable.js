'use strict';
import { faCheck, faTimes, faPencil } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import Moment from 'moment';
import objectAssign from 'object-assign';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import ReactTable from 'react-table';
import { FormGroup, Label, Input, Button, ButtonGroup } from 'reactstrap';

import ModalContainer from 'components/common/ModalContainer';
import { USER_LIST_HEADER } from 'constants/applicationConstants';

const UnapprovedUsersTable = ({
  unapprovedUserslist,
  userActions,
  channelslist,
  userName,
  theme,
  editUser
}) => {
  const [tableFilters, setTableFilters] = useState([]);
  const [userApproval, setUserApproval] = useState({
    showModal: false,
    userId: '',
    isApproved: false,
    comments: ''
  });

  useEffect(() => {
    userActions.onFetchUnapprovedUsersList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const toggleApprovalModal = (approvalId = '', approvalStatus = false) => {
    setUserApproval({
      showModal: !userApproval.showModal,
      userId: approvalId,
      isApproved: approvalStatus,
      comments: ''
    });
  };

  const setComment = (comment) =>
    setUserApproval(objectAssign({}, userApproval, { comments: comment }));

  const submitAction = (e) => {
    e.preventDefault();
    const formData = {
      userId: userApproval.userId,
      isApproved: userApproval.isApproved,
      reason: userApproval.comments
    };
    userActions.onApproveUser(formData);
    toggleApprovalModal();
  };

  const tableHeader = [
    {
      Header: 'Action',
      searchable: false,
      sortable: false,
      filterable: false,
      maxWidth: 100,
      Cell: (row) => {
        if (row.original.approver === userName && row.original.status === 'Pending')
          return (
            <ButtonGroup>
              <Button
                size="sm"
                color="success"
                title="Approve user"
                onClick={() => toggleApprovalModal(row.original.id, true)}>
                <FontAwesomeIcon icon={faCheck} />
              </Button>
              <Button
                size="sm"
                color="danger"
                title="Reject user"
                className="ml-1"
                onClick={() => toggleApprovalModal(row.original.id, false)}>
                <FontAwesomeIcon icon={faTimes} />
              </Button>
            </ButtonGroup>
          );
        else if (row.original.addedBy === userName && row.original.status === 'Rejected')
          return (
            <Button
              size="sm"
              color="warning"
              title="Edit user"
              onClick={() => editUser(row.original)}>
              <FontAwesomeIcon icon={faPencil} />
            </Button>
          );
        else return null;
      }
    },
    ...USER_LIST_HEADER,
    {
      Header: 'Supervisor',
      accessor: 'channelRoles',
      searchable: false,
      filterable: false,
      sortable: false,
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => {
        const filteredchannelList = value
          // eslint-disable-next-line react/prop-types
          .filter((channelRole) => _.includes(channelRole, 'supervisor'))
          .map((channelRole) => _.words(channelRole)[0]);

        const channels = _.uniq(filteredchannelList);

        if (channels.length > 0)
          if (channelslist.length === 1)
            return <FontAwesomeIcon icon={faCheck} className="channel-check" />;
          else return <span>{_.join(channels, ', ')}</span>;

        return null;
      }
    },
    {
      Header: 'Approval Status',
      accessor: 'status',
      // eslint-disable-next-line react/prop-types
      Cell: ({ value }) => (
        <span className={value === 'Pending' ? 'text-warning' : 'text-danger'}>{value}</span>
      ),
      filterMethod: (filter, row) => row[filter.id] === filter.value,
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          <option>Pending</option>
          <option>Rejected</option>
        </select>
      )
    }
  ];

  const subHeader = [
    { Header: 'Comment', accessor: 'reason', minWidth: 300 },
    { Header: 'Added By', accessor: 'addedBy', minWidth: 50 },
    {
      Header: 'Created Date',
      accessor: 'createdTimestamp',
      minWidth: 80,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null)
    },
    { Header: 'Approver', accessor: 'approver' },
    { Header: 'Updated By', accessor: 'updatedBy', minWidth: 50 },
    {
      Header: 'Updated Date',
      accessor: 'updatedTimestamp',
      minWidth: 80,
      Cell: ({ value }) => (value ? Moment(value).format('YYYY-MM-DD hh:mm A') : null)
    }
  ];

  return (
    <>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        data={unapprovedUserslist}
        SubComponent={(row) => (
          <div className="user-sub-table">
            <ReactTable
              columns={subHeader}
              data={[row.original]}
              defaultPageSize={1}
              showPagination={false}
            />
          </div>
        )}
        columns={tableHeader}
        noDataText="No unapproved users found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        defaultPageSize={10}
        minRows={6}
        filtered={tableFilters}
        onFilteredChange={(filtered) => setTableFilters(filtered)}
        className="-highlight -striped"
      />

      <ModalContainer
        size="ml"
        theme={theme}
        header={`User ${userApproval.isApproved ? 'Approval' : 'Rejection'} -  ${
          userApproval.userId
        }`}
        isOpen={userApproval.showModal}
        toggle={() => toggleApprovalModal()}>
        <form onSubmit={submitAction}>
          <FormGroup>
            <Label for="comment">Comment</Label>
            <Input
              type="textarea"
              name="comment"
              id="comment"
              placeholder="comment"
              onChange={(e) => setComment(e.target.value)}
              value={userApproval.comments}
              required
            />
          </FormGroup>
          <FormGroup className="d-flex justify-content-end">
            <Button type="submit" size="sm" color="primary">
              Submit
            </Button>
          </FormGroup>
        </form>
      </ModalContainer>
    </>
  );
};

UnapprovedUsersTable.propTypes = {
  unapprovedUserslist: PropTypes.array.isRequired,
  userActions: PropTypes.object.isRequired,
  channelslist: PropTypes.array.isRequired,
  userName: PropTypes.string.isRequired,
  editUser: PropTypes.func.isRequired,
  theme: PropTypes.string.isRequired
};

export default UnapprovedUsersTable;
