import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  onFetchHighValueNewAccountCount,
  onFetchHighValueNewAccountData
} from 'actions/rfiReportActions';
import HighValueNewAccountsReport from 'components/rfiReports/HighValueNewAccountsReport';

const mapStateToProps = (state) => {
  return {
    highValueNewAccount: state.rfiReports.highValueNewAccount
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchHighValueNewAccountCount: bindActionCreators(onFetchHighValueNewAccountCount, dispatch),
    fetchHighValueNewAccountData: bindActionCreators(onFetchHighValueNewAccountData, dispatch)
  };
};

const HighValueNewAccountsReportContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(HighValueNewAccountsReport);

export default HighValueNewAccountsReportContainer;
