import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import ViolatedRuleNameBadge from 'components/common/ViolatedRuleNameBadge';

const mapStateToProps = (state) => {
  return {
    ruleNames: state.ruleConfigurator.ruleNames,
    channels: state.auth.userCreds.channels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch)
  };
};

const ViolatedRuleNameBadgeContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(ViolatedRuleNameBadge);

export default ViolatedRuleNameBadgeContainer;
