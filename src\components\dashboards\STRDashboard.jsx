import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';

import { isCooperative } from 'constants/publicKey';
import STRReportListContainer from 'containers/dashboards/STRReportListContainer';

function STRDashboard({ role }) {
  const history = useHistory();

  useEffect(() => {
    if (
      role !== 'principal-officer' &&
      role !== 'maker' &&
      (isCooperative ? role === 'checker' : role !== 'checker')
    )
      history.goBack();
    document.title = 'BANKiQ FRC | STR Dashboard';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  return (
    <div className="content-wrapper">
      <STRReportListContainer />
    </div>
  );
}

STRDashboard.propTypes = {
  role: PropTypes.string.isRequired
};

export default STRDashboard;
