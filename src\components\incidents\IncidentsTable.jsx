import { faFlag, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import moment from 'moment';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import ReactTable from 'react-table';
import { Card, Button } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';
import TableLoader from 'components/loader/TableLoader';
import { getScreen } from 'constants/functions';

function IncidentsTable({ data, actions, toggleCPIFRFormModal, role, channels }) {
  const history = useHistory();

  useEffect(() => {
    actions.onFetchAllIncidents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getPage = (role, caseItem) => `${getScreen(role)}/${channels[0]}/${caseItem.uniqueTxnRef}`;

  const updateFRN = (e, data) => {
    e.preventDefault();
    const { frn } = e.target;
    const formData = {
      internalId: data.internalId,
      frn: frn.value
    };
    actions.onUpdateFRN(formData);
  };

  const selectIncidentById = (incidentId) => {
    const selectedIncident = data.list.find((d) => d.incidentId === incidentId);
    actions.onFetchIncidentByIdSuccess(selectedIncident);
    toggleCPIFRFormModal();
  };

  const headers = [
    {
      Header: '',
      id: 'actions',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 100,

      Cell: (row) => (
        <>
          <Button
            outline
            size="sm"
            title="view"
            color="primary"
            className="me-1"
            onClick={() => history.push(getPage(role, row.original))}
            onContextMenu={() => window.open(getPage(role, row.original))}>
            <FontAwesomeIcon icon={faChevronRight} />
          </Button>
          {_.includes(['checker', 'investigator', 'maker'], role) && (
            <Button
              outline
              size="sm"
              color="warning"
              title="Create Incident report"
              disabled={row.original.isFiled && _.isEmpty(row.original?.frnNo)}
              onClick={() => selectIncidentById(row.original.incidentId)}>
              <FontAwesomeIcon icon={faFlag} />
            </Button>
          )}
        </>
      )
    },
    { Header: 'Incident ID', accessor: 'incidentId' },
    {
      Header: 'Status',
      accessor: 'status',
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value=""> Select </option>
          <option>Open</option>
          <option>Closed</option>
        </select>
      )
    },
    {
      Header: 'Is Filed',
      accessor: 'isFiled',
      Cell: ({ value }) => (value ? 'Y' : 'N'),
      filterMethod: (filter, row) => {
        const rowValue = row[filter.id] ? 'Y' : 'N';
        return filter.value === '' ? true : rowValue === filter.value;
      },
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        <select onChange={(event) => onChange(event.target.value)}>
          <option value=""> Select </option>
          <option>Y</option>
          <option>N</option>
        </select>
      )
    },
    {
      Header: 'FRN',
      accessor: 'frnNo',
      width: 200,

      Cell: (row) => {
        const { frnNo, isFiled } = row.original || {};
        const isClosedStatus = row.status === 'Closed';
        const shouldShowInput = _.isEmpty(frnNo) && !isClosedStatus && isFiled;
        const canEditFRN = role !== 'supervisor';

        if (shouldShowInput && canEditFRN)
          return (
            <form onSubmit={(e) => updateFRN(e, row.original)}>
              <input
                type="text"
                name="frn"
                className="form-control"
                placeholder="Input FRN & press enter"
                pattern="^[FA][A-Za-z0-9]{1,19}$"
                title="The FRN should begin with ‘F’ for actual frauds and ‘A’ in case of attempted frauds"
                required
              />
            </form>
          );

        return frnNo ?? null;
      }
    },
    { Header: 'Updated By', accessor: 'lastUpdatedBy' },
    {
      Header: 'Update date',
      accessor: 'lastUpdateDate',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Customer Name', accessor: 'custRepName' },
    { Header: 'Unique Transaction Ref', accessor: 'uniqueTxnRef' },
    {
      Header: 'Date of Occurrence',
      id: 'occurrenceDate',
      accessor: (row) =>
        row?.isReportedByCustomer === 'Y' ? row?.custDateOfOccRep : row?.dateOfOccRepByBank,
      Cell: ({ value }) => (value ? moment(value, 'DDMMYYYY').format('YYYY-MM-DD') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id], 'DDMMYYYY')
          .format('YYYY-MM-DD')
          .match(new RegExp(filter.value, 'ig'))
    },
    {
      Header: 'Date of Entry',
      id: 'entryDate',
      accessor: (row) =>
        row?.isReportedByCustomer === 'Y' ? row?.custRepDateToBank : row?.dateOfEntry,
      Cell: ({ value }) => (value ? moment(value, 'DDMMYYYY').format('YYYY-MM-DD') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id], 'DDMMYYYY')
          .format('YYYY-MM-DD')
          .match(new RegExp(filter.value, 'ig'))
    },
    { Header: 'Is Attempted Fraud', accessor: 'isAttemptedFraud' },
    { Header: 'Channel', accessor: 'pmtChannel' },
    { Header: 'Fraud Nature', accessor: 'fraudNature' },
    { Header: 'Is Domestic', accessor: 'isDomestic' },
    { Header: 'Total Fraud Amount', accessor: 'amtInvolvedInFraud' },
    { Header: 'Reported By', accessor: 'reportedBy' },
    {
      Header: 'Reporting date',
      accessor: 'reportedDate',
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    }
  ];

  const subHeader = [
    { Header: 'Activity', accessor: 'lastActivity', minWidth: 50 },
    { Header: 'Activity By', accessor: 'lastUpdatedBy', minWidth: 50 },
    {
      Header: 'Activity date',
      accessor: 'lastUpdateDate',
      minWidth: 50,
      Cell: ({ value }) => (value ? moment(value).format('YYYY-MM-DD hh:mm A') : null),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        moment(row[filter.id]).format('YYYY-MM-DD hh:mm A').match(new RegExp(filter.value, 'ig'))
    }
  ];

  const flattenedIncidentsList =
    data.list.length > 0
      ? data.list.map((d) => ({
          ...d.incidents[0],
          incidentId: d.incidentId,
          status: d.status,
          incidentHistory: d.incidentHistory
        }))
      : [];

  const renderContent = () => {
    if (data.loader) return <TableLoader />;

    if (data.error)
      return <div className="no-data-div no-data-card-padding">{data.errorMessage}</div>;

    if (data.list.length === 0)
      return <div className="no-data-div no-data-card-padding">No transactions found</div>;

    return (
      <Card className="mt-3">
        <ReactTable
          defaultFilterMethod={(filter, row) =>
            row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
          }
          columns={headers}
          data={flattenedIncidentsList}
          SubComponent={(row) => (
            <div className="rule-sub-table">
              <ReactTable
                columns={subHeader}
                data={row.original.incidentHistory}
                defaultPageSize={50}
                minRows={1}
                showPagination={false}
              />
            </div>
          )}
          noDataText="No transaction found"
          filterable
          showPaginationTop={true}
          showPaginationBottom={false}
          minRows={5}
          showPageJump={false}
          pageSizeOptions={[5, 10, 20, 30, 40, 50]}
          className="-highlight -striped"
        />
      </Card>
    );
  };

  return <CardContainer title="All Incidents">{renderContent()}</CardContainer>;
}

IncidentsTable.propTypes = {
  data: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  toggleCPIFRFormModal: PropTypes.func.isRequired,
  role: PropTypes.string.isRequired,
  channels: PropTypes.array.isRequired
};

export default IncidentsTable;
