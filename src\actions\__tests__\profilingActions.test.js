import responses from 'mocks/responses';

import * as actions from 'actions/profilingActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

describe('profiling actions', () => {
  it('should fetch entity list', () => {
    const formData = {
      id: 123
    };

    const expectedActions = [
      { type: types.ON_FETCH_ENTITY_LIST_LOADING },
      { type: types.ON_FETCH_ENTITY_LIST_SUCCESS, response: responses.uds.profile }
    ];
    const store = mockStore({ profiling: {} });

    return store.dispatch(actions.onFetchEntityList(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should select entity', () => {
    const entity = {
      id: 123,
      name: 'neha last-name',
      entityCategory: 'merchant',
      mobileNumber: '**********',
      accountNumber: '*************'
    };
    const expectedActions = { type: types.ON_SELECT_ENTITY, entity };
    expect(actions.onSelectEntity(entity)).toEqual(expectedActions);
  });
});
