import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as dynamicCountersAction from 'actions/dynamicCountersAction';
import DynamicCountersForm from 'components/ruleEngine/DynamicCountersForm';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    dynamicCounters: state.dynamicCounters
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    dynamicCountersAction: bindActionCreators(dynamicCountersAction, dispatch)
  };
};

const DynamicCountersFormContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(DynamicCountersForm);

export default DynamicCountersFormContainer;
