import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchDecisionMatrixActions } from 'actions/scpActions';
import DecisionMatrixTable from 'components/scp/DecisionMatrixTable';

const mapStateToProps = (state) => {
  return {
    decisionMatrixActions: state.scp.decisionMatrixActions
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchDecisionMatrixActions: bindActionCreators(onFetchDecisionMatrixActions, dispatch)
  };
};

const DecisionMatrixTableContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(DecisionMatrixTable);

export default DecisionMatrixTableContainer;
