/* eslint-disable react/no-multi-comp */
import _ from 'lodash';
import React, { useEffect, useState, Suspense, lazy } from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Button, ButtonGroup, ListGroup, ListGroupItem, FormGroup, Input } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUserTie,
  faUserClock,
  faUserCog,
  faCheck,
  faCaretDown,
  faCaretRight
} from '@fortawesome/free-solid-svg-icons';
import ConfirmAlert from 'components/common/ConfirmAlert';
import CardContainer from 'components/common/CardContainer';
const CaseAssignmentModal = lazy(() =>
  import('containers/userManagement/CaseAssignmentModalContainer')
);
const RoleAssignmentModal = lazy(() =>
  import('containers/userManagement/RoleAssignmentModalContainer')
);
const ShiftAssignmentModal = lazy(() =>
  import('containers/userManagement/ShiftAssignmentModalContainer')
);
import Loader from 'components/loader/Loader';
import { USER_LIST_HEADER } from 'constants/applicationConstants';
import { isCooperative } from 'constants/publicKey';

function UsersTable({
  roleslist,
  userslist,
  shiftslist,
  channelslist,
  userChannels,
  hasCaseCriteria,
  fetchUsersList,
  userAutoCaseUpdate,
  toggleAssignShiftModal,
  toggleUpdateUserRolesModal,
  toggleUserCaseCriteriaModal,
  theme,
  deleteUser,
  loginType,
  ruleList,
  fetchRules
}) {
  const [selectedUser, setSelectedUser] = useState({});
  const [deleteMode, setDeleteMode] = useState(false);
  useEffect(() => {
    if (_.isEmpty(userslist)) fetchUsersList();
    if (ruleList.length === 0) fetchRules('frm');
  }, []);

  _.remove(
    userslist,
    (user) => _.includes(user.roles, 'admin') || _.includes(user.roles, 'supervisor')
  );

  function getChannelsForUserRole(list, role) {
    let filteredchannelList = list
      .filter((channelRole) => _.includes(channelRole, role))
      .map((channelRole) => _.words(channelRole)[0]);

    let channels = _.uniq(filteredchannelList);

    return channels.length > 0 ? (
      channelslist.length == 1 ? (
        <FontAwesomeIcon icon={faCheck} className="channel-check" />
      ) : (
        <span>{_.join(channels, ', ')}</span>
      )
    ) : null;
  }

  function toggleAutoCaseAssign(userId, userName, newValue) {
    userAutoCaseUpdate({ userId, userName, pauseAutoCase: newValue });
  }

  function getCriteriaList(UserData = []) {
    if (UserData.caseCriteriaInfo.length == 0 && !_.has(UserData, 'ruleCriteria')) return '';
    else {
      return (
        <div className="d-flex ms-5 mb-2 gap-2 flex-column">
          {_.has(UserData, 'ruleCriteria') && (
            <>
              <ListGroup horizontal="lg">
                <span className="me-2 mt-2">Rule categories:</span>
                {UserData?.ruleCriteria?.categoryNames.map((category) => (
                  <ListGroupItem key={category}>{category}</ListGroupItem>
                ))}
              </ListGroup>
              <ListGroup horizontal="lg">
                <span className="me-2 mt-2">Rule Names:</span>
                {_.filter(ruleList, (rule) =>
                  _.includes(UserData?.ruleCriteria?.ruleIds, rule.code)
                ).map((rule) => (
                  <ListGroupItem key={rule.name}>{rule.name}</ListGroupItem>
                ))}
              </ListGroup>
            </>
          )}
          <ListGroup horizontal="lg">
            <span className="me-2 mt-2">Conditions:</span>
            {UserData.caseCriteriaInfo.map((d) => (
              <ListGroupItem key={d.attribute}>
                {`${d.attribute} ${d.operator} ${d.value}`}
              </ListGroupItem>
            ))}
          </ListGroup>
        </div>
      );
    }
  }

  function cancelDelete() {
    setDeleteMode(false);
    setSelectedUser({});
  }

  function confirmDelete() {
    deleteUser(selectedUser);
    setSelectedUser({});
    setDeleteMode(false);
  }

  let filteredRoles = _.filter(
    roleslist,
    (role) =>
      !_.includes(['supervisor', 'admin', 'super-admin'], role.name) &&
      (role.channel?.includes(userChannels[0]) || role.channel?.includes(userChannels[1]))
  );

  const roleColumns = filteredRoles.map((role) => {
    return {
      Header: _.capitalize(role.name),
      accessor: 'channelRoles',
      id: role.name,
      searchable: false,
      filterable: false,
      sortable: false,
      Cell: ({ value }) => getChannelsForUserRole(value, role.name)
    };
  });

  let shiftOptions = shiftslist.map((shift) => (
    <option key={shift.shiftName}>{shift.shiftName}</option>
  ));

  const tableHeader = [
    {
      expander: true,
      // eslint-disable-next-line react/prop-types
      Expander: ({ isExpanded, ...rest }) => {
        if (
          !isCooperative &&
          hasCaseCriteria === 1 &&
          _.includes(userChannels, 'frm') &&
          _.intersection(['maker', 'checker'], rest.original?.roles)?.length > 0
        )
          return <FontAwesomeIcon icon={isExpanded ? faCaretDown : faCaretRight} />;
        else {
          return null;
        }
      },
      getProps: (state, rowInfo) => {
        if (rowInfo) {
          if (!_.intersection(['maker', 'checker'], rowInfo.original?.roles)?.length > 0) {
            return {
              onClick: (e) => {
                e.preventDefault();
              }
            };
          }
        }
        return { className: 'cursor-pointer' };
      }
    },
    {
      Header: 'Action',
      id: 'actions',
      accessor: 'userName',
      searchable: false,
      sortable: false,
      filterable: false,
      minWidth: 120,
      Cell: (row) => (
        <ButtonGroup>
          {(isCooperative ? loginType.toLowerCase() != 'qrt' : true) && (
            <Button
              outline
              size="sm"
              color="warning"
              title="Update Role"
              onClick={() => {
                setSelectedUser(row.original);
                toggleUpdateUserRolesModal();
              }}>
              <FontAwesomeIcon icon={faUserTie} />
            </Button>
          )}
          {_.intersection(userChannels, row.original?.channels)?.length > 0 &&
            !_.isEmpty(shiftslist) &&
            !_.includes(row.original.roles, 'supervisor') && (
              <Button
                outline
                size="sm"
                color="primary"
                title="Update Shift"
                onClick={() => {
                  setSelectedUser(row.original);
                  toggleAssignShiftModal();
                }}>
                <FontAwesomeIcon icon={faUserClock} />
              </Button>
            )}
          {!isCooperative &&
            _.includes(_.intersection(userChannels, row.original?.channels), 'frm') &&
            hasCaseCriteria === 1 &&
            (row.original.roles.includes('maker') || row.original.roles.includes('checker')) && (
              <Button
                outline
                size="sm"
                color="success"
                title="Update Case Assignment Criteria"
                onClick={() => {
                  setSelectedUser(row.original);
                  toggleUserCaseCriteriaModal();
                }}>
                <FontAwesomeIcon icon={faUserCog} />
              </Button>
            )}
        </ButtonGroup>
      )
    },
    ...USER_LIST_HEADER,
    isCooperative
      ? { Header: 'Roles', accessor: 'roles', Cell: ({ value }) => _.join(value, ', ') }
      : { Header: 'Roles', columns: roleColumns },
    {
      Header: 'Shift',
      accessor: 'shiftNames',
      Cell: ({ value }) => _.join(value, ', '),
      filterMethod: (filter, row) =>
        row[filter.id] &&
        filter.value &&
        _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value)),
      // eslint-disable-next-line react/prop-types
      Filter: ({ onChange }) => (
        // eslint-disable-next-line jsx-a11y/no-onchange
        <select placeholder="shifts" onChange={(event) => onChange(event.target.value)}>
          <option value="">All</option>
          {shiftOptions}
        </select>
      )
    },
    {
      Header: 'Auto Assign',
      show: _.includes(userChannels, 'frm') && hasCaseCriteria === 1 && !isCooperative,
      accessor: 'pauseAutoCase',
      Cell: (row) =>
        _.intersection(['maker', 'checker'], row.original?.roles)?.length > 0 ? (
          <FormGroup switch>
            <Input
              type="switch"
              role="switch"
              id={'autoAssignSwitch' + row.original.id}
              name="autoAssignSwitch[]"
              value={row?.value}
              checked={row?.value === 0}
              onChange={() =>
                toggleAutoCaseAssign(
                  row.original.id,
                  row.original.userName,
                  row?.value === 0 ? 1 : 0
                )
              }
            />
          </FormGroup>
        ) : null
    }
  ];

  return (
    <CardContainer title={'User List'}>
      <ReactTable
        defaultFilterMethod={(filter, row) =>
          row[filter.id] && _.includes(_.lowerCase(row[filter.id]), _.lowerCase(filter.value))
        }
        data={userslist}
        columns={tableHeader}
        SubComponent={(row) => getCriteriaList(row?.original || [])}
        noDataText="No users found"
        filterable
        showPaginationTop={true}
        showPaginationBottom={false}
        pageSizeOptions={[5, 10, 20, 30, 40, 50]}
        defaultPageSize={10}
        minRows={6}
        className={'-highlight  -striped'}
      />
      {!_.isEmpty(selectedUser) && (
        <Suspense fallback={<Loader show={true} />}>
          <RoleAssignmentModal selectedUser={selectedUser} userslist={userslist} />
          <ShiftAssignmentModal selectedUser={selectedUser} />
        </Suspense>
      )}
      {!_.isEmpty(selectedUser) && hasCaseCriteria === 1 && (
        <Suspense fallback={<Loader show={true} />}>
          <CaseAssignmentModal selectedUser={selectedUser} />
        </Suspense>
      )}
      <ConfirmAlert
        theme={theme}
        confirmAlertModal={deleteMode}
        toggleConfirmAlertModal={cancelDelete}
        confirmationAction={confirmDelete}
        confirmAlertTitle={`Are you sure you want to delete ${selectedUser.userName} user ?`}
      />
    </CardContainer>
  );
}

UsersTable.propTypes = {
  roleslist: PropTypes.array.isRequired,
  userslist: PropTypes.array.isRequired,
  shiftslist: PropTypes.array.isRequired,
  channelslist: PropTypes.array.isRequired,
  userChannels: PropTypes.array.isRequired,
  hasCaseCriteria: PropTypes.number.isRequired,
  fetchUsersList: PropTypes.func.isRequired,
  userAutoCaseUpdate: PropTypes.func.isRequired,
  toggleAssignShiftModal: PropTypes.func.isRequired,
  toggleUpdateUserRolesModal: PropTypes.func.isRequired,
  toggleUserCaseCriteriaModal: PropTypes.func.isRequired,
  theme: PropTypes.string.isRequired,
  deleteUser: PropTypes.func.isRequired,
  loginType: PropTypes.string.isRequired,
  ruleList: PropTypes.array.isRequired,
  fetchRules: PropTypes.func.isRequired
};

export default UsersTable;
