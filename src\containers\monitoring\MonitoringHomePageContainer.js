import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onShowFailureAlert } from 'actions/alertActions';
import * as monitoringActions from 'actions/monitoringActions';
import { onFetchUsersList } from 'actions/userManagementActions';
import { onFetchRulesList } from 'actions/ruleConfiguratorActions';
import { onCreateCaseAndAssign } from 'actions/caseAssignmentActions';
import MonitoringHomePage from 'components/monitoring/MonitoringHomePage';

const mapStateToProps = (state) => {
  return {
    theme: state.toggle.theme,
    authDetails: state.auth.userCreds,
    userslist: state.user.userslist,
    ruleConfigurator: state.ruleConfigurator,
    monitoringData: state.monitor,
    channels: state.auth.userCreds.channels
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRulesList: bindActionCreators(onFetchRulesList, dispatch),
    fetchUsersList: bindActionCreators(onFetchUsersList, dispatch),
    monitoringActions: bindActionCreators(monitoringActions, dispatch),
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    createCaseAndAssign: bindActionCreators(onCreateCaseAndAssign, dispatch)
  };
};

const MonitoringHomePageContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(MonitoringHomePage);

export default MonitoringHomePageContainer;
