import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onFetchNoViolationFraud } from 'actions/businessDashboardActions';
import NonViolationFrauds from 'components/dashboards/NonViolationFrauds';

const mapStateToProps = (state) => {
  return {
    noViolationFraud: state.businessDashboard.noViolationFraud
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchNoViolationFraud: bindActionCreators(onFetchNoViolationFraud, dispatch)
  };
};

const NonViolationFraudsContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)(NonViolationFrauds);

export default NonViolationFraudsContainer;
