import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { onShowFailureAlert } from 'actions/alertActions';
import { onFetchRuleNamesList } from 'actions/ruleConfiguratorActions';
import { onAdvanceSearchTxn } from 'actions/transactionHistorySearchActions';
import AdvanceSearchForm from 'components/advanceSearch/AdvanceSearchForm';

const mapStateToProps = (state) => {
  return {
    channels: state.auth.userCreds.channels,
    ruleNames: state.ruleConfigurator.ruleNames,
    hasProvisionalFields: state.user.configurations.provisionalFields
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    showFailureAlert: bindActionCreators(onShowFailureAlert, dispatch),
    advanceSearchTxn: bindActionCreators(onAdvanceSearchTxn, dispatch),
    fetchRuleNamesList: bindActionCreators(onFetchRuleNamesList, dispatch)
  };
};

const AdvanceSearchFormContainer = connect(mapStateToProps, mapDispatchToProps)(AdvanceSearchForm);

export default AdvanceSearchFormContainer;
