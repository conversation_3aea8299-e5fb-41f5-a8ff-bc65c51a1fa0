import {
  ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS,
  ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE,
  ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING,
  ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS,
  ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE
} from 'constants/actionTypes';
import client from 'utility/apiClient';

function fetchDemographicDetails(entity, entityId) {
  return client({
    url: `prefilter/${entity}/${entityId}`
  });
}

function onFetchMerchantDemographicDetailsLoading() {
  return { type: ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_LOADING };
}

function onSuccessfulFetchMerchantDemographicDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS,
    response
  };
}

function onFailureFetchMerchantDemographicDetails(response) {
  return {
    type: ON_FETCH_MERCHANT_DEMOGRAPHIC_DETAILS_FAILURE,
    response
  };
}

function onFetchMerchantDemographicDetails(merchantId) {
  return function (dispatch) {
    dispatch(onFetchMerchantDemographicDetailsLoading());
    return fetchDemographicDetails('merchantId', merchantId).then(
      (success) => {
        const resp = JSON.parse(success);
        resp.error
          ? dispatch(onFailureFetchMerchantDemographicDetails(resp))
          : dispatch(onSuccessfulFetchMerchantDemographicDetails(resp));
      },
      (error) => {
        dispatch(onFailureFetchMerchantDemographicDetails(error));
      }
    );
  };
}

function onFetchAgentDemographicDetailsLoading() {
  return { type: ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_LOADING };
}

function onSuccessfulFetchAgentDemographicDetails(response) {
  return {
    type: ON_SUCCESSFUL_FETCH_AGENT_DEMOGRAPHIC_DETAILS,
    response
  };
}

function onFailureFetchAgentDemographicDetails(response) {
  return {
    type: ON_FETCH_AGENT_DEMOGRAPHIC_DETAILS_FAILURE,
    response
  };
}

function onFetchAgentDemographicDetails(agentId) {
  return function (dispatch) {
    dispatch(onFetchAgentDemographicDetailsLoading());
    return fetchDemographicDetails('agentId', agentId).then(
      (success) => {
        const resp = JSON.parse(success);
        resp.error
          ? dispatch(onFailureFetchAgentDemographicDetails(resp))
          : dispatch(onSuccessfulFetchAgentDemographicDetails(resp));
      },
      (error) => {
        dispatch(onFailureFetchAgentDemographicDetails(error));
      }
    );
  };
}

export { onFetchMerchantDemographicDetails, onFetchAgentDemographicDetails };
