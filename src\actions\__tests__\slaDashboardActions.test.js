import responses from 'mocks/responses';

import * as actions from 'actions/slaDashboardActions';
import * as types from 'constants/actionTypes';
import { mockStore } from 'store/mockStoreConfiguration';

const userCreds = {
  userId: 1,
  email: '<EMAIL>',
  userName: 'abc',
  channelRoles: ['frm:checker'],
  roles: 'checker',
  channels: ['frm']
};

const mockedStore = {
  auth: { userCreds },
  slaDashboard: {}
};

describe('sla Dashboard actions', () => {
  it('should Fetch Sla Kpis', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      users: []
    };
    const expectedActions = [
      { type: types.ON_FETCH_SLA_KPI_LOADING },
      {
        type: types.ON_FETCH_SLA_KPI_SUCCESS,
        response: responses.slaDashboard.slaKpis
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSlaKpis(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Analyst TAT', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      usersId: 'user123'
    };
    const expectedActions = [
      { type: types.ON_FETCH_ANALYST_TAT_LOADING },
      {
        type: types.ON_FETCH_ANALYST_TAT_SUCCESS,
        response: responses.slaDashboard.analystTAT
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchAnalystTAT(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Shift Details', () => {
    const expectedActions = [
      { type: types.ON_FETCH_SHIFT_DETAILS_LOADING },
      {
        type: types.ON_FETCH_SHIFT_DETAILS_SUCCESS,
        response: responses.slaDashboard.shiftDetails
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchShiftDetails()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Employees Sla', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      users: []
    };
    const expectedActions = [
      { type: types.ON_FETCH_EMPLOYEE_SLA_LOADING },
      {
        type: types.ON_FETCH_EMPLOYEE_SLA_SUCCESS,
        response: responses.slaDashboard.employeeSla
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchEmployeesSla(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Occupancy Rate', () => {
    const expectedActions = [
      { type: types.ON_FETCH_OCCUPANCY_RATE_LOADING },
      {
        type: types.ON_FETCH_OCCUPANCY_RATE_SUCCESS,
        response: responses.slaDashboard.occupancyRate
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchOccupancyRate()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch Sla Breach Cases', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      users: []
    };
    const expectedActions = [
      { type: types.ON_FETCH_SLA_BREACH_CASES_LOADING },
      {
        type: types.ON_FETCH_SLA_BREACH_CASES_SUCCESS,
        response: responses.slaDashboard.slaBreachCases
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchSlaBreachCases(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it('should Fetch First Contact Rate', () => {
    const formData = {
      endDate: '2023-11-03T23:59:59',
      startDate: '2023-11-03T00:00:00',
      users: []
    };
    const expectedActions = [
      { type: types.ON_FETCH_FIRST_CONTACT_RATE_LOADING },
      {
        type: types.ON_FETCH_FIRST_CONTACT_RATE_SUCCESS,
        response: responses.slaDashboard.firstContactRate
      }
    ];
    const store = mockStore(mockedStore);

    return store.dispatch(actions.onFetchFirstContactRate(formData)).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
