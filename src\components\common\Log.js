import _ from 'lodash';
import React from 'react';
import Moment from 'moment';
import PropTypes from 'prop-types';
import { Card, Row, Col, Nav, NavItem } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock } from '@fortawesome/free-regular-svg-icons';

import CardContainer from 'components/common/CardContainer';

class Log extends React.Component {
  constructor(props) {
    super(props);
    this._getLogs = this._getLogs.bind(this);
    this._selectActivity = this._selectActivity.bind(this);
    this._getActivityList = this._getActivityList.bind(this);
    this.state = {
      selectedActivity: {}
    };
  }

  componentDidMount() {
    this._getLogs();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.id !== this.props.id) {
      this._getLogs();
    }
  }

  _getLogs() {
    const { module, id, fetchCaseLogs } = this.props;
    id && fetchCaseLogs(module, id);
  }

  _selectActivity = (activity) => this.setState({ selectedActivity: activity });

  _getActivityList(list) {
    const { selectedActivity } = this.state;

    return !_.isEmpty(list) ? (
      list
        .sort((a, b) =>
          a.logTimestamp > b.logTimestamp ? 1 : a.logTimestamp < b.logTimestamp ? -1 : 0
        )
        .map((activity, count) => {
          return (
            <NavItem
              key={count}
              active={
                selectedActivity.activityType === activity.activityType &&
                selectedActivity.logTimestamp === activity.logTimestamp
              }
              className="flex-fill log-list-item">
              {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
              <div
                role="button"
                aria-pressed="false"
                tabIndex={0}
                className={'case-box p-2 cursor-pointer '}
                onClick={() => this._selectActivity(activity)}>
                <div className="d-flex justify-content-between flex-wrap">
                  <span>{_.startCase(activity.activityType)}</span>
                  <span className="case-duration mb-2">
                    <FontAwesomeIcon icon={faClock} />{' '}
                    {activity.logTimestamp &&
                      Moment(activity.logTimestamp).format('YYYY-MM-DD hh:mm A')}
                  </span>
                </div>
              </div>
            </NavItem>
          );
        })
    ) : (
      <span className="p-3">No activity found</span>
    );
  }

  render() {
    const { logs, id } = this.props;
    const { selectedActivity } = this.state;
    return (
      <CardContainer title="Activity Logs">
        {_.isEmpty(id) ? (
          <div className="no-data-div">No caseId found</div>
        ) : _.isEmpty(logs.list) ? (
          <div className="no-data-div">No logs found</div>
        ) : (
          <Card>
            <Row className="mt-3">
              <Col>
                <Nav className={'d-flex flex-column '}> {this._getActivityList(logs.list)}</Nav>
              </Col>
              <Col>
                {!_.isEmpty(selectedActivity) && selectedActivity.entityId == id ? (
                  <div className={'comment '}>
                    <div className="d-flex justify-content-between flex-wrap comment-header">
                      <p>{_.startCase(selectedActivity.activityType)}</p>
                      <p className="comment-timestamp">
                        <FontAwesomeIcon icon={faClock} />{' '}
                        {selectedActivity.logTimestamp
                          ? Moment(selectedActivity.logTimestamp).format('YYYY-MM-DD hh:mm A')
                          : ''}
                      </p>
                    </div>
                    <p>{selectedActivity.description}</p>
                    <p className="mt-3">Activity by : {selectedActivity.userName}</p>
                  </div>
                ) : (
                  <div className="no-data-div">Please select an activity log</div>
                )}
              </Col>
            </Row>
          </Card>
        )}
      </CardContainer>
    );
  }
}

Log.propTypes = {
  id: PropTypes.string.isRequired,
  logs: PropTypes.object.isRequired,
  module: PropTypes.string.isRequired,
  fetchCaseLogs: PropTypes.func.isRequired
};

export default Log;
