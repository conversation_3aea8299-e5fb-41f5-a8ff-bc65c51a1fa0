import { faClock } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import _ from 'lodash';
import Moment from 'moment';
import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Nav, NavItem } from 'reactstrap';

import CardContainer from 'components/common/CardContainer';

const Log = ({ module, id, fetchCaseLogs, logs }) => {
  const [selectedActivity, setSelectedActivity] = useState({});

  useEffect(() => {
    if (id) fetchCaseLogs(module, id);
  }, [id, module, fetchCaseLogs]);

  const getActivityList = (list) => {
    if (_.isEmpty(list)) return <span className="p-3">No activity found</span>;

    const sortedList = list.sort((a, b) => {
      if (a.logTimestamp > b.logTimestamp) return 1;
      if (a.logTimestamp < b.logTimestamp) return -1;
      return 0;
    });

    return sortedList.map((activity, count) => (
      <NavItem
        key={count}
        active={
          selectedActivity.activityType === activity.activityType &&
          selectedActivity.logTimestamp === activity.logTimestamp
        }
        className="flex-fill log-list-item">
        {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
        <div
          role="button"
          aria-pressed="false"
          tabIndex={0}
          className="case-box p-2 cursor-pointer "
          onClick={() => setSelectedActivity(activity)}>
          <div className="d-flex justify-content-between flex-wrap">
            <span>{_.startCase(activity.activityType)}</span>
            <span className="case-duration mb-2">
              <FontAwesomeIcon icon={faClock} />{' '}
              {activity.logTimestamp && Moment(activity.logTimestamp).format('YYYY-MM-DD hh:mm A')}
            </span>
          </div>
        </div>
      </NavItem>
    ));
  };

  const renderMainContent = () => {
    if (_.isEmpty(id)) return <div className="no-data-div">No caseId found</div>;

    if (_.isEmpty(logs.list)) return <div className="no-data-div">No logs found</div>;

    return (
      <Card>
        <Row className="mt-3">
          <Col>
            <Nav className="d-flex flex-column "> {getActivityList(logs.list)}</Nav>
          </Col>
          <Col>
            {!_.isEmpty(selectedActivity) && selectedActivity.entityId === id ? (
              <div className="comment ">
                <div className="d-flex justify-content-between flex-wrap comment-header">
                  <p>{_.startCase(selectedActivity.activityType)}</p>
                  <p className="comment-timestamp">
                    <FontAwesomeIcon icon={faClock} />{' '}
                    {selectedActivity.logTimestamp
                      ? Moment(selectedActivity.logTimestamp).format('YYYY-MM-DD hh:mm A')
                      : ''}
                  </p>
                </div>
                <p>{selectedActivity.description}</p>
                <p className="mt-3">Activity by : {selectedActivity.userName}</p>
              </div>
            ) : (
              <div className="no-data-div">Please select an activity log</div>
            )}
          </Col>
        </Row>
      </Card>
    );
  };

  return <CardContainer title="Activity Logs">{renderMainContent()}</CardContainer>;
};

Log.propTypes = {
  id: PropTypes.string.isRequired,
  logs: PropTypes.object.isRequired,
  module: PropTypes.string.isRequired,
  fetchCaseLogs: PropTypes.func.isRequired
};

export default Log;
