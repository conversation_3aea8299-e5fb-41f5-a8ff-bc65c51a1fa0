'use strict';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { TabPane } from 'reactstrap';

import Tabs from 'components/common/Tabs';
import LimitLists from 'containers/prefilters/limitLists/LimitListsContainer';
import SpecializedLists from 'containers/prefilters/specializedLists/SpecializedListsContainer';

const PrefiltersMain = (props) => {
  const { userCreds, moduleType } = props.authDetails;
  const prefilters =
    moduleType === 'issuer' ? ['Specialized List'] : ['Specialized List', 'Limit List'];

  useEffect(() => {
    document.title = 'BANKiQ FRC | Prefilters';

    return () => {
      document.title = 'BANKiQ FRC';
    };
  }, []);

  return (
    <div className="content-wrapper ">
      <Tabs tabNames={prefilters} pills>
        <TabPane tabId={0}>
          <SpecializedLists role={userCreds.roles} />
        </TabPane>
        <TabPane tabId={1}>
          <LimitLists role={userCreds.roles} />
        </TabPane>
      </Tabs>
    </div>
  );
};

PrefiltersMain.propTypes = {
  authDetails: PropTypes.object.isRequired
};

export default PrefiltersMain;
