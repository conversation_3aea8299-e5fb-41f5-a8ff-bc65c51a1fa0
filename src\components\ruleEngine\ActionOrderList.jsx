import React from 'react';
import PropTypes from 'prop-types';
import ReOrderingList from 'components/common/ReOrderingList';

function ActionOrderList({ channel, actionList, updateActionList }) {
  const formatter = (listItem) => `${listItem.actionCode} - ${listItem.actionName}`;

  const submitUpdatedOrder = (channel) => (list) =>
    updateActionList(channel, { updatedRuleOrder: list });

  const curriedSubmit = submitUpdatedOrder(channel);

  return (
    <>
      <small className="text-muted">
        <b>*</b> <em>FRM Rule Engine Service</em> needs to be <em>RESTARTED</em> for changes to
        apply <b>*</b>
      </small>
      <ReOrderingList
        idKey="actionCode"
        formatter={formatter}
        list={actionList}
        submitUpdatedOrder={(list) => curriedSubmit(list)}
      />
    </>
  );
}

ActionOrderList.propTypes = {
  channel: PropTypes.string.isRequired,
  actionList: PropTypes.array.isRequired,
  updateActionList: PropTypes.func.isRequired
};

export default ActionOrderList;
