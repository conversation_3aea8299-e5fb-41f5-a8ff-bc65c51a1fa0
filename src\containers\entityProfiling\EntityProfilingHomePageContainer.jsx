import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as profilingActions from 'actions/profilingActions';
import {
  onFetchTransactionStatisticsDetails,
  onFetchCustomerStatisticsDetails
} from 'actions/statisticsDetailsAction';
import * as transactionHistorySearchActions from 'actions/transactionHistorySearchActions';
import EntityProfilingHomePage from 'components/entityProfiling/EntityProfilingHomePage';
import { DateRangeProvider } from 'context/DateRangeContext';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  profiling: state.profiling,
  role: state.auth.userCreds.roles,
  moduleType: state.auth.moduleType,
  statisticsDetails: state.statisticsDetails,
  channels: state.auth.userCreds.channels
});

const mapDispatchToProps = (dispatch) => ({
  profilingActions: bindActionCreators(profilingActions, dispatch),
  transactionHistorySearchActions: bindActionCreators(transactionHistorySearchActions, dispatch),
  fetchTransactionStatisticsDetails: bindActionCreators(
    onFetchTransactionStatisticsDetails,
    dispatch
  ),
  fetchCustomerStatisticsDetails: bindActionCreators(onFetchCustomerStatisticsDetails, dispatch)
});

const EntityProfilingHomePageContainer = connect(
  mapStateToProps,
  mapDispatchToProps
)((props) => (
  <DateRangeProvider contextKey="entityProfilling">
    <EntityProfilingHomePage {...props} />
  </DateRangeProvider>
));

export default EntityProfilingHomePageContainer;
