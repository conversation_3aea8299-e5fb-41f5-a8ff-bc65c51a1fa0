import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { onReOpenCase } from 'actions/caseAssignmentActions';
import STRCaseReopen from 'components/common/STRCaseReopen';

const mapStateToProps = (state) => ({
  theme: state.toggle.theme,
  userId: state.auth.userCreds.userId,
  role: state.auth.userCreds.roles,
  usersList: state.user.userslist
});

const mapDispatchToProps = (dispatch) => ({
  reOpenCase: bindActionCreators(onReOpenCase, dispatch)
});

export default connect(mapStateToProps, mapDispatchToProps)(STRCaseReopen);
