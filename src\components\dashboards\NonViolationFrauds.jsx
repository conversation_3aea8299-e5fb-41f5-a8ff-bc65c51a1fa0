import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Table } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretRight } from '@fortawesome/free-solid-svg-icons';

import CardContainer from 'components/common/CardContainer';

function NonViolationFrauds({ xchannelId, period, noViolationFraud, fetchNoViolationFraud }) {
  useEffect(() => {
    fetchNoViolationFraud({ ...period, xchannelId });
  }, [xchannelId, period.startDate]);

  const tableRows =
    noViolationFraud.data.length > 0 ? (
      noViolationFraud.data?.map((d) => (
        <tr key={d?.txnId}>
          <td>{d?.txnId}</td>
          <td>{d?.customerName}</td>
          <td>{d?.txnAmount}</td>
          <td>
            <Link
              to={{
                pathname: '/search',
                state: { txnId: d?.txnId }
              }}>
              <FontAwesomeIcon size="lg" icon={faCaretRight} />
            </Link>
          </td>
        </tr>
      ))
    ) : (
      <tr>
        <td colSpan={5}>No data found</td>
      </tr>
    );

  return (
    <CardContainer className="card-height-500" title="Fraud transactions w/o violation">
      <Table>
        <thead>
          <tr>
            <th>Txn Id</th>
            <th>Customer Name</th>
            <th>Amount</th>
            <th> </th>
          </tr>
        </thead>
        <tbody>{tableRows}</tbody>
      </Table>
    </CardContainer>
  );
}

NonViolationFrauds.propTypes = {
  period: PropTypes.object.isRequired,
  xchannelId: PropTypes.string.isRequired,
  noViolationFraud: PropTypes.object.isRequired,
  fetchNoViolationFraud: PropTypes.func.isRequired
};

export default NonViolationFrauds;
