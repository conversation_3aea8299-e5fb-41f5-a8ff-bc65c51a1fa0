import { useCallback } from 'react';
import generatePDF, { Resolution } from 'react-to-pdf';
import { useScreenshot, createFileName } from 'use-react-screenshot';

const useDownloadableDashboard = (elementId, fileNamePrefix, dashboardRef) => {
  const [, takeScreenshot] = useScreenshot({ type: 'image/jpeg' });

  const getTargetElement = useCallback(() => document.getElementById(elementId), [elementId]);

  const downloadImage = useCallback((imageURL, { fileName, fileExtension = 'jpg' } = {}) => {
    const anchorElement = document.createElement('a');
    anchorElement.href = imageURL;
    anchorElement.download = createFileName(fileExtension, fileName);
    anchorElement.click();
  }, []);

  const downloadScreenshot = useCallback(() => {
    takeScreenshot(dashboardRef.current).then((imageURL) =>
      downloadImage(imageURL, { fileName: `${fileNamePrefix}` })
    );
  }, [dashboardRef, takeScreenshot, downloadImage, fileNamePrefix]);

  const downloadPDF = useCallback(() => {
    generatePDF(getTargetElement, {
      resolution: Resolution.HIGH,
      page: { orientation: 'landscape' },
      filename: `${fileNamePrefix}.pdf`
    });
  }, [getTargetElement, fileNamePrefix]);

  return {
    dashboardRef,
    downloadScreenshot,
    downloadPDF
  };
};

export default useDownloadableDashboard;
